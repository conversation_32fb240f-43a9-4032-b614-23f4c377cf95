<template>
  <div class="image-component" :style="containerStyle">
    <img 
      v-if="imageUrl" 
      :src="imageUrl" 
      :style="imageStyle" 
      alt="组态图片"
      @error="handleImageError"
    />
    <div v-else class="image-placeholder">
      <el-icon><Picture /></el-icon>
      <span>{{ editing ? '点击上传图片' : '暂无图片' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
  editing?: boolean
}>()

// 图片加载错误
const imageLoadError = ref(false)

// 容器样式
const containerStyle = computed(() => {
  const { style } = props.component
  
  return {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    borderRadius: style.borderRadius ? `${style.borderRadius}px` : '0',
    backgroundColor: style.backgroundColor || 'transparent',
    border: style.borderWidth ? `${style.borderWidth}px solid ${style.borderColor || '#ddd'}` : 'none',
    boxShadow: style.boxShadow || 'none'
  }
})

// 图片样式
const imageStyle = computed(() => {
  const { style } = props.component
  
  // 获取图片填充模式
  const objectFit = style.objectFit || 'cover'
  
  return {
    width: '100%',
    height: '100%',
    objectFit,
    objectPosition: 'center'
  }
})

// 图片URL
const imageUrl = computed(() => {
  if (imageLoadError.value) return ''
  
  const { data } = props.component
  
  // 如果有动态数据，优先使用动态数据
  if (data.dynamic && data.dynamic.value) {
    return data.dynamic.value
  }
  
  // 否则使用静态数据
  return data.static || ''
})

// 处理图片加载错误
const handleImageError = () => {
  imageLoadError.value = true
}
</script>

<style scoped>
.image-component {
  position: relative;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  color: #909399;
}

.image-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}
</style>