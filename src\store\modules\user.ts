import { to } from 'await-to-js'
import defAva from '@/assets/images/profile.jpg'
import store from '@/store'
import { getToken, removeToken, setToken } from '@/utils/auth'
import { login as loginApi, logout as logoutApi, getInfo as getUserInfo } from '@/api/login'
import { LoginData } from '@/api/types'
import { listByIds } from '@/api/system/oss'
import { noTokenLogin } from '@/api/login'
import { listProjectRelation } from '@/api/system/user'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import { log } from 'console'
export const useUserStore = defineStore('user', () => {
  const token = ref(getToken())
  const name = ref('')
  const nickname = ref('')
  const userId = ref<string | number>('')
  const avatar = ref('')
  const roles = ref<Array<string>>([]) // 用户角色编码集合 → 判断路由权限
  const permissions = ref<Array<string>>([]) // 用户权限编码集合 → 判断按钮权限

  /**
   * 登录
   * @param userInfo
   * @returns
   */
  const login = async (userInfo: LoginData): Promise<void> => {
    const [err, res] = await to(loginApi(userInfo))
    if (res) {
      const data = res.data
      setToken(data.token)
      token.value = data.token
      return Promise.resolve()
    }
    return Promise.reject(err)
  }
  const noTokenLoginp = async (appCode: string): Promise<void> => {
    const [err, res] = await to(noTokenLogin(appCode))
    if (err || !res || res.code !== 200) {
      return Promise.reject(err || new Error('免登录失败'))
    }

    const data = res.data
    setToken(data.token)
    token.value = data.token

    // 获取项目关系列表
    const [listErr, projectList] = await to(listProjectRelation())
    if (!listErr && projectList) {
      const { wsCache } = useLocalCache()
      const cachedProjectList = wsCache.get(CACHE_KEY.projectLists)
      if (cachedProjectList) {
        wsCache.set(CACHE_KEY.projectLists, projectList)
        const selectedlistProjectItems = projectList.data[0]
        wsCache.set(CACHE_KEY.projectList, selectedlistProjectItems)
      } else {
        wsCache.set(CACHE_KEY.projectLists, projectList)
        const selectedlistProjectItems = projectList.data[0]
        wsCache.set(CACHE_KEY.projectList, selectedlistProjectItems)
      }
    }

    return Promise.resolve()
  }

  // 获取用户信息
  const getInfo = async (): Promise<void> => {
    const [err, res] = await to(getUserInfo())
    if (res) {
      const data = res.data
      const user = data.user
      const profile = user.avatar
      const profileArr = Array.isArray(profile) ? profile : [profile]
      if (data.roles && data.roles.length > 0) {
        // 验证返回的roles是否是一个非空数组
        roles.value = data.roles
        permissions.value = data.permissions
      } else {
        roles.value = ['ROLE_DEFAULT']
      }
      name.value = user.userName
      nickname.value = user.nickName
      if (profile) {
        const ossObj = await listByIds(profileArr)
        if (ossObj.data) avatar.value = ossObj.data[0].url
      } else {
        avatar.value = defAva
      }
      userId.value = user.id
      return Promise.resolve()
    }
    return Promise.reject(err)
  }

  // 注销
  const logout = async (): Promise<void> => {
    await logoutApi()
    token.value = ''
    roles.value = []
    permissions.value = []
    removeToken()
  }

  return {
    userId,
    token,
    nickname,
    avatar,
    roles,
    permissions,
    login,
    getInfo,
    logout,
    noTokenLoginp,
  }
})

export default useUserStore
// 非setup
export function useUserStoreHook() {
  return useUserStore(store)
}
