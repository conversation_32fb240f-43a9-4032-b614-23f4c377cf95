// 数据服务 - 用于模拟和管理实时数据
export interface DataPoint {
  id: string
  name: string
  value: number
  unit: string
  timestamp: number
  min?: number
  max?: number
}

export interface DataSource {
  id: string
  name: string
  type: 'simulation' | 'api' | 'websocket'
  url?: string
  dataPoints: DataPoint[]
}

class DataService {
  private dataSources: Map<string, DataSource> = new Map()
  private subscribers: Map<string, Set<(data: DataPoint) => void>> = new Map()
  private simulationIntervals: Map<string, number> = new Map()

  constructor() {
    this.initializeDefaultDataSources()
  }

  // 初始化默认数据源
  private initializeDefaultDataSources() {
    // 储罐液位数据源
    const tankDataSource: DataSource = {
      id: 'tank-levels',
      name: '储罐液位监测',
      type: 'simulation',
      dataPoints: [
        {
          id: 'tank-001-level',
          name: '1号储罐液位',
          value: 750,
          unit: 'L',
          timestamp: Date.now(),
          min: 0,
          max: 1000
        },
        {
          id: 'tank-002-level',
          name: '2号储罐液位',
          value: 450,
          unit: 'L',
          timestamp: Date.now(),
          min: 0,
          max: 800
        },
        {
          id: 'tank-003-level',
          name: '3号储罐液位',
          value: 1200,
          unit: 'L',
          timestamp: Date.now(),
          min: 0,
          max: 1500
        }
      ]
    }

    this.dataSources.set(tankDataSource.id, tankDataSource)
    this.startSimulation(tankDataSource.id)
  }

  // 获取所有数据源
  getDataSources(): DataSource[] {
    return Array.from(this.dataSources.values())
  }

  // 获取指定数据源
  getDataSource(id: string): DataSource | undefined {
    return this.dataSources.get(id)
  }

  // 获取数据点
  getDataPoint(dataSourceId: string, dataPointId: string): DataPoint | undefined {
    const dataSource = this.dataSources.get(dataSourceId)
    if (!dataSource) return undefined
    
    return dataSource.dataPoints.find(dp => dp.id === dataPointId)
  }

  // 订阅数据点变化
  subscribe(dataPointId: string, callback: (data: DataPoint) => void) {
    if (!this.subscribers.has(dataPointId)) {
      this.subscribers.set(dataPointId, new Set())
    }
    this.subscribers.get(dataPointId)!.add(callback)

    // 返回取消订阅函数
    return () => {
      const callbacks = this.subscribers.get(dataPointId)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          this.subscribers.delete(dataPointId)
        }
      }
    }
  }

  // 更新数据点值
  updateDataPoint(dataSourceId: string, dataPointId: string, value: number) {
    const dataSource = this.dataSources.get(dataSourceId)
    if (!dataSource) return

    const dataPoint = dataSource.dataPoints.find(dp => dp.id === dataPointId)
    if (!dataPoint) return

    dataPoint.value = value
    dataPoint.timestamp = Date.now()

    // 通知订阅者
    const callbacks = this.subscribers.get(dataPointId)
    if (callbacks) {
      callbacks.forEach(callback => callback(dataPoint))
    }
  }

  // 开始模拟数据
  private startSimulation(dataSourceId: string) {
    const dataSource = this.dataSources.get(dataSourceId)
    if (!dataSource || dataSource.type !== 'simulation') return

    const interval = setInterval(() => {
      dataSource.dataPoints.forEach(dataPoint => {
        // 模拟数据变化（在min-max范围内随机波动）
        const min = dataPoint.min || 0
        const max = dataPoint.max || 100
        const range = max - min
        const variation = range * 0.05 // 5%的变化幅度
        
        let newValue = dataPoint.value + (Math.random() - 0.5) * variation
        newValue = Math.max(min, Math.min(max, newValue))
        
        this.updateDataPoint(dataSourceId, dataPoint.id, Math.round(newValue))
      })
    }, 2000) // 每2秒更新一次

    this.simulationIntervals.set(dataSourceId, interval)
  }

  // 停止模拟
  stopSimulation(dataSourceId: string) {
    const interval = this.simulationIntervals.get(dataSourceId)
    if (interval) {
      clearInterval(interval)
      this.simulationIntervals.delete(dataSourceId)
    }
  }

  // 添加新数据源
  addDataSource(dataSource: DataSource) {
    this.dataSources.set(dataSource.id, dataSource)
    if (dataSource.type === 'simulation') {
      this.startSimulation(dataSource.id)
    }
  }

  // 移除数据源
  removeDataSource(id: string) {
    this.stopSimulation(id)
    this.dataSources.delete(id)
  }

  // 销毁服务
  destroy() {
    this.simulationIntervals.forEach((interval, id) => {
      clearInterval(interval)
    })
    this.simulationIntervals.clear()
    this.subscribers.clear()
    this.dataSources.clear()
  }
}

// 创建单例实例
export const dataService = new DataService()

// 导出类型
export type { DataService }
