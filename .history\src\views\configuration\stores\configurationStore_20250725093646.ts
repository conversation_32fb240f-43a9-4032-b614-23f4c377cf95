import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  ConfigurationProject, 
  ConfigurationComponent, 
  EditorState,
  DataBinding 
} from '../types'

export const useConfigurationStore = defineStore('configuration', () => {
  // 状态
  const projects = ref<ConfigurationProject[]>([])
  const currentProject = ref<ConfigurationProject | null>(null)
  const editorState = ref<EditorState>({
    selectedComponents: [],
    clipboard: [],
    history: {
      past: [],
      present: null as any,
      future: []
    },
    zoom: 1,
    grid: {
      show: true,
      size: 20,
      snap: true
    },
    rulers: true,
    layers: true
  })

  // 计算属性
  const selectedComponents = computed(() => {
    if (!currentProject.value) return []
    return currentProject.value.components.filter(
      comp => editorState.value.selectedComponents.includes(comp.id)
    )
  })

  const canUndo = computed(() => editorState.value.history.past.length > 0)
  const canRedo = computed(() => editorState.value.history.future.length > 0)

  // 项目管理
  const createProject = (project: Omit<ConfigurationProject, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProject: ConfigurationProject = {
      ...project,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    projects.value.push(newProject)
    return newProject
  }

  const updateProject = (id: string, updates: Partial<ConfigurationProject>) => {
    const index = projects.value.findIndex(p => p.id === id)
    if (index !== -1) {
      projects.value[index] = {
        ...projects.value[index],
        ...updates,
        updatedAt: new Date().toISOString()
      }
    }
  }

  const deleteProject = (id: string) => {
    const index = projects.value.findIndex(p => p.id === id)
    if (index !== -1) {
      projects.value.splice(index, 1)
    }
  }

  const setCurrentProject = (project: ConfigurationProject | null) => {
    currentProject.value = project
    if (project) {
      editorState.value.history.present = JSON.parse(JSON.stringify(project))
      editorState.value.history.past = []
      editorState.value.history.future = []
    }
  }

  // 组件操作
  const addComponent = (component: ConfigurationComponent) => {
    if (!currentProject.value) return
    
    saveToHistory()
    currentProject.value.components.push(component)
    editorState.value.selectedComponents = [component.id]
  }

  const updateComponent = (id: string, updates: Partial<ConfigurationComponent>) => {
    if (!currentProject.value) return
    
    const index = currentProject.value.components.findIndex(c => c.id === id)
    if (index !== -1) {
      saveToHistory()
      currentProject.value.components[index] = {
        ...currentProject.value.components[index],
        ...updates
      }
    }
  }

  const deleteComponent = (id: string) => {
    if (!currentProject.value) return
    
    saveToHistory()
    const index = currentProject.value.components.findIndex(c => c.id === id)
    if (index !== -1) {
      currentProject.value.components.splice(index, 1)
    }
    editorState.value.selectedComponents = editorState.value.selectedComponents.filter(
      compId => compId !== id
    )
  }

  const selectComponent = (id: string, multiple = false) => {
    if (multiple) {
      if (editorState.value.selectedComponents.includes(id)) {
        editorState.value.selectedComponents = editorState.value.selectedComponents.filter(
          compId => compId !== id
        )
      } else {
        editorState.value.selectedComponents.push(id)
      }
    } else {
      editorState.value.selectedComponents = [id]
    }
  }

  const clearSelection = () => {
    editorState.value.selectedComponents = []
  }

  // 历史记录
  const saveToHistory = () => {
    if (!currentProject.value) return
    
    editorState.value.history.past.push(
      JSON.parse(JSON.stringify(editorState.value.history.present))
    )
    editorState.value.history.present = JSON.parse(JSON.stringify(currentProject.value))
    editorState.value.history.future = []
    
    // 限制历史记录数量
    if (editorState.value.history.past.length > 50) {
      editorState.value.history.past.shift()
    }
  }

  const undo = () => {
    if (!canUndo.value || !currentProject.value) return
    
    editorState.value.history.future.unshift(
      JSON.parse(JSON.stringify(editorState.value.history.present))
    )
    const previousState = editorState.value.history.past.pop()!
    editorState.value.history.present = previousState
    currentProject.value = JSON.parse(JSON.stringify(previousState))
  }

  const redo = () => {
    if (!canRedo.value || !currentProject.value) return
    
    editorState.value.history.past.push(
      JSON.parse(JSON.stringify(editorState.value.history.present))
    )
    const nextState = editorState.value.history.future.shift()!
    editorState.value.history.present = nextState
    currentProject.value = JSON.parse(JSON.stringify(nextState))
  }

  // 复制粘贴
  const copyComponents = () => {
    editorState.value.clipboard = selectedComponents.value.map(comp => 
      JSON.parse(JSON.stringify(comp))
    )
  }

  const pasteComponents = () => {
    if (!currentProject.value || editorState.value.clipboard.length === 0) return
    
    saveToHistory()
    const newComponents = editorState.value.clipboard.map(comp => ({
      ...comp,
      id: generateId(),
      x: comp.x + 20,
      y: comp.y + 20
    }))
    
    currentProject.value.components.push(...newComponents)
    editorState.value.selectedComponents = newComponents.map(comp => comp.id)
  }

  // 编辑器设置
  const setZoom = (zoom: number) => {
    editorState.value.zoom = Math.max(0.1, Math.min(5, zoom))
  }

  const toggleGrid = () => {
    editorState.value.grid.show = !editorState.value.grid.show
  }

  const toggleRulers = () => {
    editorState.value.rulers = !editorState.value.rulers
  }

  const toggleLayers = () => {
    editorState.value.layers = !editorState.value.layers
  }

  // 工具函数
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  return {
    // 状态
    projects,
    currentProject,
    editorState,
    
    // 计算属性
    selectedComponents,
    canUndo,
    canRedo,
    
    // 项目管理
    createProject,
    updateProject,
    deleteProject,
    setCurrentProject,
    
    // 组件操作
    addComponent,
    updateComponent,
    deleteComponent,
    selectComponent,
    clearSelection,
    
    // 历史记录
    saveToHistory,
    undo,
    redo,
    
    // 复制粘贴
    copyComponents,
    pasteComponents,
    
    // 编辑器设置
    setZoom,
    toggleGrid,
    toggleRulers,
    toggleLayers
  }
})