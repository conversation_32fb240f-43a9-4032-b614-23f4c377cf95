<template>
  <div class="project-properties">
    <el-form label-position="top" size="small">
      <el-form-item label="项目名称">
        <el-input v-model="localProject.name" @change="updateProject" class="dark-text" />
      </el-form-item>
      
      <el-form-item label="项目描述">
        <el-input 
          v-model="localProject.description" 
          type="textarea" 
          rows="3"
          @change="updateProject"
          class="dark-text"
        />
      </el-form-item>
      
      <el-form-item label="画布尺寸">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-input-number 
              v-model="localProject.width" 
              :min="800" 
              :max="3840"
              controls-position="right"
              placeholder="宽度"
              @change="updateProject"
              class="dark-text"
            />
          </el-col>
          <el-col :span="12">
            <el-input-number 
              v-model="localProject.height" 
              :min="600" 
              :max="2160"
              controls-position="right"
              placeholder="高度"
              @change="updateProject"
              class="dark-text"
            />
          </el-col>
        </el-row>
      </el-form-item>
      
      <el-form-item label="背景颜色">
        <el-color-picker 
          v-model="localProject.backgroundColor" 
          show-alpha
          @change="updateProject"
        />
      </el-form-item>
      
      <el-form-item label="数据绑定">
        <div v-if="localProject.dataBindings.length === 0" class="no-bindings">
          <el-empty description="暂无数据绑定" />
        </div>
        
        <el-collapse v-else>
          <el-collapse-item 
            v-for="(binding, index) in localProject.dataBindings" 
            :key="binding.id"
            :title="binding.name"
          >
            <el-form label-position="top" size="small">
              <el-form-item label="数据源名称">
                <el-input 
                  v-model="binding.name" 
                  @change="updateProject"
                  class="dark-text"
                />
              </el-form-item>
              
              <el-form-item label="数据源类型">
                <el-select v-model="binding.type" @change="updateProject" class="dark-text">
                  <el-option label="API" value="api" />
                  <el-option label="WebSocket" value="websocket" />
                  <el-option label="MQTT" value="mqtt" />
                  <el-option label="静态数据" value="static" />
                </el-select>
              </el-form-item>
              
              <template v-if="binding.type === 'api'">
                <el-form-item label="API地址">
                  <el-input 
                    v-model="binding.config.url" 
                    placeholder="例如: https://api.example.com/data"
                    @change="updateProject"
                    class="dark-text"
                  />
                </el-form-item>
                
                <el-form-item label="请求方法">
                  <el-select v-model="binding.config.method" @change="updateProject" class="dark-text">
                    <el-option label="GET" value="GET" />
                    <el-option label="POST" value="POST" />
                    <el-option label="PUT" value="PUT" />
                    <el-option label="DELETE" value="DELETE" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="刷新间隔 (毫秒)">
                  <el-input-number 
                    v-model="binding.config.interval" 
                    :min="1000" 
                    :step="1000"
                    controls-position="right"
                    @change="updateProject"
                    class="dark-text"
                  />
                </el-form-item>
              </template>
              
              <template v-if="binding.type === 'websocket'">
                <el-form-item label="WebSocket地址">
                  <el-input 
                    v-model="binding.config.url" 
                    placeholder="例如: ws://example.com/socket"
                    @change="updateProject"
                    class="dark-text"
                  />
                </el-form-item>
              </template>
              
              <template v-if="binding.type === 'mqtt'">
                <el-form-item label="MQTT代理地址">
                  <el-input 
                    v-model="binding.config.url" 
                    placeholder="例如: mqtt://broker.example.com"
                    @change="updateProject"
                    class="dark-text"
                  />
                </el-form-item>
                
                <el-form-item label="主题">
                  <el-input 
                    v-model="binding.config.topic" 
                    placeholder="例如: sensors/temperature"
                    @change="updateProject"
                    class="dark-text"
                  />
                </el-form-item>
              </template>
              
              <el-form-item label="数据字段">
                <div v-for="(field, fieldIndex) in binding.fields" :key="fieldIndex" class="field-item">
                  <el-row :gutter="10">
                    <el-col :span="8">
                      <el-input 
                        v-model="field.key" 
                        placeholder="字段键"
                        @change="updateProject"
                        class="dark-text"
                      />
                    </el-col>
                    <el-col :span="8">
                      <el-input 
                        v-model="field.name" 
                        placeholder="显示名称"
                        @change="updateProject"
                        class="dark-text"
                      />
                    </el-col>
                    <el-col :span="6">
                      <el-select v-model="field.type" @change="updateProject" class="dark-text">
                        <el-option label="数字" value="number" />
                        <el-option label="字符串" value="string" />
                        <el-option label="布尔值" value="boolean" />
                        <el-option label="对象" value="object" />
                      </el-select>
                    </el-col>
                    <el-col :span="2">
                      <el-button 
                        type="danger" 
                        icon="Delete" 
                        circle
                        size="small"
                        @click="removeField(binding, fieldIndex)"
                      />
                    </el-col>
                  </el-row>
                </div>
                
                <el-button 
                  type="primary" 
                  plain 
                  icon="Plus"
                  @click="addField(binding)"
                >
                  添加字段
                </el-button>
              </el-form-item>
              
              <el-form-item>
                <el-button 
                  type="danger" 
                  @click="removeBinding(index)"
                >
                  删除数据源
                </el-button>
              </el-form-item>
            </el-form>
          </el-collapse-item>
        </el-collapse>
        
        <div class="add-binding">
          <el-button 
            type="primary" 
            icon="Plus"
            @click="addBinding"
          >
            添加数据源
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { ConfigurationProject, DataBinding, DataField } from '../types'

const props = defineProps<{
  project: ConfigurationProject
}>()

const emit = defineEmits<{
  update: [project: ConfigurationProject]
}>()

// 本地项目副本
const localProject = ref<ConfigurationProject>({...props.project})

// 监听项目变化，更新本地副本
watch(() => props.project, (newProject) => {
  localProject.value = {...newProject}
}, { deep: true })

// 更新项目
const updateProject = () => {
  emit('update', {...localProject.value})
}

// 添加数据绑定
const addBinding = () => {
  const newBinding: DataBinding = {
    id: 'binding_' + Date.now(),
    name: '新数据源',
    type: 'api',
    config: {
      url: '',
      method: 'GET',
      headers: {},
      params: {},
      interval: 5000
    },
    fields: []
  }
  
  localProject.value.dataBindings.push(newBinding)
  updateProject()
}

// 删除数据绑定
const removeBinding = (index: number) => {
  localProject.value.dataBindings.splice(index, 1)
  updateProject()
}

// 添加字段
const addField = (binding: DataBinding) => {
  const newField: DataField = {
    key: '',
    name: '',
    type: 'string'
  }
  
  binding.fields.push(newField)
  updateProject()
}

// 删除字段
const removeField = (binding: DataBinding, index: number) => {
  binding.fields.splice(index, 1)
  updateProject()
}
</script>

<style scoped>
.project-properties {
  color: #000;
}

.no-bindings {
  padding: 20px 0;
}

.add-binding {
  margin-top: 20px;
  text-align: center;
}

.field-item {
  margin-bottom: 10px;
}

/* 确保所有表单元素中的文字为黑色 */
:deep(.el-form-item__label) {
  color: #000 !important;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner),
:deep(.el-select-dropdown__item),
:deep(.el-radio-button__inner),
:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  color: #000 !important;
}

:deep(.el-collapse-item__header),
:deep(.el-collapse-item__content) {
  color: #000 !important;
}

/* 确保下拉选项也是黑色文字 */
:deep(.el-select-dropdown__item) {
  color: #000 !important;
}

/* 确保输入框中的文字为黑色 */
.dark-text :deep(input),
.dark-text :deep(textarea) {
  color: #000 !important;
}
</style>