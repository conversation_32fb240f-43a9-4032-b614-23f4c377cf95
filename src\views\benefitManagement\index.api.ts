// 贡献人员Api
import request from '@/utils/request'

enum Api {
  list = '/benefit/config/getBenefit',
  // 获取机组
  powerUnit = '/project/powerUnit/all',
  // 查询实时功率以及实时能耗
  PowerAndEnergy = '/benefit/config/getRealtimeData',
  // 查询抄表电量
  MonthlyElectricityAndWaste = '/benefit/config/getMonthPower',
  getAddPower = '/benefit/config/getAddPower',
}
// 获取效益展示
export const getbenefitsList = (data) => {
  return request({
    url: Api.list,
    method: 'post',
    data,
  })
}
// 查询机组数据
export const getpowerUnit = (data) => {
  return request({
    url: Api.powerUnit,
    method: 'post',
    data,
  })
}
// 查询实时能耗以及实时功率
export const getPowerAndEnergy = (data) => {
  return request({
    url: Api.PowerAndEnergy,
    method: 'post',
    data,
  })
}
// 查询垃圾处理量以及发电量
export const getMonthlyElectricityAndWaste = (data) => {
  return request({
    url: Api.MonthlyElectricityAndWaste,
    method: 'post',
    data,
  })
}
// 查询加权平均电价
export const getAddPower = (data) => {
  return request({
    url: Api.getAddPower,
    method: 'post',
    data,
  })
}
