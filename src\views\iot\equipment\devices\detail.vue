<template>
  <div class="box equipment-detail">
    <el-page-header @back="goBack" content="设备详情"></el-page-header>
    <el-divider></el-divider>
    <el-tabs v-loading="loading" type="border-card" v-model="state.activeName" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="base">
        <el-row :gutter="20" v-if="state.activeName === 'base'">
          <el-col :span="12">
            <el-descriptions :column="1" border :labelStyle="{ 'font-weight': 'bold' }">
              <el-descriptions-item label="设备ID">{{
                state.deviceDetail.deviceId
              }}</el-descriptions-item>
              <el-descriptions-item label="产品productKey">{{
                state.deviceDetail.productKey
              }}</el-descriptions-item>
              <el-descriptions-item label="设备deviceName">{{
                state.deviceDetail.deviceName
              }}</el-descriptions-item>
              <el-descriptions-item label="设备密钥">{{
                state.deviceDetail.secret
              }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{
                formatDate(state.deviceDetail.createAt)
              }}</el-descriptions-item>
              <el-descriptions-item label="设备状态">{{
                state.deviceDetail.state
                ? state.deviceDetail.state.online
                  ? "在线"
                  : "离线"
                : "离线"
              }}</el-descriptions-item>
              <el-descriptions-item label="最后上线时间">{{
                formatDate(state.deviceDetail.onlineTime)
              }}</el-descriptions-item>
            </el-descriptions></el-col
          >
          <el-col v-if="state.showDeviceMap" :span="12">
            <Map :center="state.mapLnglat" />
          </el-col>
        </el-row>
        <div style="margin: 10px 10px;color: #ccc;">设备标签&nbsp;<el-button size="small" icon="Plus" @click="addTag"></el-button></div>
        <el-descriptions :column="2" border :labelStyle="{ 'font-weight': 'bold' }">
          <el-descriptions-item v-for="tag in state.tags" :key="tag.name" :label="tag.name + '(' + tag.id + ')'">{{ tag.value
          }}</el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>

      <el-tab-pane label="属性" name="property">
        <el-table v-if="state.activeName === 'property'" :data="state.properties" border v-loading="state.loading" style="width: 100%">
          <el-table-column prop="name" label="属性名" width="250">
            <template v-slot="scope"> {{ scope.row.name }}({{ scope.row.identifier }}) </template>
          </el-table-column>
          <el-table-column prop="value" label="属性值">
            <template v-slot="scope">
              <span>{{ scope.row.value }}{{ scope.row.unit }} &nbsp;</span>
              <el-button @click="showPropertyHistory(scope.row)" size="small">历史</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="occurred" label="修改时间">
            <template v-slot="scope">
              <span>{{ formatDate(scope.row.occurred)}} </span>
            </template>
          </el-table-column>
          <el-table-column label="可读写" width="80">
            <template v-slot="scope">
              <el-tag v-if="!scope.row.write" type="info" size="small" effect="plain">只读</el-tag>
              <el-button @click="showWriteProperty(scope.row)" v-if="scope.row.write" size="small" type="success" plain>可写</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="!!state.propertyHistory.name">
          <el-divider></el-divider>
          <el-row>
            <el-col :span="2">
              <h5 style="color: #ccc;">历史数据</h5>
            </el-col>
            <el-col :span="9">
              <el-date-picker
                v-model="state.historyTime"
                type="datetimerange"
                :picker-options="state.pickerOptions"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
                @change="timeRangeChange"
              >
              </el-date-picker>
            </el-col>
            <el-col :span="4">
              <el-radio-group v-model="state.dataType">
                <el-radio-button label="">无</el-radio-button>
                <el-radio-button label="stats" @click="openPropertyTable">统计</el-radio-button>
              </el-radio-group>
            </el-col>
          </el-row>
          <PropertyTable ref="PropertyTableRef" />
          <PropertyChart :name="state.propertyHistory.name" :properties="state.propertyHistory.data"></PropertyChart>
        </div>
      </el-tab-pane>

      <el-tab-pane label="服务" name="service">
        <el-table v-if="state.activeName === 'service'" :data="state.services" border v-loading="state.loading" style="width: 100%">
          <el-table-column label="服务名称" width="180">
            <template v-slot="scope"> {{ scope.row.name }}({{ scope.row.identifier }}) </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template v-slot="scope">
              <el-button @click="showInvokeService(scope.row)" type="success" size="small" plain>调用</el-button>
            </template>
          </el-table-column>
          <el-table-column label="参数">
            <template v-slot="scope">
              <pre class="equipment-param">{{ scope.row.inputData }}</pre>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="日志" name="event">
        <el-form v-if="state.activeName === 'event'" :inline="true" :model="state.formInline" class="user-search">
          <el-form-item>
            <el-select v-model="state.formInline.type" placeholder="请选择日志类型">
              <el-option label="所有" value="" />
              <el-option label="状态" value="state" />
              <el-option label="事件" value="event" />
              <el-option label="属性" value="property" />
              <el-option label="服务" value="service" />
            </el-select>
          </el-form-item>
          <el-form-item label="搜索：">
            <el-input v-model="state.formInline.identifier" placeholder="日志识符" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="logSearch">搜索</el-button>
          </el-form-item>
        </el-form>

        <el-table :data="state.events" border v-loading="state.loading" style="width: 100%">
          <el-table-column label="时间" align="center" width="180">
            <template v-slot="scope">
              {{formatDate(scope.row.time) }}
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" align="center" width="120" />
          <el-table-column prop="name" label="名称(标识符)" align="center" width="180" />
          <el-table-column label="内容">
            <template v-slot="scope">
              <pre class="equipment-param">{{ scope.row.content.data }}</pre>
            </template>
          </el-table-column>
        </el-table>
        <div class="mt-[20px] w-400px ml-auto">
          <el-pagination
            v-model:current-page="state.formInline.pageNum"
            v-model:page-size="state.formInline.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, pager, jumper"
            :total="state.formInline.total"
            @size-change="getEvents"
            @current-change="getEvents"
          />
        </div>
        <!--        <Pagination
          :data="state.formInline"
          :total="state.formInline.total"
          :page="state.formInline.pageNum"
          :limit="state.formInline.pageSize"
          @pagination="getEvents"
        />-->
      </el-tab-pane>

      <el-tab-pane label="模拟上报" name="report">
        <el-table
          v-if="state.activeName === 'report'"
          :data="state.modelFunctions"
          highlight-current-row
          v-loading="state.loading"
          border
          element-loading-text="拼命加载中"
          style="width: 100%"
        >
          <el-table-column sortable prop="type" label="功能类型" width="100" />
          <el-table-column sortable prop="name" label="功能名称" width="180" />
          <el-table-column sortable prop="identifier" label="标识符" width="150" />
          <el-table-column sortable prop="dataTypeName" label="数据类型" width="100" />
          <el-table-column sortable prop="params" label="数据定义" />
          <el-table-column label="上报">
            <template #default="scope">
              <el-form inline v-model="scope.row" label-width="80px">
                <el-form-item label="值" v-if="scope.row.type == 'property'">
                  <el-input v-model="scope.row.value" size="small" />
                </el-form-item>
                <el-form-item label="内容" v-else>
                  <el-input type="textarea" v-model="scope.row.content" size="small" rows="4" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" size="small" @click="sendDeviceMsg(scope.row)">发送</el-button>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="设备配置" name="config">
        <DeviceConfig v-if="state.activeName === 'config'" :deviceInfo="state" />
      </el-tab-pane>

      <el-tab-pane label="模拟设备" name="simulator">
        <DeviceSimulator v-if="state.activeName === 'simulator'" :thingModelFunctions="state.modelFunctions" :deviceDetail="state.deviceDetail" />
      </el-tab-pane>

      <el-tab-pane label="网关子设备" name="subEquipment">
        <SubEquipment v-if="state.activeName === 'subEquipment'" :deviceInfo="state" />
      </el-tab-pane>

      <el-tab-pane label="模拟开关" name="switch">
        <el-button type="primary" v-if="!state.SwitchStatus" style="margin-right: 10px;" @click="monitorCik">开启数据模拟</el-button>
        <el-button type="danger" v-else @click="monitorStopCik">关闭数据模拟</el-button>
      </el-tab-pane>
      <el-tab-pane label="数据回传" name="returnStatusSwitch">
        <el-button type="primary" v-if="returnStatus===0" style="margin-right: 10px;" @click="btnOpneOeturnStatus">开启数据回传</el-button>
        <el-button type="danger" v-else @click="btnCloseOeturnStatus">关闭数据回传</el-button>
      </el-tab-pane>
      <el-tab-pane label="历史数据" name="historyData">
        <div class="history-data-upload">
          <h4 style="color: #ccc; margin-bottom: 20px;">上传历史数据文件</h4>
          <el-upload
            ref="uploadRef"
            :action="''"
            :http-request="handleUploadRequest"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :file-list="state.fileList"
            :limit="1"
            :accept="'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel'"
            drag
            style="width: 100%"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip">只能上传xlsx/xls文件，且不超过50MB</div>
            </template>
          </el-upload>
          <div style="margin-top: 20px;">
            <el-button type="primary" :loading="state.uploadLoading" @click="submitUpload" :disabled="state.fileList.length === 0">
              上传文件
            </el-button>
            <el-button @click="clearFiles">清空</el-button>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="modus配置" name="modusConfig">
        <div v-if="state.activeName === 'modusConfig'" class="modus-config">
          <div style="margin-bottom: 20px;">
            <el-form :inline="true" :model="state.modusSearchForm" class="modus-search">
              <el-form-item label="功能码：">
                <el-input v-model="state.modusSearchForm.functionCode" placeholder="请输入功能码" style="width: 200px;" />
              </el-form-item>
              <el-form-item label="属性名：">
                <el-input v-model="state.modusSearchForm.identifierName" placeholder="请输入属性名" style="width: 200px;" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="searchModusConfig">搜索</el-button>
                <el-button icon="Refresh" @click="resetModusSearch">重置</el-button>
              </el-form-item>
            </el-form>
            <!-- <el-button type="success" @click="saveModusConfig">保存配置</el-button> -->
          </div>

          <el-table :data="state.displayModusConfigList" border v-loading="state.loading" style="width: 100%">
            <el-table-column prop="identifierName" label="属性名" width="200">
              <template #default="scope">
                {{ scope.row.displayName }}
              </template>
            </el-table-column>

            <el-table-column prop="dcsAddress" label="地址">
              <template #default="scope">
                <el-input v-model="scope.row.dcsAddress" placeholder="请输入地址" :disabled="!scope.row.isEditing" />
              </template>
            </el-table-column>

            <el-table-column prop="functionCode" label="功能码">
              <template #default="scope">
                <el-input v-model="scope.row.functionCode" placeholder="请输入功能码" :disabled="!scope.row.isEditing" />
              </template>
            </el-table-column>

            <el-table-column prop="dataType" label="数据类型">
              <template #default="scope">
                <el-select v-model="scope.row.dataType" placeholder="请选择数据类型" :disabled="!scope.row.isEditing">
                  <el-option v-for="type in state.dataTypeOptions" :key="type.value" :label="type.label" :value="type.value" />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column prop="bitSort" label="字节解析规则">
              <template #default="scope">
                <el-input v-model="scope.row.bitSort" placeholder="请输入字节解析规则" :disabled="!scope.row.isEditing" />
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button v-if="!scope.row.isEditing" type="primary" size="small" @click="editModusConfig(scope.row, scope.$index)">
                  编辑
                </el-button>
                <el-button v-if="!scope.row.isEditing" type="danger" size="small" @click="deleteModusConfig(scope.row, scope.$index)">
                  删除
                </el-button>
                <div v-else>
                  <el-button type="success" size="small" @click="saveModusConfigRow(scope.row, scope.$index)"> 保存 </el-button>
                  <el-button type="info" size="small" @click="cancelEditModusConfig(scope.row, scope.$index)"> 取消 </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <el-dialog
      :title="state.title"
      v-model="state.propertyWriteFormVisible"
      width="40%"
      @close="closeDialog"
      :close-on-press-escape="false"
      :ick-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="state.propertyWriteFormVisible" label-width="120px" :model="state.propertyWriteForm" ref="propertyWriteForm">
        <div style="display: none">
          <el-input v-model="state.propertyWriteForm.identifier" type="hidden" />
        </div>
        <el-form-item label="属性值" prop="value">
          <el-input v-model="state.propertyWriteForm.value" auto-complete="off" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" :loading="state.loading" class="title" @click="submitPropertyWriteForm">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      :title="state.title"
      v-model="state.serviceFormVisible"
      width="40%"
      @close="closeDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="state.serviceFormVisible" label-width="120px" :model="state.serviceForm" ref="serviceForm">
        <div>
          <el-input v-model="state.serviceForm.identifier" type="hidden" />
          <el-input v-model="state.serviceForm.productKey" type="hidden" />
          <el-input v-model="state.serviceForm.deviceName" type="hidden" />
        </div>
        <div v-if="state?.serviceForm?.params.length === 0">是否确认调用？</div>
        <el-form-item v-for="param in state.serviceForm.params" :key="param.identifier" :label="param.identifier" prop="params">
          <el-input v-model="param.value" auto-complete="off" />
          <div class="form-tips">{{ param.name }}</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" :loading="state.loading" class="title" @click="submitServiceForm">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      title="添加设备标签"
      v-model="state.showAddTag"
      width="400px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="state.showAddTag" ref="formRef" :model="state.tagForm" :rules="state.rules" label-width="80px">
        <el-form-item label="标签名称" prop="name">
          <el-input v-model="state.tagForm.name" />
        </el-form-item>
        <el-form-item label="标识符" prop="id">
          <el-input v-model="state.tagForm.id" />
        </el-form-item>
        <el-form-item label="标签值" prop="value">
          <el-input v-model="state.tagForm.value" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitAddTag">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/formatTime'
import { getObjectModel } from '@/views/iot/equipment/api/products.api'
import {
  getDevicesDetail,
  devicesTagAdd,
  deviceSimulateSend,
  propertySet,
  deviceLogs,
  serviceInvoke,
  devicePropertyLogs,
} from '@/views/iot/equipment/api/devices.api'
import {
  monitorStop,
  monitor,
  monitorStatus,
  opneMonitorStart,
  uploadhisData,
  modbusConfigAdd,
  modbusConfigList,
  modbusConfigUpdate,
  modbusConfigDelete,
} from '@/views/iot/equipment/api/monitor.api'
import { UploadFilled } from '@element-plus/icons-vue'
import PropertyTable from './modules/PropertyTable.vue'
import SubEquipment from './modules/detail/subEquipment.vue'
import PropertyChart from './modules/PropertyChart.vue'
import DeviceConfig from './modules/detail/DeviceConfig.vue'
import Map from '@/components/Map/index.vue'
import DeviceSimulator from './modules/detail/DeviceSimulator.vue'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

const route = useRoute()
const router = useRouter()
const { id } = route.params
const { showMap } = route.query || false
const goBack = () => {
  router.back()
}
// 数据回传
const returnStatus = ref(0)

const PropertyTableRef = ref()
const formRef = ref()
const state = reactive<any>({
  loading: false,
  activeName: 'base',
  title: '',
  propertyWriteFormVisible: false,
  propertyWriteForm: {
    identifier: '',
    productKey: '',
    deviceName: '',
    value: '',
  },
  serviceFormVisible: false,
  serviceForm: {
    identifier: '',
    productKey: '',
    deviceName: '',
    params: [],
  },
  SwitchStatus: false,
  syncFlag: false,
  showAddTag: false,
  uploadLoading: false,
  fileList: [],
  showDeviceMap: false,
  tagForm: {
    name: '',
    identifier: '',
    value: '',
  },
  rules: {
    id: [{ required: true, message: '请输入标识符', trigger: 'blur' }],
    name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
    value: [{ required: true, message: '请输入标签值', trigger: 'blur' }],
  },
  deviceId: '',
  deviceDetail: {},
  thingModel: null,
  modelFunctions: [],
  properties: [],
  services: [],
  events: [],
  eventMap: {},
  mapLnglat: '',
  tags: [],
  formInline: {
    type: '',
    identifier: '',
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  deviceLogs: [],
  typeMap: {
    lifetime: '生命周期',
    state: '设备状态',
    property: '属性',
    event: '事件',
    service: '服务',
  },
  propertyHistory: {
    name: '',
    data: [],
  },
  dataType: '',
  currHistoryProperty: {},
  historyTime: [new Date(new Date().getTime() - 24 * 3600 * 1000), new Date(new Date().getTime() + 24 * 3600 * 1000)],
  pickerOptions: {
    shortcuts: [
      {
        text: '最近1小时',
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000)
          picker.$emit('pick', [start, end])
        },
      },
      {
        text: '最近6小时',
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 6)
          picker.$emit('pick', [start, end])
        },
      },
      {
        text: '最近1天',
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24)
          picker.$emit('pick', [start, end])
        },
      },
      {
        text: '最近5天',
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 5)
          picker.$emit('pick', [start, end])
        },
      },
    ],
  },
  modusConfigList: [],
  displayModusConfigList: [], // 用于展示的筛选后的列表
  propertyOptions: [],
  modusSearchForm: {
    functionCode: '',
    identifierName: '',
  },
  dataTypeOptions: [
    { label: 'float', value: 'float' },
    { label: 'int32', value: 'int32' },
    { label: 'bool', value: 'bool' },
    { label: 'enum', value: 'enum' },
    { label: 'text', value: 'text' },
    { label: 'date', value: 'date' },
    { label: 'position', value: 'position' },
  ],
})

const handleSizeChange = (val: number) => {}
const handleCurrentChange = (val: number) => {}

const getdata = () => {
  state.deviceId = id
  getDevicesDetail(state.deviceId).then((res: any) => {
    if (res.code !== 200) return
    const { data } = res
    state.syncFlag = data.syncFlag
    if (data.state && data.state.onlineTime) {
      data.onlineTime = data.state.onlineTime
    } else {
      data.onlineTime = ''
    }
    let prop = data.property || {}
    state.deviceDetail = data
    if (data?.locate.longitude && data.locate.latitude) {
      state.mapLnglat = data.locate.longitude + ',' + data.locate.latitude
    }
    state.showDeviceMap = JSON.parse(showMap as string)
    //取设备物模型信息
    if (!state.thingModel) {
      getObjectModel(data.productKey).then((objRes: any) => {
        const data = objRes.data || {}
        //取物模型功能列表
        data.model = data.model || {
          properties: [],
          events: [],
          services: [],
        }
        let model = data.model

        state.thingModel = model

        state.services = model.services

        fillProperty(prop)

        data.model.properties = data.model.properties || []
        data.model.events = data.model.events || []
        data.model.services = data.model.services || []
        data.model = JSON.parse(JSON.stringify(data.model))

        model = data.model || {}
        let modelFuncs: any[] = []
        model.properties.forEach((p) => {
          let params = JSON.stringify(p.dataType.specs || '{}', null, 4)
          modelFuncs.push({
            raw: p,
            type: 'property',
            name: p.name,
            identifier: p.identifier,
            dataTypeName: p.dataType.type,
            params: params == '{}' ? '' : params,
            value: '',
            occurred: '',
          })
        })
        model.events.forEach((e) => {
          let output = {}
          e.outputData.forEach((p) => {
            output[p.identifier] = p.name
          })
          modelFuncs.push({
            raw: e,
            type: 'event',
            name: e.name,
            identifier: e.identifier,
            dataTypeName: '-',
            params: JSON.stringify(output, null, 4),
            content: JSON.stringify(output, null, 4),
          })
        })
        model.services.forEach((s) => {
          let input = {}
          ;(s.inputData || []).forEach((p) => {
            input[p.identifier] = p.name
          })
          let output = {}
          ;(s.outputData || []).forEach((p) => {
            output[p.identifier] = p.name
          })
          modelFuncs.push({
            raw: s,
            type: 'service',
            name: s.name + '回复',
            identifier: s.identifier + '_reply',
            dataTypeName: '-',
            params: JSON.stringify(output, null, 4),
            content: JSON.stringify(output, null, 4),
          })
        })
        state.modelFunctions = modelFuncs

        // 初始化属性选项用于modus配置
        state.propertyOptions = model.properties.map((p) => ({
          identifier: p.identifier,
          displayName: `${p.name}(${p.identifier})`,
          dataType: p.dataType,
        }))

        // 自动为所有属性创建modus配置行
        state.modusConfigList = model.properties.map((p) => ({
          identifierName: p.identifier,
          displayName: `${p.name}(${p.identifier})`,
          dcsAddress: '',
          functionCode: '',
          dataType: p.dataType.type,
          bitSort: '',
          isEditing: false,
          originalData: null,
          configId: null,
        }))

        // 同步到显示列表
        state.displayModusConfigList = [...state.modusConfigList]

        // 加载已有的modbus配置
        loadModusConfig()
      })
    } else {
      fillProperty(prop)
    }

    let deviceTag = data.tag
    state.tags = []
    for (var p in deviceTag) {
      var tag = deviceTag[p]
      state.tags.push({ id: tag.id, name: tag.name, value: tag.value })
    }
  })
}
const fillProperty = (prop) => {
  let model = state.thingModel

  let props: any[] = []
  model.properties.forEach((p) => {
    props.push({
      identifier: p.identifier,
      name: p.name,
      value: prop[p.identifier]?.value ?? prop[p.identifier],
      occurred: prop[p.identifier]?.occurred ?? '',
      write: p.accessMode != 'r',
      unit: p.unit ?? '',
    })
  })
  state.properties = props
}
const addTag = () => {
  state.showAddTag = true
}
// 开启数据回传
const btnOpneOeturnStatus = () => {
  const params = {
    deviceId: state.deviceId,
    syncFlag: 1,
  }
  opneMonitorStart(params).then((res) => {
    if (res.data === true) {
      returnStatus.value = 1
      ElMessage({
        type: 'success',
        message: '开启成功',
      })
    }
  })
}
// 关闭数据回传
const btnCloseOeturnStatus = () => {
  const params = {
    deviceId: state.deviceId,
    syncFlag: 0,
  }
  opneMonitorStart(params).then((res) => {
    if (res.data === true) {
      returnStatus.value = 0
      ElMessage({
        type: 'success',
        message: '关闭成功',
      })
    }
  })
}
// 查询开关状态
const querySwitchStatus = () => {
  const params = {
    deviceId: state.deviceId,
  }
  monitorStatus(params).then((res) => {
    state.SwitchStatus = res.data
  })
}
// 查询数据回传状态
const queryreturnStatusSwitch = () => {
  returnStatus.value = state.syncFlag
}
const submitAddTag = () => {
  state.tagForm.deviceId = state.deviceId
  formRef.value.validate((valid) => {
    if (valid) {
      devicesTagAdd(state.tagForm).then(() => {
        ElMessage({
          type: 'success',
          message: '添加成功',
        })
        getdata()
        state.showAddTag = false
      })
    }
  })
}
const logSearch = () => {
  state.formInline.pageNum = 1
  getEvents()
}
const getEvents = () => {
  deviceLogs({
    deviceId: state.deviceId,
    ...state.formInline,
  }).then((res) => {
    state.formInline.total = res.data.total
    let logs: any[] = []
    res.data.rows.map((de) => {
      let row = {
        time: de.time,
        type: state.typeMap[de.type],
        name: '未知事件',
        content: de,
      }
      logs.push(row)
      if (!state.thingModel) return
      let modeEvents = state.thingModel.events
      if (modeEvents && modeEvents.length > 0) {
        modeEvents.forEach((e) => {
          if (de.identifier == e.identifier) {
            row.name = e.name
            return
          }
        })
      }
      let modeServices = state.thingModel.services
      if (de.type == 'property') {
        if (de.identifier == 'set_reply') {
          row.name = '设置回复'
        } else if (de.identifier == 'report') {
          row.name = '上报'
        } else if (de.identifier == 'set') {
          row.name = '设置'
        }
      } else if (de.type == 'state') {
        if (de.identifier == 'online') {
          row.name = '上线'
        } else {
          row.name = '下线'
        }
      } else if (de.type == 'lifetime') {
        if (de.identifier == 'register') {
          row.name = '注册'
        }
      } else if (modeServices && modeServices.length > 0) {
        var ids = de.identifier.split('_reply')
        modeServices.forEach((e) => {
          if (ids[0] == e.identifier) {
            row.name = e.name + (ids.length > 1 ? '回复' : '')
            return
          }
        })
      }

      row.name = row.name + '(' + de.identifier + ')'
      return de
    })

    state.events = logs
  })
}
const showPropertyHistory = (row) => {
  state.currHistoryProperty = row
  refreshPropertyHistory()
}
const loading = ref(false)
const refreshPropertyHistory = () => {
  var end = state.historyTime[1]
  var start = state.historyTime[0]
  loading.value = true
  devicePropertyLogs({
    deviceId: state.deviceId,
    name: state.currHistoryProperty.identifier,
    start: start.getTime(),
    end: end.getTime(),
  })
    .then((res) => {
      state.propertyHistory.name = state.currHistoryProperty.name
      state.propertyHistory.data = res.data
    })
    .finally(() => {
      loading.value = false
    })
}
const timeRangeChange = (e) => {
  refreshPropertyHistory()
}
const handleClick = (tab) => {
  tab.paneName == 'event' ? getEvents() : getdata()
  tab.paneName == 'switch' && querySwitchStatus()
  tab.paneName == 'returnStatusSwitch' && queryreturnStatusSwitch()
  tab.paneName == 'modusConfig' && loadModusConfig()
}
const showWriteProperty = (prop) => {
  state.propertyWriteFormVisible = true
  state.title = '设置属性'
  state.propertyWriteForm.identifier = prop.identifier
  state.propertyWriteForm.productKey = state.deviceDetail.productKey
  state.propertyWriteForm.deviceName = state.deviceDetail.deviceName
  state.propertyWriteForm.value = prop.value?.value ? prop.value.value : prop.value
}
const submitPropertyWriteForm = () => {
  let form = state.propertyWriteForm
  let prop = {}
  prop[form.identifier] = form.value
  propertySet({
    deviceId: state.deviceId,
    args: prop,
  }).then((res) => {
    if (res.code === 200) {
      ElMessage({
        type: 'success',
        message: '操作成功',
      })
    } else {
      ElMessage.error(res.message)
    }
  })
}
const showInvokeService = (service) => {
  state.serviceFormVisible = true
  state.title = '服务调用'
  state.serviceForm.identifier = service.identifier
  state.serviceForm.deviceId = state.deviceDetail.deviceId
  let params: any[] = []
  service.inputData.forEach((p) => {
    params.push({
      identifier: p.identifier,
      name: p.name,
      value: '',
    })
  })
  state.serviceForm.params = params
}
const submitServiceForm = () => {
  let form = state.serviceForm
  let param = {}
  state.serviceForm.params.forEach((p) => {
    param[p.identifier] = p.value
  })

  serviceInvoke({
    deviceId: state.deviceId,
    service: form.identifier,
    args: param,
  }).then((res) => {
    if (res.code === 200) {
      state.serviceFormVisible = false
      ElMessage({
        type: 'info',
        message: '操作成功',
      })
    } else {
      ElMessage({
        type: 'error',
        message: res.message,
      })
    }
  })
}
const sendDeviceMsg = (fun) => {
  //发送模拟设备消息
  let data = {}
  if (fun.type == 'property') {
    let val = fun.value
    switch (fun.dataTypeName) {
      case 'int32':
        if (val < fun.raw.dataType.specs.min || val > fun.raw.dataType.specs.max) {
          ElMessage({
            type: 'info',
            message: '数据类型错误',
          })
          return
        }
        break
      case 'bool':
        break
      case 'enum':
        if (!(val in fun.raw.dataType.specs)) {
          ElMessage({
            type: 'info',
            message: '数据类型错误',
          })
          return
        }
        break
      case 'float':
        val = parseFloat(val)
        break
    }
    data[fun.identifier] = val
  } else {
    data = JSON.parse(fun.content)
  }

  deviceSimulateSend({
    deviceId: state.deviceId,
    productKey: state.deviceDetail.productKey,
    deviceName: state.deviceDetail.deviceName,
    type: fun.type,
    identifier: fun.type == 'property' ? 'report' : fun.identifier,
    data: data,
  }).then(() => {
    ElMessage({
      type: 'info',
      message: '操作成功',
    })
  })
}
const closeDialog = () => {
  state.propertyWriteFormVisible = false
  state.serviceFormVisible = false
}
const locateChange = (e) => {
  state.propertyWriteForm.value = e[0] * 1 + ',' + e[1] * 1
}

const openPropertyTable = () => {
  PropertyTableRef.value.open(state)
}

// 文件上传相关方法
const uploadRef = ref()

const beforeUpload = (file) => {
  const isExcel =
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel' ||
    file.name.endsWith('.xlsx') ||
    file.name.endsWith('.xls')

  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }

  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('上传文件大小不能超过50MB!')
    return false
  }

  return true
}

const handleUploadRequest = (options) => {
  const formData = new FormData()
  formData.append('file', options.file)
  formData.append('deviceId', state.deviceId)

  state.uploadLoading = true
  uploadhisData(formData)
    .then((res) => {
      if (res.data.code === 200) {
        ElMessage.success('文件上传成功!')
        state.fileList = []
      } else {
        ElMessage.error(res.message || '上传失败')
      }
    })
    .catch((error) => {
      ElMessage.error('上传失败: ' + error.message)
    })
    .finally(() => {
      state.uploadLoading = false
    })
}

const handleUploadSuccess = () => {
  ElMessage.success('上传成功!')
}

const handleUploadError = () => {
  ElMessage.error('上传失败!')
}

const submitUpload = () => {
  if (state.fileList.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }
  uploadRef.value.submit()
}

const clearFiles = () => {
  state.fileList = []
  uploadRef.value.clearFiles()
}

// 数据开始模拟
const monitorCik = () => {
  const params = {
    deviceId: state.deviceId,
  }
  monitor(params).then((res) => {
    state.SwitchStatus = res.data
  })
}
const monitorStopCik = () => {
  const params = {
    deviceId: state.deviceId,
  }
  monitorStop(params).then((res) => {
    state.SwitchStatus = res.data
  })
}

// modus配置相关方法
const saveModusConfig = () => {
  if (state.modusConfigList.length === 0) {
    ElMessage.warning('没有配置数据')
    return
  }
  // 这里可以调用API保存配置

  ElMessage.success('配置已保存')
}

const editModusConfig = (row, index) => {
  // 保存原始数据，以便取消时恢复
  row.originalData = {
    dcsAddress: row.dcsAddress,
    functionCode: row.functionCode,
    dataType: row.dataType,
    bitSort: row.bitSort,
  }
  // 启用编辑模式
  row.isEditing = true
  ElMessage.info(`正在编辑 ${row.displayName} 的配置`)
}
const deleteModusConfig = (row, index) => {
  modbusConfigDelete([row.configId])
    .then((res) => {
      if (res.code === 200) {
        loadModusConfig()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    })
    .then((res) => {
      ElMessage.success('删除成功')
    })
}
const saveModusConfigRow = (row, index) => {
  // 验证必填字段
  if (!row.dcsAddress) {
    ElMessage.error('请填写完整的配置信息')
    return
  }

  // 构造基础请求参数
  const params: any = {
    deviceId: state.deviceId,
    identifierName: row.displayName.split('(')[0], // 提取属性名称，去掉括号部分
    identifierId: row.identifierName,
    dcsAddress: parseInt(row.dcsAddress),
    functionCode: row.functionCode,
    dataType: row.dataType,
    bitSort: row.bitSort || '',
    projectId: cachedProjects?.id || null,
    createAt: null,
  }

  // 判断是新增还是更新
  const isUpdate = row.configId != null

  if (isUpdate) {
    // 更新操作：添加id参数
    params.id = row.configId

    modbusConfigUpdate(params)
      .then((res) => {
        if (res.code === 200) {
          row.isEditing = false
          row.originalData = null
          ElMessage.success(`${row.displayName} 配置已更新`)
          // 重新加载数据
          loadModusConfig()
        } else {
          ElMessage.error(res.message || '更新失败')
        }
      })
      .catch((error) => {
        ElMessage.error('更新失败: ' + error.message)
      })
  } else {
    // 新增操作
    modbusConfigAdd(params)
      .then((res) => {
        if (res.code === 200) {
          row.isEditing = false
          row.originalData = null
          ElMessage.success(`${row.displayName} 配置已保存`)
          // 重新加载数据获取最新的配置信息
          loadModusConfig()
        } else {
          ElMessage.error(res.message || '保存失败')
        }
      })
      .catch((error) => {
        ElMessage.error('保存失败: ' + error.message)
      })
  }
}

const cancelEditModusConfig = (row, index) => {
  // 恢复原始数据
  if (row.originalData) {
    row.dcsAddress = row.originalData.dcsAddress
    row.functionCode = row.originalData.functionCode
    row.dataType = row.originalData.dataType
    row.bitSort = row.originalData.bitSort
  }
  // 退出编辑模式
  row.isEditing = false
  row.originalData = null
  ElMessage.info(`已取消编辑 ${row.displayName} 的配置`)
}

// 搜索modbus配置
const searchModusConfig = () => {
  applyFilterAndSort()
  // 如果只有属性名搜索，不需要调用API，直接进行筛选
  // if (state.modusSearchForm.identifierName && !state.modusSearchForm.functionCode) {
  //   applyFilterAndSort()
  // } else {
  //   // 如果有功能码搜索，需要调用API
  //   loadModusConfig()
  // }
}

// 重置搜索
const resetModusSearch = () => {
  state.modusSearchForm.functionCode = ''
  state.modusSearchForm.identifierName = ''
  loadModusConfig()
}

// 加载modbus配置数据
const loadModusConfig = () => {
  const params: any = {
    deviceId: state.deviceId,
    projectId: cachedProjects?.id || null,
  }

  // 只有功能码搜索条件才传给API
  if (state.modusSearchForm.functionCode) {
    params.functionCode = state.modusSearchForm.functionCode
  }

  modbusConfigList(params)
    .then((res) => {
      if (res.code === 200) {
        const configData = res.data || []

        // 将查询到的配置数据按identifierId匹配到表格行中
        state.modusConfigList.forEach((row) => {
          // 如果不是正在编辑的行，才重置编辑状态
          if (!row.isEditing) {
            row.originalData = null
          }

          const matchedConfig = configData.find((config) => config.identifierId === row.identifierName)

          if (matchedConfig) {
            // 找到匹配的配置，填充数据（如果不是正在编辑的行）
            if (!row.isEditing) {
              row.dcsAddress = matchedConfig.dcsAddress.toString()
              row.functionCode = matchedConfig.functionCode
              row.dataType = matchedConfig.dataType
              row.bitSort = matchedConfig.bitSort || ''
            }
            row.configId = matchedConfig.id
          } else {
            // 没有匹配的配置，清空数据（如果不是正在编辑的行）
            if (!row.isEditing) {
              row.dcsAddress = ''
              row.functionCode = ''
              row.dataType = state.propertyOptions.find((p) => p.identifier === row.identifierName)?.dataType?.type || ''
              row.bitSort = ''
            }
            row.configId = null
          }
        })

        // 应用前端筛选和排序
        applyFilterAndSort()
      } else {
        ElMessage.error(res.message || '查询配置失败')
      }
    })
    .catch((error) => {
      ElMessage.error('加载配置失败: ' + error.message)
    })
}

// 应用前端筛选和排序
const applyFilterAndSort = () => {
  let filteredList = [...state.modusConfigList]
  // 如果有属性名搜索条件，进行本地筛选
  if (state.modusSearchForm.identifierName) {
    const searchTerm = state.modusSearchForm.identifierName.toLowerCase()
    filteredList = filteredList.filter(
      (row) => row.displayName.toLowerCase().includes(searchTerm) || row.identifierName.toLowerCase().includes(searchTerm)
    )
  }
  if (state.modusSearchForm.functionCode) {
    filteredList = filteredList.filter((row) => row.functionCode.includes(state.modusSearchForm.functionCode))
  }
  // 排序逻辑
  filteredList.sort((a, b) => {
    // 1. 优先显示有配置的数据
    const aHasConfig = a.configId !== null
    const bHasConfig = b.configId !== null

    if (aHasConfig && !bHasConfig) return -1
    if (!aHasConfig && bHasConfig) return 1
    // 2. 如果有功能码搜索，匹配的排在前面
    if (state.modusSearchForm.functionCode) {
      const functionCodeSearch = state.modusSearchForm.functionCode.toLowerCase()
      const aMatches = a.functionCode.toLowerCase().includes(functionCodeSearch)
      const bMatches = b.functionCode.toLowerCase().includes(functionCodeSearch)
      if (aMatches && !bMatches) return -1
      if (!aMatches && bMatches) return 1
    }
    // 3. 如果有属性名搜索，匹配的排在前面
    if (state.modusSearchForm.identifierName) {
      const identifierSearch = state.modusSearchForm.identifierName.toLowerCase()
      const aMatches = a.displayName.toLowerCase().includes(identifierSearch) || a.identifierName.toLowerCase().includes(identifierSearch)
      const bMatches = b.displayName.toLowerCase().includes(identifierSearch) || b.identifierName.toLowerCase().includes(identifierSearch)

      if (aMatches && !bMatches) return -1
      if (!aMatches && bMatches) return 1
    }

    // 4. 按 dcsAddress 大小排序
    const aDcsAddress = parseInt(a.dcsAddress || '0') || 0
    const bDcsAddress = parseInt(b.dcsAddress || '0') || 0
    if (aDcsAddress !== bDcsAddress) {
      return aDcsAddress - bDcsAddress
    }

    // 5. 最后按原始顺序排序
    return 0
  })

  // 更新显示的列表
  state.displayModusConfigList = filteredList

  // 检查是否有结果
  if (filteredList.length === 0 && (state.modusSearchForm.functionCode || state.modusSearchForm.identifierName)) {
    ElMessage.info('未找到符合条件的配置')
  }
}

getdata()
logSearch()
</script>

<style scoped>
.form-tips {
  font-size: 12px;
  line-height: 14px;
}
.equipment-param {
  max-height: 160px;
  overflow: hidden auto;
  word-wrap: break-word;
  white-space: pre-wrap;
  font-size: 12px;
  line-height: 14px;
}
:deep(.el-tabs__item.is-active) {
}
:deep(.el-tabs__item:hover) {
  color: #fff;
}
:deep(.el-tabs__item) {
}
:deep(.el-tabs--border-card) {
  background: rgba(2, 28, 51, 0.5);
}
:deep(.el-tabs--border-card > .el-tabs__header) {
  background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important;
  border-bottom: 1px solid rgba(33, 148, 255, 1);
}
:deep(.el-tabs--border-card) {
  border: none;
}
:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {
  border-radius: 2px;
  background: linear-gradient(180deg, rgb(6, 101, 243) 0%, rgba(119, 122, 126, 0.1) 100%);
  opacity: 0.8;
  color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(0, 0, 0, 1);
}
:deep(.el-page-header__icon) {
  color: #ccc !important;
}
:deep(.el-page-header__title) {
  color: #ccc !important;
}
:deep(.el-page-header__content) {
  color: #ccc !important;
}
:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  border: none;
}
:deep(.el-card__header) {
  border: none;
}
:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}
:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}
:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}
:deep(.el-table th) {
  /* background-color: transparent; */
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}
:deep(.el-table) {
  --el-table-border-color: none;
}
/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row) {
  /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
  /* 取消单元格背景色，确保整行背景色生效 */
}
:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;

  color: #ffffff;
}
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}
:deep(.el-select__wrapper) {
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
:deep(.el-select) {
  width: 180px;
}
:deep(.el-table__body tr.current-row > td.el-table__cell) {
  /* backgroud-color: tr !important; */
  background-color: transparent !important;
}

/* 文件上传样式 */
.history-data-upload {
  padding: 20px;
}

:deep(.el-upload-dragger) {
  background-color: rgba(2, 28, 51, 0.5) !important;
  border: 1px dashed rgba(33, 148, 255, 0.5) !important;
  border-radius: 6px;
}

:deep(.el-upload-dragger:hover) {
  border-color: rgba(33, 148, 255, 1) !important;
}

:deep(.el-upload__text) {
  color: #ccc !important;
}

:deep(.el-upload__tip) {
  color: #999 !important;
}

:deep(.el-icon--upload) {
  color: rgba(33, 148, 255, 1) !important;
}

/* modus配置表格样式 */
.modus-config {
  padding: 20px;
}

.modus-config :deep(.el-table__cell) {
  padding: 8px !important;
}

.modus-config :deep(.el-input__wrapper) {
  background: rgb(3, 43, 82) !important;
  border: 1px solid #034374 !important;
}

.modus-config :deep(.el-input__inner) {
  color: #fff !important;
}

.modus-config :deep(.el-select__wrapper) {
  background: rgb(3, 43, 82) !important;
  border: 1px solid #034374 !important;
}

.modus-config :deep(.el-select__placeholder) {
  color: #999 !important;
}

.modus-config :deep(.el-select__caret) {
  color: #fff !important;
}

.modus-config :deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: rgba(3, 43, 82, 0.5) !important;
  border-color: rgba(3, 43, 82, 0.3) !important;
}

.modus-config :deep(.el-input.is-disabled .el-input__inner) {
  color: #999 !important;
  cursor: not-allowed;
}

.modus-config :deep(.el-select.is-disabled .el-select__wrapper) {
  background-color: rgba(3, 43, 82, 0.5) !important;
  border-color: rgba(3, 43, 82, 0.3) !important;
  cursor: not-allowed;
}

/* 搜索表单样式 */
.modus-search {
  padding: 15px;
  background: rgba(7, 53, 92, 0.3);
  border-radius: 4px;
  margin-bottom: 15px;
}

.modus-search :deep(.el-form-item__label) {
  color: #ccc !important;
}

.modus-search :deep(.el-input__wrapper) {
  background: rgb(3, 43, 82) !important;
  border: 1px solid #034374 !important;
}

.modus-search :deep(.el-input__inner) {
  color: #fff !important;
}
</style>
