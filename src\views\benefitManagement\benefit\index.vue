<template>
  <div class="benefit-management">
    <!-- 顶部数据统计卡片 -->
    <div v-for="(powerUnit, unitIndex) in newdata" :key="unitIndex" class="power-unit-container">
      <div class="power-unit-name">{{ powerUnit.powerUnitName }}</div>
      <div class="power-unit-section">
        <el-row :gutter="16" class="stats-row">
          <el-col :span="6" v-for="(stat, index) in powerUnit.benefitShowVoBuilderList" :key="index">
            <DataStatCard :title="stat.title" :value="stat.value.toLocaleString()" :unit="stat.unit" :type="stat.type" />
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="16" class="charts-row">
      <!-- 实时功率图表 -->
      <el-col :span="8">
        <BenefitCard
          :title="powerTitle"
          :header-value="powerData.length > 0 ? `${currentPowerValue} ${powerUnit}` : ' '"
          :footer-label="powerSecTitle"
          :footer-value="`${avgPowerValue} ${powerUnit}`"
          :dropdown-options="dropdownOptions"
          :selected-value="selectedPowerUnitId"
          @update:selected-value="handlePowerUnitChange"
        >
          <template #chart>
            <RealTimePowerChart :data="powerData" />
          </template>
        </BenefitCard>
      </el-col>

      <!-- 实时能耗图表 -->
      <el-col :span="8">
        <BenefitCard
          :title="energyTitle"
          :header-value="energyData.length>0?`${currentEnergyValue} ${energyUnit}`:''"
          :footer-label="energySecTitle"
          :footer-value="`${avgEnergyValue} ${energyUnit}`"
          :dropdown-options="dropdownOptions"
          :selected-value="selectedEnergyUnitId"
          @update:selected-value="handleEnergyUnitChange"
        >
          <template #chart>
            <RealTimeEnergyChart
              :data="energyData"
              :title="energyTitle"
              :secTitle="energySecTitle"
              :unit="energyUnit"
              :currentValue="currentEnergyValue"
              :avgValue="avgEnergyValue"
              :dropdownOptions="dropdownOptions"
              :selectedUnitId="selectedEnergyUnitId"
              @unitChange="handleEnergyUnitChange"
            />
          </template>
        </BenefitCard>
      </el-col>

      <!-- 净功率图表 -->
      <el-col :span="8">
        <BenefitCard
          :title="netPowerTitle"
          :header-value="netPowerData.length > 0 ? `${currentNetPowerValue} ${netPowerUnit}` : ''"
          :footer-label="netPowerSecTitle"
          :footer-value="`${avgNetPowerValue} ${netPowerUnit}`"
          :dropdown-options="dropdownOptions"
          :selected-value="selectedNetPowerUnitId"
          @update:selected-value="handleNetPowerUnitChange"
        >
          <template #chart>
            <RealTimePowerChart :data="netPowerData" />
          </template>
        </BenefitCard>
      </el-col>
    </el-row>

    <el-row :gutter="16" class="charts-row">
      <!-- 垃圾处理量图表 -->
      <el-col :span="12">
        <BenefitCard
          :title="wasteTitle"
          :header-value="`${wasteTitleTotal} ${wasteUnit}`"
          :footer-label="wasteSecTitle"
          :footer-value="`${avgWasteValue} ${wasteUnit}`"
          :show-date-picker="true"
          :selected-date="selectedWasteDisposalDate"
          :dropdown-options="dropdownOptions"
          :selected-value="selectedWasteUnitId"
          @update:selected-date="handleWasteDisposalDateChange"
          @update:selected-value="handleWasteUnitChange"
        >
          <template #chart>
            <RealTimeNetPowerChart :data="wasteDisposalData" />
          </template>
        </BenefitCard>
      </el-col>

      <!-- 月实时发电量图表 -->
      <el-col :span="12">
        <BenefitCard
          :title="monthlyTitle"
          :header-value="`${monthlyTitleTotal} ${monthlyUnit}`"
          :footer-label="monthlySecTitle"
          :footer-value="`${avgMonthlyValue} ${monthlyUnit}`"
          :show-date-picker="true"
          :selected-date="selectedMonthlyDate"
          @update:selected-date="handleMonthlyDateChange"
        >
          <template #chart>
            <MonthlyPowerChart :data="monthlyData" />
          </template>
        </BenefitCard>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import {
  TrendCharts,
  DataLine,
  DataAnalysis,
  DataBoard,
  Lightning,
  Timer,
  Calendar,
  Clock
} from '@element-plus/icons-vue'

type StatType = 'daily' | 'monthly' | 'yearly' | 'total'

interface StatData {
  title: string
  value: number
  unit: string
  type: StatType
}

interface PowerUnitData {
  id: number | null
  powerUnitId: number
  powerUnitName: string
  benefitShowVoBuilderList: StatData[]
  projectId: number | null
}

import DataStatCard from '@/components/DataStatCard/index.vue'
import BenefitCard from '@/components/BenefitCard/index.vue'
import RealTimePowerChart from '@/components/RealTimePowerChart/index.ts'
import RealTimeEnergyChart from '@/components/RealTimeEnergyChart/index.ts'
import RealTimeNetPowerChart from '@/components/RealTimeNetPowerChart/index.ts'
import MonthlyPowerChart from '@/components/MonthlyPowerChart/index.ts'
import { getbenefitsList,getpowerUnit,getPowerAndEnergy,getMonthlyElectricityAndWaste,getAddPower } from '../index.api'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'

const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

emitter.on('projectListChanged', (e) => {
  location.reload()
})

// 效益数据
const newdata = ref<PowerUnitData[]>([])

// 下拉框选项数据
interface DropdownOption {
  id: number
  name: string
}
const dropdownOptions = ref<DropdownOption[]>([])
const selectedPowerUnitId = ref<number>(0)
const selectedEnergyUnitId = ref<number>(0)
const selectedWasteUnitId = ref<number>(0)
const selectedNetPowerUnitId = ref<number>(0)
const selectedWasteDisposalDate = ref<string>('')
const selectedMonthlyDate = ref<string>('')

// 获取机组下拉框数据
const getPowerUnitOptions = () => {
  if (cachedProjects && cachedProjects.id) {
    const params = {
      projectId: cachedProjects.id,
    }
    getpowerUnit(params).then(res => {
      if (res.code === 200 && res.data && Array.isArray(res.data)) {
        dropdownOptions.value = res.data
        console.log(dropdownOptions.value,'dropdownOptions.value')
        // 默认选择第一个选项
        if (res.data.length > 0) {
          selectedPowerUnitId.value = res.data[0].id
          selectedEnergyUnitId.value = res.data[0].id
          selectedWasteUnitId.value = res.data[0].id
          selectedNetPowerUnitId.value = res.data[0].id
          // 在机组数据加载完成后，获取实时功率数据和实时能耗数据
          getPowerAndEnergyData()
          getEnergyConsumptionData()
          getNetPowerData()
        }
        // 初始化日期为当前年月
        const now = new Date()
        const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
        selectedWasteDisposalDate.value = currentMonth
        selectedMonthlyDate.value = currentMonth

        // 在日期初始化完成后，获取垃圾处理量数据和月实时发电量数据
        getwasteDisposalData()
        getMonthlyData()
      } else {
        console.warn('获取机组数据失败或数据格式不正确')
        dropdownOptions.value = []
      }
    }).catch(error => {
      console.error('获取机组数据失败:', error)
      dropdownOptions.value = []
    })
  }
}

// 处理实时功率下拉框选择变化
const handlePowerUnitChange = (unitId: number) => {
  selectedPowerUnitId.value = unitId
  // 重新获取实时功率数据
  getPowerAndEnergyData()
}

// 实时功率相关数据
const powerTitle = ref('实时功率')
const powerSecTitle = ref('平均功率')
const powerUnit = ref('kW')
const currentPowerValue = ref(0)
const avgPowerValue = ref(0)

// 获取实时功率数据
const getPowerAndEnergyData = () => {
  if (!cachedProjects || !cachedProjects.id) return
  const params = {
    projectId: cachedProjects.id,
    queryParam: 'load',
    powerUnitId: selectedPowerUnitId.value
  }
  getPowerAndEnergy(params).then(res => {
    if (res.code === 200 && res.data) {
      const { data } = res

      // 更新实时功率相关数据
      powerTitle.value = data.title || '实时功率'
      powerSecTitle.value = data.secTitle || '平均功率'
      powerUnit.value = data.unit || 'kW'
      avgPowerValue.value = data.avg || 0
      // 更新图表数据
      if (data.timeValues && Array.isArray(data.timeValues)) {
        powerData.value = data.timeValues.map(item => ({
          time: item.time ? new Date(item.time).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) : '--:--',
          value: item.value !== null && item.value !== undefined ? item.value : '--'
        }))
        // 获取最新的功率值作为当前值
        if (data.timeValues.length > 0) {
          const lastItem = data.timeValues[data.timeValues.length - 1]
          currentPowerValue.value = lastItem.value !== null && lastItem.value !== undefined ? lastItem.value : '--'
        }
      }
    }
  }).catch(error => {
    console.error('获取实时功率数据失败:', error)
  })
}

// 月垃圾处理量相关数据
const wasteTitle = ref('月垃圾处理量')
const wasteSecTitle = ref('平均处理量')
const wasteTitleTotal =ref('')
const wasteUnit = ref('吨')
const currentWasteValue = ref(0)
const avgWasteValue = ref(0)


// 获取月垃圾处理量
const getwasteDisposalData = () => {
  if (!cachedProjects || !cachedProjects.id) return
  const params = {
    projectId: cachedProjects.id,
    queryDate: selectedWasteDisposalDate.value + '-01',
    powerUnitId: selectedWasteUnitId.value
  }
  getAddPower(params).then((result) => {
    if (result.code === 200 && result.data) {
      const { data } = result
      // 更新月垃圾处理量相关数据
      wasteTitleTotal.value = data.total|| '--'
      wasteTitle.value = data.title || '月垃圾处理量'
      wasteSecTitle.value = data.secTitle || '平均处理量'
      wasteUnit.value = data.unit || '吨'
      avgWasteValue.value = data.avg || 0
      // 更新图表数据
      if (data.timeValues && Array.isArray(data.timeValues)) {
        wasteDisposalData.value = data.timeValues.map(item => ({
          // time: item.time,
          time: item.time ? new Date(item.time).getDate() + '日' : '--日',
          value: item.value !== null && item.value !== undefined ? item.value : '--'
        }))
        // 获取最新的处理量值作为当前值
        if (data.timeValues.length > 0) {
          const lastItem = data.timeValues[data.timeValues.length - 1]
          currentWasteValue.value = lastItem.value !== null && lastItem.value !== undefined ? lastItem.value : '--'
        }
      }
    }
  }).catch((err) => {
    console.error('获取月垃圾处理量数据失败:', err)
  })
}

// 获取月实时发电量
const getMonthlyData = () => {
  if (!cachedProjects || !cachedProjects.id) return

  const params = {
    projectId: cachedProjects.id,
    queryParam: 'dayElectricityAmount',
    queryDate: selectedMonthlyDate.value + '-01'
  }

  getMonthlyElectricityAndWaste(params).then((result) => {
    if (result.code === 200 && result.data) {
      const { data } = result
      // 更新月实时发电量相关数据
      monthlyTitleTotal.value=data.total || '--'
      monthlyTitle.value = data.title || '月实时发电量'
      monthlySecTitle.value = data.secTitle || '系统孪生增发电量'
      monthlyUnit.value = data.unit || 'kWh'
      avgMonthlyValue.value = data.avg || 0

             // 更新图表数据
       if (data.timeValues && Array.isArray(data.timeValues)) {
         monthlyData.value = data.timeValues.map(item => ({
          time: item.time ? new Date(item.time).getDate() + '日' : '--日',
          // time: item.time,
           value: item.value !== null && item.value !== undefined ? item.value : '--'
         }))

         // 获取最新的发电量值作为当前值
         if (data.timeValues.length > 0) {
           const lastItem = data.timeValues[data.timeValues.length - 1]
           currentMonthlyValue.value = lastItem.value !== null && lastItem.value !== undefined ? lastItem.value : '--'
         }
       }
    }
  }).catch((err) => {
    console.error('获取月实时发电量数据失败:', err)
  })
}

// 实时能耗相关数据
const energyTitle = ref('实时能耗')
const energySecTitle = ref('平均能耗')
const energyUnit = ref('kWh')
const currentEnergyValue = ref(0)
const avgEnergyValue = ref(0)

// 获取实时能耗数据
const getEnergyConsumptionData = () => {
  if (!cachedProjects || !cachedProjects.id) return
  const params = {
    projectId: cachedProjects.id,
    queryParam: 'power_consumption',
    powerUnitId: selectedEnergyUnitId.value
  }
  getPowerAndEnergy(params).then(res => {
    if (res.code === 200 && res.data) {
      const { data } = res

      // 更新实时能耗相关数据
      energyTitle.value = data.title || '实时能耗'
      energySecTitle.value = data.secTitle || '平均能耗'
      energyUnit.value = data.unit || 'kWh'
      avgEnergyValue.value = data.avg || 0
      // 更新图表数据
      if (data.timeValues && Array.isArray(data.timeValues)) {
        energyData.value = data.timeValues.map(item => ({
          time: item.time ? new Date(item.time).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) : '--:--',
          value: item.value !== null && item.value !== undefined ? item.value : '--'
        }))
        // 获取最新的能耗值作为当前值
        if (data.timeValues.length > 0) {
          const lastItem = data.timeValues[data.timeValues.length - 1]
          currentEnergyValue.value = lastItem.value !== null && lastItem.value !== undefined ? lastItem.value : '--'
        }
      }
    }
  }).catch(error => {
    console.error('获取实时能耗数据失败:', error)
  })
}

// 处理实时能耗下拉框选择变化
const handleEnergyUnitChange = (unitId: number) => {
  selectedEnergyUnitId.value = unitId
  // 重新获取实时能耗数据
  getEnergyConsumptionData()
}

// 获取净功率数据
const getNetPowerData = () => {
  if (!cachedProjects || !cachedProjects.id) return
  const params = {
    projectId: cachedProjects.id,
    queryParam: 'pure_load',
    powerUnitId: selectedNetPowerUnitId.value
  }
  getPowerAndEnergy(params).then(res => {
    if (res.code === 200 && res.data) {
      const { data } = res

      // 更新净功率相关数据
      netPowerTitle.value = data.title || '净功率'
      netPowerSecTitle.value = data.secTitle || '平均净功率'
      netPowerUnit.value = data.unit || 'kW'
      avgNetPowerValue.value = data.avg || 0
      // 更新图表数据
      if (data.timeValues && Array.isArray(data.timeValues)) {
        netPowerData.value = data.timeValues.map(item => ({
          time: item.time ? new Date(item.time).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) : '--:--',
          value: item.value !== null && item.value !== undefined ? item.value : '--'
        }))
        // 获取最新的净功率值作为当前值
        if (data.timeValues.length > 0) {
          const lastItem = data.timeValues[data.timeValues.length - 1]
          currentNetPowerValue.value = lastItem.value !== null && lastItem.value !== undefined ? lastItem.value : '--'
        }
      }
    }
  }).catch(error => {
    console.error('获取净功率数据失败:', error)
  })
}

// 处理净功率下拉框选择变化
const handleNetPowerUnitChange = (unitId: number) => {
  selectedNetPowerUnitId.value = unitId
  // 重新获取净功率数据
  getNetPowerData()
}

// 处理垃圾处理量机组选择变化
const handleWasteUnitChange = (unitId: number) => {
  selectedWasteUnitId.value = unitId
  // 重新获取垃圾处理量数据
  getwasteDisposalData()
}

// 处理垃圾处理量日期选择变化
const handleWasteDisposalDateChange = (date: string) => {
  selectedWasteDisposalDate.value = date
  // 重新获取垃圾处理量数据
  getwasteDisposalData()
}

// 处理月实时发电量日期选择变化
const handleMonthlyDateChange = (date: string) => {
  selectedMonthlyDate.value = date
  // 重新获取月实时发电量数据
  getMonthlyData()
}

// 获取顶部效益数据
const getbenefitsdata = () => {
  if (cachedProjects && cachedProjects.id) {
    getbenefitsList(cachedProjects.id).then(res => {
      if (res.code === 200 && res.data && Array.isArray(res.data)) {
        newdata.value = res.data
      } else {
        console.warn('获取效益数据失败或数据格式不正确')
        newdata.value = []
      }
    }).catch(error => {
      console.error('获取效益数据失败:', error)
      newdata.value = []
    })
  }
}

// 图表数据
const powerData = ref<Array<{ time: string; value: number }>>([])
const energyData = ref<Array<{ time: string; value: number }>>([])
const wasteDisposalData = ref<Array<{ time: string; value: number }>>([])
const monthlyData = ref<Array<{ time: string; value: number }>>([])
const netPowerData = ref<Array<{ time: string; value: number }>>([])

// 月实时发电量相关数据
const monthlyTitle = ref('月实时发电量')
const monthlySecTitle = ref('系统孪生增发电量')
const monthlyTitleTotal=ref('')
const monthlyUnit = ref('kWh')
const currentMonthlyValue = ref(0)
const avgMonthlyValue = ref(0)

// 净功率相关数据
const netPowerTitle = ref('净功率')
const netPowerSecTitle = ref('平均净功率')
const netPowerUnit = ref('kW')
const currentNetPowerValue = ref(0)
const avgNetPowerValue = ref(0)

// 计算当前日期对应的月实时发电量header值
const monthlyHeaderValue = computed(() => {
  // 情况1：数据为空数组时不显示
  if (!monthlyData.value || monthlyData.value.length === 0) {
    return ''
  }

  // 情况2：判断当前日期那一天有没有数据
  const today = new Date()
  const currentDay = today.getDate()
  const currentDayFormatted = String(currentDay).padStart(2, '0')

  // 查找当前日期对应的数据
  const todayData = monthlyData.value.find(item => {
    // item.time 格式可能是 "01日", "02日" 等
    const dayMatch = item.time.match(/(\d{2})/)
    if (dayMatch) {
      return dayMatch[1] === currentDayFormatted
    }
    return false
  })

  // 如果找到当前日期的数据，显示该数据；否则不显示
  if (todayData) {
    return `${todayData.value} ${monthlyUnit.value}`
  }

  return ''
})

// 其他数据（保留不变，但已被实际API数据替代）
// const currentEnergy = ref(1205.8)
// const avgEnergy = ref(1150.2)

// 生成模拟数据（用于垃圾处理量和月实时发电量）
const generateMockData = (count: number, min: number, max: number): Array<{ time: string; value: number }> => {
  const data: Array<{ time: string; value: number }> = []
  const now = new Date()

  for (let i = count - 1; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 5 * 60 * 1000) // 每5分钟一个数据点
    const value = Math.random() * (max - min) + min
    data.push({
      time: time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      value: Math.round(value * 10) / 10
    })
  }

  return data
}

onMounted(() => {
  getbenefitsdata()
  getPowerUnitOptions()
  // 确保图表组件完全渲染后再进行相关操作
  nextTick(() => {
    // 第一次尝试
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 500)
    // 第二次尝试
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 1000)
    // 第三次尝试
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 2000)
  })
})
</script>

<style lang="scss" scoped>
.benefit-management {
  padding: 16px;
  background: rgba(8, 42, 77, 0.2);
  height: calc(100vh - 90px);
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .power-unit-container {
    position: relative;
    margin-bottom: 16px;
    background: rgba(8, 42, 77, 0.1);
    border: 1px solid rgba(115, 208, 255, 0.2);
    border-radius: 8px;
    padding: 18px 8px 8px 8px;

    .power-unit-name {
      position: absolute;
      top: -12px;
      right: 12px;
      font-size: 12px;
      color: #73d0ff;
      background: rgba(8, 42, 77, 0.9);
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid rgba(115, 208, 255, 0.5);
      z-index: 2;
      font-weight: 600;
    }

    .power-unit-section {
      .stats-row {
        margin-bottom: 0;
        flex-shrink: 0;
      }
    }
  }

  .charts-row {
    flex: 1;
    margin-bottom: 16px;
    min-height: 0;

    &:last-child {
      margin-bottom: 0;
    }

    // 确保图表卡片占满高度
    :deep(.el-col) {
      height: 100%;
    }
  }
}

:deep(.el-card) {
  border-radius: 8px;
  border: 1px solid rgba(115, 208, 255, 0.3);
  height: 100%;
  background: rgba(8, 42, 77, 0.2);
  backdrop-filter: blur(10px);
}
:deep(.el-select__wrapper){
  box-shadow:none;
  background-color:transparent;
  border:  1px solid rgba(115, 208, 255, 0.3);
}
:deep(.el-select__placeholder){
  color: #fff;
}
</style>
