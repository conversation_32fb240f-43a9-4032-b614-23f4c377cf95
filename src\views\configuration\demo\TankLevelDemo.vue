<template>
  <div class="tank-level-demo">
    <div class="demo-header">
      <h2>储罐液位动态展示演示</h2>
      <p>演示储罐组件根据实时数据动态调整液位百分比</p>
    </div>
    
    <div class="demo-content">
      <!-- 控制面板 -->
      <div class="control-panel">
        <h3>数据控制面板</h3>
        
        <div class="tank-controls">
          <div v-for="tank in tanks" :key="tank.id" class="tank-control">
            <h4>{{ tank.name }}</h4>
            <div class="control-row">
              <label>当前液位:</label>
              <el-slider 
                v-model="tank.currentValue" 
                :min="0" 
                :max="tank.maxValue"
                :step="10"
                show-input
                @change="updateTankLevel(tank.id, tank.currentValue)"
              />
              <span>{{ tank.currentValue }} / {{ tank.maxValue }} {{ tank.unit }}</span>
            </div>
            <div class="control-row">
              <label>液位百分比:</label>
              <span class="percentage">{{ Math.round((tank.currentValue / tank.maxValue) * 100) }}%</span>
            </div>
          </div>
        </div>
        
        <div class="simulation-controls">
          <el-button 
            :type="isSimulating ? 'danger' : 'primary'"
            @click="toggleSimulation"
          >
            {{ isSimulating ? '停止模拟' : '开始模拟' }}
          </el-button>
          <el-button @click="resetTanks">重置储罐</el-button>
        </div>
      </div>
      
      <!-- 储罐展示区域 -->
      <div class="tanks-display">
        <h3>储罐3D模型展示</h3>
        <div class="tanks-grid">
          <div v-for="tank in tanks" :key="tank.id" class="tank-item">
            <div class="tank-header">
              <h4>{{ tank.name }}</h4>
              <div class="tank-info">
                <span>{{ tank.currentValue }}{{ tank.unit }}</span>
                <span class="percentage">{{ Math.round((tank.currentValue / tank.maxValue) * 100) }}%</span>
              </div>
            </div>
            <div class="tank-model">
              <Model3dComponent 
                :component="createTankComponent(tank)"
                :editing="false"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 数据源信息 -->
    <div class="data-source-info">
      <h3>数据源信息</h3>
      <div class="data-points">
        <div v-for="dataPoint in dataPoints" :key="dataPoint.id" class="data-point">
          <span class="point-name">{{ dataPoint.name }}:</span>
          <span class="point-value">{{ dataPoint.value }} {{ dataPoint.unit }}</span>
          <span class="point-time">{{ formatTime(dataPoint.timestamp) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import Model3dComponent from '../components/components/Model3dComponent.vue'
import { dataService, type DataPoint } from '../services/dataService'
import type { ConfigurationComponent } from '../types'

// 储罐数据
const tanks = ref([
  {
    id: 'tank-001-level',
    name: '1号储罐',
    currentValue: 750,
    maxValue: 1000,
    unit: 'L'
  },
  {
    id: 'tank-002-level', 
    name: '2号储罐',
    currentValue: 450,
    maxValue: 800,
    unit: 'L'
  },
  {
    id: 'tank-003-level',
    name: '3号储罐',
    currentValue: 1200,
    maxValue: 1500,
    unit: 'L'
  }
])

// 数据点信息
const dataPoints = ref<DataPoint[]>([])
const isSimulating = ref(false)
const unsubscribeFunctions: (() => void)[] = []

// 创建储罐组件配置
const createTankComponent = (tank: any): ConfigurationComponent => {
  return {
    id: `tank-${tank.id}`,
    type: 'model3d',
    x: 0,
    y: 0,
    width: 200,
    height: 300,
    rotation: 0,
    opacity: 1,
    data: {
      static: {
        model: 'tank',
        fillLevel: Math.round((tank.currentValue / tank.maxValue) * 100),
        dataSource: 'dynamic',
        dataPointId: tank.id,
        maxValue: tank.maxValue,
        currentValue: tank.currentValue,
        unit: tank.unit
      }
    }
  }
}

// 更新储罐液位
const updateTankLevel = (dataPointId: string, value: number) => {
  dataService.updateDataPoint('tank-levels', dataPointId, value)
}

// 切换模拟状态
const toggleSimulation = () => {
  isSimulating.value = !isSimulating.value
  // 模拟状态由dataService内部管理
}

// 重置储罐
const resetTanks = () => {
  tanks.value.forEach(tank => {
    const resetValue = Math.floor(tank.maxValue * 0.5) // 重置为50%
    tank.currentValue = resetValue
    updateTankLevel(tank.id, resetValue)
  })
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 更新数据点显示
const updateDataPoints = () => {
  const tankDataSource = dataService.getDataSource('tank-levels')
  if (tankDataSource) {
    dataPoints.value = [...tankDataSource.dataPoints]
  }
}

onMounted(() => {
  // 订阅所有储罐数据变化
  tanks.value.forEach(tank => {
    const unsubscribe = dataService.subscribe(tank.id, (dataPoint) => {
      // 更新本地储罐数据
      const localTank = tanks.value.find(t => t.id === tank.id)
      if (localTank) {
        localTank.currentValue = dataPoint.value
      }
      // 更新数据点显示
      updateDataPoints()
    })
    unsubscribeFunctions.push(unsubscribe)
  })
  
  // 初始化数据点显示
  updateDataPoints()
})

onUnmounted(() => {
  // 清理订阅
  unsubscribeFunctions.forEach(unsubscribe => unsubscribe())
})
</script>

<style scoped>
.tank-level-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.demo-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.demo-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 30px;
  margin-bottom: 30px;
}

.control-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.control-panel h3 {
  margin-top: 0;
  color: #2c3e50;
}

.tank-controls {
  margin-bottom: 20px;
}

.tank-control {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.tank-control h4 {
  margin: 0 0 10px 0;
  color: #495057;
}

.control-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.control-row label {
  min-width: 80px;
  font-weight: 500;
}

.percentage {
  font-weight: bold;
  color: #007bff;
}

.simulation-controls {
  display: flex;
  gap: 10px;
}

.tanks-display h3 {
  margin-top: 0;
  color: #2c3e50;
}

.tanks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.tank-item {
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.tank-header {
  padding: 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tank-header h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.tank-info {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #6c757d;
}

.tank-model {
  height: 300px;
  position: relative;
}

.data-source-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.data-source-info h3 {
  margin-top: 0;
  color: #2c3e50;
}

.data-points {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.data-point {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.point-name {
  font-weight: 500;
  color: #495057;
}

.point-value {
  font-weight: bold;
  color: #007bff;
}

.point-time {
  font-size: 12px;
  color: #6c757d;
}
</style>
