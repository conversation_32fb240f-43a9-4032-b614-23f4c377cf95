<template>
  <div class="data-source-manager">
    <div class="manager-header">
      <h3>数据源管理</h3>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        添加数据源
      </el-button>
    </div>

    <div class="data-source-list">
      <el-empty v-if="dataSources.length === 0" description="暂无数据源，点击上方按钮添加数据源" />

      <el-card
        v-for="dataSource in dataSources"
        :key="dataSource.id"
        class="data-source-card"
        :class="{ 'connected': dataSource.status === 'connected' }"
      >
        <template #header>
          <div class="card-header">
            <span class="source-name">{{ dataSource.name }}</span>
            <div class="source-actions">
              <el-tag
                :type="getStatusType(dataSource.status)"
                size="small"
              >
                {{ getStatusText(dataSource.status) }}
              </el-tag>
              <el-button-group size="small">
                <el-button
                  v-if="dataSource.status === 'disconnected'"
                  @click="connectDataSource(dataSource.id)"
                >
                  连接
                </el-button>
                <el-button
                  v-else
                  @click="disconnectDataSource(dataSource.id)"
                >
                  断开
                </el-button>
                <el-button @click="editDataSource(dataSource)">编辑</el-button>
                <el-button type="danger" @click="removeDataSource(dataSource.id)">删除</el-button>
              </el-button-group>
            </div>
          </div>
        </template>
        
        <div class="source-info">
          <div class="info-row">
            <span class="label">协议:</span>
            <span class="value">{{ getProtocolName(dataSource.protocol) }}</span>
          </div>
          <div class="info-row">
            <span class="label">数据点:</span>
            <span class="value">{{ dataSource.dataPoints.length }} 个</span>
          </div>
          <div class="info-row" v-if="dataSource.lastUpdate">
            <span class="label">最后更新:</span>
            <span class="value">{{ formatTime(dataSource.lastUpdate) }}</span>
          </div>
          <div class="info-row" v-if="dataSource.errorMessage">
            <span class="label">错误:</span>
            <span class="value error">{{ dataSource.errorMessage }}</span>
          </div>
        </div>
        
        <div class="data-points" v-if="dataSource.dataPoints.length > 0">
          <h4>数据点</h4>
          <el-table :data="dataSource.dataPoints" size="small" max-height="200">
            <el-table-column prop="name" label="名称" width="120" />
            <el-table-column prop="value" label="当前值" width="80">
              <template #default="{ row }">
                <span :class="{ 'good': row.quality === 'good', 'bad': row.quality === 'bad' }">
                  {{ formatValue(row.value, row.unit) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" width="60" />
            <el-table-column prop="timestamp" label="时间戳" width="120">
              <template #default="{ row }">
                {{ formatTime(row.timestamp) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
    
    <!-- 添加/编辑数据源对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingDataSource ? '编辑数据源' : '添加数据源'"
      width="600px"
    >
      <el-form :model="dataSourceForm" label-width="100px">
        <el-form-item label="名称" required>
          <el-input v-model="dataSourceForm.name" placeholder="请输入数据源名称" />
        </el-form-item>
        
        <el-form-item label="协议" required>
          <el-select v-model="dataSourceForm.protocol" @change="onProtocolChange">
            <el-option label="模拟数据" value="simulation" />
            <el-option label="HTTP API" value="http" />
            <el-option label="WebSocket" value="websocket" />
            <el-option label="MQTT" value="mqtt" />
            <el-option label="OPC UA" value="opcua" />
            <el-option label="Modbus" value="modbus" />
          </el-select>
        </el-form-item>
        
        <!-- HTTP配置 -->
        <template v-if="dataSourceForm.protocol === 'http'">
          <el-form-item label="URL" required>
            <el-input v-model="dataSourceForm.config.url" placeholder="http://example.com/api/data" />
          </el-form-item>
          <el-form-item label="请求方法">
            <el-select v-model="dataSourceForm.config.method">
              <el-option label="GET" value="GET" />
              <el-option label="POST" value="POST" />
            </el-select>
          </el-form-item>
          <el-form-item label="更新间隔(ms)">
            <el-input-number v-model="dataSourceForm.config.interval" :min="1000" :step="1000" />
          </el-form-item>
        </template>
        
        <!-- WebSocket配置 -->
        <template v-if="dataSourceForm.protocol === 'websocket'">
          <el-form-item label="WebSocket URL" required>
            <el-input v-model="dataSourceForm.config.wsUrl" placeholder="ws://example.com/ws" />
          </el-form-item>
        </template>
        
        <!-- MQTT配置 -->
        <template v-if="dataSourceForm.protocol === 'mqtt'">
          <el-form-item label="Broker URL" required>
            <el-input v-model="dataSourceForm.config.brokerUrl" placeholder="mqtt://broker.example.com" />
          </el-form-item>
          <el-form-item label="客户端ID">
            <el-input v-model="dataSourceForm.config.clientId" placeholder="client_id" />
          </el-form-item>
          <el-form-item label="主题">
            <el-input v-model="topicsInput" placeholder="topic1,topic2" />
          </el-form-item>
        </template>
        
        <!-- 数据点配置 -->
        <el-form-item label="数据点">
          <el-button @click="addDataPoint" size="small">添加数据点</el-button>
          <el-table :data="dataSourceForm.dataPoints" size="small" style="margin-top: 10px;">
            <el-table-column label="名称">
              <template #default="{ row, $index }">
                <el-input v-model="row.name" size="small" />
              </template>
            </el-table-column>
            <el-table-column label="地址/路径">
              <template #default="{ row, $index }">
                <el-input v-model="row.address" size="small" placeholder="data.temperature" />
              </template>
            </el-table-column>
            <el-table-column label="数据类型">
              <template #default="{ row, $index }">
                <el-select v-model="row.dataType" size="small">
                  <el-option label="数字" value="number" />
                  <el-option label="字符串" value="string" />
                  <el-option label="布尔" value="boolean" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="单位">
              <template #default="{ row, $index }">
                <el-input v-model="row.unit" size="small" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ row, $index }">
                <el-button @click="removeDataPoint($index)" size="small" type="danger">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveDataSource">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 临时类型定义，避免导入问题
interface DataSource {
  id: string
  name: string
  protocol: string
  status: 'connected' | 'disconnected' | 'connecting' | 'error'
  lastUpdate: number
  errorMessage?: string
  dataPoints: any[]
  config: any
}

type DataSourceProtocol = 'simulation' | 'http' | 'websocket' | 'mqtt' | 'opcua' | 'modbus'

// 数据源列表
const dataSources = ref<DataSource[]>([])

// 对话框状态
const showAddDialog = ref(false)
const editingDataSource = ref<DataSource | null>(null)

// 表单数据
const dataSourceForm = ref<{
  id: string
  name: string
  protocol: DataSourceProtocol
  config: any
  dataPoints: any[]
  status: 'disconnected' | 'connected' | 'connecting' | 'error'
  lastUpdate: number
}>({
  id: '',
  name: '',
  protocol: 'simulation',
  config: {},
  dataPoints: [],
  status: 'disconnected',
  lastUpdate: 0
})

// 主题输入（用于MQTT）
const topicsInput = ref('')

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'connected': return 'success'
    case 'connecting': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'disconnected': return '已断开'
    case 'error': return '错误'
    default: return '未知'
  }
}

// 获取协议名称
const getProtocolName = (protocol: DataSourceProtocol) => {
  const names = {
    simulation: '模拟数据',
    http: 'HTTP API',
    websocket: 'WebSocket',
    mqtt: 'MQTT',
    opcua: 'OPC UA',
    modbus: 'Modbus'
  }
  return names[protocol] || protocol
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 格式化值
const formatValue = (value: any, unit?: string) => {
  if (typeof value === 'number') {
    return `${value.toFixed(2)}${unit ? ' ' + unit : ''}`
  }
  return value?.toString() || '-'
}

// 协议变化处理
const onProtocolChange = () => {
  dataSourceForm.value.config = {}
  if (dataSourceForm.value.protocol === 'http') {
    dataSourceForm.value.config = {
      method: 'GET',
      interval: 5000
    }
  }
}

// 添加数据点
const addDataPoint = () => {
  dataSourceForm.value.dataPoints.push({
    id: `dp_${Date.now()}`,
    name: '',
    address: '',
    dataType: 'number',
    unit: '',
    value: 0,
    timestamp: Date.now(),
    quality: 'good'
  })
}

// 删除数据点
const removeDataPoint = (index: number) => {
  dataSourceForm.value.dataPoints.splice(index, 1)
}

// 连接数据源
const connectDataSource = async (id: string) => {
  const dataSource = dataSources.value.find(ds => ds.id === id)
  if (dataSource) {
    dataSource.status = 'connecting'
    // 模拟连接过程
    setTimeout(() => {
      dataSource.status = 'connected'
      dataSource.lastUpdate = Date.now()
      ElMessage.success('数据源连接成功')
    }, 1000)
  }
}

// 断开数据源
const disconnectDataSource = (id: string) => {
  const dataSource = dataSources.value.find(ds => ds.id === id)
  if (dataSource) {
    dataSource.status = 'disconnected'
    ElMessage.success('数据源已断开')
  }
}

// 编辑数据源
const editDataSource = (dataSource: DataSource) => {
  editingDataSource.value = dataSource
  dataSourceForm.value = {
    id: dataSource.id,
    name: dataSource.name,
    protocol: dataSource.protocol as DataSourceProtocol,
    config: { ...dataSource.config },
    dataPoints: [...dataSource.dataPoints],
    status: dataSource.status,
    lastUpdate: dataSource.lastUpdate
  }

  showAddDialog.value = true
}

// 删除数据源
const removeDataSource = (id: string) => {
  const index = dataSources.value.findIndex(ds => ds.id === id)
  if (index > -1) {
    dataSources.value.splice(index, 1)
    ElMessage.success('数据源已删除')
  }
}

// 保存数据源
const saveDataSource = async () => {
  if (!dataSourceForm.value.name) {
    ElMessage.error('请输入数据源名称')
    return
  }

  // 处理MQTT主题
  if (dataSourceForm.value.protocol === 'mqtt' && topicsInput.value) {
    dataSourceForm.value.config.topics = topicsInput.value.split(',').map(t => t.trim())
  }

  const dataSource: DataSource = {
    id: dataSourceForm.value.id || `ds_${Date.now()}`,
    name: dataSourceForm.value.name,
    protocol: dataSourceForm.value.protocol,
    config: dataSourceForm.value.config,
    dataPoints: dataSourceForm.value.dataPoints,
    status: 'disconnected',
    lastUpdate: 0
  }

  // 添加或更新数据源
  const existingIndex = dataSources.value.findIndex(ds => ds.id === dataSource.id)
  if (existingIndex > -1) {
    dataSources.value[existingIndex] = dataSource
  } else {
    dataSources.value.push(dataSource)
  }

  ElMessage.success('数据源保存成功')
  showAddDialog.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  dataSourceForm.value = {
    id: '',
    name: '',
    protocol: 'simulation',
    config: {},
    dataPoints: [],
    status: 'disconnected',
    lastUpdate: 0
  }
  topicsInput.value = ''
  editingDataSource.value = null
}

// 加载数据源列表
const loadDataSources = () => {
  // 初始化一些示例数据源
  if (dataSources.value.length === 0) {
    dataSources.value = [
      {
        id: 'demo_1',
        name: '模拟数据源',
        protocol: 'simulation',
        status: 'disconnected',
        lastUpdate: 0,
        config: {},
        dataPoints: []
      }
    ]
  }
}

onMounted(() => {
  loadDataSources()
})
</script>

<style scoped>
.data-source-manager {
  padding: 20px;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.data-source-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.data-source-card {
  border: 1px solid #e4e7ed;
}

.data-source-card.connected {
  border-color: #67c23a;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.source-name {
  font-weight: bold;
  font-size: 16px;
}

.source-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.source-info {
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  margin-bottom: 5px;
}

.label {
  font-weight: bold;
  width: 80px;
}

.value {
  flex: 1;
}

.value.error {
  color: #f56c6c;
}

.data-points h4 {
  margin: 10px 0;
  font-size: 14px;
}

.good {
  color: #67c23a;
}

.bad {
  color: #f56c6c;
}
</style>
