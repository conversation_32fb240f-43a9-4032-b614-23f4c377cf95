<template>
  <div ref="chartContainer" style="width: 100%; height: 100%;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import * as echarts from 'echarts'

// 导入不同状态对应的图片
import echarts0 from '@/assets/images/echarts0.png'
import echarts1 from '@/assets/images/echarts1.png'
import echarts2 from '@/assets/images/echarts2.png'



// 通过 props 接收父组件传来的数据
interface ChartDataType {
  code: number
  message: string
  data: Array<{
    time: any
    projectId: any
    powerUnitId: any
    powerUnitName: any
    datas: Array<{
      identifier: string
      name: string
      label: string
      value: number
      unit: any
      deviceId: string
      displayLog: any
      sort: number
      max: any
      min: any
      status: number
      suggestedValue: string
    }>
  }>
  requestId: string
}

const props = defineProps<{
  chartData: ChartDataType | null
}>()
// console.log(props.chartData);


const chartContainer = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null

// 处理传入数据，生成 xAxis label 和 series data
const processedData = computed(() => {
  let list: any[] = []

  if (Array.isArray(props.chartData)) {
    list = props.chartData
  } else if (
    props.chartData &&
    Array.isArray((props.chartData as any).data)
  ) {
    list = (props.chartData as any).data
  }

  if (list.length === 0) {

    return []
  }

  const ret = list.map(d => {
    // 根据 status 值选定对应的图片
    let symbolImg = echarts0
    if (d.status === 1) {
      symbolImg = echarts2
    } else if (d.status === 2) {
      symbolImg = echarts1
    }
    return {
      name: d.label || d.name, // 用于 x 轴显示的名称
      value: d.value,
      symbol: 'image://' + symbolImg, // 拼接图片路径
      suggestedValue: d.suggestedValue
    }
  })
  return ret
})



// 根据处理后的数据生成 ECharts 的配置项
const option = computed(() => ({
  tooltip: {
    show: true,
    trigger: 'item',
    formatter: (params: any) => {
      // 打印看看 params.data 结构
      console.log('tooltip params.data:', params.data)
      // 取出建议值
      const sv = params.data.suggestedValue ?? '—'
      return `${params.name}:（建议：${sv}）`
    }
  },
  grid: {
    left: '10%',
    right: '10%',
    top: '10%',
    bottom: '10%'
  },
  xAxis: {
    data: processedData.value.map(item => item.name),
    axisTick: { show: true },
    axisLine: { show: true },
    axisLabel: { color: '#fff' },
  },
  yAxis: {
    splitLine: {
      show: true,
      lineStyle: { type: 'dashed', color: '#3f4e61' }
    },
    axisTick: { show: true },
    axisLine: { show: true },
    axisLabel: { show: true, color: '#fff' },
  },
  series: [
    {
      name: '数据',
      type: 'pictorialBar',
      barCategoryGap: '40%',
      itemStyle: { opacity: 0.9 },
      label: {
        show: true,
        position: 'top',
        color: '#fff',
        formatter: '{c}',
      },
      data: processedData.value.map(item => ({
        name: item.name,
        value: item.value,
        symbol: item.symbol,
        suggestedValue: item.suggestedValue
      })),
    },
  ]
}))

// 初始化图表
onMounted(() => {
  if (chartContainer.value) {
    chartInstance = echarts.init(chartContainer.value)
    chartInstance.setOption(option.value)
    chartInstance.on('mouseover', (params) => {
      console.log('mouseover params:', params)
    })
    window.addEventListener('resize', handleResize)
  }
})

watch(option, (newOption) => {
  if (chartInstance) {
    chartInstance.setOption(newOption)
  }
})

// 自适应窗口大小
function handleResize() {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onBeforeUnmount(() => {
  if (chartInstance) {
    window.removeEventListener('resize', handleResize)
    chartInstance.dispose()
  }
})
</script>

<style scoped>
div {
  width: 100%;
  height: 100%;
}
</style>
