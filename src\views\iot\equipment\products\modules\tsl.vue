<template>
  <div>
    <vue-json-editor
      v-model:json="thingModel"
      :showBtns="false"
      :mode="'text'"
      :escapeControlCharacters="true"
      lang="zh"
      height="400"
      @change="change"
    />
    <div class="mt-[20px]">
      <el-button type="primary" @click="submitThingModelChange()">保存</el-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { toRaw } from 'vue'
import vueJsonEditor from 'vue3-ts-jsoneditor'
import { saveObjectModel } from '@/views/iot/equipment/api/products.api'

const props = defineProps({
  id: propTypes.string.def(''),
  model: propTypes.object.def({}),
})


const change =(e)=>{
  thingModel.value = JSON.parse(e.text)
}


const thingModel = ref(props.model)
watch(() => props.model, (newV) => {
  thingModel.value = newV
})

const submitThingModelChange = () => {
  if (!thingModel) {
    return
  }
  saveObjectModel({
    productKey: props.id,
    model:JSON.stringify(toRaw(thingModel.value)),
  }).then((res) => {
    if (res.data) {
      ElMessage({
        type: 'success',
        message: '保存成功',
      })
    } else {
      ElMessage({
        type: 'error',
        message: '保存失败',
      })
    }

  }).finally(()=>{
  })
}
</script>
