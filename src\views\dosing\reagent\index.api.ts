import request from '@/utils/request'

enum Api {
  modifylist = 'medical/modify/list',
  addmodify = 'medical/modify/add',
  editmodify = 'medical/modify/edit',
  deleteModify = 'medical/modify/delete',
  deletemodifyConfig = '/medical/modifyConfig/delete',
  modifyListConfig = 'medical/modifyConfig/list',
  configDetailDeviceList = '/device/list',
  configDetailThingModelByProductKey = '/product/getThingModelByProductKey',
  addCorrectivePlan = '/medical/modifyConfig/add',
  getAllUser = '/system/user/list',
  getSelectedUser = '/medical/modify/listReceiver',
  editmodifyConfig = '/medical/modifyConfig/edit',
  editReceiver = '/medical/modify/editReceiver',
  send = '/medical/modify/send',
}

// 药剂方案矫正列表
export const modifyList = (data) => {
  return request({
    url: Api.modifylist,
    method: 'post',
    data,
  })
}
// 新增药剂方案矫正列表
export const addModify = (data) => {
  return request({
    url: Api.addmodify,
    method: 'post',
    data,
  })
}
// 修改药剂方案矫正列表
export const editModify = (data) => {
  return request({
    url: Api.editmodify,
    method: 'post',
    data,
  })
}
// 查询矫正方案配置列表
export const modifyListConfig = (data) => {
  return request({
    url: Api.modifyListConfig,
    method: 'post',
    data,
  })
}
// 查询设备列表
export const getDeviceList = (data) => {
  return request({
    url: Api.configDetailDeviceList,
    method: 'post',
    data,
  })
}
// 获取设备对应点位
export const getDevicePoint = (data) => {
  return request({
    url: Api.configDetailThingModelByProductKey,
    method: 'post',
    data,
  })
}
// 新增矫正方案配置
export const addCorrectivePlan = (data) => {
  return request({
    url: Api.addCorrectivePlan,
    method: 'post',
    data,
  })
}
// 删除矫正方案
export const deleteModify = (data) => {
  return request({
    url: Api.deleteModify,
    method: 'post',
    data,
  })
}
// 删除药剂方案配置列表
export const deleteModifyConfig = (data) => {
  return request({
    url: Api.deletemodifyConfig,
    method: 'post',
    data,
  })
}
// 查询所有人员
export const getAllUser = (data) => {
  return request({
    url: Api.getAllUser,
    method: 'post',
    data,
  })
}
export const getSelectedUser = (data) => {
  return request({
    url: Api.getSelectedUser,
    method: 'post',
    data,
  })
}
// 修改矫正方案配置
export const editmodifyConfig = (data) => {
  return request({
    url: Api.editmodifyConfig,
    method: 'post',
    data,
  })
}
// 编辑推送人员
export const editPushUser = (data) => {
  return request({
    url: Api.editReceiver,
    method: 'post',
    data,
  })
}
// 推送药剂方案
export const pushPlan = (data) => {
  return request({
    url: Api.send,
    method: 'post',
    data,
  })
}
