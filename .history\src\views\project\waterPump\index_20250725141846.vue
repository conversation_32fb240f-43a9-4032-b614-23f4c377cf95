<template>
  <div class="app-container home">
    <!-- ── 表格视图 ── -->
    <div>
      <yt-table-fun @handle-add="handleRegularAdd()">
        <template #rightToolbar>
          <!-- v-hasPermi="['project:waterPump:pwdconfig']" -->
          <el-button style="margin-right: 10px" type="primary" v-if="showPwdConfigBtn" icon="Lock" plain @click="handlePwdConfig()"
            >设备密码配置</el-button
          >
          <el-button style="margin-right: 10px" type="primary" v-if="showPwdConfigBtn" plain @click="controlHistory()">控制历史</el-button>
          <el-button
            style="margin-right: 10px"
            type="primary"
            v-if="showPwdConfigBtn"
            plain
            icon="Tools"
            :disabled="selectedRows.length === 0"
            @click="confirmBatchDelete()"
          >
            批量操作
          </el-button>
          <el-radio-group v-model="layoutType">
            <el-radio-button label="table">
              <svg-icon icon-class="table2" />
            </el-radio-button>
            <el-radio-button label="card">
              <svg-icon icon-class="card" />
            </el-radio-button>
          </el-radio-group>
        </template>

        <!-- 表格视图 -->
        <template v-if="layoutType === 'table'">
          <yt-table
            ref="regularTableRef"
            :data="Regulardata"
            :column="regularColumn"
            :view-btn="false"
            :page="pageObj"
            selection
            menu-slot
            @update:page="getRegulardata"
            @changePage="getRegulardata"
            @handle-update="handleRegularUpdate"
            @handle-delete="handleRegularDel"
            @handleOpenDialog="handleOpenPowerDialog"
            @handle-selection-change="onSelectionChange"
            :total="waterPumpTotal"
          >
            <template #activate="{ row }">
              <el-switch v-model="row.activate" disabled :active-value="true" :inactive-value="false" />
            </template>

            <!-- 新增配置按钮插槽 -->
            <template #menuSlot="{ row }">
              <el-tooltip class="box-item" effect="dark" content="控制点位配置" placement="top">
                <el-button link type="warning" icon="Setting" @click="CtrlPtConfig(row)" />
              </el-tooltip>
            </template>
          </yt-table>
        </template>

        <!-- 卡片视图 -->
        <template v-else>
          <el-row class="card-list flex">
            <el-col class="card-item" v-for="(item, index) in Regulardata" :key="index" :class="item.activate ? 'success-box' : 'error-box'">
              <div class="text-box">
                <div class="title flex align-center">
                  <div class="title-l">
                    <div class="icon">
                      <svg-icon icon-class="card2" />
                    </div>
                    {{ item.name }}
                  </div>
                  <div class="title-r">
                    <status-tag
                      :type="item.onOff === 1 ? 'success' : item.onOff === 0 ? 'info' : 'danger'"
                      :text="item.onOff === 1 ? '在线' : item.onOff === 0 ? '离线' : '异常'"
                    />
                  </div>
                </div>
                <div class="text flex">
                  <div class="txt">
                    <div class="txt-item">
                      <div class="label">型号</div>
                      <div class="value">{{ item.model }}</div>
                    </div>
                    <div class="txt-item">
                      <div class="label">工作方式</div>
                      <div class="value">{{ item.workWay === 0 ? '工频' : item.workWay === 2 ? '变频' : item.workWay }}</div>
                    </div>
                  </div>
                  <div class="img">
                    <img
                      :src="item.onOff === 0 ? waterPump0 : item.onOff === 1 ? waterPump1 : waterPump3"
                      alt="设备图"
                      style="width: 50px;height: 50px;"
                    />
                  </div>
                </div>
              </div>
              <div class="btn-group">
                <el-button class="cu-btn" type="primary" icon="EditPen" plain @click="handleRegularUpdate(item)">编辑</el-button>
                <el-button class="cu-btn" type="warning" icon="Setting" v-if="showPwdConfigBtn" plain @click="CtrlPtConfig(item)">配置</el-button>
                <el-button class="cu-btn" type="info" icon="Switch" v-if="showPwdConfigBtn" plain @click="handleControl(item)">控制</el-button>
                <!-- 密码控制 -->
                <el-divider direction="vertical" />
                <el-popconfirm title="是否确认删除?" @confirm="handleRegularDel(item)">
                  <template #reference>
                    <el-button class="cu-btn" type="danger" icon="Delete" plain />
                  </template>
                </el-popconfirm>
              </div>
            </el-col>
          </el-row>
        </template>
      </yt-table-fun>
      <yt-table-form
        ref="tableRegularFormRef"
        labelWidth="160px"
        :width="600"
        align="center"
        :column="regularColumn"
        @onSuccess="handleRegularSave"
      />
    </div>

    <!-- 配置项---功率 -->
    <el-dialog
      v-model="dialogRegularVisible"
      title="功率配置"
      width="1500px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <!-- 功率表格 -->
      <yt-table-fun @handle-add="handlePowerAdd()">
        <yt-table
          :data="powerData"
          :column="powerColumn"
          :view-btn="false"
          @handle-update="handlePowerUpdate"
          @handle-delete="handlePowerDel"
          @handleOpenDialog="handleOpenwaterDialog"
        >
        </yt-table>
      </yt-table-fun>
      <!-- 新增功率参数 -->
      <yt-table-form ref="tablePowerFormRef" labelWidth="160px" :width="600" :column="powerColumn" @onSuccess="handlePowerSave"></yt-table-form>
    </el-dialog>
    <!-- 配置项--水量 -->
    <el-dialog
      v-model="dialogWaterVisible"
      title="水量配置"
      width="600px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form :model="waterForm" ref="waterFormRef">
        <div v-for="(item, index) in waterForm" :key="index" class="water-form-item">
          <el-form-item label="机组" class="inline-form-item" prop="powerUnitId">
            <el-select v-model="item.powerUnitId" clearable placeholder="请选择机组" style="width: 180px;">
              <el-option
                v-for="option in powerUnitOptions"
                :key="option.value"
                :disabled="option.disabled"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="水量" class="inline-form-item" prop="water">
            <el-input v-model="item.water" placeholder="请输入水量" style="width: 180px;"></el-input>
          </el-form-item>
          <el-button type="danger" icon="Minus" @click="removeWaterFormItem(index)" v-if="waterForm.length > 1"></el-button>
          <el-button type="primary" icon="Plus" @click="addWaterFormItem"></el-button>
        </div>
        <div style="text-align: right;">
          <el-button type="primary" @click="saveWaterData">保存</el-button>
        </div>
      </el-form>
    </el-dialog>
    <!-- 配置密码弹窗 -->
    <el-dialog v-model="passwordDialogVisible" title="请输入操作密码" width="400px" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form>
        <el-form-item label="密码" label-width="60px">
          <el-input v-model="passwordInput" show-password placeholder="请输入密码" @keyup.enter="confirmPassword" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmPassword">确认</el-button>
      </template>
    </el-dialog>
    <!-- 控制操作 -->
    <el-dialog v-model="controlDialogVisible" title="设备控制" width="450px" :close-on-press-escape="false" :close-on-click-modal="false">
      <!-- 第一排：开 / 关 -->
      <div class="control-row" style="display:flex; justify-content:space-evenly; gap:12px; margin-bottom:12px;">
        <el-button :type="start === 1 ? 'primary' : 'default'" @click="start = start === 1 ? null : 1; end = start === 1 ? null : 0">开启</el-button>
        <el-button :type="end === 1 ? 'primary' : 'default'" @click="end = end === 1 ? null : 1; start = end === 1 ? null : 0">关闭</el-button>
      </div>

      <!-- 第二排：工频 / 变频 -->
      <div class="control-row" style="display:flex; justify-content:space-evenly; gap:12px; margin-bottom:16px;">
        <el-button :type="power === 1 ? 'primary' : 'default'" :disabled="end === 1" @click="power = power === 1 ? null : 1; variable = ''"
          >工频</el-button
        >
        <el-button :type="power === 0 ? 'primary' : 'default'" :disabled="end === 1" @click="power = power === 0 ? null : 0">变频</el-button>
      </div>

      <!-- 频率输入 -->
      <el-form label-width="80px" style="text-align:center;" v-show="power === 0 && end !== 1">
        <el-form-item label="频率 (Hz)">
          <el-input v-model="variable" placeholder="请输入 Hz" @input="variable = variable.replace(/[^\d.]/g, '')" style="width:230px;" />
        </el-form-item>
      </el-form>

      <!-- 底部操作 -->
      <template #footer>
        <el-button @click="cancelControl">取消</el-button>
        <el-button type="primary" @click="confirmControl">确认</el-button>
      </template>
    </el-dialog>
    <!-- 密码配置 -->
    <el-dialog v-model="pwdconfigDialogVisible" title="设备密码配置" width="400px" :close-on-press-escape="false" :close-on-click-modal="false">
      <!-- 修改密码 -->
      <el-form @submit.prevent>
        <!-- 是否需要密码 -->
        <el-form-item label="是否需要密码" label-width="100px">
          <el-switch v-model="isNeedPassword" />
        </el-form-item>
        <el-form-item label="密码" label-width="60px" v-if="isNeedPassword">
          <el-input v-model="pwdconfigInput" show-password placeholder="请输入密码" @keyup.enter="savepwdconfig" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelpwdconfig">取消</el-button>
        <el-button type="primary" @click="savepwdconfig">保存</el-button>
      </template>
    </el-dialog>
    <!-- 控制配置 -->
    <el-dialog v-model="translateDialogVisible" title="通信参数配置" width="450px" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form :model="translateForm" label-width="100px">
        <el-form-item label="IP 地址" prop="ip">
          <el-input v-model="translateForm.ip" placeholder="请输入 IP 地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model="translateForm.port" placeholder="请输入端口" @input="translateForm.port = translateForm.port.replace(/[^\d]/g, '')" />
        </el-form-item>
        <el-form-item label="从站地址" prop="slave">
          <el-input
            v-model="translateForm.slave"
            placeholder="请输入从站地址"
            @input="translateForm.slave = translateForm.slave.replace(/[^\d]/g, '')"
          />
        </el-form-item>
        <el-form-item label="起始地址" prop="start">
          <el-input
            v-model="translateForm.start"
            placeholder="请输入起始地址"
            @input="translateForm.start = translateForm.start.replace(/[^\d]/g, '')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelTranslate">取消</el-button>
        <el-button type="primary" @click="saveTranslate">保存</el-button>
      </template>
    </el-dialog>
    <!-- 控制点位配置表单弹窗 -->
    <el-dialog
      v-model="ctrlPtFormVisible"
      :title="ctrlPtFormType === 'add' ? '新增配置' : '编辑配置'"
      width="600px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form :model="ctrlPtForm" ref="ctrlPtFormRef" label-width="140px">
        <el-form-item label="配置ID" v-if="ctrlPtForm.configId">
          <el-input v-model="ctrlPtForm.configId" readonly disabled />
        </el-form-item>

        <el-form-item label="选择设备" :rules="[{ required: true, message: '请选择设备', trigger: 'change' }]">
          <el-select v-model="ctrlPtForm.deviceKey" placeholder="请选择设备" @change="handleFormDeviceChange" style="width: 100%;">
            <el-option v-for="device in deviceOptions" :key="device.value" :label="device.label" :value="device.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="IP" :rules="[{ required: true, message: '请输入IP地址', trigger: 'blur' }]">
          <el-input v-model="ctrlPtForm.ip" placeholder="请输入IP地址" />
        </el-form-item>

        <el-form-item label="通讯方式" :rules="[{ required: true, message: '请选择通讯方式', trigger: 'change' }]">
          <el-radio-group v-model="ctrlPtForm.communicationType">
            <el-radio :label="0">OPC-UA</el-radio>
            <el-radio :label="1">ModelbusTCP</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="端口" :rules="[{ required: true, message: '请输入端口', trigger: 'blur' }]">
          <el-input v-model="ctrlPtForm.port" placeholder="请输入端口" @input="ctrlPtForm.port = ctrlPtForm.port.replace(/[^\d]/g, '')" />
        </el-form-item>

        <el-form-item label="从站地址">
          <el-input
            v-model="ctrlPtForm.slaveId"
            placeholder="请输入从站地址"
            @input="ctrlPtForm.slaveId = ctrlPtForm.slaveId.replace(/[^\d]/g, '')"
          />
        </el-form-item>

        <el-form-item label="开关状态标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.onOffCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.onOffAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="运行频率的标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.frequencyCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.frequencyAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="工作模式的标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.workModelCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.workModelAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="工作错误信息">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.workErrorCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.workErrorAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="开关设置的标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.onOffSetCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.onOffSetAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="工作模式设置的标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.workModelSetCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.workModelSetAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="频率设置的标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.frequencySetCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.frequencySetAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="ctrlPtFormVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCtrlPtSave">保存</el-button>
      </template>
    </el-dialog>
    <!-- 控制历史 -->
    <el-dialog v-model="controlHistoryDialogVisible" title="控制历史" width="800px">
      <el-table :data="controlHistoryList" border style="width: 100%">
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="createTime" label="操作时间" width="180" align="center">
          <template #default="scope">
            {{ scope.row.createTime || '暂无时间' }}
          </template>
        </el-table-column>
        <el-table-column prop="commandListStr" label="操作历史" align="left">
          <template #default="scope">
            <div v-for="(command, index) in scope.row.commandListStr" :key="index">
              {{ command }}
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container" style="margin-top: 20px; display: flex; justify-content: flex-end;">
        <el-pagination
          v-model:current-page="pageHistor.pageNum"
          v-model:page-size="pageHistor.pageSize"
          :total="controlHistoryTotal"
          @current-change="handleHistoryPageChange"
          @size-change="handleHistorySizeChange"
          layout="total, sizes, prev, pager, next"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {
  getWaterPumpList,
  savePumpList,
  getSubsystemList,
  deleteWaterPump,
  getwaterPumpPowerList,
  savepowerList,
  deletePower,
  subSystemDetail,
  saveWater,
  getCheckConfigedit,
  getCheckConfigList,
  getCheckConfigAdd,
  getDeviceList,
  getThingModelList,
  addWaterPumpCtrl,
  getWaterPumpCtrlList,
  editWaterPumpCtrl,
  getDeviceStatus,
  getControlHistory,
} from './api/pump.api'
// 字典
import { ComponentInternalInstance } from 'vue'
import YtTableFun from '@/components/common/yt-table-fun.vue'
import YtTable from '@/components/common/yt-table'
import { IColumn } from '@/components/common/types/tableCommon'
import YtTableForm from '@/components/common/yt-table-form'
import { FormInstance, FormRules } from 'element-plus'
import emitter from '@/utils/eventBus.js'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { work_way} = toRefs<any>(proxy?.useDict('work_way'))
console.log(work_way.value, 'work_way.value');

const regularTableRef = ref<InstanceType<typeof import('@/components/common/yt-table').default> | null>(null)
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const dialogRegularVisible = ref(false)
const dialogWaterVisible = ref(false)
const passwordDialogVisible = ref(false)
const controlDialogVisible = ref(false)
// 控制点位配置
const ctrlPtConfigDialogVisible = ref(false)
const tableRegularFormRef = ref()
const tablePowerFormRef = ref()
const subSystemData = ref([])
const waterFormRef = ref<FormInstance>()
const translateDialogVisible = ref(false)
const pwdconfigDialogVisible = ref(false)
const pwdconfigInput = ref('')
const translateForm = reactive({
  ip: '',
  port: '',
  slave: '',
  start: '',
})
const pwdConfig = ref<null | {
  id: number
  passwd: string
  type: number
}>(null)

const showPwdConfigBtn = computed(() => pwdConfig.value !== null)
// 固定参数数据
const Regulardata = ref([])
// 切换table/card
const layoutType = ref<'table' | 'card'>('table')
// 用于存储当前多选的行
const selectedRows = ref<any[]>([])
// 功率数据
const powerData = ref([])
// 存一下功率当前选择列的id
const powerId = ref()
const waterId = ref()
// 对水量进行处理
const powerUnitOptions = ref([])
const passwordInput = ref('')
const start = ref<1 | 0 | null>(null)
const end = ref<1 | 0 | null>(null)
const power = ref<1 | 0 | null>(null)
const variable = ref('')
let currentControlRow: any = null
// 配置
let currentTranslateRow: any = null
const handleOpenTranslate = (row: any) => {
  currentTranslateRow = row
  // 如果 row 里有初始值就赋上去，否则清空
  translateForm.ip = row.ip || ''
  translateForm.port = row.port || ''
  translateForm.slave = row.slave || ''
  translateForm.start = row.start || ''
  translateDialogVisible.value = true
}
const cancelTranslate = () => {
  translateDialogVisible.value = false
}
// 监听表格多选变化
const onSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}
// 批量操作
const confirmBatchDelete = () => {
  handleControl()
}
const isNeedPassword = ref(false)
const Pwdid = ref()
// 密码配置
const handlePwdConfig = () => {
  if (!pwdConfig.value) return
  pwdconfigDialogVisible.value = true
  pwdconfigInput.value = pwdConfig.value.passwd
  Pwdid.value = pwdConfig.value.id
  isNeedPassword.value = pwdConfig.value.type === 0
}
// 取消密码配置
const cancelpwdconfig = () => {
  pwdconfigDialogVisible.value = false
  pwdconfigInput.value = ''
}
// 保存密码配置
const savepwdconfig = () => {
  pwdconfigDialogVisible.value = false
  const params = {
    id: Pwdid.value,
    projectId: cachedProjects.id,
    passwd: pwdconfigInput.value,
    type: isNeedPassword.value ? 0 : 1,
  }
  getCheckConfigedit(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success('保存成功')
      loadPwdConfig()
    }
  })
}
const saveTranslate = () => {
  // 简单校验
  if (!translateForm.ip) {
    ElMessage.warning('IP 地址不能为空')
    return
  }
  if (!translateForm.port) {
    ElMessage.warning('端口不能为空')
    return
  }
  ElMessage.success('保存成功（模拟）')
  translateDialogVisible.value = false
}
// 查询控制历史列表
const pageHistor = reactive({
  pageNum: 1,
  pageSize: 10,
})
// 控制历史相关变量
const controlHistoryDialogVisible = ref(false)
const controlHistoryTotal = ref(0)
const controlHistoryList = ref([])
const controlHistory = () => {
  controlHistoryDialogVisible.value = true
  getControlHistoryList()
}
// 获取控制历史列表
const getControlHistoryList = () => {
  getControlHistory({
    ...pageHistor,
    projectId: cachedProjects.id,
    deviceType: 0,
  }).then((res) => {
    if (res.code === 200) {
      controlHistoryList.value = res.data.rows
      controlHistoryTotal.value = res.data.total
    }
  })
}
// 分页处理
const handleHistoryPageChange = (page: number) => {
  pageHistor.pageNum = page
  getControlHistoryList()
}
const handleHistorySizeChange = (size: number) => {
  pageHistor.pageSize = size
  pageHistor.pageNum = 1
  getControlHistoryList()
}
// 控制
const handleControl = async (item?: any) => {
  passwordInput.value = ''
  // 保存当前操作的设备信息
  if (item) {
    // 单个设备控制
    currentControlRow = item
    try {
      // 获取设备状态
      const res = await getDeviceStatus(item.id)
      if (res.code === 200 && res.data) {
        // 数据设置初始状态
        const deviceStatus = res.data
        start.value = deviceStatus.start
        end.value = deviceStatus.end
        power.value = deviceStatus.power
        variable.value = deviceStatus.variable || ''
      } else {

        resetControlState()
      }
    } catch (error) {
      console.error('获取设备状态失败:', error)
      ElMessage.error('获取设备状态失败')
      // 使用默认值
      resetControlState()
    }
  } else {
    // 批量控制 - 使用默认值
    currentControlRow = null
    resetControlState()
  }

  if (pwdConfig.value?.type === 0) {
    passwordDialogVisible.value = true
  } else {
    openControlDialog()
  }
}

// 添加一个重置控制状态的方法
const resetControlState = () => {
  start.value = null
  end.value = null
  power.value = null
  variable.value = ''
}

// 修改 openControlDialog 方法，不再重置状态
const openControlDialog = () => {
  // 不再重置状态，保持从接口获取的状态
  controlDialogVisible.value = true
}

// 3. 取消控制
const cancelControl = () => {
  controlDialogVisible.value = false
  selectedRows.value = []
  nextTick(() => {
    regularTableRef.value?.tableRef.clearSelection()
  })
}

// 4. 确认控制
const confirmControl = () => {
  let devicesToControl = []

  // 判断是单个控制还是批量控制
  if (currentControlRow) {
    // 单个设备控制
    devicesToControl = [currentControlRow]
  } else {
    // 批量控制
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请先选择要控制的设备')
      return
    }
    devicesToControl = selectedRows.value
  }

  // 为选中的设备创建控制指令
  const commandArray = devicesToControl.map((row) => {
    const command = {
      configName: row.name,
      configId: currentControlRow ? currentControlRow.id : selectedRows.value[0].id,
    }

    // 根据用户选择构建控制参数
    if (start.value === 1) {
      // 开启状态
      command.start = 1

      if (power.value === 1) {
        // 工频模式
        command.power = 1
      } else if (power.value === 0 && variable.value) {
        // 变频模式且有频率值
        command.power = 0
        command.variable = variable.value
      }
    } else if (end.value === 1) {
      // 关闭状态
      command.end = 1
    }

    return command
  })

  const params = {
    projectId: cachedProjects.id,
    type: 0,
    deviceType: 0,
    commandList: commandArray,
  }

  getCheckConfigAdd(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success('控制成功')
      ElMessage.success(`已向 ${devicesToControl.length} 台设备发送控制指令`)
    }
  })

  // 清空选择
  selectedRows.value = []
  currentControlRow = null
  nextTick(() => {
    regularTableRef.value?.tableRef.clearSelection()
  })
  controlDialogVisible.value = false
}

// 监听 power 变化，当不是变频时清空频率值
watch(power, (newVal) => {
  if (newVal !== 0) {
    // 当不是变频时
    variable.value = '' // 清空频率值
  }
})

const waterForm = ref([
  {
    powerUnitId: null,
    water: null,
  },
])
const addWaterFormItem = () => {
  waterForm.value.push({
    powerUnitId: null,
    water: null,
  })
}
const removeWaterFormItem = (index) => {
  waterForm.value.splice(index, 1)
}

const saveWaterData = () => {
  // 处理保存逻辑，格式化为JSON
  const dataToSave = waterForm.value.map((item) => ({
    powerUnitId: item.powerUnitId,
    powerUnitNames: powerUnitOptions.value.find((option) => option.value === item.powerUnitId)?.label,
    water: item.water,
  }))
  const parms = {
    id: waterId.value,
    water: JSON.stringify(dataToSave),
  }

  // 这里可以调用API保存dataToSave
  saveWater(parms)
    .then((res) => {
      if (res.code === 200) {
        ElMessage.success('保存成功')

        // 清空输入框
        waterForm.value = [
          {
            powerUnitId: null,
            water: null,
          },
        ]

        // 关闭弹框
        dialogWaterVisible.value = false
        getwaterPumpPower(powerId.value)
      }
    })
    .catch((err) => {
      ElMessage.error('保存失败')
      console.error(err)
      dialogWaterVisible.value = false
      getwaterPumpPower(powerId.value)
    })
}
const waterRules: FormRules = {
  water: [
    { required: true, message: '水量不能为空', trigger: 'blur' },
    { type: 'number', message: '水量必须是数字类型', trigger: 'blur' },
  ],
  powerUnitId: [{ required: true, message: '机组不能为空', trigger: 'blur' }],
}

// 输入水量时只允许数字
const handleWaterInput = (index: number) => {
  const value = waterForm.value[index].water
  if (value && !/^\d*\.?\d*$/.test(value.toString())) {
    waterForm.value[index].water = value.toString().replace(/[^\d.]/g, '')
  }
}

// 固定参数表头
const regularColumn = ref<IColumn[]>([
  {
    search: true,
    label: '水泵名称',
    key: 'name',
    rules: [{ required: true, message: '水泵名称不能为空' }],
  },
  {
    label: '型号',
    key: 'model',
  },
  {
    label: '工作方式',
    key: 'workWay',
    type: 'select',
    componentProps: {
      options: computed(() => work_way.value || []),
    },
    formatter: (row: any) => {
      return getWorkWayLabel(row.workWay)
    }
  },
  {
    label: '电流指标',
    key: 'currentIdentifier',
  },
  {
    label: '汽蚀余量 (m)',
    key: 'npsh',
    // rules: [{ required: true, message: '汽蚀余量不能为空' }],
  },
  {
    label: '电动机额定电压(kV)',
    key: 'voltage',
  },
  {
    label: '传动效率(%)',
    key: 'transEfficiency',
  },
  {
    label: '电机效率(%)',
    key: 'electricalEfficiency',
  },
  {
    label: '激活状态',
    key: 'activate',
    tableWidth: 120,
    type: 'switch',
    slot: true,
  },
  {
    label: '所属子系统',
    key: 'subSystemId',
    type: 'select',
    rules: [{ required: true, message: '所属子系统不能为空' }],
    // search: true,
    componentProps: {
      // defaultValue: '',
      clearable: false,
      options: [],
    },
  },
])
// 功率参数表头
const powerColumn: IColumn[] = [
  {
    search: true,
    label: '运行类型',
    key: 'title',
    rules: [{ required: true, message: '运行类型不能为空' }],
  },
  {
    label: '权重',
    key: 'weight',
    rules: [
      { required: true, message: '权重不能为空' },
      {
        validator: (rule, value, callback) => {
          if (value >= 1) {
            callback() // 校验通过
          } else {
            callback(new Error('权重必须是正整数'))
          }
        },
        trigger: 'blur',
      },
    ],
  },
  {
    label: '关联指标',
    key: 'condIdentifier',
  },
  {
    label: '条件',
    key: 'cond',
    type: 'select',
    rules: [{ required: true, message: '请选择条件' }],
    componentProps: {
      options: [
        {
          name: '大于',
          value: '>',
        },
        {
          name: '等于',
          value: '==',
        },
        {
          name: '大于等于',
          value: '>=',
        },
        {
          name: '小于',
          value: '<',
        },
        {
          name: '小于等于',
          value: '<=',
        },
        {
          name: '任意',
          value: '*',
        },
      ],
    },
  },
  {
    label: '条件值',
    key: 'matchedValue',
    rules: [{ required: true, message: '请输入电动机电流值' }],
  },
  {
    label: '转速(r/min)',
    key: 'speed',
  },

  {
    label: '流量(m³/h)',
    key: 'flow',
  },
  {
    label: '电动机额定电流(A)',
    key: 'ratedCurrent',
  },
  {
    label: '模拟电流(A)',
    key: 'current',
  },
  {
    label: '循环水泵扬程 (m)',
    key: 'lift',
  },
  {
    label: '泵效率(%)',
    key: 'efficiency',
  },
  {
    label: '循环水泵额定功率因数',
    key: 'factor',
  },
  {
    label: '循环水泵功率(kW)',
    key: 'power',
    formWatch: (scope) => {
      const power = parseFloat(scope.data.power)
      const powerFactor = parseFloat(scope.data.powerFactor)

      if (!isNaN(power) && !isNaN(powerFactor)) {
        scope.data.consumption = power * powerFactor
      } else {
        scope.data.consumption = '' // 避免 NaN 的问题
      }
    },
  },
  {
    label: '循环水泵功耗系数',
    key: 'powerFactor',
    formWatch: (scope) => {
      const power = parseFloat(scope.data.power)
      const powerFactor = parseFloat(scope.data.powerFactor)

      if (!isNaN(power) && !isNaN(powerFactor)) {
        scope.data.consumption = power * powerFactor
      } else {
        scope.data.consumption = '' // 避免 NaN 的问题
      }
    },
  },
  {
    label: '循环水泵功耗',
    key: 'consumption',
    editDisabled: true, //禁止编辑
    addDisabled: true, //禁止新增
  },
]
// 新增固定参数
const handleRegularAdd = () => {
  tableRegularFormRef.value?.openDialog('add', {})
}
// 功率新增
const handlePowerAdd = () => {
  tablePowerFormRef.value?.openDialog('add', {})
}
// 水量新增
const tableWaterFormRef = ref()
// 新增固定参数弹窗前操作
const openBeforeFun = ({ type, data }) => {
  if (type === 'add') {
    data.id = ''
  }
}
// 编辑固定参数
const handleRegularUpdate = (row: any) => {
  tableRegularFormRef.value?.openDialog('update', row)
}
// 编辑功率参数
const handlePowerUpdate = (row: any) => {
  tablePowerFormRef.value?.openDialog('update', row)
}
// 删除固定参数
const handleRegularDel = (row: any) => {
  loading.value = true
  deleteWaterPump([row.id])
    .then((res) => {
      if (res.code === 200) {
        ElMessage.success('删除成功!')
        getRegulardata()
      }
    })
    .finally(() => {
      loading.value = false
    })
}
// 功率参数删除
const handlePowerDel = (row: any) => {
  deletePower([row.id]).then((res) => {
    if (res.code === 200) {
      ElMessage.success('删除成功!')
      getwaterPumpPower(powerId.value)
    }
  })
}
const waterPumpTotal = ref(0)
const pageObj = reactive({
  pageNum: 1,
  pageSize: 10,
})
// 查询固定参数数据
const getRegulardata = () => {
  const params = {
    projectId: cachedProjects.id,
    pageNum: pageObj.pageNum,
    pageSize: pageObj.pageSize,
  }
  getWaterPumpList(params).then((res) => {
    if (res.data.rows.length > 0) {
      Regulardata.value = res.data.rows.map((item: any) => {
        // 转换工作方式
        const workWayLabel = work_way.value?.find(way => way.value === item.workWay)?.label || item.workWay
        return {
          ...item,
          workWay: workWayLabel, // 直接存储label值
          activate: item.activate === 1, // 如果activate为1则转换为true，否则转换为false
        }
      })
      waterPumpTotal.value = res.data.total
    }
  })
}
const loading = ref(false)
// 保存固定参数
const handleRegularSave = ({ type, data, cancel }) => {
  loading.value = true
  const { id: projectId } = cachedProjects
  if (type == 'add') {
    const { activate, ...restData } = data
    // console.log(data, '-----')

    const modifiedData = {
      ...restData,
      projectId,
      activate: activate ? 1 : 0,
    }

    savePumpList(toRaw(modifiedData))
      .then((res) => {
        if (res.code === 200) {
          cancel()
          getRegulardata()
        }
      })
      .finally(() => {
        loading.value = false
      })
  } else if (type === 'update') {
    const { activate, ...restData } = data
    const modifiedData = {
      ...restData,
      projectId,
      activate: activate ? 1 : 0,
    }

    savePumpList(toRaw(modifiedData))
      .then((res) => {
        if (res.code === 200) {
          cancel()
          getRegulardata()
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}
// 保存功率参数
const handlePowerSave = ({ type, data, cancel }) => {
  loading.value = true

  const power = parseFloat(data.power)
  const powerFactor = parseFloat(data.powerFactor)

  if (type === 'add') {
    data.configId = powerId.value
    savepowerList(data)
      .then((res) => {
        // console.log(res, '功率新增')
        cancel()
        getwaterPumpPower(powerId.value)
      })
      .finally(() => {
        loading.value = false
      })
  } else if (type === 'update') {
    savepowerList(data)
      .then((res) => {
        // console.log(res, '功率更新')
        cancel()
        getwaterPumpPower(powerId.value)
      })
      .finally(() => {
        loading.value = false
      })
  }
}
const getSubSystemData = () => {
  const params = {
    identifier: 'water_pump',
    projectId: cachedProjects.id,
  }
  return getSubsystemList(params).then((res) => {
    if (res.code === 200) {
      subSystemData.value = res.data
      const subSystemColumn = regularColumn.value.find((col) => col.key === 'subSystemId')

      if (subSystemColumn && subSystemColumn.componentProps) {
        // 更新 componentProps.options 并确保触发响应式更新
        subSystemColumn.componentProps = {
          ...subSystemColumn.componentProps,
          options: res.data.map((item: any) => ({
            value: item.id,
            label: item.powerUnitNames + item.name,
          })),
        }

        // 手动触发 Vue 的响应性更新
        regularColumn.value = [...regularColumn.value]
      }
    }
  })
}

// 打开功率表格
const handleOpenPowerDialog = (row: any) => {
  dialogRegularVisible.value = true
  powerId.value = row.id
  getwaterPumpPower(row.id)

  subSystemDetail(row.subSystemId).then((res) => {
    // console.log(res, '--所属子系统')

    let options = []
    const powerUnitIds = res.data.powerUnitIds
    const powerUnitNames = res.data.powerUnitNames

    // 判断是否包含逗号，处理单个或多个值的情况
    if (powerUnitIds.includes(',') && powerUnitNames.includes('，')) {
      const idsArray = powerUnitIds.split(',')
      const namesArray = powerUnitNames.split('，') // 假设名字是用中文逗号分隔的

      // 组装多个 options 数据
      options = idsArray.map((id, index) => ({
        value: id,
        label: namesArray[index],
      }))
    } else {
      // 处理单个 option 的情况
      options = [
        {
          value: powerUnitIds,
          label: powerUnitNames,
        },
      ]
    }
    powerUnitOptions.value = options
  })
}

// 查询功率列表
const getwaterPumpPower = (data) => {
  const parms = {
    configId: data,
  }
  getwaterPumpPowerList(parms).then((res) => {
    // console.log(res,'--功率列表数据')
    powerData.value = res.data.rows
  })
}
// 打开水量表格
const handleOpenwaterDialog = (row: any) => {
  dialogWaterVisible.value = true
  waterId.value = row.id

  // 解析 water 字段中的 JSON 字符串
  if (row.water) {
    try {
      const parsedWater = JSON.parse(row.water)
      // 如果解析后的数据是数组且有内容，则赋值给 waterForm
      if (Array.isArray(parsedWater) && parsedWater.length > 0) {
        waterForm.value = parsedWater.map((item) => ({
          powerUnitId: item.powerUnitId,
          water: item.water,
        }))
      } else {
        // 如果数据为空数组，则初始化一个空对象到 waterForm
        waterForm.value = [{ powerUnitId: null, water: null }]
      }
    } catch (error) {
      console.error('解析 water 数据失败:', error)
      ElMessage.error('水量数据格式错误')
      // 初始化一个空对象到 waterForm 以防止渲染出错
      waterForm.value = [{ powerUnitId: null, water: null }]
    }
  } else {
    // 如果 water 字段为空，则初始化一个空对象到 waterForm
    waterForm.value = [{ powerUnitId: null, water: null }]
  }

  // 手动触发 Vue 的响应性更新
  waterForm.value = [...waterForm.value]
}
async function loadPwdConfig() {
  const params = { projectId: cachedProjects.id }
  try {
    const res = await getCheckConfigList(params)
    if (res.data.rows.length > 0) {
      const row = res.data.rows[0]
      pwdConfig.value = {
        id: row.id,
        passwd: row.passwd,
        type: row.type,
      }
      // 立刻初始化三个控件
      isNeedPassword.value = row.type === 0
      pwdconfigInput.value = row.passwd
      Pwdid.value = row.id
    }
  } catch (err) {
    console.error('加载密码配置失败', err)
  }
}

// 控制点位配置数据
const ctrlPtFormVisible = ref(false)
const ctrlPtFormType = ref<'add' | 'edit'>('add')
const ctrlPtFormRef = ref<FormInstance>()
const ctrlPtEditIndex = ref(-1)

// 设备选择
const deviceOptions = ref<any[]>([])
// 物模型选项（所有下拉框共用）
const thingModelOptions = ref<any[]>([])
// 当前表单的物模型选项
const currentThingModelOptions = ref<any[]>([])

// 控制点位配置表单
const ctrlPtForm = reactive({
  id: '', // 记录ID，用于编辑
  configId: '', // 配置ID，来自row.id
  deviceKey: '', // 设备ID
  deviceName: '', // 设备名称
  productKey: '', // 产品密钥，保存时需要
  ip: '',
  communicationType: 1, // 默认选择ModelbusTCP
  port: '',
  slaveId: '',
  onOffCode: '',
  onOffAddress: '',
  frequencyCode: '',
  frequencyAddress: '',
  workModelCode: '',
  workModelAddress: '',
  workErrorCode: '',
  workErrorAddress: '',
  onOffSetCode: '',
  onOffSetAddress: '',
  workModelSetCode: '',
  workModelSetAddress: '',
  frequencySetCode: '',
  frequencySetAddress: '',
})

// 获取选项标签
const getOptionLabel = (options: any[], value: string) => {
  const option = options.find((opt) => opt.value === value)
  return option ? option.label : value || '-'
}

// 获取设备列表
const getDevicedata = async () => {
  const params = {
    projectId: cachedProjects.id,
  }
  try {
    const res = await getDeviceList(params)
    if (res.code === 200 && res.data) {
      // 根据实际API数据结构调整映射，包含category字段
      deviceOptions.value = res.data.rows.map((device: any) => ({
        label: device.name, // 设备名称作为显示标签
        value: device.id, // 设备ID作为值，这个会存储到deviceKey中
        productKey: device.productKey, // 产品密钥，用于调用物模型API
        deviceName: device.name, // 设备名称
        // deviceId: device.id, // 设备ID
        category: device.category || '', // 设备类型
      }))
    }
  } catch (error) {
    ElMessage.error('获取设备列表失败')
  }
}

// 表单中设备选择变化处理
const handleFormDeviceChange = async (value: string) => {
  if (value) {
    // 设置deviceKey为选中的设备ID
    ctrlPtForm.deviceKey = value

    // 根据选中的设备ID获取设备信息
    const selectedDeviceInfo = deviceOptions.value.find((device) => device.value === value)

    if (selectedDeviceInfo) {
      ctrlPtForm.deviceName = selectedDeviceInfo.label // 使用label作为设备名称
      ctrlPtForm.productKey = selectedDeviceInfo.productKey // 保存productKey到表单

      // 获取该设备的物模型数据，使用productKey
      if (selectedDeviceInfo.productKey) {
        const options = await getThingModelData(selectedDeviceInfo.productKey)
        currentThingModelOptions.value = options
      }
    }
  } else {
    // 清空所有设备相关信息
    ctrlPtForm.deviceKey = ''
    ctrlPtForm.deviceName = ''
    ctrlPtForm.productKey = ''
    currentThingModelOptions.value = []
  }

  // 清空其他字段
  ctrlPtForm.onOffCode = ''
  ctrlPtForm.onOffAddress = ''
  ctrlPtForm.frequencyCode = ''
  ctrlPtForm.frequencyAddress = ''
  ctrlPtForm.workModelCode = ''
  ctrlPtForm.workModelAddress = ''
  ctrlPtForm.workErrorCode = ''
  ctrlPtForm.workErrorAddress = ''
  ctrlPtForm.onOffSetCode = ''
  ctrlPtForm.onOffSetAddress = ''
  ctrlPtForm.workModelSetCode = ''
  ctrlPtForm.workModelSetAddress = ''
  ctrlPtForm.frequencySetCode = ''
  ctrlPtForm.frequencySetAddress = ''
}

// 存储所有产品的物模型数据映射
const productThingModelMap = reactive(new Map<string, any[]>())

// 根据 productKey 和 identifier 获取对应的 name
const getNameByProductKeyAndIdentifier = (productKey: string, identifier: string): string => {
  if (!productKey || !identifier) return '-'
  const thingModelList = productThingModelMap.get(productKey)
  if (!thingModelList) return identifier // 如果没有找到物模型数据，返回原始值
  const item = thingModelList.find((model: any) => model.identifier === identifier)
  return item ? item.name : identifier // 如果找到就返回 name，否则返回原始值
}
// 加载单个产品的物模型数据
const loadThingModelForProduct = async (productKey: string) => {
  if (!productKey || productThingModelMap.has(productKey)) {
    return // 如果已经加载过，直接返回
  }
  try {
    const res = await getThingModelList(productKey)
    if (res.code === 200 && res.data) {
      // 将物模型数据的 properties 存储到映射中
      const thingModelData = res.data.model.properties || []
      productThingModelMap.set(productKey, thingModelData)
    }
  } catch (error) {
    console.error(`加载产品 ${productKey} 的物模型数据失败:`, error)
    productThingModelMap.set(productKey, []) // 设置空数组避免重复请求
  }
}

// 获取控制点位配置数据
const getWaterPumpCtrlData = async (configId: string) => {
  const params = {
    // projectId: cachedProjects.id,
    deviceType: 0, // 水泵设备类型为0
    configId: configId,
  }
  try {
    const res = await getWaterPumpCtrlList(params)
    if (res.code === 200 && res.data && res.data.id) {
      // 有数据且有id，说明是编辑模式
      const data = res.data
      ctrlPtForm.id = data.id || ''
      ctrlPtForm.configId = data.configId || configId
      ctrlPtForm.ip = data.ip || ''
      ctrlPtForm.communicationType = data.communicationType || 1
      ctrlPtForm.port = data.port || ''
      ctrlPtForm.slaveId = data.slaveId || ''
      ctrlPtForm.productKey = data.productKey || ''

      // 根据productKey找到对应的设备并设置deviceKey
      if (data.productKey) {
        const selectedDevice = deviceOptions.value.find((device) => device.productKey === data.productKey)
        if (selectedDevice) {
          ctrlPtForm.deviceKey = selectedDevice.value // 设备ID
          ctrlPtForm.deviceName = selectedDevice.label // 设备名称
        }
      }

      // 解析JSON字符串字段
      if (data.onOff) {
        const onOffData = JSON.parse(data.onOff)
        ctrlPtForm.onOffCode = onOffData.code || ''
        ctrlPtForm.onOffAddress = onOffData.address || ''
      }

      if (data.frequency) {
        const frequencyData = JSON.parse(data.frequency)
        ctrlPtForm.frequencyCode = frequencyData.code || ''
        ctrlPtForm.frequencyAddress = frequencyData.address || ''
      }

      if (data.workModel) {
        const workModelData = JSON.parse(data.workModel)
        ctrlPtForm.workModelCode = workModelData.code || ''
        ctrlPtForm.workModelAddress = workModelData.address || ''
      }

      if (data.workError) {
        const workErrorData = JSON.parse(data.workError)
        ctrlPtForm.workErrorCode = workErrorData.code || ''
        ctrlPtForm.workErrorAddress = workErrorData.address || ''
      }

      if (data.onOffSet) {
        const onOffSetData = JSON.parse(data.onOffSet)
        ctrlPtForm.onOffSetCode = onOffSetData.code || ''
        ctrlPtForm.onOffSetAddress = onOffSetData.address || ''
      }

      if (data.workModelSet) {
        const workModelSetData = JSON.parse(data.workModelSet)
        ctrlPtForm.workModelSetCode = workModelSetData.code || ''
        ctrlPtForm.workModelSetAddress = workModelSetData.address || ''
      }

      if (data.frequencySet) {
        const frequencySetData = JSON.parse(data.frequencySet)
        ctrlPtForm.frequencySetCode = frequencySetData.code || ''
        ctrlPtForm.frequencySetAddress = frequencySetData.address || ''
      }

      // 如果有productKey，加载对应的物模型数据
      if (data.productKey) {
        await loadThingModelForProduct(data.productKey)
        const options = await getThingModelData(data.productKey)
        currentThingModelOptions.value = options
      }

      // 设置表单类型为编辑
      ctrlPtFormType.value = 'edit'
    } else {
      // 如果没有数据或data为null，则为新增模式
      ctrlPtFormType.value = 'add'
      resetCtrlPtForm()
      ctrlPtForm.configId = configId
    }
  } catch (error) {
    console.error('获取水泵控制点位配置失败:', error)
    // 如果获取失败，也设置为新增模式
    ctrlPtFormType.value = 'add'
    resetCtrlPtForm()
    ctrlPtForm.configId = configId
  }
}

// 控制点位配置
const CtrlPtConfig = async (row: any) => {
  // 设置配置ID
  const configId = row.id || row.configId || ''

  // 首先加载设备列表，确保 deviceOptions 有数据
  await getDevicedata()

  // 然后加载控制点位数据和物模型数据
  await getWaterPumpCtrlData(configId)

  // 打开表单弹窗
  ctrlPtFormVisible.value = true
}

// 重置表单
const resetCtrlPtForm = () => {
  Object.assign(ctrlPtForm, {
    id: '',
    configId: '',
    deviceKey: '',
    deviceName: '',
    productKey: '',
    ip: '',
    communicationType: 1, // 默认选择ModelbusTCP
    port: '',
    slaveId: '',
    onOffCode: '',
    onOffAddress: '',
    frequencyCode: '',
    frequencyAddress: '',
    workModelCode: '',
    workModelAddress: '',
    workErrorCode: '',
    workErrorAddress: '',
    onOffSetCode: '',
    onOffSetAddress: '',
    workModelSetCode: '',
    workModelSetAddress: '',
    frequencySetCode: '',
    frequencySetAddress: '',
  })
  currentThingModelOptions.value = []
}

// 保存控制点位配置
const handleCtrlPtSave = async () => {
  // 表单验证
  if (!ctrlPtForm.deviceKey) {
    ElMessage.warning('请选择设备')
    return
  }
  if (!ctrlPtForm.ip) {
    ElMessage.warning('请输入IP地址')
    return
  }
  if (!ctrlPtForm.port) {
    ElMessage.warning('请输入端口')
    return
  }

  // 构建保存数据
  const saveData: any = {
    configId: ctrlPtForm.configId,
    productKey: ctrlPtForm.productKey,
    deviceType: 0, // 水泵设备类型为0
    communicationType: ctrlPtForm.communicationType,
    ip: ctrlPtForm.ip,
    port: parseInt(ctrlPtForm.port),
    slaveId: ctrlPtForm.slaveId ? parseInt(ctrlPtForm.slaveId) : null,
    onOff: ctrlPtForm.onOffCode ? JSON.stringify({
      code: ctrlPtForm.onOffCode,
      address: ctrlPtForm.onOffAddress
    }) : null,
    frequency: ctrlPtForm.frequencyCode ? JSON.stringify({
      code: ctrlPtForm.frequencyCode,
      address: ctrlPtForm.frequencyAddress
    }) : null,
    workModel: ctrlPtForm.workModelCode ? JSON.stringify({
      code: ctrlPtForm.workModelCode,
      address: ctrlPtForm.workModelAddress
    }) : null,
    workError: ctrlPtForm.workErrorCode ? JSON.stringify({
      code: ctrlPtForm.workErrorCode,
      address: ctrlPtForm.workErrorAddress
    }) : null,
    onOffSet: ctrlPtForm.onOffSetCode ? JSON.stringify({
      code: ctrlPtForm.onOffSetCode,
      address: ctrlPtForm.onOffSetAddress
    }) : null,
    workModelSet: ctrlPtForm.workModelSetCode ? JSON.stringify({
      code: ctrlPtForm.workModelSetCode,
      address: ctrlPtForm.workModelSetAddress
    }) : null,
    frequencySet: ctrlPtForm.frequencySetCode ? JSON.stringify({
      code: ctrlPtForm.frequencySetCode,
      address: ctrlPtForm.frequencySetAddress
    }) : null,
    projectId: cachedProjects.id,
  }

  // 如果是编辑模式，需要添加id字段
  if (ctrlPtFormType.value === 'edit' && ctrlPtForm.id) {
    saveData.id = ctrlPtForm.id
  }

  try {
    let res
    if (ctrlPtFormType.value === 'add') {
      // 新增时调用新增接口
      res = await addWaterPumpCtrl(saveData)
    } else {

      // 编辑时调用编辑接口
      res = await editWaterPumpCtrl(saveData)
    }
    
    if (res.code === 200) {
      ElMessage.success(ctrlPtFormType.value === 'add' ? '新增成功' : '编辑成功')
      ctrlPtFormVisible.value = false
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 获取物模型列表 - 用于表单下拉框
const getThingModelData = (productKey: string) => {
  return getThingModelList(productKey).then((res) => {
    if (res.code === 200 && res.data) {
      const options = res.data.model.properties.map((item: any) => ({
        label: item.name || item.identifier,
        value: item.identifier, // 使用 identifier 作为 value
      }))
      return options
    }
    return []
  })
}

// 新增控制点位配置
const handleCtrlPtAdd = () => {
  ctrlPtFormType.value = 'add'
  // 新增时不要重置 configId，保留从 CtrlPtConfig 传入的值
  const currentConfigId = ctrlPtForm.configId
  resetCtrlPtForm()
  ctrlPtForm.configId = currentConfigId // 恢复 configId
  ctrlPtFormVisible.value = true
}

// 编辑控制点位配置
const handleCtrlPtEdit = async (row: any, index: number) => {
  ctrlPtFormType.value = 'edit'
  ctrlPtEditIndex.value = index

  // 通过 productKey 找到对应的设备信息
  if (row.productKey) {
    const selectedDevice = deviceOptions.value.find((device) => device.productKey === row.productKey)

    if (selectedDevice) {
      // 设置设备相关信息
      ctrlPtForm.deviceKey = selectedDevice.value // 设备ID
      ctrlPtForm.deviceName = selectedDevice.label // 设备名称
      ctrlPtForm.productKey = selectedDevice.productKey // 产品密钥

      // 获取物模型数据用于下拉框
      const options = await getThingModelData(selectedDevice.productKey)
      currentThingModelOptions.value = options
    } else {
      // 如果没有找到对应设备，清空设备信息但保留其他字段
      ctrlPtForm.deviceKey = ''
      ctrlPtForm.deviceName = ''
      ctrlPtForm.productKey = row.productKey // 保留原始 productKey
      currentThingModelOptions.value = []
      ElMessage.warning('未找到对应的设备信息，请重新选择设备')
    }
  }

  // 设置其他表单字段（这些字段的值就是 identifier）
  ctrlPtForm.configId = row.configId
  ctrlPtForm.onOffCode = row.onOffCode
  ctrlPtForm.onOffAddress = row.onOffAddress
  ctrlPtForm.frequencyCode = row.frequencyCode
  ctrlPtForm.frequencyAddress = row.frequencyAddress
  ctrlPtForm.workModelCode = row.workModelCode
  ctrlPtForm.workModelAddress = row.workModelAddress
  ctrlPtForm.workErrorCode = row.workErrorCode
  ctrlPtForm.workErrorAddress = row.workErrorAddress
  ctrlPtForm.onOffSetCode = row.onOffSetCode
  ctrlPtForm.onOffSetAddress = row.onOffSetAddress
  ctrlPtForm.workModelSetCode = row.workModelSetCode
  ctrlPtForm.workModelSetAddress = row.workModelSetAddress
  ctrlPtForm.frequencySetCode = row.frequencySetCode
  ctrlPtForm.frequencySetAddress = row.frequencySetAddress

  ctrlPtFormVisible.value = true
}


// 在线
import waterPump1 from '@/assets/images/waterPump1.png'
// 离线
import waterPump0 from '@/assets/images/waterPump0.png'
// 故障
import waterPump3 from '@/assets/images/waterPump3.png'

onMounted(async () => {
  // 确保字典数据加载完成
  if (!work_way.value) {
    await proxy?.useDict('work_way')
  }
  // 确保子系统数据加载完后再加载表格数据
  getSubSystemData().then(() => {
    getRegulardata()
  })
  await loadPwdConfig()
})

emitter.on('projectListChanged', (e) => {
  location.reload()
})
// 添加一个计算属性来转换工作方式的值
const getWorkWayLabel = (value: string) => {
  if (!work_way.value) return value
  const match = work_way.value.find((item: any) => item.value === value)
  console.log(match,'match--')
  return match ? match.label : value
}
</script>

<style scoped lang="scss">
.card-list {
  display: flex;
  flex-wrap: wrap;
  margin: -8px;

  .card-item {
    // background: #fff;
    border: 1px solid rgba(33, 148, 255, 0.5);
    border-radius: 3px;
    flex: 0 0 calc(25% - 16px);
    margin: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: box-shadow 0.3s;

    &.success-box {
      /* 在线时可以有轻微高亮背景 */
      // .text-box { background: rgba(238, 250, 255, 0.4); }
    }

    &.error-box {
      /* 离线时用红色底的提示 */
      .text-box {
        //background: rgba(255, 241, 241, 0.4);
      }
    }

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .text-box {
      padding: 16px;

      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;

        .title-l {
          display: flex;
          align-items: center;

          .icon {
            margin-right: 8px;
            display: flex;
            align-items: center;
          }
        }
      }

      .text {
        display: flex;
        align-items: flex-start;
        font-size: 14px;

        .txt {
          flex: 1;

          .txt-item {
            display: flex;
            margin-bottom: 10px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              width: 80px;
              color: #717c8e;
            }

            .value {
              color: #0070ff;

              &.active {
                color: #0070ff;
              }
            }
          }
        }

        .img {
          width: 100px;
          height: 100px;
          margin-left: 16px;
          text-align: right;

          img {
            text-align: right;
            object-fit: contain;
          }
        }
      }
    }

    .btn-group {
      padding: 12px 16px;
      border-top: 1px solid rgba(33, 148, 255, 0.5);
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .cu-btn {
        width: calc((100% - 48px) / 4);
        margin-left: 8px;
      }

      .el-button {
        padding: 8px;
      }

      /* 如果加了分隔线 */
      ::v-deep(.el-divider--vertical) {
        margin: 0 8px;
      }
    }
  }
}

/* 响应式：三列 */
@media screen and (max-width: 1400px) {
  .card-list .card-item {
    flex: 0 0 calc(33.333% - 16px);
  }
}

/* 两列 */
@media screen and (max-width: 1024px) {
  .card-list .card-item {
    flex: 0 0 calc(50% - 16px);
  }
}

/* 一列 */
@media screen and (max-width: 600px) {
  .card-list .card-item {
    flex: 0 0 calc(100% - 16px);
  }
}

:deep(.el-select__wrapper) {
  width: 180px;
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}

:deep(.el-select__placeholder) {
  color: #fff;
}
:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

:deep(.el-table__cell) {
  // color: rgba(204, 204, 204, 1) !important;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}
:deep(.el-pagination) {
  // 页码按钮的间距
  .el-pager li {
    margin: 0 4px;  // 调整页码之间的间距
  }
  // 上一页下一页按钮的间距
  .btn-prev {
    margin-right: 4px;  // 上一页按钮右侧间距
  }
  .btn-next {
    margin-left: 4px;   // 下一页按钮左侧间距
  }
}
</style>
