<template>
  <div class="historical">
    <EmptyDataPage :imageSrc="emptyImagePath" v-if="tabs.tabsdata.length==0" />
    <div v-else>
      <div class="top">
        <el-select
          class="customSelect"
          v-if="crewOptions.length >= 2"
          v-model="selectCrewVal"
          @change="crewSelectChange"
          placeholder="机组切换"
          size="large"
          style="width: 170px"
        >
          <el-option v-for="item in crewOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select
          class="customSelect"
          v-model="selectVal"
          @change="handleSelectChange"
          placeholder="日期快捷键"
          size="large"
          style="width: 140px;margin: 0 10px 0 10px;"
        >
          <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="block">
          <el-date-picker
            v-model="pickerVal"
            type="datetimerange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            date-format="YYYY/MM/DD ddd"
            time-format="A hh:mm:ss"
            :disabled-date="disabledDate"
            @change="selectpicker"
            class="customdatapicker"
            size="large"
          />
        </div>
      </div>
      <div v-if="tabs.tabsdata.length > 0" class="contet">
        <el-tabs type="border-card" v-model="activeTab">
          <el-tab-pane v-for="tab in tabs.tabsdata" :key="tab.name" :label="`${tab.label} (${tab.unit})`" :name="tab.name"> </el-tab-pane>
        </el-tabs>
        <div style="height: 100%; width: 100%;">
          <PublicCharts :chartOptions="chartOptions" :data="tabsEData" />
          <PublicCharts :chartOptions="steamChartOptions" :data="steamData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts/core'
import emptyImagePath from '@/assets/images/noData.png'
import { TooltipComponent, LegendComponent, TitleComponent, ToolboxComponent, GridComponent } from 'echarts/components'
import { PieChart, LineChart, GaugeChart } from 'echarts/charts'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import PublicCharts from '@/components/publicCharts/index.vue'
import { ref, onMounted, watch, nextTick } from 'vue'
import { hisCalCurve, echatsData } from './index'
import { formatDate } from '@/utils/formatTime'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
import { ElMessageBox } from 'element-plus'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const selectVal = ref('')
const selectCrewVal = ref('')
const pickerVal = ref<[Date, Date]>([new Date(new Date().setDate(new Date().getDate() - 7)), new Date()])

echarts.use([
  TooltipComponent,
  LegendComponent,
  PieChart,
  CanvasRenderer,
  LabelLayout,
  TitleComponent,
  ToolboxComponent,
  GridComponent,
  LineChart,
  UniversalTransition,
  GaugeChart,
])

const selectOptions = [
  { value: 'option1', label: '今天' },
  { value: 'option2', label: '昨天' },
  { value: 'option3', label: '上周' },
  { value: 'option4', label: '本月' },
  { value: 'option5', label: '上月' },
  { value: 'option6', label: '一年' },
]

const crewOptions = ref([])
const tabs = ref({
  tabsdata: [],
  referdata: [],
})

const activeTab = ref('')
const steamData = ref([]) // 新增变量用于蒸汽图表的数据
const tabsEData = ref([]) // 新增变量用于tbas切换图表的数据

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

emitter.on('projectListChanged', (e) => {
  location.reload()
})

const crewSelectChange = (value: string) => {
  gettabsData(value)
}

const selectpicker = (value: string[]) => {
  const start = formatDate(value[0])
  const end = formatDate(value[1])

  // 将日期字符串转为 Date 对象
  const startDate = new Date(start)
  const endDate = new Date(end)

  // 计算两个日期的差值（以毫秒为单位）
  const timeDiff = endDate.getTime() - startDate.getTime()

  // 将差值转换为天数
  const dayDiff = timeDiff / (1000 * 3600 * 24)

  // 检查日期差是否超过 365 天
  if (dayDiff > 365) {
    ElMessageBox.alert('开始时间到结束时间不能大于 365 天', '警告', {
      confirmButtonText: '确定',
      type: 'warning',
      showClose: false, // 禁止用户手动关闭
    }).then(() => {
      // 这里可以处理点击确定按钮后的逻辑
    })
    // 2秒后自动关闭弹窗
    setTimeout(() => {
      ElMessageBox.close()
    }, 3000)
    return
  }

  pickerVal.value = [start, end]
  getechartsdata()
}

// 这个是查询是那个项目的下面某个机组的
const getselectCrewVal = () => {
  const parms = {
    projectId: cachedProjects.id,
    configType: 3,
  }
  hisCalCurve(parms).then((res) => {

    if (res.data.length > 0) {
      crewOptions.value = res.data.map((item) => {
        return {
          value: item.id,
          label: item.name,
        }
      })
      if (crewOptions.value.length > 0) {
        selectCrewVal.value = crewOptions.value[0].value

        gettabsData(crewOptions.value[0].value)
      }
    } else {
      tabs.value = { tabsdata: [], referdata: [] }
    }
  })
}

// 这个是查询的所有切换的数据
const gettabsData = (value: string) => {
  const parms = {
    id: value,
    projectId: cachedProjects.id,
    configType: 3,
  }
  hisCalCurve(parms).then((res) => {
    if (res.data.length > 0) {
      tabs.value = {
        tabsdata: res.data
          .map((crew) =>
            crew.models.map((model) => ({
              name: model.item.identifier,
              label: model.item.name,
              deviceId: model.item.deviceId,
              unit: model.item.unit,
            }))
          )
          .flat(),
        referdata: res.data
          .map((crew) =>
            crew.models.map((model) => ({
              name: model.refer.identifier,
              label: model.refer.name,
              deviceId: model.refer.deviceId,
              unit: model.refer.unit,
            }))
          )
          .flat(),
      }
      if (tabs.value.tabsdata.length > 0) {
        activeTab.value = tabs.value.tabsdata[0].name
      }
    } else {
      tabs.value = { tabsdata: [], referdata: [] }
    }
  })
}

const handleSelectChange = (value: string) => {
  const now = new Date()
  let start: Date, end: Date

  switch (value) {
    case 'option1':
      start = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0)
      end = now
      break
    case 'option2':
      start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 0, 0, 0)
      end = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59)
      break
    case 'option3':
      start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay() - 6, 0, 0, 0)
      end = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay(), 23, 59, 59)
      break
    case 'option4':
      start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0)
      end = now
      break
    case 'option5':
      start = new Date(now.getFullYear(), now.getMonth() - 1, 1, 0, 0, 0)
      end = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59)
      break
    case 'option6':
      start = new Date(now)
      start.setFullYear(now.getFullYear() - 1)
      end = now
      break
    default:
      start = now
      end = now
      break
  }
  pickerVal.value = [start, end]
  getechartsdata()
}

const isLoading = ref(false)

const handleTabClick = async (tab, event) => {
  if (isLoading.value) {
    // 如果当前正在加载，直接返回，避免重复加载
    return
  }
  isLoading.value = true
  await getechartsdata()
  isLoading.value = false
}

const getechartsdata = async () => {
  const [startDate, endDate] = pickerVal.value
  if (!startDate || !endDate) {
    return
  }
  const currentTab = activeTab.value
  const matchedTab = tabs.value.tabsdata.find((tab) => tab.name === currentTab)

  // 获取refData数据
  const matchedIndex = tabs.value.tabsdata.findIndex((tab) => tab.name === matchedTab.name)
  const refData = tabs.value.referdata[matchedIndex] || {}
  // const refData = tabs.value.referdata[0]
  if (!matchedTab) {
    return
  }

  const start = formatDate(startDate)
  const end = formatDate(endDate)

  isLoading.value = true
  // 使用 matchedTab 调用 echatsData 更新 chartOptions
  const params1 = {
    startTime: start,
    endTime: end,
    deviceId: matchedTab.deviceId,
    identifier: matchedTab.name,
    displayStats: true,
  }

  // 使用 refData 调用 echatsData 更新 steamChartOptions
  const params2 = {
    startTime: start,
    endTime: end,
    deviceId: refData.deviceId,
    identifier: refData.name,
    displayStats: true,
  }


  try {
    const [res1, res2] = await Promise.all([echatsData(params1), echatsData(params2)])

    if (res1.code === 200 && res1.data && res1.data.his) {
      const times = res1.data.his.map((item) => item.time)
      const values = res1.data.his.map((item) => item.value)
      const avg = res1.data.avg !== null ? res1.data.avg : ''
      const max = res1.data.max !== null ? res1.data.max : ''
      const min = res1.data.min !== null ? res1.data.min : ''
      chartOptions.value = {
        ...chartOptions.value,
        title: {
          ...chartOptions.value.title,
          text: `平均值: ${avg}, 最大值: ${max}, 最小值: ${min}`, // 直接在标题中显示统计信息
        },
        legend: {
          data: [matchedTab.label], // 图例显示标签及单位
          textStyle: {
            fontSize: 15,
            color: 'rgba(204, 204, 204, 1)',
            fontWeight: 500,
          },
        },
        xAxis: {
          ...chartOptions.value.xAxis,
          type: 'category',
          boundaryGap: false,
          data: times, // 确保这个数组是排序好的时间戳或时间字符串
          axisLabel: {
            // if (condition) {

            // }
            formatter: (value) => {
              const date = new Date(value)
              const month = (date.getMonth() + 1).toString().padStart(2, '0')
              const day = date.getDate().toString().padStart(2, '0')
              const hours = date.getHours().toString().padStart(2, '0')
              const minutes = date.getMinutes().toString().padStart(2, '0')
              const seconds = date.getSeconds().toString().padStart(2, '0')
              // return `${month}-${day} ${hours}:${minutes}:${seconds}`
              return `${month}-${day} ${hours}:${minutes}`
            },
          },
        },
        toolbox: {
          feature: {
            dataZoom: {
              yAxisIndex: 'none',
            },
            // restore: {},
            saveAsImage: {},
          },
        },
        series: [
          {
            ...chartOptions.value.series[0],
            name: matchedTab.label, // 将 series 的 name 设置为 matchedTab.label
            data: values,
          },
        ],
      }
      tabsEData.value = values
    }

    if (res2.code === 200 && res2.data && res2.data.his) {
      const times = res2.data.his.map((item) => item.time)
      const values = res2.data.his.map((item) => item.value)
      const avg = res2.data.avg !== null ? res2.data.avg : ''
      const max = res2.data.max !== null ? res2.data.max : ''
      const min = res2.data.min !== null ? res2.data.min : ''

      steamChartOptions.value = {
        ...steamChartOptions.value,
        title: {
          ...steamChartOptions.value.title,
          // text: `${refData.label} - 平均值: ${avg}, 最大值: ${max}, 最小值: ${min}`, // 直接在标题中显示统计信息
          text: `平均值: ${avg}, 最大值: ${max}, 最小值: ${min}`, // 直接在标题中显示统计信息
        },
        legend: {
          data: [refData.label], // 图例显示标签及单位
          textStyle: {
            fontSize: 15,
            color: 'rgba(204, 204, 204, 1)',
            fontWeight: 500,
          },
        },
        xAxis: {
          ...steamChartOptions.value.xAxis,
          data: times,
          axisLabel: {
            formatter: (value) => {
              const date = new Date(value)
              const month = (date.getMonth() + 1).toString().padStart(2, '0')
              const day = date.getDate().toString().padStart(2, '0')
              const hours = date.getHours().toString().padStart(2, '0')
              const minutes = date.getMinutes().toString().padStart(2, '0')
              const seconds = date.getSeconds().toString().padStart(2, '0')
              // return `${month}-${day} ${hours}:${minutes}:${seconds}`
              return `${month}-${day} ${hours}:${minutes}`
            },
          },
        },
        series: [
          {
            ...steamChartOptions.value.series[0],
            name: refData.label, // 将 series 的 name 设置为 refData.label
            data: values,
          },
        ],
      }
      steamData.value = values
    }
  } catch (error) {
    console.error('Error fetching data:', error)
  }finally {
    isLoading.value = false // 请求完成后无论成功还是失败都设置为 false
  }
}

const chartOptions = ref({
  tooltip: {
    trigger: 'axis',
    position: function (pt: any) {
      return [pt[0], '10%']
    },
    backgroundColor: '#142433',
    textStyle: {
      color: '#fff', // 修改字体颜色
      fontSize: 14, // 可以同时修改字体大小等其他属性
    },
  },
  legend: {
    data: [],
    textStyle: {
      fontSize: 14,
      color: 'rgba(204, 204, 204, 1)',
      fontWeight: 400,
    },
  },
  title: {
    text: '暂无数据',
    left: '20px', // 水平居右对齐
    textStyle: {
      fontSize: 14, // 设置字体大小
      color: '#fff', // 设置字体颜色
      fontWeight: 'normal', // 设置字体粗细
      lineHeight: 40, // 可选：设置行高，用于文本旋转后调整间距
    },
  },
  toolbox: {
    feature: {
      dataZoom: {
        yAxisIndex: 'none',
      },
      // restore: {},
      saveAsImage: {},
      // dataView: { show: true, readOnly: false },
      // magicType: { show: true, type: ['line', 'bar'] },
    },
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [],
    axisLine: {
      lineStyle: {
        color: 'rgba(204, 204, 204, 1)', // 修改 x 轴轴线的颜色
        width: 1,
      },
    },
    axisLabel: {
      lineStyle: {
        color: 'rgba(62, 65, 77, 1)', // 修改 x 轴刻度线的颜色
      },
    },
  },
  yAxis: {
    type: 'value',
    boundaryGap: [0, '100%'],
    // splitLine: {
    //   lineStyle: {
    //     color: 'rgba(62, 65, 77, 1)', // 修改与 x 轴平行的网格线颜色
    //   },
    // },
    splitLine: {
        show: true,
        lineStyle: { type: 'dashed', color: '#c6c7c8' },
      },
    axisLabel: {
      color: 'rgba(204, 204, 204, 1)',
      fontSize: 14,
    },
  },
  dataZoom: [
    {
      type: 'inside',
      start: 0,
      end: 100,
      dataBackgroundColor: '#6882b8',
    },
    {
      start: 0,
      end: 100,
    },
  ],
  series: [
    {
      name: '暂无数据--',
      type: 'line',
      data: [],
      connectNulls: false, // 保留 null 以显示断点
      symbol: 'circle', // 可以改为 'circle' 或其他符号来突出显示数据点
      symbolSize: 8, // 增大符号的尺寸提高可见性
      sampling: 'lttb',
      lineStyle: {
        width: 2, // 增加线宽度
      },
      itemStyle: {
        color: '#2194ff',
      },
      // background: linear-gradient(180deg, rgba(33, 148, 255, 0.4) 0%, rgba(33, 148, 255, 0.04) 100%);
      // background: linear-gradient(180deg, rgb(33, 148, 255) 0%, rgb(33, 148, 255) 100%);

      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(33, 148, 255, 0.4)' }, // 对应渐变的起点颜色
          { offset: 1, color: 'rgba(33, 148, 255, 0.04)' }, // 对应渐变的终点颜色
        ]),
      },
    },
  ],
})

const steamChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    position: function (pt: any) {
      return [pt[0], '10%']
    },
    backgroundColor: '#142433',
    textStyle: {
      color: '#fff', // 修改字体颜色
      fontSize: 14, // 可以同时修改字体大小等其他属性
    },
  },
  legend: {
    data: [],
    textStyle: {
      fontSize: 14,
      color: 'rgba(204, 204, 204, 1)',
      fontWeight: 400,
    },
  },
  title: {
    text: '暂无数据--',
    left: '20px', // 水平居右对齐
    textStyle: {
      fontSize: 14, // 设置字体大小
      color: '#fff', // 设置字体颜色
      fontWeight: 'normal', // 设置字体粗细
      lineHeight: 40, // 可选：设置行高，用于文本旋转后调整间距
    },
  },
  toolbox: {
    feature: {
      dataZoom: {
        yAxisIndex: 'none',
      },
      // restore: {},
      saveAsImage: {},
    },
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [],
    axisLine: {
      lineStyle: {
        color: 'rgba(204, 204, 204, 1)', // 修改 x 轴轴线的颜色
        width: 1,
      },
    },
    axisLabel: {
      lineStyle: {
        color: 'rgba(62, 65, 77, 1)', // 修改 x 轴刻度线的颜色
      },
    },
  },
  yAxis: {
    type: 'value',
    boundaryGap: [0, '100%'],
    splitLine: {
        show: true,
        lineStyle: { type: 'dashed', color: '#c6c7c8' },
      },
    axisLabel: {
      color: 'rgba(204, 204, 204, 1)',
      fontSize: 14,
    },
  },
  dataZoom: [
    {
      type: 'inside',
      start: 0,
      end: 100,
    },
    {
      start: 0,
      end: 100,
    },
  ],
  series: [
    {
      name: '暂无数据--',
      type: 'line',
      data: [],
      connectNulls: false, // 保留 null 以显示断点
      symbol: 'circle', // 可以改为 'circle' 或其他符号来突出显示数据点
      symbolSize: 8, // 增大符号的尺寸提高可见性
      sampling: 'lttb',
      lineStyle: {
        width: 2, // 增加线宽度
      },
      itemStyle: {
        color: '#2194ff',
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(33, 148, 255, 0.4)' }, // 对应渐变的起点颜色
          { offset: 1, color: 'rgba(33, 148, 255, 0.04)' }, // 对应渐变的终点颜色
        ]),
      },
    },
  ],
})

onMounted(() => {
  getselectCrewVal()
})

watch(activeTab, async (newVal) => {
  await nextTick()
  getechartsdata()
})
</script>
<style lang="scss">
@import '@/assets/styles/datapicker.scss'; /* 全局样式 */
</style>
<style scoped>
:deep(.el-select__wrapper){
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:none !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
/* 下拉框 */
.customSelect :deep(.el-input__wrapper) {
  border: 1px solid #034374 !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  background: rgba(3, 43, 82, 1) !important;
  color: #ffffff !important;
}
.customSelect :deep(.el-input__inner) {
  font-size: 14px;
  font-weight: 400;
  color: #ffffff !important;
}
/* --------------- */
.el-input__inner,
.el-input__inner:hover,
.el-input__inner:after {
  background: #032b52 !important;
  border: 1px solid #01e6f4 !important;
  color: #fff;
}
.el-select-dropdown__item {
  background-color: #032b52;
  color: #bab6b6;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  /* color: #fff; */
}

.el-popper[x-placement^='bottom'] .popper__arrow,
.el-popper[x-placement^='bottom'] .popper__arrow::after {
  border-bottom-color: #01e6f4;
}
:deep(.el-popper.is-light) {
  background: transparent;
}
.el-select-dropdown .el-select-dropdown__item:hover,
.el-select-dropdown .el-select-dropdown__item.selected {
  color: #fff;
}
/* .el-select-dropdown .el-select-dropdown__item.hover {
  background: red;
} */
/* --------- */
.historical {
  /* background: #fff; */
  /* box-shadow: inset 0px 2px 28px #2194ff; */
  height: calc(100% +50px);
}
.chart {
  width: 100%;
  height: 400px;
}
.contet {
  padding: 0 20px 0 20px;
}
.top {
  padding: 20px;
  display: flex;
}

/* tbas */

:deep(.el-tabs__item:hover) {
  color: #fff; /* 悬停时的文字颜色 -------------*/
}
:deep(.el-tabs__item) {
  /* opacity: 0; */
  /* background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important; */
}
:deep(.el-tabs--border-card) {
  background: rgba(2, 28, 51, 0.5);
  /* padding-bottom: 140px; */
}
:deep(.el-tabs--border-card > .el-tabs__header) {
  background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important;
  border-bottom: 1px solid rgba(33, 148, 255, 1);
}
:deep(.el-tabs--border-card) {
  border: none;
}
:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {
  border-radius: 2px;
  background: linear-gradient(180deg, rgb(6, 101, 243) 0%, rgba(119, 122, 126, 0.1) 100%);
  opacity: 0.8;
  color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(0, 0, 0, 1);
}
</style>
