<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form ref="queryFormRef" :inline="true">
          <el-form-item label="告警等级">
            <el-select v-model="state.query.level" placeholder="请选择" clearable>
              <el-option v-for="item in alert_level" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>
    <el-card shadow="never">
      <el-table v-loading="state.loading" :data="alertlist">
        <el-table-column label="序号" type="index" align="center" width="80" />
        <el-table-column prop="name" label="名称" align="center"></el-table-column>
        <el-table-column prop="productName" label="所属网关" align="center" width="80"></el-table-column>
        <el-table-column label="告警等级" align="center" width="80">
          <!-- <template #default="{ row }">
            {{ pump_work_way.find(item => item.value === String(row.workWay))?.label || "" }}
          </template> -->
          <template #default="{ row }">
            <el-tag v-if="row.level == 1" type="warning">低限预警</el-tag>
            <el-tag v-if="row.level == 2" type="warning">低限报警</el-tag>
            <el-tag v-if="row.level == 3" effect="dark" type="danger">低限严重警告</el-tag>
            <el-tag v-if="row.level == 4" type="warning">高限预警</el-tag>
            <el-tag v-if="row.level == 5" type="danger">高限报警</el-tag>
            <el-tag v-if="row.level == 6" effect="dark" type="danger">高限严重警告</el-tag>
            <el-tag v-if="row.level == -1" type="primary">正常值</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dealFlag" label="状态" align="center" width="80">
          <template #default="{ row }">
            <el-tag v-if="row.dealFlag == 0" type="danger">未处理</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastOccurTime" label="最近告警时间" align="center"></el-table-column>
        <el-table-column prop="reason" label="告警原因" align="center"></el-table-column>
      </el-table>
      <pagination
        v-if="state.total > 0"
        v-model:page="state.page.pageNum"
        v-model:limit="state.page.pageSize"
        :total="state.total"
        layout="total, prev, pager, next, jumper"
        @pagination="getpagination"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getAlarmCountPopup } from '../index.api'
import { ComponentInternalInstance } from 'vue'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { alert_level } = toRefs<any>(proxy?.useDict('alert_level'))

const alertlist = ref([])
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const { id: projectId } = cachedProjects
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})
const showSearch = ref(true)
emitter.on('projectListChanged', (e) => {
  location.reload()
})
const handleQuery = () => {
  getList()
}
const getpagination = (pagination: { page: number; limit: number }) => {
  state.page.pageNum = pagination.page
  state.page.pageSize = pagination.limit
  getList()
}
const getList = () => {
  const params = {
    projectId,
    dealFlag: 0,
    ...state.page,
    ...state.query,

  }
  getAlarmCountPopup(params).then((res) => {
    if (res.data.rows.length > 0) {
      alertlist.value = res.data.rows
      state.total = res.data.total
      state.loading = false
    } else {
      alertlist.value = []
    }
  })
}
const resetQuery = () => {
  state.query = {}
  getList()
}
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  border: none;
}

:deep(.el-card__body) {
  border: none;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

:deep(.el-table__cell) {
  // color: rgba(204, 204, 204, 1) !important;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}

:deep(.el-tree) {
  background-color: transparent;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #07355c;
}

:deep(.el-tree-node__expand-icon) {
  color: #fff;
}

:deep(.el-tree-node__label) {
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}

:deep(.el-select__tags .el-tag--info) {
  background-color: #153059 !important;
}

:deep(.el-tag.el-tag--info) {
  color: #fff !important;
}
:deep(.el-table__header thead tr th){
  background: #1F5499 !important;
}
:deep(.el-table tr){
  border: 1px solid #00AAFF !important;
}
:deep(.el-card){
  border: 2px solid #00AAFF !important;
}
</style>
