<template>
  <div 
    class="hyperbolic-cooling-tower"
    :style="componentStyle"
  >
    <!-- 双曲线冷却塔主体 -->
    <svg
      :width="component.width"
      :height="component.height"
      viewBox="0 0 120 160"
      class="tower-svg"
      preserveAspectRatio="xMidYMid meet"
    >
      <!-- 塔体渐变定义 -->
      <defs>
        <linearGradient id="towerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" :style="`stop-color:${towerColor};stop-opacity:0.8`" />
          <stop offset="50%" :style="`stop-color:${towerColor};stop-opacity:1`" />
          <stop offset="100%" :style="`stop-color:${towerColor};stop-opacity:0.8`" />
        </linearGradient>
        
        <!-- 水蒸气渐变 -->
        <radialGradient id="steamGradient" cx="50%" cy="50%" r="50%">
          <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8" />
          <stop offset="70%" style="stop-color:#e6f3ff;stop-opacity:0.4" />
          <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
        </radialGradient>
      </defs>
      
      <!-- 底部支撑结构（钢架桁架） -->
      <g class="support-structure">
        <!-- 基础平台 -->
        <rect x="20" y="140" width="80" height="8" :fill="baseColor" stroke="#666" stroke-width="1"/>

        <!-- 三角形桁架支撑 -->
        <g class="truss-supports">
          <polygon v-for="i in 8" :key="i"
            :points="`${25 + i * 12},140 ${30 + i * 12},140 ${27.5 + i * 12},130`"
            fill="#4a4a4a" stroke="#333" stroke-width="0.5"/>
          <line v-for="i in 7" :key="i"
            :x1="30 + i * 12" :y1="140"
            :x2="37 + i * 12" :y2="140"
            stroke="#4a4a4a" stroke-width="2"/>
        </g>
      </g>
      
      <!-- 双曲线塔体 -->
      <path
        :d="towerPath"
        fill="url(#towerGradient)"
        stroke="#666"
        stroke-width="2"
      />

      <!-- 混凝土分层纹理 -->
      <g class="concrete-layers" opacity="0.3">
        <line v-for="i in 22" :key="i"
          :x1="getLayerStartX(i)" :y1="30 + i * 5"
          :x2="getLayerEndX(i)" :y2="30 + i * 5"
          stroke="#999" stroke-width="0.5"/>
      </g>
      
      <!-- 塔顶开口 -->
      <ellipse cx="60" cy="25" rx="17.5" ry="6" :fill="baseColor" stroke="#666" stroke-width="1"/>
      
      <!-- 水蒸气效果 -->
      <g v-if="isRunning" class="steam-effect">
        <ellipse 
          v-for="(steam, index) in steamClouds" 
          :key="index"
          :cx="steam.x" 
          :cy="steam.y" 
          :rx="steam.rx" 
          :ry="steam.ry" 
          fill="url(#steamGradient)"
          :opacity="steam.opacity"
          class="steam-cloud"
          :style="`animation-delay: ${steam.delay}s`"
        />
      </g>
      
      <!-- 进水管道 -->
      <rect x="5" y="100" width="15" height="8" :fill="pipeColor" stroke="#666" stroke-width="1"/>
      <text x="2" y="98" font-size="8" fill="#666">进水</text>
      
      <!-- 出水管道 -->
      <rect x="100" y="120" width="15" height="8" :fill="pipeColor" stroke="#666" stroke-width="1"/>
      <text x="102" y="118" font-size="8" fill="#666">出水</text>
      
      <!-- 状态指示器 -->
      <circle 
        cx="105" 
        cy="35" 
        r="4" 
        :fill="statusColor" 
        stroke="#fff" 
        stroke-width="1"
        class="status-indicator"
      />
      
      <!-- 温度显示 -->
      <text x="60" y="80" text-anchor="middle" font-size="12" :fill="textColor" class="temperature-text">
        {{ displayTemperature }}°C
      </text>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
}>()

// 动画状态
const animationFrame = ref(0)
let animationId: number | null = null

// 组件样式
const componentStyle = computed(() => ({
  width: `${props.component.width}px`,
  height: `${props.component.height}px`,
  transform: `rotate(${props.component.rotation || 0}deg)`,
  opacity: props.component.opacity || 1
}))

// 获取数据值
const getValue = (key: string, defaultValue: any = 0) => {
  const data = props.component.data
  if (data?.dynamic?.[key] !== undefined) {
    return data.dynamic[key]
  }
  return data?.static?.[key] ?? defaultValue
}

// 运行状态
const isRunning = computed(() => getValue('isRunning', true))
const temperature = computed(() => getValue('temperature', 35))
const efficiency = computed(() => getValue('efficiency', 85))

// 显示温度
const displayTemperature = computed(() => {
  return Math.round(temperature.value * 10) / 10
})

// 颜色配置 - 更接近真实混凝土颜色
const towerColor = computed(() => getValue('towerColor', '#d4d0c8'))
const baseColor = computed(() => getValue('baseColor', '#a0a0a0'))
const pipeColor = computed(() => getValue('pipeColor', '#4a90e2'))
const textColor = computed(() => getValue('textColor', '#333'))

// 状态颜色
const statusColor = computed(() => {
  if (!isRunning.value) return '#ff4444'
  if (efficiency.value > 80) return '#44ff44'
  if (efficiency.value > 60) return '#ffaa44'
  return '#ff4444'
})

// 双曲线路径 - 真实的双曲线形状
const towerPath = computed(() => {
  // 双曲线冷却塔：底部最大，中间最细，顶部中等
  // 底部宽度: 50px, 中间最细: 20px, 顶部: 35px
  return `
    M 35 140
    Q 25 130 28 120
    Q 32 110 38 100
    Q 44 90 48 80
    Q 50 70 52 60
    Q 53 50 54 40
    Q 55 35 57.5 30
    L 62.5 30
    Q 65 35 66 40
    Q 67 50 68 60
    Q 70 70 72 80
    Q 76 90 82 100
    Q 88 110 92 120
    Q 95 130 85 140
    Z
  `
})

// 水蒸气云朵 - 从较小的塔顶开口冒出
const steamClouds = computed(() => {
  if (!isRunning.value) return []

  return [
    { x: 55, y: 18, rx: 6, ry: 3, opacity: 0.7, delay: 0 },
    { x: 62, y: 15, rx: 9, ry: 5, opacity: 0.5, delay: 0.4 },
    { x: 58, y: 10, rx: 12, ry: 6, opacity: 0.4, delay: 0.8 },
    { x: 65, y: 12, rx: 8, ry: 4, opacity: 0.6, delay: 1.2 },
    { x: 52, y: 8, rx: 10, ry: 5, opacity: 0.3, delay: 1.6 },
    { x: 60, y: 5, rx: 14, ry: 7, opacity: 0.2, delay: 2.0 }
  ]
})

// 计算分层纹理的起始和结束位置
const getLayerStartX = (layerIndex) => {
  const y = 30 + layerIndex * 5
  // 根据双曲线形状计算对应高度的宽度
  if (y >= 140) return 35 // 底部
  if (y >= 120) return 35 - (140 - y) * 0.5
  if (y >= 100) return 28 + (120 - y) * 0.35
  if (y >= 80) return 38 - (100 - y) * 0.5
  if (y >= 60) return 48 - (80 - y) * 0.5
  if (y >= 40) return 52 - (60 - y) * 0.2
  if (y >= 30) return 54 - (40 - y) * 0.2
  return 57.5
}

const getLayerEndX = (layerIndex) => {
  const y = 30 + layerIndex * 5
  // 根据双曲线形状计算对应高度的宽度
  if (y >= 140) return 85 // 底部
  if (y >= 120) return 85 + (140 - y) * 0.5
  if (y >= 100) return 92 - (120 - y) * 0.35
  if (y >= 80) return 82 + (100 - y) * 0.5
  if (y >= 60) return 72 + (80 - y) * 0.5
  if (y >= 40) return 68 + (60 - y) * 0.2
  if (y >= 30) return 66 + (40 - y) * 0.2
  return 62.5
}

// 动画循环
const animate = () => {
  animationFrame.value += 1
  animationId = requestAnimationFrame(animate)
}

onMounted(() => {
  if (isRunning.value) {
    animate()
  }
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped>
.hyperbolic-cooling-tower {
  position: relative;
  display: block;
  user-select: none;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.tower-svg {
  filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.2));
  width: 100%;
  height: 100%;
  display: block;
  /* 确保SVG能够正确缩放 */
  max-width: 100%;
  max-height: 100%;
}

.steam-cloud {
  animation: steamRise 3s ease-in-out infinite;
}

@keyframes steamRise {
  0% {
    transform: translateY(0) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) scale(1);
    opacity: 0.6;
  }
  100% {
    transform: translateY(-20px) scale(1.2);
    opacity: 0;
  }
}

.status-indicator {
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.temperature-text {
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
}

.steam-effect {
  pointer-events: none;
}
</style>
