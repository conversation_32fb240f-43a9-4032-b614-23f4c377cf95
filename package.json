{"name": "ruoyi-vue-plus", "version": "5.0.0-SNAPSHOT", "description": "RuoYi-Vue-Plus多租户管理系统", "author": "LionLi", "license": "MIT", "scripts": {"dev": "vite serve --mode development", "exh": "vite serve --mode exh", "bizdev": "vite serve --mode bizdev", "bizexh": "vite serve --mode bizexh", "serve": "vite serve --mode development", "start": "vite serve --mode development", "build:prod": "vite build --mode production", "build:exh": "vite build --mode exh &&vue-tsc --noEmit", "build:bizdev": "vite build --mode bizdev", "build:bizexh": "vite build --mode bizexh", "preview": "vite preview", "lint": "eslint src/**/*.{ts,js,vue} --fix", "prepare": "husky install", "prettier": "prettier --write ."}, "repository": {"type": "git", "url": "https://gitee.com/JavaLionLi/plus-ui.git"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@codemirror/lang-html": "^6.4.3", "@codemirror/lang-javascript": "^6.1.8", "@codemirror/lang-json": "^6.0.1", "@codemirror/theme-one-dark": "^6.1.2", "@element-plus/icons-vue": "2.1.0", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/vue3": "^6.1.17", "@types/three": "^0.177.0", "@vueup/vue-quill": "1.1.0", "@vueuse/core": "9.5.0", "animate.css": "4.1.1", "await-to-js": "^3.0.0", "axios": "^1.3.4", "bcryptjs": "^2.4.3", "Buffer": "^0.0.0", "codemirror": "^6.0.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "echarts": "5.4.1", "element-plus": "^2.8.7", "file-saver": "^2.0.5", "fuse.js": "6.6.2", "gsap": "^3.13.0", "js-cookie": "3.0.1", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "mqtt": "^4.3.7", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "6.2.0", "pinia": "2.0.22", "screenfull": "6.0.0", "swiper": "^11.1.14", "three": "^0.177.0", "three-stdlib": "^2.36.0", "vue": "3.2.45", "vue-codemirror": "^6.1.1", "vue-cropper": "1.0.3", "vue-echarts": "^6.6.0", "vue-i18n": "9.2.2", "vue-router": "4.1.4", "vue-types": "^5.0.3", "vue3-ts-jsoneditor": "^2.9.0", "wangeditor": "^4.7.15", "web-storage-cache": "^1.1.1", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify/json": "^2.2.40", "@intlify/unplugin-vue-i18n": "0.8.2", "@types/file-saver": "2.0.5", "@types/js-cookie": "3.0.3", "@types/lodash-es": "^4.17.12", "@types/node": "18.14.2", "@types/nprogress": "0.2.0", "@types/path-browserify": "^1.0.0", "@typescript-eslint/eslint-plugin": "5.56.0", "@typescript-eslint/parser": "5.56.0", "@unocss/preset-attributify": "^0.50.6", "@unocss/preset-icons": "^0.50.6", "@unocss/preset-uno": "^0.50.6", "@vitejs/plugin-vue": "4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/compiler-sfc": "3.2.45", "autoprefixer": "10.4.14", "eslint": "8.36.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "9.9.0", "fast-glob": "^3.2.11", "husky": "7.0.4", "postcss": "^8.4.21", "prettier": "2.8.6", "sass": "1.56.1", "typescript": "4.9.5", "unocss": "^0.50.8", "unplugin-auto-import": "0.13.0", "unplugin-icons": "0.15.1", "unplugin-vue-components": "0.23.0", "vite": "4.3.1", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vitest": "^0.29.7", "vue-eslint-parser": "9.1.0", "vue-tsc": "0.35.0"}}