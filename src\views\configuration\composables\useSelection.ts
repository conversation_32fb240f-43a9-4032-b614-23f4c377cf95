import { ref, computed } from 'vue'
import type { ConfigurationComponent } from '../types'

export interface SelectionRect {
  startX: number
  startY: number
  endX: number
  endY: number
}

export function useSelection() {
  // 选中的组件ID列表
  const selectedComponentIds = ref<string[]>([])
  
  // 框选状态
  const isSelecting = ref(false)
  const selectionRect = ref<SelectionRect | null>(null)
  
  // 计算属性
  const hasSelection = computed(() => selectedComponentIds.value.length > 0)
  const isMultiSelection = computed(() => selectedComponentIds.value.length > 1)
  
  // 选择单个组件
  const selectComponent = (componentId: string, multiSelect = false) => {
    if (multiSelect) {
      // 多选模式
      const index = selectedComponentIds.value.indexOf(componentId)
      if (index > -1) {
        // 已选中，取消选择
        selectedComponentIds.value.splice(index, 1)
      } else {
        // 未选中，添加到选择列表
        selectedComponentIds.value.push(componentId)
      }
    } else {
      // 单选模式
      selectedComponentIds.value = [componentId]
    }
  }
  
  // 选择多个组件
  const selectComponents = (componentIds: string[], append = false) => {
    if (append) {
      // 追加选择
      componentIds.forEach(id => {
        if (!selectedComponentIds.value.includes(id)) {
          selectedComponentIds.value.push(id)
        }
      })
    } else {
      // 替换选择
      selectedComponentIds.value = [...componentIds]
    }
  }
  
  // 取消选择组件
  const deselectComponent = (componentId: string) => {
    const index = selectedComponentIds.value.indexOf(componentId)
    if (index > -1) {
      selectedComponentIds.value.splice(index, 1)
    }
  }
  
  // 清空选择
  const clearSelection = () => {
    selectedComponentIds.value = []
  }
  
  // 全选组件
  const selectAll = (components: ConfigurationComponent[]) => {
    selectedComponentIds.value = components.map(c => c.id)
  }
  
  // 反选
  const invertSelection = (components: ConfigurationComponent[]) => {
    const allIds = components.map(c => c.id)
    const newSelection = allIds.filter(id => !selectedComponentIds.value.includes(id))
    selectedComponentIds.value = newSelection
  }
  
  // 检查组件是否被选中
  const isComponentSelected = (componentId: string) => {
    return selectedComponentIds.value.includes(componentId)
  }
  
  // 开始框选
  const startSelection = (x: number, y: number) => {
    isSelecting.value = true
    selectionRect.value = {
      startX: x,
      startY: y,
      endX: x,
      endY: y
    }
  }
  
  // 更新框选区域
  const updateSelection = (x: number, y: number) => {
    if (isSelecting.value && selectionRect.value) {
      selectionRect.value.endX = x
      selectionRect.value.endY = y
    }
  }
  
  // 结束框选
  const endSelection = (components: ConfigurationComponent[], multiSelect = false) => {
    if (!isSelecting.value || !selectionRect.value) return
    
    // 计算框选区域
    const rect = selectionRect.value
    const minX = Math.min(rect.startX, rect.endX)
    const maxX = Math.max(rect.startX, rect.endX)
    const minY = Math.min(rect.startY, rect.endY)
    const maxY = Math.max(rect.startY, rect.endY)
    
    // 找到在框选区域内的组件
    const selectedInRect = components.filter(component => {
      const compMinX = component.x
      const compMaxX = component.x + component.width
      const compMinY = component.y
      const compMaxY = component.y + component.height
      
      // 检查组件是否与框选区域相交
      return !(compMaxX < minX || compMinX > maxX || compMaxY < minY || compMinY > maxY)
    })
    
    const selectedIds = selectedInRect.map(c => c.id)
    
    if (multiSelect) {
      // 多选模式：追加到现有选择
      selectComponents(selectedIds, true)
    } else {
      // 单选模式：替换现有选择
      selectComponents(selectedIds, false)
    }
    
    // 重置框选状态
    isSelecting.value = false
    selectionRect.value = null
  }
  
  // 取消框选
  const cancelSelection = () => {
    isSelecting.value = false
    selectionRect.value = null
  }
  
  // 获取框选区域的样式
  const getSelectionRectStyle = () => {
    if (!selectionRect.value) return {}
    
    const rect = selectionRect.value
    const minX = Math.min(rect.startX, rect.endX)
    const maxX = Math.max(rect.startX, rect.endX)
    const minY = Math.min(rect.startY, rect.endY)
    const maxY = Math.max(rect.startY, rect.endY)
    
    return {
      position: 'absolute',
      left: `${minX}px`,
      top: `${minY}px`,
      width: `${maxX - minX}px`,
      height: `${maxY - minY}px`,
      border: '1px dashed #409eff',
      backgroundColor: 'rgba(64, 158, 255, 0.1)',
      pointerEvents: 'none',
      zIndex: 1000
    }
  }
  
  return {
    // 状态
    selectedComponentIds,
    isSelecting,
    selectionRect,
    
    // 计算属性
    hasSelection,
    isMultiSelection,
    
    // 选择方法
    selectComponent,
    selectComponents,
    deselectComponent,
    clearSelection,
    selectAll,
    invertSelection,
    isComponentSelected,
    
    // 框选方法
    startSelection,
    updateSelection,
    endSelection,
    cancelSelection,
    getSelectionRectStyle
  }
}
