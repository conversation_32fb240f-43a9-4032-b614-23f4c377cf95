<template>
  <yt-crud
    ref="crudRef"
    :data="data"
    :column="column"
    :table-props="{
          selection: false,//多选
          dialogBtn:false,
          menuSlot: true,//自定义操作按钮
        }"
    :form-props="{
          width: 550,
          labelWidth:220
        }"
    @save-fun="onSave"
    @del-fun="handleDelete"
    @onLoad="getData"
    :loading="state.loading"
    :total="state.total"
    v-model:page="state.page"
    v-model:query="state.query"
  >
    <template #baseStartDate="{ row }">
      {{ formatDate(row.baseStartDate) }}
    </template>
    <template #baseEndDate="{ row }">
      {{ formatDate(row.baseEndDate) }}
    </template>
  </yt-crud>
</template>
<script lang="ts" setup>
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
import {benefitConfigList,addConfigbenifit,deleteConfigbenifit,editConfigbenifit} from './index.api'
import { getpowerUnit } from '../index.api'
import { IColumn } from '@/components/common/types/tableCommon'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

const data = ref([])
const powerUnitOptions = ref<{label: string, value: number}[]>([])

const column = ref<IColumn[]>([
  {
    label: '功率变化率(%)',
    key: 'baseChangeRate',
    // search: true,
    align: 'center',
  },
  {
    label: '基准开始时间',
    key: 'baseStartDate',
    // search: true,
    type: 'date',
    align: 'center',
    slot: true,
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    label: '基准结束时间',
    key: 'baseEndDate',
    // search: true,
    type: 'date',
    align: 'center',
    slot: true,
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    label:'所属机组',
    key:'powerUnitId',
    align:'center',
    type:'select',
    componentProps: {
      options: powerUnitOptions
    }
  }
])

emitter.on('projectListChanged', (e) => {
  location.reload()
})

// 格式化日期函数
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 格式化日期为完整时间格式（用于提交数据）
const formatDateTimeForSubmit = (dateString: string) => {
  if (!dateString) return ''
  const date = typeof dateString === 'string' ? dateString : dateString
  // 如果已经是 YYYY-MM-DD 格式，直接拼接时间
  if (date.includes('T') || date.includes(' ')) {
    const justDate = date.split('T')[0].split(' ')[0]
    return `${justDate} 00:00:00`
  }
  return `${date} 00:00:00`
}
const getpowerUnitData = () => {
    const params = {
        projectId:cachedProjects.id
    }
    getpowerUnit(params).then((res)=>{
        console.log(res,'--')
        if(res.code === 200 && res.data) {
            // 将接口数据转换为下拉框选项格式
            powerUnitOptions.value = res.data.map(item => ({
                label: item.name,
                value: item.id
            }))
        }
    })
}
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})
const onSave = ({ type, data, cancel }: any) => {
  const { id: projectId } = cachedProjects

  // 根据选中的powerUnitId找到对应的机组名称
  let powerUnitName = ''
  if (data.powerUnitId) {
    const selectedUnit = powerUnitOptions.value.find(option => option.value === data.powerUnitId)
    powerUnitName = selectedUnit ? selectedUnit.label : ''
  }

  const modifiedData = {
    ...data,
    projectId,
    powerUnitName, // 添加机组名称
    // 格式化日期字段
    baseStartDate: data.baseStartDate ? formatDateTimeForSubmit(data.baseStartDate) : '',
    baseEndDate: data.baseEndDate ? formatDateTimeForSubmit(data.baseEndDate) : '',
  }

  if (type == 'add') {
    addConfigbenifit(modifiedData).then((res) => {
      if (res.code == 200) {
        ElMessage.success('添加成功')
        cancel()
        getData()
      }
    })
  } else if (type == 'update') {
    editConfigbenifit(modifiedData).then((res) => {
      if (res.code == 200) {
        ElMessage.success('修改成功')
        cancel()
        getData()
      }
    })
  }
}
const { id: projectId } = cachedProjects
const handleDelete = (row: any) => {
  console.log(row, '===')
  deleteConfigbenifit([row.id]).then((res) => {
    if (res.code == 200) {
      ElMessage.success('删除成功')
      getData()
    }
  })
}
const getData = () => {
  state.loading = true
  const project = { ...state.query, projectId }
  benefitConfigList(project).then((res) => {
    state.loading = false
    state.total = res.data.total
    data.value = res.data.rows
  })
}

// 初始化时加载机组数据
onMounted(() => {
  getpowerUnitData()
})
</script>
<style scoped>
:deep(.el-select__wrapper) {
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
</style>
