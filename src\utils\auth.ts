import { useCache, CACHE_KEY } from '@/hooks/web/useCache'
const { wsCache } = useCache()
const TokenKey = 'Admin-Token'

const tokenStorage = useStorage<null | string>(To<PERSON><PERSON><PERSON>, null)

export const getToken = () => tokenStorage.value

export const setToken = (token: string) => (tokenStorage.value = token)

export const removeToken = () => (tokenStorage.value = null)

// ========== 租户相关 ==========

export const getTenantId = () => {
  return wsCache.get(CACHE_KEY.TenantId)
}

export const setTenantId = (username: string) => {
  wsCache.set(CACHE_KEY.TenantId, username)
}
