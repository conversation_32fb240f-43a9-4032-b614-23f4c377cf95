<template>
  <div class="configuration-editor">
    <div class="editor-header">
      <div class="editor-title">
        <h2>{{ currentProject?.name || '组态编辑器' }}</h2>
      </div>
      <div class="editor-actions">
        <el-button-group>
          <el-button @click="undo" :disabled="!canUndo">
            <el-icon><ArrowLeft /></el-icon>
            撤销
          </el-button>
          <el-button @click="redo" :disabled="!canRedo">
            <el-icon><ArrowRight /></el-icon>
            重做
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button @click="copySelected" :disabled="!hasSelection">
            <el-icon><CopyDocument /></el-icon>
            复制
          </el-button>
          <el-button @click="pasteComponents" :disabled="!canPaste">
            <el-icon><Document /></el-icon>
            粘贴
          </el-button>
          <el-button @click="deleteSelected" :disabled="!hasSelection" type="danger">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button @click="saveProject" type="primary">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
          <el-button @click="previewProject">
            <el-icon><View /></el-icon>
            预览
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="editor-main">
      <div class="editor-sidebar">
        <component-library />
      </div>
      
      <div class="editor-canvas-container">
        <div class="editor-toolbar">
          <el-button-group>
            <el-button @click="zoomIn">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
            <el-button @click="zoomOut">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="resetZoom">
              <el-icon><FullScreen /></el-icon>
              {{ Math.round(editorState.zoom * 100) }}%
            </el-button>
          </el-button-group>
          
          <el-switch
            v-model="editorState.grid.show"
            inline-prompt
            active-text="网格"
            inactive-text="网格"
          />
          
          <el-switch
            v-model="editorState.grid.snap"
            inline-prompt
            active-text="吸附"
            inactive-text="吸附"
          />
        </div>
        
        <div 
          class="editor-canvas"
          :style="{
            width: `${currentProject?.width || 1920}px`,
            height: `${currentProject?.height || 1080}px`,
            backgroundColor: currentProject?.backgroundColor || '#f0f0f0',
            transform: `scale(${editorState.zoom})`,
            backgroundImage: editorState.grid.show ? 'linear-gradient(#ddd 1px, transparent 1px), linear-gradient(90deg, #ddd 1px, transparent 1px)' : 'none',
            backgroundSize: `${editorState.grid.size}px ${editorState.grid.size}px`
          }"
          @click="onCanvasClick"
          @dragover="onDragOver"
          @drop="onDrop"
        >
          <template v-if="currentProject">
            <component-renderer
              v-for="component in currentProject.components"
              :key="component.id"
              :component="component"
              :selected="isSelected(component.id)"
              :editing="true"
              @select="selectComponent"
              @update="updateComponent"
            />
          </template>
          <div v-else class="empty-canvas">
            <el-empty description="请创建或选择一个组态项目" />
          </div>
        </div>
      </div>
      
      <div class="editor-properties">
        <h3>属性面板</h3>
        <template v-if="hasSelection">
          <component-properties
            :component="selectedComponents[0]"
            @update="updateComponentProperties"
          />
        </template>
        <template v-else-if="currentProject">
          <project-properties
            :project="currentProject"
            @update="updateProjectProperties"
          />
        </template>
        <template v-else>
          <el-empty description="未选择任何对象" />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useConfigurationStore } from '../stores/configurationStore'
import ComponentLibrary from '../components/ComponentLibrary.vue'
import ComponentRenderer from '../components/ComponentRenderer.vue'
import ComponentProperties from '../components/ComponentProperties.vue'
import ProjectProperties from '../components/ProjectProperties.vue'
import type { ConfigurationComponent, ConfigurationProject, ComponentTemplate } from '../types'

const route = useRoute()
const router = useRouter()
const configStore = useConfigurationStore()

// 获取当前项目
const currentProject = computed(() => configStore.currentProject)

// 获取编辑器状态
const editorState = computed(() => configStore.editorState)

// 获取选中的组件
const selectedComponents = computed(() => configStore.selectedComponents)

// 是否有选中的组件
const hasSelection = computed(() => selectedComponents.value.length > 0)

// 是否可以撤销
const canUndo = computed(() => editorState.value.history.past.length > 0)

// 是否可以重做
const canRedo = computed(() => editorState.value.history.future.length > 0)

// 是否可以粘贴
const canPaste = computed(() => editorState.value.clipboard.length > 0)

// 组件是否被选中
const isSelected = (id: string) => {
  return editorState.value.selectedComponents.includes(id)
}

// 加载项目
onMounted(() => {
  const projectId = route.params.id as string
  if (projectId) {
    loadProject(projectId)
  } else {
    createNewProject()
  }
})

// 加载项目
const loadProject = async (id: string) => {
  try {
    // 这里应该从API加载项目数据
    // 临时使用示例数据
    const project: ConfigurationProject = {
      id,
      name: '示例组态项目',
      description: '这是一个示例组态项目',
      width: 1920,
      height: 1080,
      backgroundColor: '#f0f0f0',
      components: [],
      dataBindings: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'admin',
      status: 'draft'
    }
    
    configStore.setCurrentProject(project)
  } catch (error) {
    ElMessage.error('加载项目失败')
    router.push('/configuration/list')
  }
}

// 创建新项目
const createNewProject = () => {
  const project: ConfigurationProject = {
    id: 'project_' + Date.now(),
    name: '新建组态项目',
    description: '',
    width: 1920,
    height: 1080,
    backgroundColor: '#f0f0f0',
    components: [],
    dataBindings: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'admin',
    status: 'draft'
  }
  
  configStore.setCurrentProject(project)
}

// 画布点击事件
const onCanvasClick = (event: MouseEvent) => {
  // 如果点击的是画布本身，而不是组件，则清除选择
  if (event.target === event.currentTarget) {
    configStore.clearSelection()
  }
}

// 拖拽经过画布
const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy'
  }
}

// 拖拽放置到画布
const onDrop = (event: DragEvent) => {
  event.preventDefault()
  
  if (!event.dataTransfer || !currentProject.value) return
  
  const componentData = event.dataTransfer.getData('component')
  if (!componentData) return
  
  try {
    const template = JSON.parse(componentData) as ComponentTemplate
    
    // 获取放置位置（相对于画布）
    const canvasRect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    const x = (event.clientX - canvasRect.left) / editorState.value.zoom
    const y = (event.clientY - canvasRect.top) / editorState.value.zoom
    
    // 如果启用了网格吸附
    const gridSize = editorState.value.grid.size
    const snapX = editorState.value.grid.snap ? Math.round(x / gridSize) * gridSize : x
    const snapY = editorState.value.grid.snap ? Math.round(y / gridSize) * gridSize : y
    
    // 创建新组件
    const component: ConfigurationComponent = {
      id: 'component_' + Date.now() + '_' + Math.floor(Math.random() * 1000),
      type: template.type,
      name: template.name,
      x: snapX,
      y: snapY,
      z: 0,
      width: template.defaultProps.width || 100,
      height: template.defaultProps.height || 100,
      rotation: 0,
      opacity: 1,
      visible: true,
      locked: false,
      style: template.defaultProps.style || {},
      data: template.defaultProps.data || { static: null },
      ...(template.defaultProps as any)
    }
    
    // 添加组件到项目
    configStore.addComponent(component)
  } catch (error) {
    console.error('添加组件失败', error)
  }
}

// 选择组件
const selectComponent = (id: string, multiple = false) => {
  configStore.selectComponent(id, multiple)
}

// 更新组件
const updateComponent = (component: ConfigurationComponent) => {
  configStore.updateComponent(component)
}

// 更新组件属性
const updateComponentProperties = (component: ConfigurationComponent) => {
  configStore.updateComponent(component)
}

// 更新项目属性
const updateProjectProperties = (project: ConfigurationProject) => {
  if (!currentProject.value) return
  
  currentProject.value = {
    ...currentProject.value,
    ...project
  }
  
  configStore.saveHistory()
}

// 复制选中的组件
const copySelected = () => {
  configStore.copySelectedComponents()
  ElMessage.success('已复制选中组件')
}

// 粘贴组件
const pasteComponents = () => {
  configStore.pasteComponents()
}

// 删除选中的组件
const deleteSelected = () => {
  if (!hasSelection.value) return
  
  ElMessageBox.confirm('确定要删除选中的组件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    selectedComponents.value.forEach(component => {
      configStore.deleteComponent(component.id)
    })
    ElMessage.success('删除成功')
  }).catch(() => {})
}

// 保存项目
const saveProject = async () => {
  if (!currentProject.value) return
  
  try {
    // 这里应该调用API保存项目
    // 临时模拟保存
    currentProject.value.updatedAt = new Date().toISOString()
    configStore.updateProject(currentProject.value)
    
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 预览项目
const previewProject = () => {
  if (!currentProject.value) return
  
  router.push(`/configuration/demonstrate/${currentProject.value.id}`)
}

// 放大
const zoomIn = () => {
  editorState.value.zoom = Math.min(editorState.value.zoom + 0.1, 3)
}

// 缩小
const zoomOut = () => {
  editorState.value.zoom = Math.max(editorState.value.zoom - 0.1, 0.1)
}

// 重置缩放
const resetZoom = () => {
  editorState.value.zoom = 1
}
</script>

<style scoped>
.configuration-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
  background-color: #fff;
}

.editor-title h2 {
  margin: 0;
  font-size: 18px;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

.editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-sidebar {
  width: 250px;
  height: 100%;
  overflow-y: auto;
  background-color: #fff;
  border-right: 1px solid var(--el-border-color-light);
}

.editor-canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.editor-toolbar {
  padding: 10px;
  display: flex;
  gap: 10px;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid var(--el-border-color-light);
}

.editor-canvas {
  flex: 1;
  position: relative;
  transform-origin: top left;
  overflow: auto;
  margin: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.editor-properties {
  width: 300px;
  height: 100%;
  overflow-y: auto;
  padding: 10px;
  background-color: #fff;
  border-left: 1px solid var(--el-border-color-light);
  color: #000;
}

.editor-properties h3 {
  margin-top: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color-light);
  color: #000;
}

.empty-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 确保属性面板中的所有文字都是黑色的 */
:deep(.editor-properties) {
  color: #000 !important;
}

:deep(.editor-properties .el-form-item__label) {
  color: #000 !important;
}

:deep(.editor-properties .el-input__inner),
:deep(.editor-properties .el-textarea__inner),
:deep(.editor-properties .el-select-dropdown__item),
:deep(.editor-properties .el-radio-button__inner),
:deep(.editor-properties .el-input-number__decrease),
:deep(.editor-properties .el-input-number__increase) {
  color: #000 !important;
}

:deep(.editor-properties .el-collapse-item__header),
:deep(.editor-properties .el-collapse-item__content) {
  color: #000 !important;
}

:deep(.editor-properties .el-select-dropdown__item) {
  color: #000 !important;
}

:deep(.editor-properties input),
:deep(.editor-properties textarea) {
  color: #000 !important;
}

:deep(.editor-properties .el-tabs__item) {
  color: #000 !important;
}

:deep(.editor-properties .el-tabs__item.is-active) {
  color: var(--el-color-primary) !important;
}

:deep(.editor-properties .el-form) {
  color: #000 !important;
}

:deep(.editor-properties .el-switch__label) {
  color: #000 !important;
}

:deep(.editor-properties .el-checkbox__label) {
  color: #000 !important;
}

:deep(.editor-properties .el-radio__label) {
  color: #000 !important;
}

:deep(.editor-properties .el-button) {
  color: inherit;
}
</style>