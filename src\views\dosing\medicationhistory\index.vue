<template>
  <yt-crud
    ref="crudRef"
    :data="data"
    :column="column"
    :table-props="{
            selection: false,//多选
            dialogBtn:false,
            menu: false,//没有操作
            editBtn: false,
            delBtn: false,
            
          }"
    :form-props="{
            width: 550,
            labelWidth:150
          }"
    @save-fun="onSave"
    @del-fun="handleDelete"
    @onLoad="getData"
    :loading="state.loading"
    :total="state.total"
    v-model:page="state.page"
    v-model:query="state.query"
  >
  </yt-crud>
</template>
<script lang="ts" setup>
import { medicalList } from './index.api'
import { IColumn } from '@/components/common/types/tableCommon'
import YtCrud from '@/components/common/yt-crud.vue'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
import { log } from 'console'
import { el } from 'element-plus/es/locale'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
import { ComponentInternalInstance } from 'vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance

// import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
emitter.on('projectListChanged', (e) => {
  location.reload()
})

const data = ref([])

const column = ref<IColumn[]>([
  {
    label: '药剂名称',
    key: 'name',
    search: true,
    // rules: [{ required: true, message: '药剂名称不能为空' }],
    align: 'center',
  },

  {
    label: '加药量',
    key: 'addNumber',
    // search: true,
    align: 'center',
  },
  {
    label: '药剂浓度',
    key: 'addConcentration',
    // search: true,
    align: 'center',
  },
  {
    label: '加药时间',
    key: 'addDate',
    // search: true,
    align: 'center',
  },
  {
    label: '操作人员',
    key: 'addPerson',
    // search: true,
    // search: true,
    align: 'center',
  },
  {
    label: '操作人员联系方式',
    key: 'addPersionPhone',
    // search: true,
    align: 'center',
  },

])
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})
const onSave = ({ type, data, cancel }: any) => {}
const downloadPlan = (row) => {
  proxy?.$download.oss(row.address)
}
const { id: projectId } = cachedProjects
const handleDelete = (row: any) => {}
const getData = () => {
  state.loading = true
  medicalList({
    ...state.page,
    ...state.query,
    projectId,
  }).then((res) => {
    state.loading = false
    state.total = res.data.total
    data.value = res.data.rows
  })
}
</script>

<style scoped>
:deep(.el-select__wrapper) {
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
</style>
