<template>
  <div ref="chartContainer" style="height: 100%; width: 100%; top: 20px"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { defineProps } from 'vue'

const props = defineProps<{
  data: Record<string, { value: number; time: string; unit: string }[]>
  legendData: string[]
  lineColors: string[]
  xAxisRotation: number
  yAxisConfig?: (boolean | number)[] // 用于配置每条线使用左侧(false/0)或右侧(true/1)Y轴
}>()

const chartContainer = ref<HTMLDivElement | null>(null)
let myChart: echarts.ECharts | null = null

onMounted(() => {
  nextTick(() => {
    if (chartContainer.value) {
      myChart = echarts.init(chartContainer.value)
      setTimeout(() => {
        updateChart()
      }, 100) // 延迟更新图表，确保容器完全加载
      window.addEventListener('resize', resizeChart)
    }
  })
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeChart)
  if (myChart) {
    myChart.dispose()
    myChart = null
  }
})

watch(
  () => props.data,
  () => {
    if (myChart) {
      updateChart()
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => props.legendData,
  () => {
    if (myChart) {
      updateChart()
    }
  }
)

watch(
  () => props.lineColors,
  () => {
    if (myChart) {
      updateChart()
    }
  }
)

watch(
  () => props.yAxisConfig,
  () => {
    if (myChart) {
      updateChart()
    }
  }
)

function updateChart() {
  if (!props.data || props.legendData.length === 0) {
    console.warn('No data available for chart rendering.')
    return
  }

  if (myChart) {
    myChart.resize() // 调整图表尺寸以适应容器
  }

  const dateList =
    props.data[props.legendData[0]]?.map((item) => {
      const date = new Date(item.time)
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDay()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${hours}:${minutes}`
    }) || []

  const seriesData = props.legendData.map((legend, index) => {
    const useRightAxis = props.yAxisConfig ? props.yAxisConfig[index] === true || props.yAxisConfig[index] === 1 : false
    return {
      name: legend,
      type: 'line',
      showSymbol: true,
      symbolSize: 1.5,
      smooth: true,
      yAxisIndex: useRightAxis ? 1 : 0,
      data: props.data[legend]?.map((item) => item.value) || [],
      lineStyle: {
        color: props.lineColors[index] || '#fff',
      },
      itemStyle: {
        color: props.lineColors[index] || '#fff',
        borderColor: props.lineColors[index] || '#fff',
        borderWidth: 0,
      },
    }
  })

  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((item) => {
          // Safely check for null, NaN, or undefined values
          const rawValue = item.data
          const displayValue = rawValue == null || (typeof rawValue === 'number' && isNaN(rawValue)) ? '--' : rawValue
          // If you've stored units for each data point
          const unit = props.data[item.seriesName]?.[item.dataIndex]?.unit || ''

          result += `${item.marker}${item.seriesName}: ${displayValue} ${unit}<br/>`
        })
        return result
      },
    },
    legend: {
      data: props.legendData,
      textStyle: {
        fontSize: 14,
        color: 'rgba(204, 204, 204, 1)',
      },
      icon: 'circle',
      itemWidth: 6,
      itemHeight: 6,
    },
    xAxis: {
      type: 'category',
      data: dateList,
      boundaryGap: false,
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        formatter: function (value, index) {
          return index % 2 === 0 ? value : ''
        },
        rotate: props.xAxisRotation ?? 0,
        textStyle: {
          color: 'rgba(204, 204, 204, 1)',
          fontSize: 12,
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(204, 204, 204, 1)',
        },
      },
    },
    yAxis: [
      {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: 'rgba(62, 65, 77, 1)',
          },
        },
        axisLabel: {
          color: 'rgba(204, 204, 204, 1)',
          fontSize: 14,
        },
      },
      {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: 'rgba(204, 204, 204, 1)',
          fontSize: 14,
        },
      },
    ],
    series: seriesData,
  }

  if (myChart) {
    myChart.setOption(option)
  }
}

function resizeChart() {
  if (myChart) {
    myChart.resize()
  }
}
</script>

<style scoped lang="scss">
.curveChart {
  height: 300px; // 明确设置高度以避免塌陷
  width: 100%;
  min-height: 300px; // 添加最小高度防止塌陷
}
</style>
