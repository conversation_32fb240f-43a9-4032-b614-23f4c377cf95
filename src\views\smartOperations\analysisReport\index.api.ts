import request from '@/utils/request'

enum Api {
  list = '/manager/analysisReport/list',
  candidatelist = '/manager/analysisReportDetail/list',
  manuallyTriggered = '/manager/analysisReport/manual/analysis/template',
  download = '/manager/analysisReport/trace/export',
}

// 查询分析报告
export const getSmartList = (data) => {
  return request({
    url: Api.list,
    method: 'post',
    data,
  })
}
// 查询候选方案
export const getSmartCandidateList = (data) => {
  return request({
    url: Api.candidatelist,
    method: 'post',
    data,
  })
}
// 手动触发智能运维分析
// 定义一个手动触发的函数，接收一个参数data
export const manuallyTriggereddata = (data) => {
  return request({
    url: Api.manuallyTriggered,
    method: 'post',
    data,
  })
}
// 导出报告日志
export const downloadReportLog = (data) => {
  return request({
    url: Api.download,
    method: 'post',
    data,
    responseType: 'blob',
  })
}
