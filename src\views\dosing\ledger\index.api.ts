import request from '@/utils/request'

enum Api {
  ledgerList = '/medical/ledger/list',
  ledgerAdd = '/medical/ledger/add',
  ledgerEdit = '/medical/ledger/edit',
  ledgerDelete = '/medical/ledger/delete',
}

// 查看台账列表
export const getledgerList = (data) => {
  return request({
    url: Api.ledgerList,
    method: 'post',
    data,
  })
}

// 新增台账
export const addledger = (data) => {
  return request({
    url: Api.ledgerAdd,
    method: 'post',
    data,
  })
}

// 编辑台账
export const editledger = (data) => {
  return request({
    url: Api.ledgerEdit,
    method: 'post',
    data,
  })
}

// 删除台账
export const deletelLedger = (data) => {
  return request({
    url: Api.ledgerDelete,
    method: 'post',
    data,
  })
}
