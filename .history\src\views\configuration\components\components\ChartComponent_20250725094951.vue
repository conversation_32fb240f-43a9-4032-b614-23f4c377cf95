<template>
  <div class="chart-component">
    <v-chart 
      class="chart" 
      :option="chartOption" 
      :autoresize="true"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { 
  BarChart, 
  LineChart, 
  <PERSON>hart, 
  Scatter<PERSON>hart,
  GaugeChart,
  RadarChart
} from 'echarts/charts'
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  DataZoomComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import type { ConfigurationComponent } from '../../types'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  Bar<PERSON>hart,
  LineChart,
  <PERSON>hart,
  <PERSON>atter<PERSON>hart,
  Gauge<PERSON>hart,
  RadarChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  DataZoomComponent
])

const props = defineProps<{
  component: ConfigurationComponent
  editing?: boolean
}>()

// 图表选项
const chartOption = computed(() => {
  const { data } = props.component
  
  // 如果有动态数据，优先使用动态数据
  if (data.dynamic && data.dynamic.value) {
    return data.dynamic.value
  }
  
  // 否则使用静态数据
  return data.static || {
    xAxis: {
      type: 'category',
      data: ['暂无数据']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [0],
      type: 'line'
    }]
  }
})

// 监听组件大小变化
const resizeObserver = ref<ResizeObserver | null>(null)

onMounted(() => {
  // 创建 ResizeObserver 监听容器大小变化
  if (window.ResizeObserver) {
    const chartContainer = document.querySelector('.chart-component')
    if (chartContainer) {
      resizeObserver.value = new ResizeObserver(() => {
        // 触发图表重绘
        const chartInstance = document.querySelector('.chart') as any
        if (chartInstance && chartInstance.__chartInstance) {
          chartInstance.__chartInstance.resize()
        }
      })
      
      resizeObserver.value.observe(chartContainer)
    }
  }
})

onUnmounted(() => {
  // 清理 ResizeObserver
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
})
</script>

<style scoped>
.chart-component {
  width: 100%;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>