import { throttle, debounce } from 'lodash-es'

/**
 * 性能优化工具集
 */

// 缓存管理器
export class CacheManager<T = any> {
  private cache = new Map<string, { data: T; timestamp: number; hits: number }>()
  private maxSize: number
  private ttl: number // 生存时间(毫秒)

  constructor(maxSize = 100, ttl = 5 * 60 * 1000) {
    this.maxSize = maxSize
    this.ttl = ttl
  }

  set(key: string, value: T): void {
    // 如果缓存已满，删除最少使用的项
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }

    this.cache.set(key, {
      data: value,
      timestamp: Date.now(),
      hits: 0
    })
  }

  get(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null

    // 检查是否过期
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }

    // 增加命中次数
    item.hits++
    return item.data
  }

  has(key: string): boolean {
    return this.cache.has(key) && !this.isExpired(key)
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }

  // 获取缓存统计信息
  getStats() {
    const items = Array.from(this.cache.values())
    const totalHits = items.reduce((sum, item) => sum + item.hits, 0)
    const avgHits = items.length > 0 ? totalHits / items.length : 0

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      totalHits,
      avgHits: Math.round(avgHits * 100) / 100,
      hitRate: totalHits > 0 ? Math.round((totalHits / (totalHits + items.length)) * 10000) / 100 : 0
    }
  }

  private isExpired(key: string): boolean {
    const item = this.cache.get(key)
    return item ? Date.now() - item.timestamp > this.ttl : true
  }

  private evictLRU(): void {
    let lruKey = ''
    let lruHits = Infinity
    let oldestTime = Infinity

    for (const [key, item] of this.cache.entries()) {
      if (item.hits < lruHits || (item.hits === lruHits && item.timestamp < oldestTime)) {
        lruKey = key
        lruHits = item.hits
        oldestTime = item.timestamp
      }
    }

    if (lruKey) {
      this.cache.delete(lruKey)
    }
  }
}

// 性能监控器
export class PerformanceMonitor {
  private metrics = new Map<string, number[]>()
  private maxSamples = 100

  // 记录性能指标
  record(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }

    const samples = this.metrics.get(name)!
    samples.push(value)

    // 保持样本数量在限制内
    if (samples.length > this.maxSamples) {
      samples.shift()
    }
  }

  // 获取平均值
  getAverage(name: string): number {
    const samples = this.metrics.get(name)
    if (!samples || samples.length === 0) return 0

    return samples.reduce((sum, val) => sum + val, 0) / samples.length
  }

  // 获取最近的值
  getLatest(name: string): number {
    const samples = this.metrics.get(name)
    return samples && samples.length > 0 ? samples[samples.length - 1] : 0
  }

  // 获取最大值
  getMax(name: string): number {
    const samples = this.metrics.get(name)
    return samples && samples.length > 0 ? Math.max(...samples) : 0
  }

  // 获取最小值
  getMin(name: string): number {
    const samples = this.metrics.get(name)
    return samples && samples.length > 0 ? Math.min(...samples) : 0
  }

  // 获取所有指标概览
  getAllMetrics() {
    const result: Record<string, any> = {}

    for (const [name, samples] of this.metrics.entries()) {
      if (samples.length > 0) {
        result[name] = {
          latest: this.getLatest(name),
          average: Math.round(this.getAverage(name) * 100) / 100,
          max: this.getMax(name),
          min: this.getMin(name),
          samples: samples.length
        }
      }
    }

    return result
  }

  // 清除指标
  clear(name?: string): void {
    if (name) {
      this.metrics.delete(name)
    } else {
      this.metrics.clear()
    }
  }
}

// 内存监控
export class MemoryMonitor {
  private lastCleanup = Date.now()
  private cleanupInterval = 5 * 60 * 1000 // 5分钟

  // 获取内存使用情况
  getMemoryUsage() {
    const memory = (performance as any).memory
    if (memory) {
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        usedMB: Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
        totalMB: Math.round(memory.totalJSHeapSize / 1024 / 1024 * 100) / 100,
        usagePercent: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 10000) / 100
      }
    }
    return null
  }

  // 检查是否需要清理
  shouldCleanup(): boolean {
    const now = Date.now()
    if (now - this.lastCleanup > this.cleanupInterval) {
      this.lastCleanup = now
      return true
    }
    return false
  }

  // 格式化内存大小
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i]
  }
}

// FPS监控器
export class FPSMonitor {
  private frameCount = 0
  private lastTime = performance.now()
  private fps = 60
  private fpsHistory: number[] = []
  private maxHistory = 60
  private callback?: (fps: number) => void

  constructor(callback?: (fps: number) => void) {
    this.callback = callback
    this.measure()
  }

  private measure = () => {
    this.frameCount++
    const now = performance.now()
    
    if (now - this.lastTime >= 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (now - this.lastTime))
      
      this.fpsHistory.push(this.fps)
      if (this.fpsHistory.length > this.maxHistory) {
        this.fpsHistory.shift()
      }
      
      this.frameCount = 0
      this.lastTime = now
      
      if (this.callback) {
        this.callback(this.fps)
      }
    }
    
    requestAnimationFrame(this.measure)
  }

  getCurrentFPS(): number {
    return this.fps
  }

  getAverageFPS(): number {
    if (this.fpsHistory.length === 0) return this.fps
    return Math.round(this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length)
  }

  getMinFPS(): number {
    return this.fpsHistory.length > 0 ? Math.min(...this.fpsHistory) : this.fps
  }

  getMaxFPS(): number {
    return this.fpsHistory.length > 0 ? Math.max(...this.fpsHistory) : this.fps
  }
}

// 渲染优化器
export class RenderOptimizer {
  private renderQueue: (() => void)[] = []
  private isRendering = false
  private batchSize = 10

  // 批量渲染
  batchRender(renderFn: () => void): void {
    this.renderQueue.push(renderFn)
    
    if (!this.isRendering) {
      this.processQueue()
    }
  }

  private processQueue = async () => {
    this.isRendering = true
    
    while (this.renderQueue.length > 0) {
      const batch = this.renderQueue.splice(0, this.batchSize)
      
      // 执行批量渲染
      batch.forEach(fn => fn())
      
      // 让出控制权给浏览器
      await new Promise(resolve => requestAnimationFrame(resolve))
    }
    
    this.isRendering = false
  }

  // 清空渲染队列
  clearQueue(): void {
    this.renderQueue = []
  }

  // 获取队列长度
  getQueueLength(): number {
    return this.renderQueue.length
  }
}

// 空间索引（用于快速查找组件）
export class SpatialIndex {
  private gridSize: number
  private grid = new Map<string, Set<string>>()

  constructor(gridSize = 200) {
    this.gridSize = gridSize
  }

  // 添加对象到索引
  add(id: string, x: number, y: number, width: number, height: number): void {
    const cells = this.getCells(x, y, width, height)
    
    cells.forEach(cell => {
      if (!this.grid.has(cell)) {
        this.grid.set(cell, new Set())
      }
      this.grid.get(cell)!.add(id)
    })
  }

  // 从索引中移除对象
  remove(id: string, x: number, y: number, width: number, height: number): void {
    const cells = this.getCells(x, y, width, height)
    
    cells.forEach(cell => {
      const cellSet = this.grid.get(cell)
      if (cellSet) {
        cellSet.delete(id)
        if (cellSet.size === 0) {
          this.grid.delete(cell)
        }
      }
    })
  }

  // 查询区域内的对象
  query(x: number, y: number, width: number, height: number): Set<string> {
    const result = new Set<string>()
    const cells = this.getCells(x, y, width, height)
    
    cells.forEach(cell => {
      const cellSet = this.grid.get(cell)
      if (cellSet) {
        cellSet.forEach(id => result.add(id))
      }
    })
    
    return result
  }

  // 清空索引
  clear(): void {
    this.grid.clear()
  }

  private getCells(x: number, y: number, width: number, height: number): string[] {
    const startX = Math.floor(x / this.gridSize)
    const endX = Math.floor((x + width) / this.gridSize)
    const startY = Math.floor(y / this.gridSize)
    const endY = Math.floor((y + height) / this.gridSize)
    
    const cells: string[] = []
    
    for (let gx = startX; gx <= endX; gx++) {
      for (let gy = startY; gy <= endY; gy++) {
        cells.push(`${gx},${gy}`)
      }
    }
    
    return cells
  }

  // 获取统计信息
  getStats() {
    const totalCells = this.grid.size
    const totalObjects = Array.from(this.grid.values())
      .reduce((sum, set) => sum + set.size, 0)
    
    return {
      totalCells,
      totalObjects,
      avgObjectsPerCell: totalCells > 0 ? Math.round(totalObjects / totalCells * 100) / 100 : 0
    }
  }
}

// 高性能防抖函数
export const createOptimizedDebounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  options: { leading?: boolean; trailing?: boolean; maxWait?: number } = {}
): T => {
  return debounce(func, delay, {
    leading: options.leading ?? false,
    trailing: options.trailing ?? true,
    maxWait: options.maxWait
  })
}

// 高性能节流函数
export const createOptimizedThrottle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  options: { leading?: boolean; trailing?: boolean } = {}
): T => {
  return throttle(func, delay, {
    leading: options.leading ?? true,
    trailing: options.trailing ?? false
  })
}

// 全局性能监控实例
export const globalPerformanceMonitor = new PerformanceMonitor()
export const globalMemoryMonitor = new MemoryMonitor()
export const globalCacheManager = new CacheManager()

// 创建优化的定时器
export class OptimizedTimer {
  private timers = new Map<string, number>()

  setTimeout(id: string, callback: () => void, delay: number): void {
    this.clearTimeout(id)
    const timer = window.setTimeout(() => {
      callback()
      this.timers.delete(id)
    }, delay)
    this.timers.set(id, timer)
  }

  clearTimeout(id: string): void {
    const timer = this.timers.get(id)
    if (timer) {
      window.clearTimeout(timer)
      this.timers.delete(id)
    }
  }

  setInterval(id: string, callback: () => void, delay: number): void {
    this.clearInterval(id)
    const timer = window.setInterval(callback, delay)
    this.timers.set(id, timer)
  }

  clearInterval(id: string): void {
    const timer = this.timers.get(id)
    if (timer) {
      window.clearInterval(timer)
      this.timers.delete(id)
    }
  }

  clearAll(): void {
    this.timers.forEach(timer => window.clearTimeout(timer))
    this.timers.clear()
  }

  getActiveTimers(): string[] {
    return Array.from(this.timers.keys())
  }
}

export const globalTimer = new OptimizedTimer() 