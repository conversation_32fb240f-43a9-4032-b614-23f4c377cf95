<template>
  <div class="null-data">
    <img src="@/assets/images/noDatax.svg" alt="暂无数据" />
    <p class="box-text">暂无数据</p>
  </div>
</template>

<script>
export default {
  name: 'NullData',
  data() {
    return {}
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.null-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  img {
    width: 100px;
    height: 100px;
  }
  .box-text {
    font-size: 14px;
    color: #999;
  }
}
</style>
