<template>
  <svg 
    class="connection-line"
    :style="{
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
      zIndex: 1
    }"
  >
    <defs>
      <!-- 箭头标记 -->
      <marker
        :id="`arrow-${connection.id}`"
        markerWidth="10"
        markerHeight="10"
        refX="8"
        refY="3"
        orient="auto"
        markerUnits="strokeWidth"
      >
        <polygon
          points="0,0 0,6 9,3"
          :fill="connection.style.arrowColor"
        />
      </marker>
      
      <!-- 流动动画渐变 -->
      <linearGradient
        v-if="connection.animation?.enabled && connection.animation.type === 'flow'"
        :id="`flow-gradient-${connection.id}`"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0%" :stop-color="connection.style.strokeColor" stop-opacity="0.3" />
        <stop offset="50%" :stop-color="connection.style.strokeColor" stop-opacity="1" />
        <stop offset="100%" :stop-color="connection.style.strokeColor" stop-opacity="0.3" />
        <animateTransform
          attributeName="gradientTransform"
          type="translate"
          :values="getFlowAnimationValues()"
          :dur="`${connection.animation.speed}s`"
          repeatCount="indefinite"
        />
      </linearGradient>
    </defs>
    
    <!-- 连接线路径 -->
    <path
      :d="linePath"
      :stroke="getStrokeColor()"
      :stroke-width="connection.style.strokeWidth"
      :stroke-dasharray="connection.style.strokeDasharray"
      fill="none"
      :marker-end="`url(#arrow-${connection.id})`"
      class="connection-path"
      :class="{
        'animated-dash': connection.animation?.enabled && connection.animation.type === 'dash',
        'animated-pulse': connection.animation?.enabled && connection.animation.type === 'pulse'
      }"
      :style="getAnimationStyle()"
    />
    
    <!-- 连接线标签 -->
    <text
      v-if="connection.name"
      :x="labelPosition.x"
      :y="labelPosition.y"
      text-anchor="middle"
      dominant-baseline="middle"
      class="connection-label"
      :style="{
        fontSize: '12px',
        fill: connection.style.strokeColor,
        fontWeight: 'bold'
      }"
    >
      {{ connection.name }}
    </text>
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ComponentConnection, ConfigurationComponent } from '../types'

const props = defineProps<{
  connection: ComponentConnection
  sourceComponent: ConfigurationComponent
  targetComponent: ConfigurationComponent
}>()

// 计算源组件锚点位置
const sourcePoint = computed(() => {
  const comp = props.sourceComponent
  const anchor = props.connection.sourceAnchor
  
  let x = comp.x
  let y = comp.y
  
  switch (anchor.position) {
    case 'top':
      x += comp.width / 2
      break
    case 'right':
      x += comp.width
      y += comp.height / 2
      break
    case 'bottom':
      x += comp.width / 2
      y += comp.height
      break
    case 'left':
      y += comp.height / 2
      break
    case 'center':
      x += comp.width / 2
      y += comp.height / 2
      break
  }
  
  if (anchor.offset) {
    x += anchor.offset.x
    y += anchor.offset.y
  }
  
  return { x, y }
})

// 计算目标组件锚点位置
const targetPoint = computed(() => {
  const comp = props.targetComponent
  const anchor = props.connection.targetAnchor
  
  let x = comp.x
  let y = comp.y
  
  switch (anchor.position) {
    case 'top':
      x += comp.width / 2
      break
    case 'right':
      x += comp.width
      y += comp.height / 2
      break
    case 'bottom':
      x += comp.width / 2
      y += comp.height
      break
    case 'left':
      y += comp.height / 2
      break
    case 'center':
      x += comp.width / 2
      y += comp.height / 2
      break
  }
  
  if (anchor.offset) {
    x += anchor.offset.x
    y += anchor.offset.y
  }
  
  return { x, y }
})

// 计算连接线路径
const linePath = computed(() => {
  const start = sourcePoint.value
  const end = targetPoint.value
  
  switch (props.connection.style.lineType) {
    case 'straight':
      return `M ${start.x} ${start.y} L ${end.x} ${end.y}`
    
    case 'curved':
      const dx = end.x - start.x
      const dy = end.y - start.y
      const controlOffset = Math.abs(dx) * 0.5
      
      return `M ${start.x} ${start.y} C ${start.x + controlOffset} ${start.y}, ${end.x - controlOffset} ${end.y}, ${end.x} ${end.y}`
    
    case 'polyline':
      const midX = start.x + (end.x - start.x) / 2
      return `M ${start.x} ${start.y} L ${midX} ${start.y} L ${midX} ${end.y} L ${end.x} ${end.y}`
    
    default:
      return `M ${start.x} ${start.y} L ${end.x} ${end.y}`
  }
})

// 计算标签位置
const labelPosition = computed(() => {
  const start = sourcePoint.value
  const end = targetPoint.value
  
  return {
    x: start.x + (end.x - start.x) / 2,
    y: start.y + (end.y - start.y) / 2 - 10
  }
})

// 获取描边颜色
const getStrokeColor = () => {
  if (props.connection.animation?.enabled && props.connection.animation.type === 'flow') {
    return `url(#flow-gradient-${props.connection.id})`
  }
  return props.connection.style.strokeColor
}

// 获取流动动画值
const getFlowAnimationValues = () => {
  const start = sourcePoint.value
  const end = targetPoint.value
  const distance = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2))
  
  if (props.connection.animation?.direction === 'backward') {
    return `${distance} 0; 0 0`
  }
  return `0 0; ${distance} 0`
}

// 获取动画样式
const getAnimationStyle = () => {
  if (!props.connection.animation?.enabled) return {}
  
  const speed = props.connection.animation.speed
  
  switch (props.connection.animation.type) {
    case 'dash':
      return {
        animation: `dash-flow ${speed}s linear infinite`
      }
    case 'pulse':
      return {
        animation: `pulse-glow ${speed}s ease-in-out infinite alternate`
      }
    default:
      return {}
  }
}
</script>

<style scoped>
.connection-line {
  pointer-events: none;
}

.connection-path {
  transition: stroke-width 0.2s ease;
}

.connection-path:hover {
  stroke-width: calc(var(--stroke-width, 2) + 1);
}

.connection-label {
  pointer-events: auto;
  cursor: pointer;
}

/* 虚线流动动画 */
@keyframes dash-flow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 20px;
  }
}

.animated-dash {
  stroke-dasharray: 5 5;
  animation: dash-flow 2s linear infinite;
}

/* 脉冲发光动画 */
@keyframes pulse-glow {
  0% {
    stroke-width: 2px;
    opacity: 0.7;
  }
  100% {
    stroke-width: 4px;
    opacity: 1;
  }
}

.animated-pulse {
  animation: pulse-glow 1.5s ease-in-out infinite alternate;
}
</style>