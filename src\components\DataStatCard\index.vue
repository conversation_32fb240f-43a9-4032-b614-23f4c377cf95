<template>
  <el-card class="data-stat-card" shadow="hover">
    <div class="stat-content">
      <div class="stat-icon" :class="`stat-icon--${type}`">
        <el-icon :size="24">
          <Lightning v-if="type === 'daily'" />
          <Calendar v-if="type === 'monthly'" />
          <Clock v-if="type === 'yearly'" />
          <Timer v-if="type === 'cumulative' || type === 'total'" />
        </el-icon>
      </div>
      <div class="stat-info">
        <div class="stat-title">{{ title }}</div>
        <div class="stat-value">{{ value }}</div>
        <div class="stat-unit">{{ unit }}</div>
      </div>
      <div class="stat-trend" v-if="trend">
        <el-icon :size="16" :color="trendColor">
          <ArrowUp v-if="trend > 0" />
          <ArrowDown v-if="trend < 0" />
          <Minus v-if="trend === 0" />
        </el-icon>
        <span class="trend-text" :style="{ color: trendColor }">{{ Math.abs(trend) }}%</span>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts" name="DataStatCard">
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Minus, Lightning, Timer, Calendar, Clock } from '@element-plus/icons-vue'

interface Props {
  title: string
  value: string | number
  unit: string
  type: 'daily' | 'monthly' | 'yearly' | 'cumulative' | 'total'
  trend?: number
}

const props = defineProps<Props>()

const trendColor = computed(() => {
  if (props.trend && props.trend > 0) return '#67C23A'
  if (props.trend && props.trend < 0) return '#F56C6C'
  return '#909399'
})
</script>

<style lang="scss" scoped>
.data-stat-card {
  height: 120px;
  border-radius: 8px;
  border: 1px solid rgba(115, 208, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(8, 42, 77, 0.2);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;

  &:hover {
    box-shadow: 0 4px 12px rgba(51, 221, 255, 0.4);
    transform: translateY(-1px);
    border-color: rgba(115, 208, 255, 0.6);
  }

  .stat-content {
    display: flex;
    align-items: center;
    gap: 12px;
    justify-content: space-between;
    width: 100%;
    padding: 8px 12px;

    .stat-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 8px;
      flex-shrink: 0;

      &--daily {
        background: linear-gradient(135deg, rgba(115, 208, 255, 0.2) 0%, rgba(77, 222, 252, 0.3) 100%);
        .el-icon {
          color: #73d0ff;
        }
      }

      &--cumulative,
      &--total {
        background: linear-gradient(135deg, rgba(180, 208, 252, 0.2) 0%, rgba(171, 209, 255, 0.3) 100%);
        .el-icon {
          color: #b4d0fc;
        }
      }

      &--monthly {
        background: linear-gradient(135deg, rgba(77, 222, 252, 0.2) 0%, rgba(115, 208, 255, 0.3) 100%);
        .el-icon {
          color: #4ddefc;
        }
      }

      &--yearly {
        background: linear-gradient(135deg, rgba(153, 197, 255, 0.2) 0%, rgba(179, 199, 255, 0.3) 100%);
        .el-icon {
          color: #99c5ff;
        }
      }
    }

    .stat-info {
      flex: 1;
      display: flex;
      align-items: baseline;
      gap: 8px;
      min-width: 0;

      .stat-title {
        font-size: 14px;
        color: #b4d0fc;
        white-space: nowrap;
        flex-shrink: 0;
      }

      .stat-value {
        font-size: 20px;
        font-weight: 700;
        color: #73d0ff;
        font-family: 'Arial', sans-serif;
        line-height: 1;
        text-shadow: 0 0 8px rgba(115, 208, 255, 0.5);
        flex-shrink: 0;
      }

      .stat-unit {
        font-size: 10px;
        color: rgba(180, 208, 252, 0.7);
        line-height: 1;
        white-space: nowrap;
        flex-shrink: 0;
      }
    }

    .stat-trend {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-shrink: 0;

      .trend-text {
        font-size: 11px;
        font-weight: 600;
      }
    }
  }
}

:deep(.el-card__body) {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
