// 组态相关类型定义

// 组态项目信息
export interface ConfigurationProject {
  id: string
  name: string
  description?: string
  thumbnail?: string
  width: number
  height: number
  backgroundColor: string
  components: ConfigurationComponent[]
  connections: ComponentConnection[]
  dataBindings: DataBinding[]
  createdAt: string
  updatedAt: string
  createdBy: string
  status: 'draft' | 'published' | 'archived'
}

// 组态组件
export interface ConfigurationComponent {
  id: string
  type: ComponentType
  name: string
  x: number
  y: number
  z?: number
  width: number
  height: number
  rotation: number
  opacity: number
  visible: boolean
  locked: boolean
  style: ComponentStyle
  data: ComponentData
  animation?: ComponentAnimation
  events?: ComponentEvent[]
}

// 组件类型
export type ComponentType = 
  | 'text'           // 文本
  | 'image'          // 图片
  | 'shape'          // 形状
  | 'chart'          // 图表
  | 'gauge'          // 仪表盘
  | 'progress'       // 进度条
  | 'button'         // 按钮
  | 'switch'         // 开关
  | 'input'          // 输入框
  | 'video'          // 视频
  | 'iframe'         // 内嵌页面
  | 'model3d'        // 3D模型
  | 'container'      // 容器

// 组件样式
export interface ComponentStyle {
  backgroundColor?: string
  borderColor?: string
  borderWidth?: number
  borderRadius?: number
  fontSize?: number
  fontColor?: string
  fontWeight?: string
  textAlign?: 'left' | 'center' | 'right'
  boxShadow?: string
  gradient?: {
    type: 'linear' | 'radial'
    colors: string[]
    direction?: number
  }
}

// 组件数据
export interface ComponentData {
  static?: any
  dynamic?: {
  dynamic?: {
    dataSource: string
    field: string
    format?: string
    unit?: string
    value?: any
  }
}

// 组件动画
export interface ComponentAnimation {
  type: 'fade' | 'slide' | 'rotate' | 'scale' | 'bounce'
  duration: number
  delay: number
  repeat: boolean
  trigger: 'load' | 'click' | 'hover' | 'data'
}

// 组件事件
export interface ComponentEvent {
  type: 'click' | 'hover' | 'change'
  action: 'navigate' | 'popup' | 'api' | 'script'
  params: any
}

// 数据绑定
export interface DataBinding {
  id: string
  name: string
  type: 'api' | 'websocket' | 'mqtt' | 'static'
  config: {
    url?: string
    method?: string
    headers?: Record<string, string>
    params?: Record<string, any>
    interval?: number
  }
  fields: DataField[]
}

// 数据字段
export interface DataField {
  key: string
  name: string
  type: 'number' | 'string' | 'boolean' | 'object'
  unit?: string
  format?: string
}

// 编辑器状态
export interface EditorState {
  selectedComponents: string[]
  clipboard: ConfigurationComponent[]
  history: {
    past: ConfigurationProject[]
    present: ConfigurationProject
    future: ConfigurationProject[]
  }
  zoom: number
  grid: {
    show: boolean
    size: number
    snap: boolean
  }
  rulers: boolean
  layers: boolean
}

// 组件库分类
export interface ComponentCategory {
  id: string
  name: string
  icon: string
  components: ComponentTemplate[]
}

// 组件模板
export interface ComponentTemplate {
  id: string
  name: string
  icon: string
  type: ComponentType
  defaultProps: Partial<ConfigurationComponent>
  preview: string
}

// 组件连接线
export interface ComponentConnection {
export interface ComponentConnection {
  id: string
  name: string
  sourceComponent: string
  targetComponent: string
  sourceAnchor: ConnectionAnchor
  targetAnchor: ConnectionAnchor
  style: ConnectionStyle
  animation?: ConnectionAnimation
  data?: any
}

// 连接锚点
export interface ConnectionAnchor {
  position: 'top' | 'right' | 'bottom' | 'left' | 'center'
  offset?: { x: number; y: number }
}

// 连接线样式
export interface ConnectionStyle {
  strokeColor: string
  strokeWidth: number
  strokeDasharray?: string
  arrowSize: number
  arrowColor: string
  lineType: 'straight' | 'curved' | 'polyline'
}

// 连接线动画
export interface ConnectionAnimation {
  enabled: boolean
  type: 'flow' | 'pulse' | 'dash'
  speed: number
  direction: 'forward' | 'backward' | 'bidirectional'
}
