<template>
  <div
    class="component-wrapper"
    :class="{
      'is-selected': selected,
      'is-editing': editing,
      'is-locked': component.locked
    }"
    :style="wrapperStyle"
    :data-component-id="component.id"
    @click.stop="onSelect"
    @mousedown="onMouseDown"
  >
    <!-- 组件内容 -->
    <component
      :is="componentMap[component.type]"
      :component="component"
      :editing="editing"
    />
    
    <!-- 连接点 - 在编辑模式下显示 -->
    <template v-if="editing && !component.locked">
      <!-- 4个连接点 -->
      <div
        v-for="(point, index) in connectionPoints"
        :key="index"
        class="connection-point"
        :class="[point.position, { 'active': activePoint === point.position }]"
        :style="point.style"
        :data-anchor="point.position"
        @mousedown.stop="onConnectionStart($event, point.position)"
        @mouseenter="highlightPoint(point.position)"
        @mouseleave="unhighlightPoint"
      ></div>
    </template>
    
    <!-- 编辑模式下的控制点 - 调整大小和旋转控制点 -->
    <template v-if="editing && selected && !component.locked">
      <!-- 调整大小的控制点 -->
      <div
        v-for="(handle, index) in resizeHandles"
        :key="index"
        class="resize-handle"
        :class="handle.class"
        @mousedown.stop="onResizeStart($event, handle.cursor, handle.class)"
      ></div>

      <!-- 旋转控制点 -->
      <div
        class="rotate-handle"
        @mousedown.stop="onRotateStart"
      >
        <el-icon><Refresh /></el-icon>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import type { ConfigurationComponent } from '../types'
import TextComponent from './components/TextComponent.vue'
import ImageComponent from './components/ImageComponent.vue'
import ShapeComponent from './components/ShapeComponent.vue'
import ChartComponent from './components/ChartComponent.vue'
import GaugeComponent from './components/GaugeComponent.vue'
import ButtonComponent from './components/ButtonComponent.vue'
import Model3dComponent from './components/Model3dComponent.vue'
import HyperbolicCoolingTowerComponent from './components/HyperbolicCoolingTowerComponent.vue'
import MechanicalCoolingTowerComponent from './components/MechanicalCoolingTowerComponent.vue'
import CondenserComponent from './components/CondenserComponent.vue'
import CoolingTower25DComponent from './components/CoolingTower25DComponent.vue'
import Condenser25DComponent from './components/Condenser25DComponent.vue'
import StraightPipeComponent from './components/StraightPipeComponent.vue'
import Custom3DModelComponent from './components/Custom3DModelComponent.vue'

// 组件类型映射
const componentMap = {
  text: TextComponent,
  image: ImageComponent,
  shape: ShapeComponent,
  chart: ChartComponent,
  gauge: GaugeComponent,
  button: ButtonComponent,
  model3d: Model3dComponent,
  hyperbolicCoolingTower: HyperbolicCoolingTowerComponent,
  mechanicalCoolingTower: MechanicalCoolingTowerComponent,
  condenser: CondenserComponent,
  coolingTower25d: CoolingTower25DComponent,
  condenser25d: Condenser25DComponent,
  straightPipe: StraightPipeComponent,
  pipe: StraightPipeComponent,  // 兼容性映射
  custom3dModel: Custom3DModelComponent
}

const props = defineProps<{
  component: ConfigurationComponent
  selected?: boolean
  editing?: boolean
}>()

const emit = defineEmits(['select', 'update', 'startConnection', 'finishConnection', 'updateTempConnection', 'cancelConnection'])

// 当前高亮的连接点
const activePoint = ref<string | null>(null)

// 组件样式
const wrapperStyle = computed(() => {
  return {
    left: `${props.component.x}px`,
    top: `${props.component.y}px`,
    width: `${props.component.width}px`,
    height: `${props.component.height}px`,
    transform: `rotate(${props.component.rotation}deg)`,
    opacity: props.component.opacity,
    zIndex: props.component.z || 0,
    display: props.component.visible ? 'block' : 'none',
    cursor: getCursor()
  }
})

// 获取光标样式
const getCursor = () => {
  if (!props.editing || props.component.locked) return 'default'
  if (props.connectionMode) return 'crosshair'
  return 'move'
}

// 连接点配置 - 只在四个边的中点
const connectionPoints = computed(() => [
  { position: 'top', style: { top: '-4px', left: '50%', transform: 'translateX(-50%)' } },
  { position: 'right', style: { top: '50%', right: '-4px', transform: 'translateY(-50%)' } },
  { position: 'bottom', style: { bottom: '-4px', left: '50%', transform: 'translateX(-50%)' } },
  { position: 'left', style: { top: '50%', left: '-4px', transform: 'translateY(-50%)' } }
])

// 调整大小的控制点 - 只在四个角
const resizeHandles = [
  { class: 'top-left', cursor: 'nwse-resize' },
  { class: 'top-right', cursor: 'nesw-resize' },
  { class: 'bottom-right', cursor: 'nwse-resize' },
  { class: 'bottom-left', cursor: 'nesw-resize' }
]

// 拖拽状态
const isDragging = ref(false)
const isResizing = ref(false)
const isRotating = ref(false)
const startX = ref(0)
const startY = ref(0)
const startWidth = ref(0)
const startHeight = ref(0)
const startRotation = ref(0)
const resizeCursor = ref('')
const resizeHandleClass = ref('')
const startComponentX = ref(0)
const startComponentY = ref(0)

// 选择组件
const onSelect = (event: MouseEvent) => {
  if (props.connectionMode) {
    // 连接模式下，只有在没有点击连接点时才触发连接完成到中心点
    // 这里不应该自动完成连接，应该让用户明确点击连接点
    return
  }

  emit('select', props.component.id, event.shiftKey)
}

// 开始拖拽
const onMouseDown = (event: MouseEvent) => {
  // 在连接模式下不允许拖拽组件
  if (!props.editing || props.component.locked || props.connectionMode) return

  // 如果没有选中，先选中
  if (!props.selected) {
    emit('select', props.component.id, event.shiftKey)
    return
  }

  isDragging.value = true
  startX.value = event.clientX
  startY.value = event.clientY
  startComponentX.value = props.component.x
  startComponentY.value = props.component.y

  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

// 连接拖拽状态
const isConnectionDragging = ref(false)
const connectionStartPosition = ref<string>('')

// 开始连接拖拽
const onConnectionStart = (event: MouseEvent, position: string) => {
  event.stopPropagation()
  event.preventDefault()

  console.log('ComponentRenderer: 开始连接拖拽', props.component.id, position)
  console.log('ComponentRenderer: 组件位置', props.component.x, props.component.y)
  console.log('ComponentRenderer: 组件尺寸', props.component.width, props.component.height)

  isConnectionDragging.value = true
  connectionStartPosition.value = position

  // 计算连接点的实际位置
  const point = getConnectionPointPosition(position)

  console.log('ComponentRenderer: 连接点位置', point)

  // 发送开始连接事件
  emit('startConnection', props.component.id, { point, anchor: position })

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', onConnectionDrag)
  document.addEventListener('mouseup', onGlobalMouseUp)
}

// 连接拖拽过程中
const onConnectionDrag = (event: MouseEvent) => {
  if (!isConnectionDragging.value) return

  console.log('ComponentRenderer: 连接拖拽中', event.clientX, event.clientY)

  // 触发画布的鼠标移动事件来更新临时连接线
  // 这样可以复用现有的画布事件处理逻辑
  const canvasElement = document.querySelector('.editor-canvas')
  if (canvasElement) {
    // 创建一个新的鼠标移动事件，但不使用dispatchEvent避免递归
    // 直接调用画布的事件处理函数
    const canvasRect = canvasElement.getBoundingClientRect()
    const mockEvent = {
      clientX: event.clientX,
      clientY: event.clientY,
      currentTarget: canvasElement
    } as MouseEvent

    // 手动调用画布的鼠标移动处理逻辑
    const point = {
      x: (event.clientX - canvasRect.left),
      y: (event.clientY - canvasRect.top)
    }

    console.log('ComponentRenderer: 更新临时连接点', point)

    // 通过emit传递给父组件
    emit('updateTempConnection', point)
  }
}





// 全局鼠标松开处理 - 检测是否在连接点附近
const onGlobalMouseUp = (event: MouseEvent) => {
  if (!isConnectionDragging.value) return

  console.log('全局鼠标松开，检测连接目标', event.clientX, event.clientY)

  // 获取鼠标位置下的所有元素（包括被遮挡的）
  const elementsUnderMouse = document.elementsFromPoint(event.clientX, event.clientY)
  console.log('鼠标下的所有元素:', elementsUnderMouse.map(el => el.className))

  // 查找连接点元素
  let connectionPointElement = null
  let componentWrapper = null

  for (const element of elementsUnderMouse) {
    if (element.classList.contains('connection-point')) {
      connectionPointElement = element
      componentWrapper = element.closest('.component-wrapper')
      break
    }
  }

  if (connectionPointElement && componentWrapper) {
    console.log('松开在连接点上，完成连接')

    // 获取锚点位置
    const anchor = connectionPointElement.getAttribute('data-anchor')
    const componentId = componentWrapper.getAttribute('data-component-id')

    if (anchor && componentId) {
      // 计算连接点位置
      const point = getConnectionPointPosition(anchor)

      console.log('全局检测完成连接:', componentId, anchor, point)

      // 发送完成连接事件
      emit('finishConnection', componentId, { point, anchor })
    } else {
      console.log('连接点信息不完整，取消连接')
      emit('cancelConnection')
    }
  } else {
    // 备用检测：使用距离计算检测是否在连接点附近
    const nearbyConnectionPoint = findNearbyConnectionPoint(event.clientX, event.clientY)

    if (nearbyConnectionPoint) {
      console.log('备用检测：松开在连接点附近，完成连接')

      const { componentId, anchor, point } = nearbyConnectionPoint
      console.log('备用检测完成连接:', componentId, anchor, point)

      // 发送完成连接事件
      emit('finishConnection', componentId, { point, anchor })
    } else {
      console.log('松开不在连接点上，取消连接')
      // 取消连接
      emit('cancelConnection')
    }
  }

  // 清理拖拽状态
  onConnectionDragEnd()
}

// 查找附近的连接点（备用检测方法）
const findNearbyConnectionPoint = (mouseX: number, mouseY: number) => {
  const threshold = 20 // 20像素的检测范围

  // 获取所有连接点
  const connectionPoints = document.querySelectorAll('.connection-point')

  for (let i = 0; i < connectionPoints.length; i++) {
    const point = connectionPoints[i]
    const rect = point.getBoundingClientRect()
    const pointCenterX = rect.left + rect.width / 2
    const pointCenterY = rect.top + rect.height / 2

    // 计算距离
    const distance = Math.sqrt(
      Math.pow(mouseX - pointCenterX, 2) + Math.pow(mouseY - pointCenterY, 2)
    )

    if (distance <= threshold) {
      // 找到附近的连接点
      const anchor = point.getAttribute('data-anchor')
      const componentWrapper = point.closest('.component-wrapper')
      const componentId = componentWrapper?.getAttribute('data-component-id')

      if (anchor && componentId) {
        const pointPos = getConnectionPointPosition(anchor)
        return {
          componentId,
          anchor,
          point: pointPos
        }
      }
    }
  }

  return null
}

// 清理连接拖拽状态
const onConnectionDragEnd = () => {
  console.log('清理连接拖拽状态')
  isConnectionDragging.value = false
  connectionStartPosition.value = ''

  // 移除全局事件监听
  document.removeEventListener('mousemove', onConnectionDrag)
  document.removeEventListener('mouseup', onGlobalMouseUp)
}

// 获取连接点的实际位置
const getConnectionPointPosition = (position: string) => {
  const { x, y, width, height } = props.component

  switch (position) {
    case 'top':
      return { x: x + width / 2, y }
    case 'right':
      return { x: x + width, y: y + height / 2 }
    case 'bottom':
      return { x: x + width / 2, y: y + height }
    case 'left':
      return { x, y: y + height / 2 }
    default:
      return { x: x + width / 2, y: y + height / 2 }
  }
}

// 高亮连接点
const highlightPoint = (position: string) => {
  activePoint.value = position
}

// 取消高亮连接点
const unhighlightPoint = () => {
  activePoint.value = null
}

// 拖拽移动
const onMouseMove = (event: MouseEvent) => {
  if (isResizing.value) {
    handleResize(event)
  } else if (isRotating.value) {
    handleRotate(event)
  } else if (isDragging.value) {
    const dx = event.clientX - startX.value
    const dy = event.clientY - startY.value
    
    const updatedComponent = { ...props.component }
    updatedComponent.x = startComponentX.value + dx
    updatedComponent.y = startComponentY.value + dy
    
    emit('update', updatedComponent)
  }
}

// 结束拖拽
const onMouseUp = () => {
  isDragging.value = false
  isResizing.value = false
  isRotating.value = false
  
  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', onMouseUp)
}

// 开始调整大小
const onResizeStart = (event: MouseEvent, cursor: string, handleClass?: string) => {
  if (!props.editing || props.component.locked) return

  isResizing.value = true
  resizeCursor.value = cursor
  resizeHandleClass.value = handleClass || ''
  startX.value = event.clientX
  startY.value = event.clientY
  startWidth.value = props.component.width
  startHeight.value = props.component.height
  startComponentX.value = props.component.x
  startComponentY.value = props.component.y

  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

// 处理调整大小
const handleResize = (event: MouseEvent) => {
  const dx = event.clientX - startX.value
  const dy = event.clientY - startY.value

  const updatedComponent = { ...props.component }

  // 根据具体的控制点类名调整大小和位置
  switch (resizeHandleClass.value) {
    case 'top-left':
      // 左上角 - 向左上拖拽时，组件变大，位置向左上移动
      updatedComponent.width = Math.max(10, startWidth.value - dx)
      updatedComponent.height = Math.max(10, startHeight.value - dy)
      updatedComponent.x = startComponentX.value + (startWidth.value - updatedComponent.width)
      updatedComponent.y = startComponentY.value + (startHeight.value - updatedComponent.height)
      break
    case 'top-right':
      // 右上角 - 向右上拖拽时，宽度增加，高度减少，Y位置上移
      updatedComponent.width = Math.max(10, startWidth.value + dx)
      updatedComponent.height = Math.max(10, startHeight.value - dy)
      updatedComponent.y = startComponentY.value + (startHeight.value - updatedComponent.height)
      break
    case 'bottom-right':
      // 右下角 - 向右下拖拽时，组件变大，位置不变
      updatedComponent.width = Math.max(10, startWidth.value + dx)
      updatedComponent.height = Math.max(10, startHeight.value + dy)
      break
    case 'bottom-left':
      // 左下角 - 向左下拖拽时，宽度减少，高度增加，X位置左移
      updatedComponent.width = Math.max(10, startWidth.value - dx)
      updatedComponent.height = Math.max(10, startHeight.value + dy)
      updatedComponent.x = startComponentX.value + (startWidth.value - updatedComponent.width)
      break
  }

  emit('update', updatedComponent)
}

// 开始旋转
const onRotateStart = (event: MouseEvent) => {
  if (!props.editing || props.component.locked) return
  
  isRotating.value = true
  startX.value = event.clientX
  startY.value = event.clientY
  startRotation.value = props.component.rotation
  
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

// 处理旋转
const handleRotate = (event: MouseEvent) => {
  // 计算组件中心点
  const componentCenterX = props.component.x + props.component.width / 2
  const componentCenterY = props.component.y + props.component.height / 2
  
  // 计算鼠标相对于组件中心的角度
  const startAngle = Math.atan2(startY.value - componentCenterY, startX.value - componentCenterX)
  const currentAngle = Math.atan2(event.clientY - componentCenterY, event.clientX - componentCenterX)
  
  // 计算角度差（弧度转度）
  let angleDiff = (currentAngle - startAngle) * (180 / Math.PI)
  
  // 更新组件旋转角度
  const updatedComponent = { ...props.component }
  updatedComponent.rotation = (startRotation.value + angleDiff) % 360
  
  emit('update', updatedComponent)
}

// 组件挂载和卸载时处理事件监听
onMounted(() => {
  // 可以在这里添加其他初始化逻辑
})

onUnmounted(() => {
  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', onMouseUp)
  // 清理连接拖拽事件监听器
  document.removeEventListener('mousemove', onConnectionDrag)
  document.removeEventListener('mouseup', onGlobalMouseUp)
})
</script>

<style scoped>
.component-wrapper {
  position: absolute;
  box-sizing: border-box;
}

.is-editing {
  user-select: none;
}

.is-selected {
  outline: 2px solid var(--el-color-primary);
}

.is-locked {
  cursor: not-allowed !important;
}



/* 连接点样式 */
.connection-point {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: var(--el-color-primary);
  border: 2px solid #fff;
  border-radius: 50%;
  cursor: crosshair;
  opacity: 1;
  transition: all 0.2s;
  z-index: 9999;
  pointer-events: auto;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.component-wrapper:hover .connection-point,
.component-wrapper.is-selected .connection-point {
  opacity: 1;
}

/* 在编辑模式下，连接点始终可见 */
.component-wrapper .connection-point {
  opacity: 0.8;
}

.connection-point:hover {
  background-color: #67c23a;
  transform: scale(1.4);
  box-shadow: 0 0 12px rgba(103, 194, 58, 0.8);
}

.connection-point.active {
  background-color: #67c23a;
  transform: scale(1.3);
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.6);
}

/* 调整大小控制点样式 */
.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: var(--el-color-primary);
  border: 1px solid #fff;
  border-radius: 50%;
}

.top-left {
  top: -4px;
  left: -4px;
  cursor: nwse-resize;
}

.top-right {
  top: -4px;
  right: -4px;
  cursor: nesw-resize;
}

.bottom-right {
  bottom: -4px;
  right: -4px;
  cursor: nwse-resize;
}

.bottom-left {
  bottom: -4px;
  left: -4px;
  cursor: nesw-resize;
}

.rotate-handle {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background-color: var(--el-color-primary);
  border: 1px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: grab;
}

.rotate-handle:active {
  cursor: grabbing;
}
</style>