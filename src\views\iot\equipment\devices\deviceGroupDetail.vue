<template>
  <yt-crud
    ref="crudRef"
    :data="data"
    :column="column"
    :loading="state.loading"
    :total="state.total"
    v-model:page="state.page"
    v-model:query="state.query"
    :tableProps=" {
    selection: true,
    viewBtn: false,
    editBtn: false,
    delBtn: false,
    menuSlot: true,
    menuWidth: 300,
    dialogBtn:false
  }"
    :funProps="{
    addBtn: false,
    delBtn: true,
    delBtnText: '批量退组'
  }"
    @on-load="getData"
    @delFun="onDelete"
  >
    <template #menuSlot="scope">
      <el-button type="text" size="small" @click="removeFromGroup(scope.row.id)">退组</el-button>
    </template>

    <template #online="scope">
      <el-tag class="state" v-if="scope.row.online === true" type="success" size="small">在线</el-tag>
      <el-tag class="state" v-else-if="scope.row.online === false" type="danger" size="small">离线</el-tag>
    </template>

    <template #group="scope">
      <el-tag v-for="i,k in scope.row.group" :key="k">{{ i.name }}</el-tag>
    </template>
  </yt-crud>
</template>
<script lang="ts" setup>
import { IColumn } from '@/components/common/types/tableCommon'

import YtCrud from '@/components/common/yt-crud.vue'

import { getDevicesList, removeDeviceFromDeviceGroup } from '../api/devices.api'
import { getProductsList, IProductsVO } from '../api/products.api'

const route = useRoute()
const { id } = route.params

const column = ref<IColumn[]>([
  {
    label: '设备ID',
    key: 'deviceId',
  },
  {
    label: '产品',
    key: 'productKey',
    type: 'select',
    search: true,
    colSpan: 12,
    tableWidth: 120,
    editDisabled: true,
    componentProps: {
      labelAlias: 'name',
      valueAlias: 'productKey',
      options: [],
    },
  },
  {
    label: '设备DN',
    key: 'deviceName',
    tableWidth: 240,
    search: true
  },
  {
    label: '分组',
    key: 'group',
    tableWidth: 240,
    slot: true,
  },
  {
    label: '状态',
    key: 'online',
    type: 'select',
    componentProps: {
      options: [
        {
          label: '在线',
          value: true,
        },
        {
          label: '离线',
          value: false,
        },
      ],
    },
    search: false,
    tableWidth: 80,
    slot: true,
  },
  {
    label: '创建时间',
    key: 'createAt',
    tableWidth: 180,
    sortable: true,
    type: 'date',
  },
])

const data = ref()

const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {
    group: id
  },
})

// 产品字典
const productOptions = ref<IProductsVO[]>([])
const getDict = () => {
  getProductsList({
    pageNum: 1,
    pageSize: 99999,
  }).then((res) => {
    productOptions.value = res.data.rows || []
    column.value.forEach((item) => {
      if (item.key === 'productKey') {
        item.componentProps.options = productOptions.value
      }
    })
  })
}
getDict()

const getData = () => {
  state.loading = true
  getDevicesList({
    ...state.page,
    ...state.query}).then(res => {
      data.value = res.data.rows
      state.total = res.data.total
  }).finally(() => state.loading = false)
}


// removeFromGroup 退组
const removeFromGroup = (deviceId: string) => {

  removeDeviceFromDeviceGroup({group: id, devices: [deviceId]}).then(res => {
    // 退组成功
    if (res.code == 200 ) {
      ElMessage.success('退组成功!')
      getData()
    }
  })
}


// 删除
const onDelete = async (row: any) => {
  state.loading = true
  if (row instanceof Array) {
    await removeDeviceFromDeviceGroup({group: id, devices: row.map((m) => m.id)})
  } else {
    await removeDeviceFromDeviceGroup({group: id, devices: [row.id]})
  }
  ElMessage.success('退组成功!')
  state.loading = false
  getData()
}
</script>

<style lang="scss" scoped>
:deep(.el-select__wrapper){
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
</style>

<style scoped>
 :deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  border: none;
}
:deep(.el-card__header) {
  border: none;
}
:deep(.el-table,
.el-table__expanded-cell ){
  background-color: transparent !important;
}
:deep(.el-table__body tr,
.el-table__body td) {
  padding: 0;
  height: 40px;
}
:deep(.el-table tr ){
  border: none;
  background-color: transparent;
}
:deep(.el-table th) {
  /* background-color: transparent; */
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}
:deep(.el-table) {
  --el-table-border-color: none;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row) {
  /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
  /* 取消单元格背景色，确保整行背景色生效 */
}
:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;

  color: #ffffff;
}
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}
</style>
