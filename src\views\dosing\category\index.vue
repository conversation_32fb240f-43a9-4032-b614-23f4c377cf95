<template>
  <yt-crud
    ref="crudRef"
    :data="data"
    :column="column"
    :table-props="{
        selection: false,//多选
        dialogBtn:false,
        menuSlot: true,//自定义操作按钮
      }"
    :form-props="{
        width: 550,
        labelWidth:150
      }"
    @save-fun="onSave"
    @del-fun="handleDelete"
    @onLoad="getData"
    :loading="state.loading"
    :total="state.total"
    v-model:page="state.page"
    v-model:query="state.query"
  >
  </yt-crud>
</template>

<script lang="ts" setup>
import {addpharmaceutical,getPharmaceuticalList,editPharmaceutical,deletePharmaceutical} from './index.api'
import { IColumn } from '@/components/common/types/tableCommon'
import YtCrud from '@/components/common/yt-crud.vue'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
import { log } from 'console'
import { el } from 'element-plus/es/locale'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

// import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
emitter.on('projectListChanged', (e) => {
  location.reload()
})

const data = ref([
])

const column = ref<IColumn[]>([
    {
    label: '药剂名称',
    key: 'name',
    search: true,
    rules: [{ required: true, message: '药剂名称不能为空' }],
    align: 'center',
  },
  {
    label: '存量(kg)',
    key: 'volume',
    // search: true,
    align: 'center',
  },
])
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})
const onSave = ({ type, data, cancel }: any) => {
    const { id: projectId } = cachedProjects
    const modifiedData = {
    ...data,
    projectId,
  }
    if(type == 'add'){
        addpharmaceutical(modifiedData).then(res=>{
            if(res.code == 200){
                ElMessage.success('添加成功')
                cancel()
                getData()
            }
        })
    }else if(type == 'update'){
        editPharmaceutical(modifiedData).then(res=>{
            if(res.code == 200){
                ElMessage.success('修改成功')
                cancel()
                getData()
            }
        })
    }
}
const { id: projectId } = cachedProjects
const handleDelete = (row: any) => {
    console.log(row,'===')
    deletePharmaceutical([row.id]).then(res=>{
        if(res.code == 200){
            ElMessage.success('删除成功')
            getData()
        }
    })
}
const getData = () => {
  state.loading = true
  const project = { ...state.query, projectId }
  getPharmaceuticalList( project).then(res=>{
    state.loading = false
    state.total = res.data.total
    data.value = res.data.rows
  })
}
</script>

<style scoped>
:deep(.el-select__wrapper){
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
</style>
