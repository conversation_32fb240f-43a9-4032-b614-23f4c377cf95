<template>
  <div class="model-editor">
    <div class="editor-layout">
      <!-- 左侧：设备类型列表 -->
      <div class="device-types-panel">
        <div class="panel-header">
          <h3>设备类型</h3>
          <el-button type="primary" size="small" @click="showAddTypeDialog = true">
            <el-icon><Plus /></el-icon>
            新建类型
          </el-button>
        </div>

        <div class="device-type-list">
          <div
            v-for="deviceType in deviceTypes"
            :key="deviceType.id"
            class="device-type-item"
            :class="{ active: selectedDeviceType?.id === deviceType.id }"
            @click="selectDeviceType(deviceType)"
          >
            <div class="type-icon" :style="{ backgroundColor: deviceType.color }">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="type-info">
              <div class="type-name">{{ deviceType.name }}</div>
              <div class="type-desc">{{ deviceType.description }}</div>
            </div>
            <div class="type-actions">
              <el-button size="small" text @click.stop="editDeviceType(deviceType)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button size="small" text @click.stop="deleteDeviceType(deviceType)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：3D编辑器 -->
      <div class="model-editor-panel">
        <div class="editor-toolbar">
          <div class="toolbar-left">
            <span class="current-editing"> 编辑模型: {{ selectedDeviceType?.name || '请选择设备类型' }} </span>
          </div>
          <div class="toolbar-right">
            <el-button-group>
              <el-button :type="viewMode === 'edit' ? 'primary' : ''" @click="viewMode = 'edit'"> 编辑模式 </el-button>
              <el-button :type="viewMode === 'preview' ? 'primary' : ''" @click="viewMode = 'preview'"> 预览模式 </el-button>
            </el-button-group>
            <el-button @click="resetModel">重置</el-button>
            <el-button type="primary" @click="saveModel" :disabled="!selectedDeviceType"> 保存模型 </el-button>
          </div>
        </div>

        <!-- 3D场景容器 -->
        <div ref="editorContainer" class="editor-container"></div>
      </div>

      <!-- 右侧：模型属性面板 -->
      <div class="properties-panel">
        <div class="panel-header">
          <h3>模型属性</h3>
        </div>

        <div v-if="selectedDeviceType" class="properties-content">
          <!-- 基本属性 -->
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="基本属性" name="basic">
              <el-form label-width="80px" size="small">
                <el-form-item label="几何形状">
                  <el-select v-model="modelConfig.geometry" @change="updateGeometry">
                    <el-option label="立方体" value="box" />
                    <el-option label="圆柱体" value="cylinder" />
                    <el-option label="球体" value="sphere" />
                    <el-option label="圆锥体" value="cone" />
                    <el-option label="环形体" value="torus" />
                  </el-select>
                </el-form-item>

                <el-form-item label="尺寸">
                  <div class="size-inputs">
                    <el-input-number v-model="modelConfig.size.width" :min="0.1" :max="5" :step="0.1" size="small" />
                    <el-input-number v-model="modelConfig.size.height" :min="0.1" :max="5" :step="0.1" size="small" />
                    <el-input-number v-model="modelConfig.size.depth" :min="0.1" :max="5" :step="0.1" size="small" />
                  </div>
                </el-form-item>
              </el-form>
            </el-collapse-item>

            <el-collapse-item title="材质属性" name="material">
              <el-form label-width="80px" size="small">
                <el-form-item label="基础颜色">
                  <el-color-picker v-model="modelConfig.material.color" @change="updateMaterial" />
                </el-form-item>

                <el-form-item label="金属度">
                  <el-slider v-model="modelConfig.material.metalness" :min="0" :max="1" :step="0.1" @change="updateMaterial" />
                </el-form-item>

                <el-form-item label="粗糙度">
                  <el-slider v-model="modelConfig.material.roughness" :min="0" :max="1" :step="0.1" @change="updateMaterial" />
                </el-form-item>

                <el-form-item label="透明度">
                  <el-slider v-model="modelConfig.material.opacity" :min="0" :max="1" :step="0.1" @change="updateMaterial" />
                </el-form-item>
              </el-form>
            </el-collapse-item>

            <el-collapse-item title="状态指示" name="status">
              <el-form label-width="80px" size="small">
                <el-form-item label="显示状态">
                  <el-switch v-model="modelConfig.showStatus" @change="updateStatusDisplay" />
                </el-form-item>

                <el-form-item label="正常状态" v-if="modelConfig.showStatus">
                  <el-color-picker v-model="modelConfig.statusColors.normal" />
                </el-form-item>

                <el-form-item label="警告状态" v-if="modelConfig.showStatus">
                  <el-color-picker v-model="modelConfig.statusColors.warning" />
                </el-form-item>

                <el-form-item label="故障状态" v-if="modelConfig.showStatus">
                  <el-color-picker v-model="modelConfig.statusColors.error" />
                </el-form-item>
              </el-form>
            </el-collapse-item>

            <el-collapse-item title="动画效果" name="animation">
              <el-form label-width="80px" size="small">
                <el-form-item label="旋转动画">
                  <el-switch v-model="modelConfig.animation.rotate" @change="updateAnimation" />
                </el-form-item>

                <el-form-item label="呼吸效果">
                  <el-switch v-model="modelConfig.animation.pulse" @change="updateAnimation" />
                </el-form-item>

                <el-form-item label="浮动效果">
                  <el-switch v-model="modelConfig.animation.float" @change="updateAnimation" />
                </el-form-item>
              </el-form>
            </el-collapse-item>
          </el-collapse>

          <!-- 预设模板 -->
          <div class="preset-templates">
            <h4>预设模板</h4>
            <div class="template-grid">
              <div v-for="template in modelTemplates" :key="template.id" class="template-item" @click="applyTemplate(template)">
                <div class="template-preview" :style="{ backgroundColor: template.color }"></div>
                <span class="template-name">{{ template.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-selection">请选择一个设备类型开始编辑</div>
      </div>
    </div>

    <!-- 新建设备类型对话框 -->
    <el-dialog v-model="showAddTypeDialog" title="新建设备类型" width="500px">
      <el-form :model="typeForm" :rules="typeRules" ref="typeFormRef" label-width="100px">
        <el-form-item label="类型标识" prop="id">
          <el-input v-model="typeForm.id" placeholder="如: pump, tank, valve" />
        </el-form-item>
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="typeForm.name" placeholder="如: 水泵设备" />
        </el-form-item>
        <el-form-item label="类型描述">
          <el-input v-model="typeForm.description" type="textarea" placeholder="设备类型的详细描述" />
        </el-form-item>
        <el-form-item label="主题颜色">
          <el-color-picker v-model="typeForm.color" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddTypeDialog = false">取消</el-button>
        <el-button type="primary" @click="addDeviceType">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Plus, Monitor, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

interface DeviceType {
  id: string
  name: string
  description: string
  color: string
  modelConfig?: ModelConfig
}

interface ModelConfig {
  geometry: string
  size: { width: number; height: number; depth: number }
  material: {
    color: string
    metalness: number
    roughness: number
    opacity: number
  }
  showStatus: boolean
  statusColors: {
    normal: string
    warning: string
    error: string
  }
  animation: {
    rotate: boolean
    pulse: boolean
    float: boolean
  }
}

interface ModelTemplate {
  id: string
  name: string
  color: string
  config: Partial<ModelConfig>
}

// 3D场景相关
const editorContainer = ref<HTMLElement>()
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
let currentModel: THREE.Object3D | null = null
let animationId: number

// 状态管理
const selectedDeviceType = ref<DeviceType | null>(null)
const viewMode = ref<'edit' | 'preview'>('edit')
const activeCollapse = ref(['basic', 'material'])
const showAddTypeDialog = ref(false)

// 设备类型数据
const deviceTypes = ref<DeviceType[]>([
  {
    id: 'pump',
    name: '水泵设备',
    description: '用于液体输送和循环',
    color: '#4CAF50'
  },
  {
    id: 'tank',
    name: '储罐设备',
    description: '用于液体储存',
    color: '#2196F3'
  },
  {
    id: 'valve',
    name: '阀门设备',
    description: '用于流量控制',
    color: '#FF9800'
  },
  {
    id: 'sensor',
    name: '传感器设备',
    description: '用于数据采集',
    color: '#9C27B0'
  },
  {
    id: 'pipe',
    name: '管道设备',
    description: '用于介质传输',
    color: '#607D8B'
  }
])

// 模型配置
const modelConfig = ref<ModelConfig>({
  geometry: 'cylinder',
  size: { width: 1, height: 1.5, depth: 1 },
  material: {
    color: '#4CAF50',
    metalness: 0.3,
    roughness: 0.4,
    opacity: 1.0
  },
  showStatus: true,
  statusColors: {
    normal: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336'
  },
  animation: {
    rotate: false,
    pulse: false,
    float: false
  }
})

// 表单数据
const typeForm = ref({
  id: '',
  name: '',
  description: '',
  color: '#409EFF'
})

const typeFormRef = ref()

const typeRules = {
  id: [{ required: true, message: '请输入类型标识', trigger: 'blur' }],
  name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }]
}

// 预设模板
const modelTemplates: ModelTemplate[] = [
  {
    id: 'cooling_tower_template',
    name: '冷却塔',
    color: '#2E7D32',
    config: {
      geometry: 'cylinder',
      size: { width: 3.5, height: 8.0, depth: 3.5 },
      material: { color: '#388E3C', metalness: 0.1, roughness: 0.8 },
      animation: { rotate: false, pulse: true, float: false }
    }
  },
  {
    id: 'water_pump_template',
    name: '水泵',
    color: '#1976D2',
    config: {
      geometry: 'cylinder',
      size: { width: 1.8, height: 1.2, depth: 1.8 },
      material: { color: '#1976D2', metalness: 0.7, roughness: 0.3 },
      animation: { rotate: true, pulse: false, float: false }
    }
  },
  {
    id: 'condenser_template',
    name: '凝汽器',
    color: '#424242',
    config: {
      geometry: 'box',
      size: { width: 4.0, height: 2.5, depth: 1.8 },
      material: { color: '#616161', metalness: 0.8, roughness: 0.2 },
      animation: { rotate: false, pulse: false, float: false }
    }
  },
  {
    id: 'pipeline_template',
    name: '管道',
    color: '#795548',
    config: {
      geometry: 'cylinder',
      size: { width: 0.6, height: 6.0, depth: 0.6 },
      material: { color: '#8D6E63', metalness: 0.6, roughness: 0.4 },
      animation: { rotate: false, pulse: false, float: false }
    }
  },
  {
    id: 'electrical_switch_template',
    name: '电闸',
    color: '#FFC107',
    config: {
      geometry: 'box',
      size: { width: 1.2, height: 1.8, depth: 0.8 },
      material: { color: '#FFD54F', metalness: 0.3, roughness: 0.6 },
      animation: { rotate: false, pulse: true, float: false }
    }
  },
  {
    id: 'steam_turbine_template',
    name: '汽轮机',
    color: '#E65100',
    config: {
      geometry: 'cylinder',
      size: { width: 2.8, height: 4.5, depth: 2.8 },
      material: { color: '#FF6F00', metalness: 0.9, roughness: 0.1 },
      animation: { rotate: true, pulse: false, float: false }
    }
  },
  {
    id: 'vacuum_pump_template',
    name: '真空泵',
    color: '#7B1FA2',
    config: {
      geometry: 'cylinder',
      size: { width: 1.5, height: 2.2, depth: 1.5 },
      material: { color: '#8E24AA', metalness: 0.6, roughness: 0.4 },
      animation: { rotate: true, pulse: true, float: false }
    }
  }
]

onMounted(() => {
  nextTick(() => {
    initThreeJS()
  })
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  if (renderer) {
    renderer.dispose()
  }
})

// 初始化Three.js场景
function initThreeJS() {
  if (!editorContainer.value) return

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf5f5f5)

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    editorContainer.value.clientWidth / editorContainer.value.clientHeight,
    0.1,
    1000
  )
  camera.position.set(5, 5, 5)

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(editorContainer.value.clientWidth, editorContainer.value.clientHeight)
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  editorContainer.value.appendChild(renderer.domElement)

  // 创建控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true

  // 添加光源
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(10, 10, 5)
  directionalLight.castShadow = true
  scene.add(directionalLight)

  // 添加地面
  const groundGeometry = new THREE.PlaneGeometry(10, 10)
  const groundMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff })
  const ground = new THREE.Mesh(groundGeometry, groundMaterial)
  ground.rotation.x = -Math.PI / 2
  ground.receiveShadow = true
  scene.add(ground)

  // 开始渲染循环
  animate()
}

// 渲染循环
function animate() {
  animationId = requestAnimationFrame(animate)
  controls.update()
  renderer.render(scene, camera)
}

// 选择设备类型
function selectDeviceType(deviceType: DeviceType) {
  selectedDeviceType.value = deviceType

  // 加载该类型的模型配置
  if (deviceType.modelConfig) {
    modelConfig.value = { ...deviceType.modelConfig }
  } else {
    // 使用默认配置
    modelConfig.value.material.color = deviceType.color
    modelConfig.value.statusColors.normal = deviceType.color
  }

  createModel()
}

// 创建3D模型
function createModel() {
  if (!scene) return

  // 移除现有模型
  if (currentModel) {
    scene.remove(currentModel)
  }

  const config = modelConfig.value
  let geometry: THREE.BufferGeometry

  // 根据几何类型创建geometry
  switch (config.geometry) {
    case 'box':
      geometry = new THREE.BoxGeometry(config.size.width, config.size.height, config.size.depth)
      break
    case 'cylinder':
      geometry = new THREE.CylinderGeometry(
        config.size.width / 2,
        config.size.width / 2,
        config.size.height,
        32
      )
      break
    case 'sphere':
      geometry = new THREE.SphereGeometry(config.size.width / 2, 32, 16)
      break
    case 'cone':
      geometry = new THREE.ConeGeometry(config.size.width / 2, config.size.height, 32)
      break
    case 'torus':
      geometry = new THREE.TorusGeometry(config.size.width / 2, config.size.depth / 4, 16, 100)
      break
    default:
      geometry = new THREE.CylinderGeometry(0.5, 0.5, 1, 32)
  }

  // 创建材质
  const material = new THREE.MeshStandardMaterial({
    color: config.material.color,
    metalness: config.material.metalness,
    roughness: config.material.roughness,
    opacity: config.material.opacity,
    transparent: config.material.opacity < 1
  })

  // 创建网格
  currentModel = new THREE.Mesh(geometry, material)
  currentModel.position.y = config.size.height / 2
  currentModel.castShadow = true
  scene.add(currentModel)
}

// 更新几何形状
function updateGeometry() {
  createModel()
}

// 更新材质
function updateMaterial() {
  if (currentModel && currentModel instanceof THREE.Mesh) {
    const material = currentModel.material as THREE.MeshStandardMaterial
    const config = modelConfig.value.material

    material.color.setStyle(config.color)
    material.metalness = config.metalness
    material.roughness = config.roughness
    material.opacity = config.opacity
    material.transparent = config.opacity < 1
  }
}

// 更新状态显示
function updateStatusDisplay() {
  // 实现状态指示器的显示逻辑
  console.log('更新状态显示:', modelConfig.value.showStatus)
}

// 更新动画
function updateAnimation() {
  // 实现动画效果的逻辑
  console.log('更新动画:', modelConfig.value.animation)
}

// 应用预设模板
function applyTemplate(template: ModelTemplate) {
  Object.assign(modelConfig.value, template.config)
  createModel()
  ElMessage.success(`已应用模板: ${template.name}`)
}

// 重置模型
function resetModel() {
  if (selectedDeviceType.value) {
    modelConfig.value = {
      geometry: 'cylinder',
      size: { width: 1, height: 1.5, depth: 1 },
      material: {
        color: selectedDeviceType.value.color,
        metalness: 0.3,
        roughness: 0.4,
        opacity: 1.0
      },
      showStatus: true,
      statusColors: {
        normal: selectedDeviceType.value.color,
        warning: '#FF9800',
        error: '#F44336'
      },
      animation: {
        rotate: false,
        pulse: false,
        float: false
      }
    }
    createModel()
    ElMessage.success('模型已重置')
  }
}

// 保存模型
function saveModel() {
  if (selectedDeviceType.value) {
    selectedDeviceType.value.modelConfig = { ...modelConfig.value }

    // 这里应该保存到服务器或本地存储
    const savedModels = JSON.parse(localStorage.getItem('deviceModels') || '{}')
    savedModels[selectedDeviceType.value.id] = modelConfig.value
    localStorage.setItem('deviceModels', JSON.stringify(savedModels))

    ElMessage.success('模型保存成功')
  }
}

// 编辑设备类型
function editDeviceType(deviceType: DeviceType) {
  typeForm.value = {
    id: deviceType.id,
    name: deviceType.name,
    description: deviceType.description,
    color: deviceType.color
  }
  showAddTypeDialog.value = true
}

// 删除设备类型
function deleteDeviceType(deviceType: DeviceType) {
  ElMessageBox.confirm(
    `确定要删除设备类型 "${deviceType.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = deviceTypes.value.findIndex(t => t.id === deviceType.id)
    if (index > -1) {
      deviceTypes.value.splice(index, 1)
      if (selectedDeviceType.value?.id === deviceType.id) {
        selectedDeviceType.value = null
        if (currentModel) {
          scene.remove(currentModel)
          currentModel = null
        }
      }
      ElMessage.success('设备类型删除成功')
    }
  }).catch(() => {
    // 用户取消
  })
}

// 添加设备类型
function addDeviceType() {
  typeFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      const newType: DeviceType = {
        id: typeForm.value.id,
        name: typeForm.value.name,
        description: typeForm.value.description,
        color: typeForm.value.color
      }

      deviceTypes.value.push(newType)
      showAddTypeDialog.value = false

      // 重置表单
      typeForm.value = {
        id: '',
        name: '',
        description: '',
        color: '#409EFF'
      }

      ElMessage.success('设备类型添加成功')
    }
  })
}
</script>

<style scoped>
::v-deep(.el-form-item__label){
    color: #8bee0a !important;
}
.model-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.editor-layout {
  flex: 1;
  display: flex;
}

.device-types-panel {
  width: 300px;
  border-right: 1px solid #e4e7ed;
  background: #fafafa;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.device-type-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.device-type-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.device-type-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.device-type-item.active {
  border-color: #409EFF;
  background: #f0f8ff;
}

.type-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
}

.type-info {
  flex: 1;
}

.type-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.type-desc {
  font-size: 12px;
  color: #666;
}

.type-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.device-type-item:hover .type-actions {
  opacity: 1;
}

.model-editor-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-editing {
  font-weight: 500;
  color: #333;
}

.toolbar-right {
  display: flex;
  gap: 10px;
  align-items: center;
}

.editor-container {
  flex: 1;
  position: relative;
  background: #f5f5f5;
}

.properties-panel {
  width: 350px;
  border-left: 1px solid #e4e7ed;
  background: #fafafa;
  display: flex;
  flex-direction: column;
}

.properties-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.size-inputs {
  display: flex;
  gap: 8px;
}

.size-inputs .el-input-number {
  flex: 1;
}

.preset-templates {
  margin-top: 20px;
}

.preset-templates h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.template-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.template-preview {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  margin-bottom: 8px;
}

.template-name {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.no-selection {
  padding: 40px 20px;
  text-align: center;
  color: #999;
}
</style>
