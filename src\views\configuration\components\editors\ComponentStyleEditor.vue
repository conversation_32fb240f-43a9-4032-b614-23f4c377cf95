<template>
  <div class="style-editor">
    <el-form label-position="top" size="small">
      <!-- 背景颜色 -->
      <el-form-item label="背景颜色">
        <el-color-picker 
          v-model="localStyle.backgroundColor" 
          show-alpha
          @change="updateStyle"
        />
      </el-form-item>
      
      <!-- 边框 -->
      <el-form-item label="边框">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-input-number 
              v-model="localStyle.borderWidth" 
              :min="0" 
              :max="10"
              controls-position="right"
              placeholder="宽度"
              @change="updateStyle"
              class="dark-text"
            />
          </el-col>
          <el-col :span="12">
            <el-color-picker 
              v-model="localStyle.borderColor" 
              show-alpha
              @change="updateStyle"
            />
          </el-col>
        </el-row>
      </el-form-item>
      
      <!-- 圆角 -->
      <el-form-item label="圆角">
        <el-slider 
          v-model="localStyle.borderRadius" 
          :min="0" 
          :max="50" 
          :step="1"
          show-input
          @change="updateStyle"
          class="dark-text"
        />
      </el-form-item>
      
      <!-- 文本样式 (仅对文本类组件显示) -->
      <template v-if="['text', 'button'].includes(component.type)">
        <el-form-item label="字体大小">
          <el-input-number
            v-model="localStyle.fontSize"
            :min="8"
            :max="72"
            controls-position="right"
            @change="updateStyle"
            class="dark-text"
          />
        </el-form-item>

        <el-form-item label="字体颜色">
          <el-color-picker
            v-model="localStyle.fontColor"
            show-alpha
            @change="updateStyle"
          />
        </el-form-item>

        <el-form-item label="字体粗细">
          <el-select v-model="localStyle.fontWeight" @change="updateStyle" class="dark-text">
            <el-option label="正常" value="normal" />
            <el-option label="粗体" value="bold" />
            <el-option label="细体" value="lighter" />
          </el-select>
        </el-form-item>

        <el-form-item label="文本对齐">
          <el-radio-group v-model="localStyle.textAlign" @change="updateStyle" class="dark-text">
            <el-radio-button label="left">左对齐</el-radio-button>
            <el-radio-button label="center">居中</el-radio-button>
            <el-radio-button label="right">右对齐</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </template>

      <!-- 管道样式 (仅对管道类组件显示) -->
      <template v-if="['straightPipe', 'pipe'].includes(component.type)">
        <el-form-item label="管道颜色">
          <el-color-picker
            v-model="localStyle.pipeColor"
            show-alpha
            @change="updateStyle"
          />
        </el-form-item>

        <el-form-item label="管道暗色">
          <el-color-picker
            v-model="localStyle.pipeDarkColor"
            show-alpha
            @change="updateStyle"
          />
        </el-form-item>

        <el-form-item label="管道亮色">
          <el-color-picker
            v-model="localStyle.pipeLightColor"
            show-alpha
            @change="updateStyle"
          />
        </el-form-item>

        <el-form-item label="流体颜色">
          <el-color-picker
            v-model="localStyle.fluidColor"
            show-alpha
            @change="updateStyle"
          />
        </el-form-item>

        <el-form-item label="端口颜色">
          <el-color-picker
            v-model="localStyle.portColor"
            show-alpha
            @change="updateStyle"
          />
        </el-form-item>

        <!-- SVG管道特有样式 -->
        <template v-if="component.type === 'svgPipe'">
          <el-form-item label="流体透明度">
            <el-slider
              v-model="localStyle.fluidOpacity"
              :min="0"
              :max="1"
              :step="0.1"
              show-input
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="显示法兰">
            <el-switch
              v-model="localStyle.showFlanges"
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="显示流向箭头">
            <el-switch
              v-model="localStyle.showFlowArrows"
              @change="updateStyle"
            />
          </el-form-item>
        </template>
      </template>

      <!-- 工业组件样式 (储罐、泵、阀门) -->
      <template v-if="component.type === 'model3d'">
        <!-- 储罐样式 -->
        <template v-if="component.data?.static?.model === 'tank'">
          <el-form-item label="储罐颜色">
            <el-color-picker
              v-model="localStyle.tankColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="储罐顶部颜色">
            <el-color-picker
              v-model="localStyle.tankTopColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="储罐底部颜色">
            <el-color-picker
              v-model="localStyle.tankBottomColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="液体颜色">
            <el-color-picker
              v-model="localStyle.liquidColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="透明度">
            <el-slider
              v-model="localStyle.tankOpacity"
              :min="0"
              :max="1"
              :step="0.1"
              show-input
              @change="updateStyle"
            />
          </el-form-item>
        </template>

        <!-- 水泵样式 -->
        <template v-if="component.data?.static?.model === 'pump'">
          <el-form-item label="泵体颜色">
            <el-color-picker
              v-model="localStyle.pumpBodyColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="管道颜色">
            <el-color-picker
              v-model="localStyle.pumpPipeColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="运行状态颜色">
            <el-color-picker
              v-model="localStyle.pumpRunningColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="停止状态颜色">
            <el-color-picker
              v-model="localStyle.pumpStoppedColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>
        </template>

        <!-- 阀门样式 -->
        <template v-if="component.data?.static?.model === 'valve'">
          <el-form-item label="阀体颜色">
            <el-color-picker
              v-model="localStyle.valveBodyColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="开启状态颜色">
            <el-color-picker
              v-model="localStyle.valveOpenColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="关闭状态颜色">
            <el-color-picker
              v-model="localStyle.valveClosedColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>

          <el-form-item label="管道颜色">
            <el-color-picker
              v-model="localStyle.valvePipeColor"
              show-alpha
              @change="updateStyle"
            />
          </el-form-item>
        </template>
      </template>
      
      <!-- 阴影 -->
      <el-form-item label="阴影">
        <el-switch 
          v-model="shadowEnabled" 
          @change="updateShadow"
          class="dark-text"
        />
        
        <template v-if="shadowEnabled">
          <el-row :gutter="10" class="shadow-controls">
            <el-col :span="12">
              <el-form-item label="模糊度">
                <el-slider 
                  v-model="shadowBlur" 
                  :min="0" 
                  :max="20" 
                  :step="1"
                  @change="updateShadow"
                  class="dark-text"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="颜色">
                <el-color-picker 
                  v-model="shadowColor" 
                  show-alpha
                  @change="updateShadow"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-form-item>
      
      <!-- 渐变 -->
      <el-form-item label="渐变背景">
        <el-switch 
          v-model="gradientEnabled" 
          @change="updateGradient"
          class="dark-text"
        />
        
        <template v-if="gradientEnabled">
          <el-form-item label="渐变类型">
            <el-radio-group v-model="gradientType" @change="updateGradient" class="dark-text">
              <el-radio-button label="linear">线性</el-radio-button>
              <el-radio-button label="radial">径向</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item v-if="gradientType === 'linear'" label="方向">
            <el-slider 
              v-model="gradientDirection" 
              :min="0" 
              :max="360" 
              :step="15"
              show-input
              @change="updateGradient"
              class="dark-text"
            />
          </el-form-item>
          
          <el-form-item label="颜色">
            <div class="gradient-colors">
              <div 
                v-for="(color, index) in gradientColors" 
                :key="index"
                class="color-item"
              >
                <el-color-picker 
                  v-model="gradientColors[index]" 
                  show-alpha
                  @change="updateGradient"
                />
                <el-button 
                  v-if="gradientColors.length > 2"
                  type="danger" 
                  icon="Delete" 
                  circle
                  size="small"
                  @click="removeGradientColor(index)"
                />
              </div>
              
              <el-button 
                v-if="gradientColors.length < 5"
                type="primary" 
                icon="Plus"
                circle
                size="small"
                @click="addGradientColor"
              />
            </div>
          </el-form-item>
        </template>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
}>()

const emit = defineEmits(['update'])

// 本地样式副本
const getDefaultStyle = () => {
  const baseStyle = {...props.component.style} as any

  // 为管道组件添加默认样式
  if (['straightPipe', 'pipe'].includes(props.component.type)) {
    return {
      ...baseStyle,
      pipeColor: baseStyle.pipeColor || '#c0c0c0',
      pipeDarkColor: baseStyle.pipeDarkColor || '#8a8a8a',
      pipeLightColor: baseStyle.pipeLightColor || '#e0e0e0',
      fluidColor: baseStyle.fluidColor || '#4A90E2',
      portColor: baseStyle.portColor || '#666',
      fluidOpacity: baseStyle.fluidOpacity || 0.8,
      showFlanges: baseStyle.showFlanges !== false,
      showFlowArrows: baseStyle.showFlowArrows !== false
    }
  }

  // 为工业组件添加默认样式
  if (props.component.type === 'model3d') {
    const model = props.component.data?.static?.model

    if (model === 'tank') {
      return {
        ...baseStyle,
        tankColor: baseStyle.tankColor || '#999999',
        tankTopColor: baseStyle.tankTopColor || '#777777',
        tankBottomColor: baseStyle.tankBottomColor || '#777777',
        liquidColor: baseStyle.liquidColor || '#3399ff',
        tankOpacity: baseStyle.tankOpacity || 0.7
      }
    }

    if (model === 'pump') {
      return {
        ...baseStyle,
        pumpBodyColor: baseStyle.pumpBodyColor || '#3366cc',
        pumpPipeColor: baseStyle.pumpPipeColor || '#999999',
        pumpRunningColor: baseStyle.pumpRunningColor || '#00cc00',
        pumpStoppedColor: baseStyle.pumpStoppedColor || '#cc0000'
      }
    }

    if (model === 'valve') {
      return {
        ...baseStyle,
        valveBodyColor: baseStyle.valveBodyColor || '#666666',
        valveOpenColor: baseStyle.valveOpenColor || '#00ff00',
        valveClosedColor: baseStyle.valveClosedColor || '#ff0000',
        valvePipeColor: baseStyle.valvePipeColor || '#999999'
      }
    }
  }

  return baseStyle
}

const localStyle = ref(getDefaultStyle())

// 阴影设置
const shadowEnabled = ref(!!props.component.style.boxShadow)
const shadowBlur = ref(props.component.style.boxShadow ? parseInt(props.component.style.boxShadow.split('px')[0]) || 5 : 5)
const shadowColor = ref(props.component.style.boxShadow ? props.component.style.boxShadow.split(') ')[0] + ')' : 'rgba(0, 0, 0, 0.3)')

// 渐变设置
const gradientEnabled = ref(!!props.component.style.gradient)
const gradientType = ref(props.component.style.gradient?.type || 'linear')
const gradientDirection = ref(props.component.style.gradient?.direction || 90)
const gradientColors = ref(props.component.style.gradient?.colors || ['#409EFF', '#ffffff'])

// 监听组件变化，更新本地副本
watch(() => props.component.style, (newStyle) => {
  localStyle.value = getDefaultStyle()
  
  // 更新阴影设置
  shadowEnabled.value = !!newStyle.boxShadow
  if (newStyle.boxShadow) {
    shadowBlur.value = parseInt(newStyle.boxShadow.split('px')[0]) || 5
    shadowColor.value = newStyle.boxShadow.split(') ')[0] + ')'
  }
  
  // 更新渐变设置
  gradientEnabled.value = !!newStyle.gradient
  if (newStyle.gradient) {
    gradientType.value = newStyle.gradient.type || 'linear'
    gradientDirection.value = newStyle.gradient.direction || 90
    gradientColors.value = [...(newStyle.gradient.colors || ['#409EFF', '#ffffff'])]
  }
}, { deep: true })

// 更新样式
const updateStyle = () => {
  emit('update', {...localStyle.value})
}

// 更新阴影
const updateShadow = () => {
  if (shadowEnabled.value) {
    localStyle.value.boxShadow = `${shadowBlur.value}px ${shadowBlur.value}px ${shadowBlur.value * 2}px ${shadowColor.value}`
  } else {
    localStyle.value.boxShadow = undefined
  }
  
  updateStyle()
}

// 更新渐变
const updateGradient = () => {
  if (gradientEnabled.value) {
    localStyle.value.gradient = {
      type: gradientType.value,
      colors: [...gradientColors.value],
      direction: gradientType.value === 'linear' ? gradientDirection.value : undefined
    }
  } else {
    localStyle.value.gradient = undefined
  }
  
  updateStyle()
}

// 添加渐变颜色
const addGradientColor = () => {
  gradientColors.value.push('#ffffff')
  updateGradient()
}

// 删除渐变颜色
const removeGradientColor = (index: number) => {
  gradientColors.value.splice(index, 1)
  updateGradient()
}
</script>

<style scoped>
.style-editor {
  padding: 10px;
  color: #000;
}

.shadow-controls {
  margin-top: 10px;
}

.gradient-colors {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 确保所有表单元素中的文字为黑色 */
:deep(.el-form-item__label) {
  color: #000 !important;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner),
:deep(.el-select-dropdown__item),
:deep(.el-radio-button__inner),
:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  color: #000 !important;
}

/* 确保下拉选项也是黑色文字 */
:deep(.el-select-dropdown__item) {
  color: #000 !important;
}

/* 确保输入框中的文字为黑色 */
.dark-text :deep(input),
.dark-text :deep(textarea) {
  color: #000 !important;
}
</style>