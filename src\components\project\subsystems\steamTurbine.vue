<template>
  <div id="table-wrap">
    <!-- 新增行按钮 -->
    <el-button type="primary" @click="addRow" style="margin-bottom: 20px;">新增行</el-button>

    <!-- 可编辑表格-Vue3 + ElementPlus -->
    <el-table :data="questionChoiceVOlist" border :row-class-name="tableRowClassName">
      <el-table-column type="index" label="序号" align="center" :resizable="false" width="70" />

      <template #empty>
        <el-empty description="暂无数据" />
      </template>

      <el-table-column
        :resizable="false"
        align="center"
        v-for="(col, idx) in columnList"
        :key="col.prop"
        :prop="col.prop"
        :label="col.label"
        :index="idx"
      >
        <template #default="{ row }">
          <!-- 判断字段类型，选择合适的输入组件 -->
          <el-input v-if="row.isEditing" v-model="row[col.prop]" size="small" placeholder="请输入" />
          <span v-else>{{ row[col.prop] }}</span>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" align="center" width="220">
        <template #default="{ row, $index }">
          <el-button size="small" @click="editRow(row)" v-if="!row.isEditing">编辑</el-button>
          <el-button size="small" type="primary" @click="saveRow(row)" v-if="row.isEditing">保存</el-button>
          <el-button size="small" type="danger" @click="deleteRow($index)">删除</el-button>
          <el-button size="small" @click="cancelEdit(row)" v-if="row.isEditing">取消</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, nextTick,defineProps, watch} from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {subSystemParamList,subSystemParamAdd,subSystemParamEdit,deleteCategories} from '@/api/project/api'
const props = defineProps<{rowData: Data | null}>()

interface Column {
 prop: string;
 label: string;
 type?: string;
}

interface Data {
	paramName: string;
	paramIdentifier: string;
	paramValue: string;
  orderNum?:number|string;
  remark?:string;
	unitName: string | number;
 isEditing?: boolean; // 新增的字段，表示是否处于编辑状态
 [key: string]: unknown;
}

const state = reactive({
 columnList: [
  { prop: 'paramName', label: '设计参数名称' },
  { prop: 'paramIdentifier', label: '设计参数标识' },
  { prop: 'paramValue', label: '参数值' },
  { prop: 'unitName', label: '单位' },
  { prop: 'orderNum', label: '排序'},
  { prop: 'remark', label: '备注'},

 ] as Column[],
 questionChoiceVOlist: [] as Data[],
 // 用于暂存编辑前的数据，以便在取消编辑时恢复
 backupRow: {} as { [key: number]: Data },
})
const { columnList, questionChoiceVOlist, backupRow } = toRefs(state)

// 编辑行
const editRow = (row: Data) => {
 // 备份编辑前的数据
 backupRow.value[row.id as number] = { ...row }
 row.isEditing = true
}

// 保存行
const saveRow = (row: Data) => {
  row.isEditing = false

  if (row.isNew) {
    // 如果是新增行
    const params = {
      paramName: row.paramName,
      paramIdentifier: row.paramIdentifier,
      paramValue: row.paramValue,
      unitName: row.unitName,
      orderNum:row.orderNum,
      remark:row.remark,
      subSystemId: props.rowData.id,
    }
    subSystemParamAdd(params).then(res => {
      if (res.code == 200) {
        ElMessage.success('保存成功')
        row.isNew = false // 保存成功后将 isNew 设置为 false
      } else {
        ElMessage.error('保存失败')
      }
      getsubSystemParamList()
    })
  } else {
    // 如果是编辑行
    const params = {
      paramName: row.paramName,
      paramIdentifier: row.paramIdentifier,
      paramValue: row.paramValue,
      unitName: row.unitName,
      orderNum:row.orderNum,
      remark:row.remark,
      subSystemId: props.rowData.id,
      id: row.id
    }
    subSystemParamEdit(params).then(res => {
      if (res.code == 200) {
        ElMessage.success('编辑成功')
      } else {
        ElMessage.error('编辑失败')
      }
      getsubSystemParamList()
    })
  }
}
// 删除行
// 删除行
const deleteRow = (index: number) => {
  ElMessageBox.confirm('此操作将永久删除该行, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      const row = questionChoiceVOlist.value[index]
      // 调用 deleteCategories 接口
      deleteCategories([row.id])
        .then((res) => {
          if (res.code === 200) {
            // 如果接口调用成功，删除行数据
            if (row.isEditing && backupRow.value[row.id as number]) {
              delete backupRow.value[row.id as number]
            }
            questionChoiceVOlist.value.splice(index, 1)
            ElMessage.success('删除成功')
            getsubSystemParamList()
          } else {
            ElMessage.error('删除失败')
            getsubSystemParamList()
          }
        })
        .catch(() => {
          ElMessage.error('删除失败')
        })
    })
    .catch(() => ElMessage.info('已取消删除'))
}


// 取消编辑
const cancelEdit = (row: Data) => {
  if (row.isNew) {
    // 如果是新增行，取消编辑时直接移除该行
    questionChoiceVOlist.value.splice(row.row_index, 1)
  } else {
    // 如果是编辑行，恢复备份的数据
    const backupData = backupRow.value[row.id as number]
    if (backupData) {
      Object.keys(backupData).forEach((key) => {
        row[key] = backupData[key]
      })
      delete backupRow.value[row.id as number]
    }
    row.isEditing = false
  }
}
// 新增行
const addRow = () => {
 // 新增的行默认处于编辑状态
 const newRow: Data = {
	paramName: '',
	paramIdentifier: '',
	paramValue: '',
	unitName: '',
  orderNum:'',
  remark:'',
  isEditing: true,
  isNew: true, // 标记为新增行
 }
 questionChoiceVOlist.value.unshift(newRow)
 // 滚动到表格顶部，使新增的行可见
 nextTick(() => {
  const table = document.querySelector('.el-table__body-wrapper')
  if (table) {
	  table.scrollTop = 0
  }
 })
}

// 添加表格行下标
const tableRowClassName = ({ row, rowIndex }: { row: any; rowIndex: number }) => {
 row.row_index = rowIndex
}

// 获取子系统参数列表
const getsubSystemParamList=()=>{
	const sid=props.rowData.id?props.rowData.id:''
	if (sid===null) {
		return
	}
	const params={
		pageNum:1,
		pageSize:50,
		subSystemId:sid
	}
	subSystemParamList(params).then((res)=>{
		if (res.data.rows.length > 0) {
			state.questionChoiceVOlist=res.data.rows

		}else{
			state.questionChoiceVOlist=[]
		}

	})
}
watch(
  () => props.rowData,
  (newValue, oldValue) => {
    if (newValue && newValue !== oldValue) {
      getsubSystemParamList()
    }
  },
  { immediate: true } // 这个选项会在初始化时立即调用一次
)
</script>

<style lang="scss" scoped>
#table-wrap {
 width: 100%;
 height: 100%;
}
</style>
