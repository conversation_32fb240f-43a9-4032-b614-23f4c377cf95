import { getToken } from '@/utils/auth'
import { CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'

const { wsCache } = useLocalCache()

class WebSocketService {
  private static instance: WebSocketService | null = null
  private ws: WebSocket | null = null
  private heartBeatInterval: number | undefined
  private readonly url: string
  // 吴本地 // 本地测试服务器 // const wsUrl = `ws://192.168.77.131:8089/websocket/message/${projectId}/${stageId}` // 阿里云 // const wsUrl = `ws://120.26.239.96:8089/websocket/message/${projectId}/${stageId}` // 瀚蓝项目10.2.63.151 // const wsUrl = `ws://10.2.63.151:8089/websocket/message/${projectId}/${stageId}` // 惠州三号机组 // const wsUrl = `ws://172.103.1.162:8089/websocket/message/${projectId}/${stageId}` // 惠州四号机组 // const wsUrl = `ws://172.104.1.172:8089/websocket/message/${projectId}/${stageId}`

  // 本地 ws://192.168.77.131:8089
  // 华清
  // 阿里云 ws://120.26.239.96:8089
  // 惠州三号机组 ws://172.103.1.162:8089
  // 惠州四号机组 ws://172.104.1.172:8089
  // 鞍钢 192.168.1.100 
  // 天津晨兴行192.168.2.108
  // 瀚蓝项目10.2.63.151:8089
  // 天津晨兴：192.168.101.110
  // 承德192.168.3.195
  private constructor() {
    const projectId = wsCache.get(CACHE_KEY.projectList)?.id
    const stageId = getToken()
    this.url = `ws://192.168.2.108:8089/websocket/message/${projectId}/${stageId}`
    // this.url = `ws://172.104.1.172:8089/websocket/message/${projectId}/${stageId}`
    // this.url = `ws://10.2.63.151:8089/websocket/message/${projectId}/${stageId}`
    window.addEventListener('beforeunload', this.closeConnection.bind(this))
  }

  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService()
    }
    return WebSocketService.instance
  }

  public connect() {
    if (!this.ws || this.ws.readyState === WebSocket.CLOSED) {
      this.initWebSocket()
    }
  }

  private initWebSocket() {
    this.ws = new WebSocket(this.url)

    this.ws.onopen = () => {
      console.log('WebSocket连接已建立')
      this.startHeartBeat()
    }

    this.ws.onclose = () => {
      // console.log('断开连接')
      this.stopHeartBeat()
    }

    this.ws.onerror = (error) => {
      // console.error('WebSocket错误', error)
      this.reconnect()
    }

    this.ws.onmessage = (event) => {
      // 处理收到的消息
      // console.log('收到的消息:', event.data)
    }
  }

  private startHeartBeat() {
    const token = getToken()
    this.heartBeatInterval = window.setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(`${token}HEARTCMD`)
        console.log('心跳已发送')
      }
    }, 30000) // 每30秒发送一次心跳包
  }

  private stopHeartBeat() {
    if (this.heartBeatInterval) {
      clearInterval(this.heartBeatInterval)
      this.heartBeatInterval = undefined
    }
  }

  private reconnect() {
    // console.log('正在重新连接WebSocket')
    this.stopHeartBeat()
    setTimeout(() => this.initWebSocket(), 1000) // 1秒后重连
  }

  public disconnect() {
    if (this.ws) {
      this.ws.close()
      this.stopHeartBeat()
      this.ws = null
    }
  }

  public closeConnection() {
    this.disconnect()
    WebSocketService.instance = null // 重置实例为 null
  }

  public sendMessage(message: string) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(message)
    } else {
      console.error('WebSocket is not open')
    }
  }
}

export default WebSocketService
