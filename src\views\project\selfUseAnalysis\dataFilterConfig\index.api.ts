import request from '@/utils/request'

// 定义DTO类型，用于封装API请求参数
export interface DataFilterConfigDTO {
  id?: number
  projectId: number
  filterType: string | number
  item?: any
}

export interface DiagnosisDashboardResponseDTO {
  projectId: number
  powerUnitId: number
  powerUnitName: string
  filterType: number[]
}

// 日历数据查询DTO
export interface CalendarDataDTO {
  projectId: number
  powerUnitId: number
  filterType: number
}

// 报表数据查询DTO
export interface ReportDataDTO {
  projectId: number
  powerUnitId: number
  filterType: number
  reportMonth: string
}

enum Api {
  dataFilterConfigList = '/project/dataFilterConfig/list',
  dataFilterConfigAdd = '/project/dataFilterConfig/add',
  dataFilterConfigEdit = '/project/dataFilterConfig/edit',
  dataFilterConfigDelete = '/project/dataFilterConfig/delete',
  diagnosisDashboard = '/project/dataFilterConfig/diagnosis/dashboard',
  dataFilterReportMonth = '/smartOperations/dataFilterReport/month',
  dataFilterReportView = '/smartOperations/dataFilterReport/view',
  variableFrequencyPumpReportView = '/smartOperations/variableFrequencyPumpReport/view',
}

// 获取数据过滤配置列表
export const getDataFilterConfigList = (data: any) => {
  return request({
    url: Api.dataFilterConfigList,
    method: 'post',
    data,
  })
}

// 添加数据过滤配置
export const addDataFilterConfig = (data: DataFilterConfigDTO) => {
  return request({
    url: Api.dataFilterConfigAdd,
    method: 'post',
    data,
  })
}

// 编辑数据过滤配置
export const editDataFilterConfig = (data: DataFilterConfigDTO) => {
  return request({
    url: Api.dataFilterConfigEdit,
    method: 'post',
    data,
  })
}

// 删除数据过滤配置
export const deleteDataFilterConfig = (ids: number[]) => {
  return request({
    url: Api.dataFilterConfigDelete,
    method: 'post',
    data: ids,
  })
}

// 获取诊断仪表板数据
export const getDiagnosisDashboard = (projectId: number) => {
  return request({
    url: Api.diagnosisDashboard,
    method: 'post',
    data: projectId,
  })
}
// 查询日历数据
export const getCalendarData = (params: CalendarDataDTO) => {
  return request({
    url: Api.dataFilterReportMonth,
    method: 'post',
    data: params,
  })
}
// 查询导出数据
export const getdataFilterReportView = (params: ReportDataDTO) => {
  return request({
    url: Api.dataFilterReportView,
    method: 'post',
    data: params,
  })
}
// 查询变频泵报表数据
export const getvariableFrequencyPumpReportView = (params: ReportDataDTO) => {
  return request({
    url: Api.variableFrequencyPumpReportView,
    method: 'post',
    data: params,
  })
}
