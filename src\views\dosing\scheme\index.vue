<template>
  <div>
    <transition></transition>
    <el-card shadow="never">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="openDialog('add')">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" plain @click="pushConfiguration">推送配置</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" plain @click="oneClickPush">一键推送</el-button>
        </el-col>
      </el-row>
      <el-table :loading="state.loading" :data="data" style="width: 100%">
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="药品名称" prop="medicalManageId" align="center" :formatter="formatMedicineName" />
        <el-table-column label="加药方式" prop="addMethod" align="center" :formatter="formatAddMethod" />
        <el-table-column label="投加浓度" prop="addConcentration" align="center" />
        <el-table-column label="投加量" prop="addNumber" align="center" />
        <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
          <template #default="{ row }">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="openDialog('edit', row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(row)" />
            </el-tooltip>
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="openDialog('view', row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="state.total > 0"
        v-model:page="state.page.pageNum"
        v-model:limit="state.page.pageSize"
        :total="state.total"
        layout="total, prev, pager, next, jumper"
        @pagination="getpagination"
      />
    </el-card>

    <!-- 主弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisibleMain" width="550px" :before-close="() => (dialogVisibleMain = false)">
      <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
        <!-- 药品名称 -->
        <el-form-item label="药品名称" prop="medicalManageId">
          <el-select v-model="form.medicalManageId" placeholder="请选择药品" filterable :disabled="isView">
            <el-option v-for="item in PharmaceuticalAllList" :key="item.id" :label="item.name" :value="item.id.toString()" />
          </el-select>
        </el-form-item>
        <!-- 加药方式 -->
        <el-form-item label="加药方式" prop="addMethod">
          <el-radio-group v-model="form.addMethod" :disabled="isView">
            <el-radio label="0">自动控制投加</el-radio>
            <el-radio label="1">冲击式投加</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 投加浓度 -->
        <el-form-item label="投加浓度" prop="addConcentration">
          <el-input-number v-model="form.addConcentration" :min="0" placeholder="请输入投加浓度" :disabled="isView" style="width:50%;" />
          <strong style="margin-left: 10px;color: #fff;">mg/L</strong>
        </el-form-item>
        <!-- 投加量 -->
        <el-form-item label="投加量" prop="addNumber">
          <el-input-number v-model="form.addNumber" :min="0" placeholder="请输入投加量" :disabled="isView" style="width:50%;" />
          <strong style="margin-left: 10px;color: #fff;">KG</strong>
        </el-form-item>
        <!-- 频率类型 -->
        <el-form-item label="频率类型" prop="dateType">
          <el-radio-group v-model="form.dateType" :disabled="isView" @change="onDateTypeChange">
            <el-radio label="0">星期</el-radio>
            <el-radio label="1">日期</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 频率选择 -->
        <el-form-item label="频率" prop="addRateList">
          <template v-if="form.dateType === '0'">
            <el-checkbox-group v-model="form.addRateList" :disabled="isView">
              <el-checkbox label="1">星期一</el-checkbox>
              <el-checkbox label="2">星期二</el-checkbox>
              <el-checkbox label="3">星期三</el-checkbox>
              <el-checkbox label="4">星期四</el-checkbox>
              <el-checkbox label="5">星期五</el-checkbox>
              <el-checkbox label="6">星期六</el-checkbox>
              <el-checkbox label="7">星期日</el-checkbox>
            </el-checkbox-group>
          </template>
          <template v-else>
            <el-checkbox-group v-model="form.addRateList" :disabled="isView">
              <el-checkbox v-for="d in daysInMonth" :key="d" :label="d.toString()">{{ d }}日</el-checkbox>
            </el-checkbox-group>
          </template>
        </el-form-item>
        <!-- 推送方式 -->
        <el-form-item label="推送方式" prop="channelId">
          <el-select v-model="form.channelId" placeholder="请选择推送方式" filterable :disabled="isView">
            <el-option v-for="opt in Pushdatavalue" :key="opt.value" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisibleMain = false">{{ isView ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!isView" type="primary" @click="mode==='add' ? submitAdd() : submitEdit()">
          {{ mode==='add' ? '添加' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 推送配置弹窗 -->
    <el-dialog v-model="dialogVisiblePush" title="请选择推送人员" width="500px" :before-close="handleClose">
      <template #default>
        <el-checkbox :value="isAllSelected" @change="toggleAllSelection">全选</el-checkbox>
        <el-checkbox-group v-model="selectedStaff">
          <div v-for="staff in staffList" :key="staff.id" class="checkbox-item">
            <el-checkbox :label="staff.id.toString()"> {{ staff.userName }} - {{ staff.nickName }} - {{ staff.phonenumber }} </el-checkbox>
          </div>
        </el-checkbox-group>
      </template>
      <template #footer>
        <el-button @click="dialogVisiblePush = false">取消</el-button>
        <el-button type="primary" @click="confirmPush">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import {
  getPharmaceuticalAll,
  addScheme,
  editScheme,
  deleteScheme,
  getSchemeList,
  oneClickPushScheme,
  getAllUser,
  getSelectedUser,
  saveSelectedUser,
} from './index.api'
import { getConfigList } from '../../iot/channel/api/configs.api'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import { ElMessage } from 'element-plus'

const { wsCache } = useLocalCache()
const { id: projectId } = wsCache.get(CACHE_KEY.projectList)

// 数据列表
const data = ref<any[]>([])
const state = reactive({ page: { pageNum: 1, pageSize: 10 }, total: 0, loading: false, query: {} })

function getData() {
  state.loading = true
  getSchemeList({ projectId, ...state.query, ...state.page }).then(res => {
    state.loading = false
    state.total = res.data.total
    data.value = res.data.rows
  })
}

// 下拉数据
const PharmaceuticalAllList = ref<any[]>([])
const Pushdatavalue = ref<{ label: string; value: string }[]>([])

function getPharmaceuticaldata() {
  getPharmaceuticalAll({ projectId }).then(res => {
    PharmaceuticalAllList.value = res.data
  })
}
function getpagination() {
  getData()
}
function getPushdata() {
  getConfigList({ projectId }).then(res => {
    Pushdatavalue.value = res.data.rows.map((o: any) => ({ label: o.title, value: o.id.toString() }))
  })
}

onMounted(() => {
  getData()
  getPharmaceuticaldata()
  getPushdata()
})

// 表格formatter
const medicineMap = computed(() => {
  const m: Record<number, string> = {}
  PharmaceuticalAllList.value.forEach(i => (m[i.id] = i.name))
  return m
})
function formatMedicineName(_r: any, _c: any, val: number) {
  return medicineMap.value[val] || '—'
}
function formatAddMethod(_r: any, _c: any, val: any) {
  return val === '0' || val === 0 ? '自动控制投加' : '冲击式投加'
}
const onDateTypeChange=()=> {
  form.addRateList = []
}

// 主弹窗
const dialogVisibleMain = ref(false)
const mode = ref<'add' | 'edit' | 'view'>('add')
const formRef = ref()
const form = reactive({
  id: '',
  medicalManageId: '',
  addMethod: '1',
  addConcentration: null as number | null,
  addNumber: null as number | null,
  addRateList: [] as string[],
  dateType: '0',
  channelId: '',
})

// 天数列表
const daysInMonth = Array.from({ length: 31 }, (_, i) => i + 1)

const formRules = {
  medicalManageId: [{ required: true, message: '药品名称不能为空', trigger: 'change' }],
  addMethod: [{ required: true, message: '请选择加药方式', trigger: 'change' }],
  addConcentration: [{ required: true, message: '请输入投加浓度' }],
  addNumber: [{ required: true, message: '请输入投加量' }],
  addRateList: [{ required: true, message: '请选择频率', trigger: 'change' }],
}
const isView = computed(() => mode.value === 'view')
const dialogTitle = computed(() => (mode.value === 'add' ? '新增配置' : mode.value === 'edit' ? '编辑配置' : '查看配置'))

function openDialog(m: 'add' | 'edit' | 'view', row?: any) {
  mode.value = m
  dialogVisibleMain.value = true
  nextTick(() => {
    formRef.value?.resetFields()
    if (m !== 'add' && row) {
      form.id = row.id
      form.medicalManageId = row.medicalManageId.toString()
      form.addMethod = row.addMethod.toString()
      form.addConcentration = row.addConcentration
      form.addNumber = row.addNumber
      form.addRateList = row.addRate.split(',')
      form.dateType = row.dateType?.toString() || '0'
      form.channelId = row.channelId?.toString() || ''
    }
  })
}

function submitAdd() {
  formRef.value?.validate(ok => {
    if (!ok) return
    const name = PharmaceuticalAllList.value.find(i => i.id === Number(form.medicalManageId))?.name || ''
    addScheme({
      projectId,
      medicalManageId: form.medicalManageId,
      name,
      addMethod: Number(form.addMethod),
      addConcentration: Number(form.addConcentration),
      addNumber: Number(form.addNumber),
      addRate: form.addRateList.sort().join(','),
      dateType: form.dateType,
      channelId: form.channelId,
    }).then(() => {
      ElMessage.success('添加成功')
      dialogVisibleMain.value = false
      getData()
    })
  })
}
function submitEdit() {
  // 保存前清除另一种模式的选择
  // if (form.dateType === '0') {
  //   form.addRateList = form.addRateList.filter(n => Number(n) >= 1 && Number(n) <= 7)
  // } else {
  //   form.addRateList = form.addRateList.filter(n => Number(n) >= 1 && Number(n) <= 31)
  // }
  formRef.value?.validate(ok => {
    if (!ok) return
    const name = PharmaceuticalAllList.value.find(i => i.id === Number(form.medicalManageId))?.name || ''
    editScheme({
      id: form.id,
      projectId,
      medicalManageId: form.medicalManageId,
      name,
      addMethod: Number(form.addMethod),
      addConcentration: Number(form.addConcentration),
      addNumber: Number(form.addNumber),
      addRate: form.addRateList.sort().join(','),
      dateType: form.dateType,
      channelId: form.channelId,
    }).then(() => {
      ElMessage.success('保存成功')
      dialogVisibleMain.value = false
      getData()
    })
  })
}

// 删除
function handleDelete(row: any) {
  deleteScheme([row.id]).then(() => {
    ElMessage.success('删除成功')
    getData()
  })
}

// 推送配置弹窗
const dialogVisiblePush = ref(false)
const staffList = ref<any[]>([])
const selectedStaff = ref<string[]>([])
const isAllSelected = ref(false)

function toggleAllSelection(v: boolean) {
  isAllSelected.value = v
  selectedStaff.value = v ? staffList.value.map(s => s.id.toString()) : []
}

function pushConfiguration() {
  dialogVisiblePush.value = true
  getAllUser({}).then(r => {
    staffList.value = r.data.rows
    getSelectedUser({ projectId }).then(r2 => {
      selectedStaff.value = staffList.value
        .filter(s => r2.data.includes(s.id.toString()))
        .map(s => s.id.toString())
      isAllSelected.value = selectedStaff.value.length === staffList.value.length
    })
  })
}

function confirmPush() {
  saveSelectedUser({ projectId, receiver: selectedStaff.value.join(',') }).then(() => {
    dialogVisiblePush.value = false
    selectedStaff.value = []
  })
}

// 一键推送
function oneClickPush() {
  oneClickPushScheme({ projectId }).then(() => ElMessage.success('推送成功'))
}
</script>

<style scoped>
:deep(.el-select__wrapper) {
  /* width: 200px; */
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  background-color: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}

:deep(.el-select__placeholder) {
  color: #fff;
}

:deep(.el-checkbox__label) {
  color: #fff;
}
</style>
<style lang="scss" scoped>
:deep(.el-tag.el-tag--info) {
  color: #000 !important;
}

:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  border: none;
}

:deep(.el-card__body) {
  border: none;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

:deep(.el-table__cell) {
  // color: rgba(204, 204, 204, 1) !important;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}

:deep(.el-tree) {
  background-color: transparent;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #07355c;
}

:deep(.el-tree-node__expand-icon) {
  color: #fff;
}

:deep(.el-select__wrapper) {
  width: 180px;
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}

:deep(.el-select__placeholder) {
  color: #fff;
}

:deep(.el-tree-node__label) {
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}

:deep(.el-select__tags .el-tag--info) {
  background-color: #153059 !important;
}
</style>
