<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form ref="queryFormRef" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="daterangeCreateTime"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>
    <el-card shadow="never">
      <el-table v-loading="state.loading" :data="data">
        <el-table-column label="序号" type="index" align="center" width="80" />
        <el-table-column prop="name" label="名称" align="center"></el-table-column>
        <el-table-column prop="workWay" label="工作模式">
          <template #default="{ row }">
            {{ pump_work_way.find(item => item.value === String(row.workWay))?.label || "" }}
          </template>
        </el-table-column>
        <el-table-column prop="beforeValue" label="变化前值" align="center"></el-table-column>
        <el-table-column prop="afterValue" label="变化后值" align="center"></el-table-column>
        <el-table-column prop="description" label="描述" align="center"></el-table-column>
        <el-table-column prop="createTime" label="时间" align="center"></el-table-column>
      </el-table>
      <pagination
        v-if="state.total > 0"
        v-model:page="state.page.pageNum"
        v-model:limit="state.page.pageSize"
        :total="state.total"
        layout="total, prev, pager, next, jumper"
        @pagination="getpagination"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { waterPumpHistoryData } from './index.api'
import { IColumn } from '@/components/common/types/tableCommon'
import YtCrud from '@/components/common/yt-crud.vue'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
import { log } from 'console'
import { el } from 'element-plus/es/locale'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { pump_work_way } = toRefs<any>(proxy?.useDict('pump_work_way'))

const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
import { ComponentInternalInstance } from 'vue'

const { id: projectId } = cachedProjects
const data = ref([])
const showSearch = ref(true)
emitter.on('projectListChanged', (e) => {
  location.reload()
})
const daterangeCreateTime = ref([])
// 分页 & 加载
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})
const getList = () => {
  let startTime = null
  let endTime = null
  if (daterangeCreateTime.value.length > 0) {
    startTime = daterangeCreateTime.value[0]
    endTime = daterangeCreateTime.value[1]
  }
  const param = {
    ...state.page,
    ...state.query,
    startTime: startTime,
    endTime: endTime,
    projectId: projectId,
  }
  waterPumpHistoryData(param).then((res) => {
    if (res.code === 200) {
      data.value = res.data.rows
      state.total = res.data.total
      state.loading = false
    } else {
      data.value = []
    }
  })
}
// 查询
const handleQuery = () => {
  getList()
}
// 重置
const resetQuery = () => {
  daterangeCreateTime.value = []
  getList()
}
const getpagination = (pagination: { page: number; limit: number }) => {
  state.page.pageNum = pagination.page
  state.page.pageSize = pagination.limit
  getList()
}
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  border: none;
}

:deep(.el-card__body) {
  border: none;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

:deep(.el-table__cell) {
  // color: rgba(204, 204, 204, 1) !important;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}

:deep(.el-tree) {
  background-color: transparent;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #07355c;
}

:deep(.el-tree-node__expand-icon) {
  color: #fff;
}

:deep(.el-tree-node__label) {
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}

:deep(.el-select__tags .el-tag--info) {
  background-color: #153059 !important;
}

:deep(.el-tag.el-tag--info) {
  color: #fff !important;
}
</style>
