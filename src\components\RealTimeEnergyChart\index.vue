<template>
  <div ref="chartRef" class="energy-chart"></div>
</template>

<script setup lang="ts" name="RealTimeEnergyChart">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  data?: Array<{ time: string; value: number }>
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  height: '100%'
})

const chartRef = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  setOptions()

  // 确保图表正确渲染
  nextTick(() => {
    if (chart) {
      chart.resize()
    }
  })

  window.addEventListener('resize', handleResize)
}

const setOptions = () => {
  if (!chart) return

  const xData = props.data.map(item => item.time)
  const yData = props.data.map(item => item.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 43, 137, 0.9)',
      borderColor: 'rgba(115, 208, 255, 0.6)',
      borderWidth: 1,
      textStyle: {
        color: '#73d0ff'
      },
      formatter: (params: any) => {
        const point = params[0]
        return `${point.name}<br/>实时能耗: <span style="color: #4ddefc; font-weight: bold;">${point.value} kWh</span>`
      }
    },
        grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xData,
      axisLine: {
        lineStyle: {
          color: 'rgba(115, 208, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#b4d0fc',
        fontSize: 11
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#F5F7FA',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'line',
        data: yData,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#67C23A',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(103, 194, 58, 0.4)'
              },
              {
                offset: 1,
                color: 'rgba(103, 194, 58, 0.05)'
              }
            ]
          }
        }
      }
    ],
    backgroundColor: 'rgba(8, 42, 77, 0.2)'
  }

  chart.setOption(option)
}

const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

watch(
  () => props.data,
  () => {
    nextTick(() => {
      setOptions()
    })
  },
  { deep: true }
)

onMounted(() => {
  nextTick(() => {
    initChart()
    // 延迟确保容器完全渲染 - 增加延迟时间
    setTimeout(() => {
      if (chart) {
        chart.resize()
      }
    }, 300)

    // 添加更多重试机制
    setTimeout(() => {
      if (chart) {
        chart.resize()
      }
    }, 600)

    setTimeout(() => {
      if (chart) {
        chart.resize()
      }
    }, 1000)
  })
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.energy-chart {
  width: 100%;
  height: 100%;
  flex: 1;
}
</style>
