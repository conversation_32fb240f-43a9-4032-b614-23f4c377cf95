.el-date-picker .has-sidebar .has-time {
    background: rgba(3, 43, 82, 1) !important;
    color: #fff !important;
    border: 1px solid #034374 !important;
}

.el-date-range-picker__time-picker-wrap .el-time-panel {
    background: rgba(3, 43, 82, 1) !important;
    color: #fff !important;
}

.el-time-spinner__item {
    color: #ffffff !important;
}

.el-picker-panel {
    color: #fff;//设置当前面板的月份的字体为白色，记为1
    background: #002450;//定义整体面板的颜色
    border: 1px solid #1384b4;//定义整体面板的轮廓
    .el-picker-panel__icon-btn {//设置年份月份调节按钮颜色，记为2
      color: #ffffff !important;
     }
    .el-date-picker__header-label{//设置年月显示颜色，记为3
        color: #ffffff !important;
    }
    .el-date-table th {//设置星期颜色，记为4
        color:#ffffff !important;
    }
    
  }
  
/* 设置时分秒选择器的选中状态背景颜色和字体颜色 */
.el-time-spinner__item.is-active {
    background-color: rgba(3, 67, 116, 1) !important; /* 选中状态背景颜色 */
    color: #fff !important; /* 选中状态字体颜色 */
}
.el-time-panel__btn{
    color: #fff !important;
}
/* 设置时分秒选择器的鼠标触摸事件背景颜色 */
.el-date-range-picker__time-picker-wrap .el-time-spinner__item:hover {
    background-color: #0070ff !important; /* 鼠标触摸事件背景颜色 */
    color: #fff !important; /* 鼠标触摸事件字体颜色 */
}

.el-date-table td.disabled .el-date-table-cell {
    background: rgba(3, 43, 82, 1) !important;
    color: #958e8e !important;
}

.el-date-picker__header-label {
    color: #ffffff !important;
}

.el-date-table th {
    color: #fff !important;
}

.el-range-editor--large .el-range-input {
    color: #fff !important;
}

.el-icon-d-arrow-left:before,
.el-icon-arrow-left:before,
.el-icon-arrow-right:before,
.el-icon-d-arrow-right:before {
    color: #fff !important;
}

.el-picker-panel__footer {
    background-color: rgba(3, 43, 82, 1) !important;
    border: 1px solid #034374 !important;
}

.el-picker-panel [slot=sidebar], .el-picker-panel__sidebar {
    background-color: rgba(3, 43, 82, 1) !important;
    border-right: 1px solid #034374 !important;
}

.el-picker-panel__shortcut {
    color: #fff !important;
}

.el-date-picker__time-header {
    border-bottom: 1px solid #034374 !important;
}

.el-popper[x-placement^=bottom] .popper__arrow::after {
    top: 1px;
    margin-left: -6px;
    border-top-width: 0;
    border-bottom-color: rgba(3, 43, 82, 1) !important;
}

.el-popper[x-placement^=top] .popper__arrow::after {
    bottom: 1px;
    margin-left: -6px;
    border-top-color: #034374 !important;
    border-bottom-width: 0;
}

.el-picker-panel {
    background: rgba(3, 43, 82, 1) !important;
    color: #fff !important;
}

.el-date-table td.in-range div,
.el-date-table td.in-range div:hover,
.el-date-table.is-week-mode .el-date-table__row.current div,
.el-date-table.is-week-mode .el-date-table__row:hover div {
    background-color: rgba(3, 67, 116, 1) !important; /* 修改选中范围的背景颜色 */
    color: #fff !important; /* 选中范围的字体颜色为白色 */
}

.el-date-range-picker__time-header {
    border-bottom: 1px solid #034374 !important;
}

.el-date-range-picker__content.is-left {
    border-right: 1px solid #034374 !important;
}

/* 三角号 */
.el-popper.is-light .el-popper__arrow::before {
    border: 1px solid #034374 !important;
    background: rgba(3, 43, 82, 1) !important;
    right: 0;
}

.el-popper.is-pure {
    border: 1px solid #034374 !important;
}
.el-select-dropdown__list{
    margin: 0 !important;
}
.customdatapicker.el-input__wrapper {
    border: 1px solid #034374 !important;
    box-shadow: 0 0 0 0px #034374 inset !important;
    background: rgba(3, 43, 82, 1) !important;
}
.customdatapicker.el-input__wrapper .customdatapicker.el-input__inner {
    // background: rgba(3, 43, 82, 1) !important;
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF !important;
}
/* 下方按钮 */
.el-picker-panel__footer .el-button.is-text {
    color: #fff !important;
    border: 0 solid transparent;
    background-color: transparent !important;
}

.el-picker-panel__footer .el-button {
    background-color: #034374 !important; /* 取消按钮的背景颜色 */
    border: none !important;
    color: #fff !important;
}

/* 鼠标悬停时取消按钮的背景颜色 */
.el-picker-panel__footer .el-button:hover {
    background-color: #034374 !important;
}

.el-button.is-text:not(.is-disabled):hover {
    background-color: #034374 !important;
}

.in-range {
    color: #034374 !important;
}
.el-picker-panel .el-time-panel{
    background: #032b52 !important;
  }
.el-time-spinner__item:hover:not(.is-disabled):not(.is-active){
    background: #032b52 !important;
  }
  .el-date-editor .el-range-input{
    color:#fff
  }