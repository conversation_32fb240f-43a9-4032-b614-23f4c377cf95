# 组件开发任务清单

## 📋 概述
当前组态图系统中新增的工业组件、仪表组件和控制组件只在配置中定义，但实际的Vue组件文件尚未开发。本文档列出了需要开发的组件清单和开发优先级。

---

## 🎯 开发状态总览

### ✅ 已完成组件 (12个)
- TextComponent.vue - 文本组件
- ImageComponent.vue - 图片组件  
- ShapeComponent.vue - 形状组件
- ButtonComponent.vue - 按钮组件
- ChartComponent.vue - 图表组件
- GaugeComponent.vue - 仪表盘组件
- Model3dComponent.vue - 3D模型组件
- HyperbolicCoolingTowerComponent.vue - 双曲线冷却塔
- MechanicalCoolingTowerComponent.vue - 机械通风冷却塔
- CondenserComponent.vue - 凝汽器
- CoolingTower25DComponent.vue - 2.5D冷却塔
- Condenser25DComponent.vue - 2.5D凝汽器

### ❌ 待开发组件 (9个)

---

## 🚧 待开发组件清单

### 1. 管道组件 (2个)

#### 1.1 直管道组件 - StraightPipeComponent.vue
- **组件ID**: `straightPipe`
- **组件类型**: `pipe`
- **显示数据**:
  - 流体类型 (水/蒸汽/油)
  - 流动方向 (left/right/up/down)
  - 流速指示 (m/s)
  - 管道压力 (MPa)
  - 温度 (°C)
- **视觉特征**: 直线管道，流动动画，温度颜色指示
- **优先级**: 🔥 高

#### 1.2 弯管道组件 - BendPipeComponent.vue
- **组件ID**: `bendPipe`
- **组件类型**: `pipe`
- **显示数据**:
  - 流体类型 (水/蒸汽/油)
  - 弯曲角度 (90°/45°/自定义)
  - 流动方向
  - 流速指示 (m/s)
  - 管道压力 (MPa)
- **视觉特征**: L型弯管，流动动画，可配置弯曲方向
- **优先级**: 🔥 高

### 2. 控制组件 (2个)

#### 2.1 开关组件 - SwitchComponent.vue
- **组件ID**: `switch`
- **组件类型**: `control`
- **交互功能**:
  - 开关状态切换 (ON/OFF)
  - 远程控制支持
  - 状态反馈
  - 权限控制
- **视觉特征**: 工业级开关样式，状态指示灯
- **优先级**: 🔥 高

#### 2.2 状态灯组件 - StatusLightComponent.vue
- **组件ID**: `statusLight`
- **组件类型**: `indicator`
- **显示功能**:
  - 多状态指示 (正常/警告/故障/停机)
  - 颜色映射 (绿/黄/红/灰)
  - 闪烁效果 (慢闪/快闪/常亮)
  - 状态文字显示
- **视觉特征**: 圆形LED指示灯，多色状态
- **优先级**: 🔥 高

### 3. 主要设备组件 (5个)

#### 3.1 汽轮机组件 - SteamTurbineComponent.vue
- **组件ID**: `steamTurbine`
- **组件类型**: `equipment`
- **显示数据**:
  - 运行状态 (运行/停机/启动中)
  - 转速 (3000 RPM)
  - 功率输出 (600 MW)
  - 进汽压力 (24 MPa)
  - 进汽温度 (566°C)
  - 排汽压力 (4.9 kPa)
  - 振动值 (μm)
- **视觉特征**: 汽轮机外形，转子旋转动画，蒸汽流动
- **优先级**: 🔥 高

#### 3.2 真空泵组件 - VacuumPumpComponent.vue
- **组件ID**: `vacuumPump`
- **组件类型**: `equipment`
- **显示数据**:
  - 运行状态 (运行/停机)
  - 真空度 (-95 kPa)
  - 转速 (1450 RPM)
  - 电流 (85 A)
  - 温度 (65°C)
  - 冷却水流量 (120 L/min)
- **视觉特征**: 真空泵外形，旋转指示，真空度显示
- **优先级**: 🔥 高

#### 3.3 循环水泵组件 - CirculatingPumpComponent.vue
- **组件ID**: `circulatingPump`
- **组件类型**: `equipment`
- **显示数据**:
  - 运行状态 (运行/停机/备用)
  - 流量 (28000 m³/h)
  - 扬程 (45 m)
  - 转速 (590 RPM)
  - 电流 (420 A)
  - 功率 (2800 kW)
  - 轴承温度 (55°C)
- **视觉特征**: 离心泵外形，叶轮旋转，水流指示
- **优先级**: 🔥 高

#### 3.4 发电机组件 - GeneratorComponent.vue
- **组件ID**: `generator`
- **组件类型**: `equipment`
- **显示数据**:
  - 运行状态 (发电/停机/同期中)
  - 有功功率 (600 MW)
  - 无功功率 (150 MVar)
  - 电压 (22 kV)
  - 电流 (15750 A)
  - 频率 (50 Hz)
  - 功率因数 (0.85)
  - 定子温度 (85°C)
- **视觉特征**: 发电机外形，旋转动画，电气连接
- **优先级**: 🔥 高

#### 3.5 旁滤组件 - SideFilterComponent.vue
- **组件ID**: `sideFilter`
- **组件类型**: `equipment`
- **显示数据**:
  - 运行状态 (运行/停机/反洗中)
  - 进水流量 (500 m³/h)
  - 出水流量 (495 m³/h)
  - 进水压力 (0.6 MPa)
  - 出水压力 (0.4 MPa)
  - 压差 (0.2 MPa)
  - 反洗周期 (24 h)
- **视觉特征**: 过滤器外形，水流方向，压差指示
- **优先级**: 🔥 高

---

## 📅 开发计划

### 第一阶段：基础组件 (预计2天)
1. **SwitchComponent.vue** - 开关组件
2. **StatusLightComponent.vue** - 状态灯组件

**原因**: 控制和指示组件相对简单，可以快速实现并测试交互功能

### 第二阶段：管道组件 (预计2天)
1. **StraightPipeComponent.vue** - 直管道组件
2. **BendPipeComponent.vue** - 弯管道组件

**原因**: 管道是工业系统的基础连接元素，优先实现有助于后续设备连接

### 第三阶段：主要设备组件 (预计5天)
1. **CirculatingPumpComponent.vue** - 循环水泵 (相对简单)
2. **VacuumPumpComponent.vue** - 真空泵
3. **SideFilterComponent.vue** - 旁滤
4. **GeneratorComponent.vue** - 发电机
5. **SteamTurbineComponent.vue** - 汽轮机 (最复杂)

**原因**: 按复杂度递增顺序开发，汽轮机作为核心设备最后实现

---

## 🛠 开发规范

### 文件命名
- 文件名：`{ComponentName}Component.vue`
- 存放路径：`src/views/configuration/components/components/`

### 组件结构
```vue
<template>
  <div class="component-wrapper" :style="componentStyle">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
  selected?: boolean
  editing?: boolean
}>()

const emit = defineEmits<{
  select: [id: string]
  update: [component: ConfigurationComponent]
}>()
</script>
```

### 数据绑定
- 支持静态数据和动态数据
- 实时数据更新
- 数据验证和格式化

### 样式规范
- 使用CSS变量
- 响应式设计
- 主题色彩统一

---

## 🎯 下一步行动

1. **选择开发顺序**: 建议从仪表组件开始
2. **创建组件模板**: 基于现有组件创建标准模板
3. **逐个实现**: 每完成一个组件进行测试
4. **集成测试**: 确保组件在组态图中正常工作
5. **文档更新**: 更新组件库说明文档

---

**创建时间**: 2025-01-30  
**状态**: 📋 规划中  
**负责人**: 开发团队
