<template>
  <div class="model-upload-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <!-- 文件上传 -->
      <el-form-item label="模型文件" prop="file" required>
        <el-upload
          ref="uploadRef"
          class="upload-area"
          drag
          :auto-upload="false"
          :show-file-list="false"
          :accept="acceptedFormats"
          :before-upload="handleBeforeUpload"
          :on-change="handleFileChange"
        >
          <div v-if="!selectedFile" class="upload-content">
            <el-icon class="upload-icon"><UploadFilled /></el-icon>
            <div class="upload-text">
              <p>将文件拖到此处，或<em>点击上传</em></p>
              <p class="upload-hint">支持 GLB, GLTF, OBJ, FBX, 3DS, DAE, STL 格式</p>
              <p class="upload-hint">文件大小不超过 50MB</p>
            </div>
          </div>
          <div v-else class="file-selected">
            <el-icon class="file-icon"><Document /></el-icon>
            <div class="file-info">
              <p class="file-name">{{ selectedFile.name }}</p>
              <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
            </div>
            <el-button
              type="text"
              @click.stop="removeFile"
              class="remove-btn"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </el-upload>
      </el-form-item>

      <!-- 模型信息 -->
      <el-form-item label="模型名称" prop="name" required>
        <el-input
          v-model="form.name"
          placeholder="请输入模型名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="模型描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入模型描述"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="分类" prop="category">
        <el-select v-model="form.category" placeholder="请选择分类">
          <el-option label="工业设备" value="industrial" />
          <el-option label="建筑结构" value="building" />
          <el-option label="机械零件" value="mechanical" />
          <el-option label="其他" value="general" />
        </el-select>
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-input
          v-model="tagsInput"
          placeholder="请输入标签，用逗号分隔"
          @blur="updateTags"
        />
        <div v-if="form.tags.length > 0" class="tags-display">
          <el-tag
            v-for="(tag, index) in form.tags"
            :key="index"
            closable
            @close="removeTag(index)"
            style="margin-right: 8px; margin-top: 8px;"
          >
            {{ tag }}
          </el-tag>
        </div>
      </el-form-item>

      <el-form-item label="公开模型">
        <el-switch
          v-model="form.isPublic"
          active-text="公开"
          inactive-text="私有"
        />
        <div class="form-hint">
          公开的模型可以被其他用户使用
        </div>
      </el-form-item>
    </el-form>

    <!-- 上传进度 -->
    <div v-if="uploadProgress" class="upload-progress">
      <el-progress
        :percentage="uploadProgress.percentage"
        :status="uploadProgress.percentage === 100 ? 'success' : undefined"
      />
      <p class="progress-text">
        上传中... {{ uploadProgress.percentage }}%
        ({{ formatFileSize(uploadProgress.loaded) }} / {{ formatFileSize(uploadProgress.total) }})
      </p>
    </div>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel" :disabled="uploading">取消</el-button>
      <el-button
        type="primary"
        @click="handleUpload"
        :loading="uploading"
        :disabled="!selectedFile"
      >
        {{ uploading ? '上传中...' : '上传模型' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules, type UploadInstance } from 'element-plus'
import { UploadFilled, Document, Close } from '@element-plus/icons-vue'
import { modelService, type Model3DInfo, type UploadProgress } from '../services/modelService'

const emit = defineEmits(['upload-success', 'upload-error', 'cancel'])

// 表单引用
const formRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()

// 表单数据
const form = reactive({
  name: '',
  description: '',
  category: 'general',
  tags: [] as string[],
  isPublic: false
})

// 其他状态
const selectedFile = ref<File | null>(null)
const tagsInput = ref('')
const uploading = ref(false)
const uploadProgress = ref<UploadProgress | null>(null)

// 支持的文件格式
const acceptedFormats = '.glb,.gltf,.obj,.fbx,.3ds,.dae,.stl'

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}

// 文件上传前的检查
const handleBeforeUpload = (file: File) => {
  // 检查文件类型
  const fileExtension = file.name.split('.').pop()?.toLowerCase()
  const allowedExtensions = ['glb', 'gltf', 'obj', 'fbx', '3ds', 'dae', 'stl']
  
  if (!allowedExtensions.includes(fileExtension || '')) {
    ElMessage.error('不支持的文件格式！')
    return false
  }

  // 检查文件大小 (50MB)
  const maxSize = 50 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过 50MB！')
    return false
  }

  return false // 阻止自动上传
}

// 文件选择变化
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
  
  // 自动填充模型名称
  if (!form.name && file.raw) {
    const nameWithoutExt = file.raw.name.replace(/\.[^/.]+$/, '')
    form.name = nameWithoutExt
  }
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  uploadRef.value?.clearFiles()
}

// 更新标签
const updateTags = () => {
  if (tagsInput.value.trim()) {
    const tags = tagsInput.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag && !form.tags.includes(tag))
    
    form.tags.push(...tags)
    tagsInput.value = ''
  }
}

// 移除标签
const removeTag = (index: number) => {
  form.tags.splice(index, 1)
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 处理上传
const handleUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请选择要上传的文件')
    return
  }

  // 表单验证
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) return

  uploading.value = true
  uploadProgress.value = null

  try {
    // 监听上传进度
    const progressRef = modelService.getUploadProgress()
    const unwatch = progressRef.value ? null : setInterval(() => {
      uploadProgress.value = modelService.getUploadProgress().value
    }, 100)

    // 上传模型
    const modelInfo = await modelService.uploadModel(selectedFile.value, {
      name: form.name,
      description: form.description,
      category: form.category,
      tags: form.tags,
      isPublic: form.isPublic
    })

    if (unwatch) clearInterval(unwatch)
    uploadProgress.value = null

    emit('upload-success', modelInfo)
  } catch (error) {
    uploadProgress.value = null
    emit('upload-error', (error as Error).message)
  } finally {
    uploading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.model-upload-form {
  padding: 20px 0;
}

.upload-area {
  width: 100%;
}

.upload-area :deep(.el-upload) {
  width: 100%;
}

.upload-area :deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-content {
  text-align: center;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
  color: #606266;
}

.upload-text em {
  color: #409eff;
  font-style: normal;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

.file-selected {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 20px;
}

.file-icon {
  font-size: 32px;
  color: #409eff;
  margin-right: 12px;
}

.file-info {
  flex: 1;
}

.file-name {
  margin: 0 0 4px 0;
  font-weight: 500;
  color: #303133;
}

.file-size {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.remove-btn {
  color: #f56c6c;
}

.tags-display {
  margin-top: 8px;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.upload-progress {
  margin: 20px 0;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.progress-text {
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}
</style>
