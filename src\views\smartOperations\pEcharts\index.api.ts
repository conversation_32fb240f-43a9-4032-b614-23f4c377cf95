import request from '@/utils/request'

enum Api {
  tbasList = '/device/all',
  dataList = '/device/point/data',
  ecahrtsData = '/device/deviceProperty/downSampling/list',
}

// 查询数据监控tabs
export const getTabsList = (data) => {
  return request({
    url: Api.tbasList,
    method: 'post',
    data,
  })
}
// 查询数据监控内容
export const getDataList = (data) => {
  return request({
    url: Api.dataList,
    method: 'post',
    data,
  })
}
// 查询图表
export const getechartsData = (data) => {
  return request({
    url: Api.ecahrtsData,
    method: 'post',
    data,
  })
}
