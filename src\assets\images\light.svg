<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="52" height="45"><defs><filter id="a" width="118.8%" height="122.5%" x="-9.4%" y="-6.2%" filterUnits="objectBoundingBox"><feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="1"/><feColorMatrix in="shadowBlurOuter1" result="shadowMatrixOuter1" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><filter id="c" width="108.3%" height="110%" x="-4.2%" y="-2.5%" filterUnits="objectBoundingBox"><feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation=".5"/><feColorMatrix in="shadowBlurOuter1" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/></filter><rect id="b" width="48" height="40" x="0" y="0" rx="4"/></defs><g fill="none" fill-rule="evenodd" filter="url(#a)" transform="translate(2 1)"><mask id="d" fill="#fff"><use xlink:href="#b"/></mask><use xlink:href="#b" fill="#000" filter="url(#c)"/><use xlink:href="#b" fill="#F0F2F5"/><path fill="#FFF" d="M0 0h16v40H0z" mask="url(#d)"/><path fill="#FFF" d="M0 0h48v10H0z" mask="url(#d)"/></g></svg>