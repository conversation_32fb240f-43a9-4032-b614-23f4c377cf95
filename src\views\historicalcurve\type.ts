export interface hisDataQuery {
  id?: number
  projectId: number
  configType: number
}
export interface hisModelsData {
  identifier: string
  name: string
  value?: string
  unit?: string
  deviceId: string
  remark?: string
}
export interface hisDataVO {
  name: string
  id: number
  models: hisModelsData[]
}

export interface echatsQuery {
  deviceId: string
  identifier: string
  startTime: string
  endTime: string
  unit: string
}
export interface echatshisdata {
  value: number
  occur?: string
  time: string
}
export interface echatsVO {
  his: echatshisdata[]
  avg: number
  max: number
  min: number
}
