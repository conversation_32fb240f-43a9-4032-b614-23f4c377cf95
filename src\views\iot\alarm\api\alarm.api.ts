// 告警Api
import request from '@/utils/request'

enum Api {
  configList = '/alert/selectAlertConfigPage',
  configUpdate = '/alert/updateAlertConfig',
  configAdd = '/alert/createAlertConfig',
  configDelete = '/alert/deleteAlertConfigById',
  msgList = '/alert/selectAlertRecordPage',
  alertList = '/alert/list',
  setsList = '/project/powerPointConfig/all',
  configDetail = '/system/configDetail/list',
  configDetailAdd = '/system/configDetail/add',
  configDetailDeviceList = '/device/list',
  configDetailpowerUnitall = '/project/powerUnit/all',
  configDetailThingModelByProductKey = '/product/getThingModelByProductKey',
  configDetaildelete = '/system/configDetail/delete',
  configDetailedit = 'system/configDetail/edit',
  selectAlertRecordPage = '/alert/selectAlertRecordPage',
  aramcopyConfig = '/system/configDetail/copy/config',
  aramcopydetail = '/system/configDetail/copy/detail',
}
// 明细复制
export const aramcopydetail = (data) => {
  return request({
    url: Api.aramcopydetail,
    method: 'post',
    data,
  })
}
// 外层复制按钮
export const aramcopyConfig = (data) => {
  return request({
    url: Api.aramcopyConfig,
    method: 'post',
    data,
  })
}
// 获取告警配置列表
export const getConfigList = (data) => {
  return request({
    url: Api.configList,
    method: 'post',
    data,
  })
}
// 查询机组
export const belongingUnit = (data) => {
  return request({
    url: Api.setsList,
    method: 'post',
    data,
  })
}
// 获取告警列表
export const getAlertList = (data) => {
  return request({
    url: Api.alertList,
    method: 'post',
    data,
  })
}
// 告警配置保存
export const saveConfig = (data) => {
  return request({
    url: data.id ? Api.configUpdate : Api.configAdd,
    method: 'post',
    data,
  })
}
// 告警配置删除
export const deleteConfig = (data) => {
  return request({
    url: Api.configDelete,
    method: 'post',
    data,
  })
}
// 告警消息
export const getMsgList = (data) => {
  return request({
    url: Api.msgList,
    method: 'post',
    data,
  })
}

// 查询智能运维配置详情
export const getConfigDetail = (data) => {
  return request({
    url: Api.configDetail,
    method: 'post',
    data,
  })
}

// 新增智能运维配置
export const addConfigDetail = (data) => {
  return request({
    url: Api.configDetailAdd,
    method: 'post',
    data,
  })
}
// 查询设备列表
export const getDeviceList = (data) => {
  return request({
    url: Api.configDetailDeviceList,
    method: 'post',
    data,
  })
}
// 查询机组
export const getpowerUnitall = (data) => {
  return request({
    url: Api.configDetailpowerUnitall,
    method: 'post',
    data,
  })
}
// 获取设备对应点位
export const getDevicePoint = (data) => {
  return request({
    url: Api.configDetailThingModelByProductKey,
    method: 'post',
    data,
  })
}
// 删除告警配置
export const deleteConfigDetail = (data) => {
  return request({
    url: Api.configDetaildelete,
    method: 'post',
    data,
  })
}
// 修改告警配置
export const editConfigDetail = (data) => {
  return request({
    url: Api.configDetailedit,
    method: 'post',
    data,
  })
}
// 查询历史报警信息alert/selectAlertRecordPage
export const getHistoryAlarm = (data) => {
  return request({
    url: Api.selectAlertRecordPage,
    method: 'post',
    data,
  })
}
