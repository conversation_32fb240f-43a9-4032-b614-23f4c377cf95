<template>
  <div class="app-container home">
    <yt-table-fun @handle-add="handleRegularAdd()">
      <template #rightToolbar>
        <el-button style="margin-right: 10px" type="primary" v-if="showPwdConfigBtn" icon="Lock" plain @click="handlePwdConfig()"
          >设备密码配置</el-button
        >
        <el-button style="margin-right: 10px" type="primary" v-if="showPwdConfigBtn" plain @click="controlHistory()">控制历史</el-button>

        <el-button
          style="margin-right: 10px"
          type="primary"
          v-if="showPwdConfigBtn"
          plain
          icon="Tools"
          :disabled="!selectedRows.length"
          @click="confirmBatchDelete()"
          >批量操作</el-button
        >
        <el-radio-group v-model="layoutType">
          <el-radio-button label="table">
            <svg-icon icon-class="table2" />
          </el-radio-button>
          <el-radio-button label="card">
            <svg-icon icon-class="card" />
          </el-radio-button>
        </el-radio-group>
      </template>
      <template v-if="layoutType === 'table'">
        <yt-table
          ref="regularTableRef"
          :data="fundata"
          :column="funColumn"
          :view-btn="false"
          :dialogBtn="false"
          :page="pageObj"
          selection
          menu-slot
          @update:page="getFundata"
          @changePage="getFundata"
          @handle-update="handleFunUpdate"
          @handle-delete="handleFunDel"
          @handle-selection-change="onSelectionChange"
          :total="fanConfigTotal"
        >
          <template #activate="{ row }">
            <el-switch v-model="row.activate" disabled :active-value="true" :inactive-value="false" />
          </template>
          <!-- 新增配置按钮插槽 -->
          <template #menuSlot="{ row }">
            <el-tooltip class="box-item" effect="dark" content="控制点位配置" placement="top">
              <el-button link type="warning" icon="Setting" @click="CtrlPtConfig(row)" />
            </el-tooltip>
          </template>
          <!-- 工作方式插槽 -->
          <template #workWay="{ row }">
            {{ getWorkWayLabel(row.workWay) }}
          </template>
        </yt-table>
      </template>
      <template v-else>
        <div class="card-groups-container">
          <div v-for="group in groupedData" :key="group.id" class="card-group">
            <!-- 分组标题 -->
            <div class="group-header" @click="toggleGroupCollapse(group.id)">
              <div class="group-title">
                <svg-icon :icon-class="groupCollapseStates[group.id] ? 'arrow-down' : 'arrow-right'" class="collapse-icon" />
                <span>{{ group.name }}</span>
                <span class="group-count">({{ group.items.length }})</span>
              </div>
            </div>
            
            <!-- 分组内容 -->
            <transition name="group-collapse">
              <el-row v-if="groupCollapseStates[group.id]" class="card-list flex">
                <el-col class="card-item" v-for="(item, index) in group.items" :key="index" :class="item.activate ? 'success-box' : 'error-box'">
                  <div class="text-box">
                    <div class="title flex align-center">
                      <div class="title-l">
                        <div class="icon">
                          <svg-icon icon-class="card2" />
                        </div>
                        {{ item.name }}
                      </div>
                      <div class="title-r">
                        <status-tag
                          :type="item.onOff === 1 ? 'success' : item.onOff === 0 ? 'info' : 'danger'"
                          :text="item.onOff === 1 ? '在线' : item.onOff === 0 ? '离线' : '异常'"
                        />
                      </div>
                    </div>
                    <div class="text flex">
                      <div class="txt">
                        <div class="txt-item">
                          <div class="label">型号</div>
                          <div class="value">{{ item.model }}</div>
                        </div>
                        <div class="txt-item">
                          <div class="label">工作方式</div>
                          <div class="value">{{ getWorkWayLabel(item.workWay) }}</div>
                        </div>
                      </div>
                      <div class="img">
                        <img :src="item.onOff === 0 ? fan0 : item.onOff === 1 ? fan1 : fan3" alt="设备图" style="width: 50px;height: 50px;" />
                      </div>
                    </div>
                  </div>
                  <div class="btn-group">
                    <el-button class="cu-btn" type="primary" icon="EditPen" plain @click="handleFunUpdate(item)">编辑</el-button>
                    <el-button class="cu-btn" type="warning" icon="Setting" v-if="showPwdConfigBtn" plain @click="handleOpenTranslate(item)"
                      >配置</el-button
                    >
                    <el-button class="cu-btn" type="info" icon="Switch" v-if="showPwdConfigBtn" plain @click="handleControl(item)">控制</el-button>
                    <!-- 密码控制 -->
                    <el-divider direction="vertical" />
                    <el-popconfirm title="是否确认删除?" @confirm="handleFunDel(item)">
                      <template #reference>
                        <el-button class="cu-btn" type="danger" icon="Delete" plain />
                      </template>
                    </el-popconfirm>
                  </div>
                </el-col>
              </el-row>
            </transition>
          </div>
        </div>
      </template>
    </yt-table-fun>
    <!-- 新增固定参数 -->
    <yt-table-form
      ref="tableRegularFormRef"
      labelWidth="180px"
      :width="600"
      align="center"
      :column="funColumn"
      @onSuccess="handleFunSave"
    ></yt-table-form>
    <!-- 控制配置 -->
    <el-dialog v-model="translateDialogVisible" title="通信参数配置" width="450px" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form :model="translateForm" label-width="100px">
        <el-form-item label="IP 地址" prop="ip">
          <el-input v-model="translateForm.ip" placeholder="请输入 IP 地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model="translateForm.port" placeholder="请输入端口" @input="translateForm.port = translateForm.port.replace(/[^\d]/g, '')" />
        </el-form-item>
        <el-form-item label="从站地址" prop="slave">
          <el-input
            v-model="translateForm.slave"
            placeholder="请输入从站地址"
            @input="translateForm.slave = translateForm.slave.replace(/[^\d]/g, '')"
          />
        </el-form-item>
        <el-form-item label="起始地址" prop="start">
          <el-input
            v-model="translateForm.start"
            placeholder="请输入起始地址"
            @input="translateForm.start = translateForm.start.replace(/[^\d]/g, '')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelTranslate">取消</el-button>
        <el-button type="primary" @click="saveTranslate">保存</el-button>
      </template>
    </el-dialog>
    <!-- 配置密码弹窗 -->
    <el-dialog v-model="passwordDialogVisible" title="请输入操作密码" width="400px" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form>
        <!-- 密码 -->
        <el-form-item label="密码" label-width="60px">
          <el-input v-model="passwordInput" show-password placeholder="请输入操作密码" @keyup.enter="confirmPassword" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmPassword">确认</el-button>
      </template>
    </el-dialog>
    <!-- 控制操作 -->
    <el-dialog v-model="controlDialogVisible" title="设备控制" width="450px" :close-on-press-escape="false" :close-on-click-modal="false">
      <!-- 第一排：开 / 关 -->
      <div class="control-row" style="display:flex; justify-content:space-evenly; gap:12px; margin-bottom:12px;">
        <el-button :type="start === 1 ? 'primary' : 'default'" @click="start = 1; end = 0">开启</el-button>
        <el-button :type="end === 1 ? 'primary' : 'default'" @click="end = 1; start = 0; power = 0; variable = '0'">关闭</el-button>
      </div>

      <!-- 第二排：工频 / 变频 -->
      <div class="control-row" style="display:flex; justify-content:space-evenly; gap:12px; margin-bottom:16px;">
        <el-button
          :type="power === 1 ? 'primary' : 'default'"
          :disabled="end === 1 || isFrequencyDisabled"
          @click="power = power === 1 ? null : 1"
        >工频</el-button>
        <el-button
          :type="power === 0 ? 'primary' : 'default'"
          :disabled="end === 1 || isVariableFrequencyDisabled"
          @click="power = power === 0 ? null : 0"
        >变频</el-button>
      </div>

      <!-- 频率输入 -->
      <el-form label-width="80px" style="text-align:center;" v-show="power === 0 && end !== 1">
        <el-form-item label="频率 (Hz)">
          <el-input v-model="variable" disabled placeholder="请输入 Hz" @input="variable = variable.replace(/[^\d.]/g, '')" style="width:230px;" />
        </el-form-item>
        <el-form-item label="频率 (%)">
          <el-input v-model="variablePercent" placeholder="请输入 %" @input="variablePercent = variablePercent.replace(/[^\d.]/g, '')" style="width:230px;" />
        </el-form-item>
      </el-form>

      <!-- 底部操作 -->
      <template #footer>
        <el-button @click="cancelControl">取消</el-button>
        <el-button type="primary" @click="confirmControl">确认</el-button>
      </template>
    </el-dialog>
    <el-dialog v-model="pwdconfigDialogVisible" title="设备密码配置" width="400px" :close-on-press-escape="false" :close-on-click-modal="false">
      <!-- 修改密码 -->
      <el-form @submit.prevent>
        <!-- 是否需要密码 -->
        <el-form-item label="是否需要密码" label-width="100px">
          <el-switch v-model="isNeedPassword" />
        </el-form-item>
        <el-form-item label="密码" label-width="60px" v-if="isNeedPassword">
          <el-input v-model="pwdconfigInput" show-password placeholder="请输入密码" @keyup.enter="savepwdconfig" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelpwdconfig">取消</el-button>
        <el-button type="primary" @click="savepwdconfig">保存</el-button>
      </template>
    </el-dialog>
    <!-- 控制点位配置表单弹窗 -->
    <el-dialog v-model="ctrlPtFormVisible" :title="ctrlPtFormType === 'add' ? '新增控制点位配置' : '编辑控制点位配置'" width="600px">
      <el-form :model="ctrlPtForm" ref="ctrlPtFormRef" label-width="140px">
        <el-form-item label="配置ID" v-if="ctrlPtForm.configId">
          <el-input v-model="ctrlPtForm.configId" readonly disabled />
        </el-form-item>
        <el-form-item label="IP" :rules="[{ required: true, message: '请输入IP地址', trigger: 'blur' }]">
          <el-input v-model="ctrlPtForm.ip" placeholder="请输入IP地址" />
        </el-form-item>

        <el-form-item label="通讯方式" :rules="[{ required: true, message: '请选择通讯方式', trigger: 'change' }]">
          <el-radio-group v-model="ctrlPtForm.communicationType">
            <el-radio :label="0">OPC-UA</el-radio>
            <el-radio :label="1">ModelbusTCP</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="端口" :rules="[{ required: true, message: '请输入端口', trigger: 'blur' }]">
          <el-input v-model="ctrlPtForm.port" placeholder="请输入端口" @input="ctrlPtForm.port = ctrlPtForm.port.replace(/[^\d]/g, '')" />
        </el-form-item>

        <el-form-item label="从站地址">
          <el-input
            v-model="ctrlPtForm.slaveId"
            placeholder="请输入从站地址"
            @input="ctrlPtForm.slaveId = ctrlPtForm.slaveId.replace(/[^\d]/g, '')"
          />
        </el-form-item>

        <el-form-item label="支持工作方式" :rules="[{ required: true, message: '请选择支持工作方式', trigger: 'change' }]">
          <el-select v-model="ctrlPtForm.assistWorkMode" placeholder="请选择支持工作方式" style="width: 100%;">
            <el-option
              v-for="option in assist_work_mode"
              :key="option.value"
              :label="option.label"
              :value="parseInt(option.value)"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="选择设备" :rules="[{ required: true, message: '请选择设备', trigger: 'change' }]">
          <el-select v-model="ctrlPtForm.deviceKey" placeholder="请选择设备" @change="handleFormDeviceChange" style="width: 100%;">
            <el-option v-for="device in deviceOptions" :key="device.value" :label="device.label" :value="device.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="开关状态标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.onOffCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.onOffAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="运行频率的标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.frequencyCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.frequencyAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="工作模式的标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.workModelCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.workModelAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="工作错误信息">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.workErrorCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.workErrorAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="开关设置的标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.onOffSetCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.onOffSetAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="工作模式设置的标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.workModelSetCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.workModelSetAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>

        <el-form-item label="频率设置的标识">
          <div style="display: flex; gap: 10px;">
            <el-select v-model="ctrlPtForm.frequencySetCode" placeholder="请选择" clearable style="flex: 1;">
              <el-option v-for="option in currentThingModelOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
            <el-input v-model="ctrlPtForm.frequencySetAddress" placeholder="通讯地址" style="width: 120px;" />
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="ctrlPtFormVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCtrlPtSave">保存</el-button>
      </template>
    </el-dialog>
    <!-- 控制历史 -->
    <el-dialog v-model="controlHistoryDialogVisible" title="控制历史" width="800px">
      <el-table :data="controlHistoryList" border style="width: 100%">
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="createTime" label="操作时间" width="180" align="center">
          <template #default="scope">
            {{ scope.row.createTime || '暂无时间' }}
          </template>
        </el-table-column>
        <el-table-column prop="commandListStr" label="操作历史" align="left">
          <template #default="scope">
            <div v-for="(command, index) in scope.row.commandListStr" :key="index">
              {{ command }}
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container" style="margin-top: 20px; display: flex; justify-content: flex-end;">
        <el-pagination
          v-model:current-page="pageHistor.pageNum"
          v-model:page-size="pageHistor.pageSize"
          :total="controlHistoryTotal"
          @current-change="handleHistoryPageChange"
          @size-change="handleHistorySizeChange"
          layout="total, sizes, prev, pager, next"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {
  getSubsystemList,
  listFanConfig,
  deleteFun,
  saveFunList,
  getCheckConfigedit,
  getCheckConfigList,
  addhistory,
  // 新增：控制点位配置相关接口
  addFanCtrl,
  getFanCtrlList,
  editFanCtrl,
  getDeviceList,
  getThingModelList,
  getDeviceStatus,
  getControlHistory,
} from './index'
import { ComponentInternalInstance } from 'vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { work_way,assist_work_mode } = toRefs<any>(proxy?.useDict('work_way','assist_work_mode'))

import YtTableFun from '@/components/common/yt-table-fun.vue'
import YtTable from '@/components/common/yt-table'
import { IColumn } from '@/components/common/types/tableCommon'
import YtTableForm from '@/components/common/yt-table-form'
import emitter from '@/utils/eventBus.js'
const regularTableRef = ref<InstanceType<typeof import('@/components/common/yt-table').default> | null>(null)
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const tableRegularFormRef = ref()
const fundata = ref<any[]>([])
const subSystemData = ref<any[]>([])
const controlDialogVisible = ref(false)
const passwordDialogVisible = ref(false)
// 控制历史相关变量
const controlHistoryDialogVisible = ref(false)
const controlHistoryTotal = ref(0)
// 用于存储当前多选的行
const selectedRows = ref<any[]>([])
const layoutType = ref<'table' | 'card'>('table')
// 分组折叠状态管理
const groupCollapseStates = ref<Record<string, boolean>>({})

// 按子系统分组的数据
const groupedData = computed(() => {
  const groups = new Map<string, any>()
  
  fundata.value.forEach((item: any) => {
    const subSystemId = item.subSystemId || 'unknown'
    if (!groups.has(subSystemId)) {
      // 找到对应的子系统信息
      const subSystemInfo = subSystemData.value.find((sub: any) => sub.id === subSystemId)
      groups.set(subSystemId, {
        id: subSystemId,
        name: subSystemInfo ? `${subSystemInfo.powerUnitNames}${subSystemInfo.name}` : '未知子系统',
        items: []
      })
      
      // 初始化折叠状态（默认展开）
      if (!(subSystemId in groupCollapseStates.value)) {
        groupCollapseStates.value[subSystemId] = true
      }
    }
    groups.get(subSystemId)!.items.push(item)
  })
  
  return Array.from(groups.values())
})

// 切换分组折叠状态
const toggleGroupCollapse = (groupId: string) => {
  groupCollapseStates.value[groupId] = !groupCollapseStates.value[groupId]
}

// 在线
import fan1 from '@/assets/images/fan1.png'
// 离线
import fan0 from '@/assets/images/fan0.png'
import fan3 from '@/assets/images/fan3.png'
const funColumn: IColumn[] = [
  {
    search: true,
    label: '风机名称',
    key: 'name',
    rules: [{ required: true, message: '风机名称不能为空' }],
  },
  {
    label: '型号',
    key: 'model',
  },
  { label: '权重', key: 'weight' },
  { label: '工作频率指标', key: 'condIdentifier' },
  {
    label: '工作方式',
    key: 'workWay',
    type: 'select', // 让表单用下拉框
    slot: true,     // 表格展示自定义
    componentProps: {
      options: computed(() => work_way.value || []),
    },
  },
  {
    label: '支持工作方式',
    key: 'assistWorkMode',
    type: 'select',
    get componentProps() {
      return {
        options: (toRaw(assist_work_mode.value) || []).map(item => ({
          ...item,
          value: parseInt(item.value) // 确保value是数字类型，与数据库中的数据类型一致
        })),
      };
    },
  },
  { label: '设备标识', key: 'currentDevice' },
  {
    label: '风机轴功率(kW)',
    key: 'power',
    rules: [{ required: true, message: '风机轴功率不能为空' }],
    formWatch: (scope) => {
      const power = parseFloat(scope.data.power)
      const powerFactor = parseFloat(scope.data.powerFactor)

      if (!isNaN(power) && !isNaN(powerFactor)) {
        scope.data.consumption = power * powerFactor
      } else {
        scope.data.consumption = '' // 避免 NaN 的问题
      }
    },
  },
  {
    label: '功耗系数',
    key: 'powerFactor',
    rules: [{ required: true, message: '功耗系数不能为空' }],
    formWatch: (scope) => {
      const power = parseFloat(scope.data.power)
      const powerFactor = parseFloat(scope.data.powerFactor)

      if (!isNaN(power) && !isNaN(powerFactor)) {
        scope.data.consumption = power * powerFactor
      } else {
        scope.data.consumption = '' // 避免 NaN 的问题
      }
    },
  },
  {
    label: '风机功耗(kW)',
    key: 'consumption',
    editDisabled: true, //禁止编辑
    addDisabled: true, //禁止新增
  },
  {
    label: '电动机额定电压(kV)',
    key: 'voltage',
  },
  {
    label: '额定电流(A)',
    key: 'ratedCurrent',
  },
  {
    label: '模拟电流(A)',
    key: 'current',
  },
  {
    label: '风机电流标识',
    key: 'currentIdentifier',
    rules: [{ required: true, message: '电动机额定电压不能为空' }],
  },
  {
    label: '激活状态',
    key: 'activate',
    tableWidth: 120,
    type: 'switch',
    slot: true,
    rules: [{ required: true, message: '请选择激活状态' }],
  },
  {
    label: '所属子系统',
    key: 'subSystemId',
    type: 'select',
    rules: [{ required: true, message: '所属子系统不能为空' }],
    // search: true,
    componentProps: {
      // defaultValue: '',
      clearable: false,
      options: [],
    },
  },
  {
    label: '备注',
    key: 'description',
    // type:'textarea'
  },
]
const isNeedPassword = ref(false)
const pwdconfigDialogVisible = ref(false)
const pwdconfigInput = ref('')
const Pwdid = ref()
const pwdConfig = ref<null | {
  id: number
  passwd: string
  type: number
}>(null)
const showPwdConfigBtn = computed(() => pwdConfig.value !== null)
// 密码配置
const handlePwdConfig = () => {
  if (!pwdConfig.value) return
  pwdconfigDialogVisible.value = true
  pwdconfigInput.value = pwdConfig.value.passwd
  Pwdid.value = pwdConfig.value.id
  isNeedPassword.value = pwdConfig.value.type === 0
}
// 取消密码配置
const cancelpwdconfig = () => {
  pwdconfigDialogVisible.value = false
  pwdconfigInput.value = ''
}
const getWorkWayLabel = (value: string) => {
  if (!work_way.value) return value
  const match = work_way.value.find((item: any) => item.value === value)
  console.log(match, 'match--')
  return match ? match.label : value
}
// 保存密码配置
const savepwdconfig = () => {
  pwdconfigDialogVisible.value = false
  const params = {
    id: Pwdid.value,
    projectId: cachedProjects.id,
    passwd: pwdconfigInput.value,
    type: isNeedPassword.value ? 0 : 1,
  }
  getCheckConfigedit(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success('保存成功')
      loadPwdConfig()
    }
  })
}
// 批量操作
const confirmBatchDelete = () => {
  handleControl()
}
const translateDialogVisible = ref(false)
const translateForm = reactive({
  ip: '',
  port: '',
  slave: '',
  start: '',
})
// 配置
let currentTranslateRow: any = null
const handleOpenTranslate = (row: any) => {
  currentTranslateRow = row
  // 如果 row 里有初始值就赋上去，否则清空
  translateForm.ip = row.ip || ''
  translateForm.port = row.port || ''
  translateForm.slave = row.slave || ''
  translateForm.start = row.start || ''
  translateDialogVisible.value = true
}
// 取消
const cancelTranslate = () => {
  translateDialogVisible.value = false
}
const saveTranslate = () => {
  // 简单校验
  if (!translateForm.ip) {
    ElMessage.warning('IP 地址不能为空')
    return
  }
  if (!translateForm.port) {
    ElMessage.warning('端口不能为空')
    return
  }
  // … 可加更多校验 …

  // 模拟保存，带上当前行的 id
  ElMessage.success('保存成功（模拟）')
  translateDialogVisible.value = false
}

// 控制相关变量
const passwordInput = ref('')
const start = ref<1 | 0 | null>(null)
const end = ref<1 | 0 | null>(null)
const power = ref<1 | 0 | null>(null)
const variable = ref('')
const variablePercent = ref('')
const currentControlRow = ref<any>(null)

// 新增：存储设备的初始状态
const deviceInitialState = ref<{
  start: 1 | 0 | null
  end: 1 | 0 | null
  power: 1 | 0 | null
  variable: string
}>({
  start: null,
  end: null,
  power: null,
  variable: '',
})
// 查询控制历史列表
const pageHistor = reactive({
  pageNum: 1,
  pageSize: 10,
})
const controlHistoryList = ref<any[]>([])
const controlHistory = () => {
  controlHistoryDialogVisible.value = true
  getControlHistoryList()
}
// 获取控制历史列表
const getControlHistoryList = () => {
  getControlHistory({
    ...pageHistor,
    projectId: cachedProjects.id,
    deviceType: 1,
  }).then((res) => {
    if (res.code === 200) {
      controlHistoryList.value = res.data.rows
      controlHistoryTotal.value = res.data.total
    }
  })
}
// 分页处理
const handleHistoryPageChange = (page: number) => {
  pageHistor.pageNum = page
  getControlHistoryList()
}
const handleHistorySizeChange = (size: number) => {
  pageHistor.pageSize = size
  pageHistor.pageNum = 1
  getControlHistoryList()
}

const handleControl = async (item?: any) => {
  // 保存当前操作的设备信息
  if (item) {
    // 单个设备控制 - 从最新的表格数据中获取设备信息
    const latestDeviceData = fundata.value.find(device => device.id === item.id)
    currentControlRow.value = latestDeviceData || item // 优先使用最新数据，如果找不到则使用传入的数据

    // 先获取设备状态
    try {
      const res = await getDeviceStatus(item.id)
      if (res.code === 200 && res.data) {
        // 将获取到的设备状态设置到控制变量中
        start.value = res.data.start === 1 ? 1 : res.data.start === 0 ? 0 : null
        end.value = res.data.end === 1 ? 1 : res.data.end === 0 ? 0 : null
        power.value = res.data.power === 1 ? 1 : res.data.power === 0 ? 0 : null
        variable.value = res.data.variable || ''
        variablePercent.value = res.data.variablePercent || ''
        // 保存初始状态
        deviceInitialState.value = {
          start: start.value,
          end: end.value,
          power: power.value,
          variable: variable.value,
        }
      }
    } catch (error) {
      console.error('获取设备状态失败:', error)
      // 如果获取状态失败，重置为初始状态
      start.value = null
      end.value = null
      power.value = null
      variable.value = ''
      variablePercent.value = ''
      deviceInitialState.value = {
        start: null,
        end: null,
        power: null,
        variable: '',
      }
    }
  } else {
    // 批量控制
    currentControlRow.value = null
    // 批量控制时重置状态
    start.value = null
    end.value = null
    power.value = null
    variable.value = ''
    variablePercent.value = ''
    deviceInitialState.value = {
      start: null,
      end: null,
      power: null,
      variable: '',
    }
  }

  passwordInput.value = ''

  // 状态获取完成后，再进行密码验证或直接打开控制弹窗
  if (pwdConfig.value?.type === 0) {
    passwordDialogVisible.value = true
  } else {
    openControlDialog()
  }
}

// 2. 密码确认
const confirmPassword = () => {
  const CORRECT = pwdConfig.value?.passwd
  if (passwordInput.value === CORRECT) {
    passwordDialogVisible.value = false
    openControlDialog()
  } else {
    ElMessage.error('密码错误，请重试')
  }
}

const openControlDialog = () => {
  // 直接打开控制弹窗，状态已经在 handleControl 中设置好了
  controlDialogVisible.value = true
}

// 根据 assistWorkMode 控制工频和变频按钮的可用性（最优先判断规则）
// 0代表工频（不能选择变频），1代表变频（不能选择工频），2代表工变频（可以切换）
const isFrequencyDisabled = computed(() => {
  // 单个设备控制时，根据当前设备的 assistWorkMode 判断
  if (currentControlRow.value) {
    const assistWorkMode = currentControlRow.value.assistWorkMode
    // assistWorkMode为1时，工频不可选（兼容字符串和数字类型）
    return assistWorkMode === '1' || assistWorkMode === 1
  }

  // 批量控制时，如果所有选中设备都不支持工频，则禁用工频按钮
  if (selectedRows.value.length > 0) {
    return selectedRows.value.every(row => row.assistWorkMode === '1' || row.assistWorkMode === 1)
  }

  return false
})

const isVariableFrequencyDisabled = computed(() => {
  // 单个设备控制时，根据当前设备的 assistWorkMode 判断
  if (currentControlRow.value) {
    const assistWorkMode = currentControlRow.value.assistWorkMode
    // assistWorkMode为0时，变频不可选（兼容字符串和数字类型）
    return assistWorkMode === '0' || assistWorkMode === 0
  }

  // 批量控制时，如果所有选中设备都不支持变频，则禁用变频按钮
  if (selectedRows.value.length > 0) {
    return selectedRows.value.every(row => row.assistWorkMode === '0' || row.assistWorkMode === 0)
  }

  return false
})

// 3. 取消控制
const cancelControl = () => {
  controlDialogVisible.value = false
  selectedRows.value = []
  currentControlRow.value = null
}

// 4. 确认控制
const confirmControl = () => {
  let devicesToControl: any[] = []

  // 判断是单个控制还是批量控制
  if (currentControlRow.value) {
    // 单个设备控制
    devicesToControl = [currentControlRow.value]
  } else {
    // 批量控制
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请先选择要控制的设备')
      return
    }
    devicesToControl = selectedRows.value
  }

  if (start.value === null && end.value === null) {
    ElMessage.warning('请选择 开启 或 关闭')
    return
  }

  // 为选中的设备创建控制指令
  const commandArray = devicesToControl.map((row) => {
    const command: any = {
      configName: row.name,
      configId: currentControlRow.value ? currentControlRow.value.id : selectedRows.value[0].id,
    }

    // 根据用户选择构建控制参数
    if (start.value === 1) {
      // 开启状态
      command.start = 1

      if (power.value === 1) {
        // 工频模式
        command.power = 1
      } else if (power.value === 0 && variablePercent.value) {
        // 变频模式且有频率值
        command.power = 0
        command.variablePercent = variablePercent.value
      }
    } else if (end.value === 1) {
      // 关闭状态
      command.end = 1
    }

    return command
  })

  const params = {
    projectId: cachedProjects.id,
    type: 0,
    deviceType: 1, // 风机设备类型为1
    commandList: commandArray,
  }

  addhistory(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success('控制成功')
    }
  })

  ElMessage.success(`已向 ${devicesToControl.length} 台设备发送控制指令`)

  // 清空选择
  selectedRows.value = []
  currentControlRow.value = null
  controlDialogVisible.value = false
}

watch(power, (newVal) => {
  if (newVal !== 0) {
    // 当不是变频时
    variable.value = '' // 清空频率值
    variablePercent.value = '' // 清空百分比频率值
  }
})

// 监听表格多选变化
const onSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

const handleFunUpdate = (row: any) => {
  tableRegularFormRef.value?.openDialog('update', row)
}

const loading = ref(false)
const handleFunDel = (row: any) => {
  loading.value = true
  deleteFun([row.id])
    .then((res) => {
      if (res.code === 200) {
        ElMessage.success('删除成功!')
        getFundata()
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const handleRegularAdd = () => {
  tableRegularFormRef.value?.openDialog('add', {})
}

const handleFunSave = ({ type, data, cancel }) => {
  loading.value = true
  const { id: projectId } = cachedProjects
  if (type == 'add') {
    const { activate, ...restData } = data

    const modifiedData = {
      ...restData,
      projectId,
      activate: activate ? 1 : 0,
    }

    saveFunList(toRaw(modifiedData))
      .then((res) => {
        if (res.code === 200) {
          cancel()
          getFundata()
        }
      })
      .finally(() => {
        loading.value = false
      })
  } else if (type === 'update') {
    const { activate, ...restData } = data
    const modifiedData = {
      ...restData,
      projectId,
      activate: activate ? 1 : 0,
    }

    saveFunList(toRaw(modifiedData))
      .then((res) => {
        if (res.code === 200) {
          cancel()
          getFundata()
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}
const fanConfigTotal = ref(0)
const pageObj = reactive({
  pageNum: 1,
  pageSize: 10,
})
// 获取风机数据
const getFundata = () => {
  const params = {
    projectId: cachedProjects.id,
    pageNum: pageObj.pageNum,
    pageSize: pageObj.pageSize,
  }
  listFanConfig(params).then((res) => {
    if (res.data.rows.length > 0) {
      fundata.value = res.data.rows.map((item: any) => ({
        ...item,
        activate: item.activate === 1, // 如果activate为1则转换为true，否则转换为false
        workWay: String(item.workWay), // 确保 workWay 是字符串
        assistWorkMode: typeof item.assistWorkMode === 'string' ? parseInt(item.assistWorkMode) : item.assistWorkMode, // 确保 assistWorkMode 是数字类型
      }))
      fanConfigTotal.value = res.data.total
    }
  })
}
// 查询所属子系统
const getSubSystemData = () => {
  const params = {
    identifier: 'cooling_tower',
    projectId: cachedProjects.id,
  }

  return getSubsystemList(params).then((res) => {
    subSystemData.value = res.data // 保存子系统数据
    if (res.code === 200) {
      const subSystemColumn = funColumn.find((col) => col.key === 'subSystemId')

      if (subSystemColumn) {
        // 强制触发响应式更新
        subSystemColumn.componentProps = {
          ...subSystemColumn.componentProps, // 保留原有属性
          options: res.data.map((item: any) => ({
            value: item.id,
            label: item.powerUnitNames + item.name,
          })),
        }
      }
    }
  })
}
async function loadPwdConfig() {
  const params = { projectId: cachedProjects.id }
  try {
    const res = await getCheckConfigList(params)
    if (res.data.rows.length > 0) {
      const row = res.data.rows[0]
      pwdConfig.value = {
        id: row.id,
        passwd: row.passwd,
        type: row.type,
      }
      // 立刻初始化三个控件
      isNeedPassword.value = row.type === 0
      pwdconfigInput.value = row.passwd
      Pwdid.value = row.id
    }
  } catch (err) {
    console.error('加载密码配置失败', err)
  }
}
async function getCheckConfig() {
  // getCheckConfig().then((res) => {
  // })
}
onMounted(async () => {
  getSubSystemData().then(() => {
    getFundata() // 在获取子系统数据后，获取表格数据
  })
  await loadPwdConfig()
})

emitter.on('projectListChanged', (e) => {
  location.reload()
})

// 新增：控制点位配置相关变量
const ctrlPtFormVisible = ref(false)
const ctrlPtFormType = ref('add') // 'add' | 'edit'
const ctrlPtEditIndex = ref(-1)

// 控制点位配置表单
const ctrlPtForm = reactive({
  id: '', // 记录ID，用于编辑
  configId: '',
  deviceKey: '',
  deviceName: '',
  productKey: '',
  ip: '',
  communicationType: 1, // 默认选择modelbusTCP
  port: '',
  slaveId: '',
  assistWorkMode: null as number | null, // 支持工作方式
  onOffCode: '',
  onOffAddress: '',
  frequencyCode: '',
  frequencyAddress: '',
  workModelCode: '',
  workModelAddress: '',
  workErrorCode: '',
  workErrorAddress: '',
  onOffSetCode: '',
  onOffSetAddress: '',
  workModelSetCode: '',
  workModelSetAddress: '',
  frequencySetCode: '',
  frequencySetAddress: '',
})

// 设备选项和物模型数据
const deviceOptions = ref<any[]>([])
const currentThingModelOptions = ref<any[]>([])

// 存储所有产品的物模型数据映射
const productThingModelMap = reactive(new Map<string, any[]>())

// 根据 productKey 和 identifier 获取对应的 name
const getNameByProductKeyAndIdentifier = (productKey: string, identifier: string): string => {
  if (!productKey || !identifier) return '-'
  const thingModelList = productThingModelMap.get(productKey)
  if (!thingModelList) return identifier // 如果没有找到物模型数据，返回原始值
  const item = thingModelList.find((model: any) => model.identifier === identifier)
  return item ? item.name : identifier // 如果找到就返回 name，否则返回原始值
}

// 加载单个产品的物模型数据
const loadThingModelForProduct = async (productKey: string) => {
  if (!productKey || productThingModelMap.has(productKey)) {
    return // 如果已经加载过，直接返回
  }
  try {
    const res = await getThingModelList(productKey)
    if (res.code === 200 && res.data) {
      // 将物模型数据的 properties 存储到映射中
      const thingModelData = res.data.model.properties || []
      productThingModelMap.set(productKey, thingModelData)
    }
  } catch (error) {
    console.error(`加载产品 ${productKey} 的物模型数据失败:`, error)
    productThingModelMap.set(productKey, []) // 设置空数组避免重复请求
  }
}

// 获取设备列表
const getDevicedata = async () => {
  const params = {
    projectId: cachedProjects.id,
  }
  try {
    const res = await getDeviceList(params)
    if (res.code === 200 && res.data) {
      // 根据实际API数据结构调整映射，包含category字段
      deviceOptions.value = res.data.rows.map((device: any) => ({
        label: device.name, // 设备名称作为显示标签
        value: device.id, // 设备ID作为值，这个会存储到deviceKey中
        productKey: device.productKey, // 产品密钥，用于调用物模型API
        deviceName: device.name, // 设备名称
        deviceId: device.id, // 设备ID
        category: device.category || '', // 设备类型
      }))
    }
  } catch (error) {
    ElMessage.error('获取设备列表失败')
  }
}

// 表单中设备选择变化处理
const handleFormDeviceChange = async (value: string) => {
  if (value) {
    // 设置deviceKey为选中的设备ID
    ctrlPtForm.deviceKey = value

    // 根据选中的设备ID获取设备信息
    const selectedDeviceInfo = deviceOptions.value.find((device) => device.value === value)

    if (selectedDeviceInfo) {
      ctrlPtForm.deviceName = selectedDeviceInfo.label // 使用label作为设备名称
      ctrlPtForm.productKey = selectedDeviceInfo.productKey // 保存productKey到表单

      // 获取该设备的物模型数据，使用productKey
      if (selectedDeviceInfo.productKey) {
        const options = await getThingModelData(selectedDeviceInfo.productKey)
        currentThingModelOptions.value = options
      }
    }
  } else {
    // 清空选择时重置相关字段
    ctrlPtForm.deviceName = ''
    ctrlPtForm.productKey = ''
    currentThingModelOptions.value = []
  }
}

// 获取控制点位配置数据 - 注意deviceType改为1（风机）
const getFanCtrlData = async (configId: string) => {
  const params = {
    // projectId: cachedProjects.id,
    deviceType: 1, // 风机设备类型为1
    configId: configId,
  }
  try {
    const res = await getFanCtrlList(params)
    if (res.code === 200 && res.data && res.data.id) {
      // 有数据且有id，说明是编辑模式
      const data = res.data
      ctrlPtForm.id = data.id || ''
      ctrlPtForm.configId = data.configId || configId
      ctrlPtForm.ip = data.ip || ''
      ctrlPtForm.communicationType = data.communicationType || 1
      ctrlPtForm.port = data.port || ''
      ctrlPtForm.slaveId = data.slaveId || ''
      ctrlPtForm.productKey = data.productKey || ''

      // 根据productKey找到对应的设备并设置deviceKey
      if (data.productKey) {
        const selectedDevice = deviceOptions.value.find((device) => device.productKey === data.productKey)
        if (selectedDevice) {
          ctrlPtForm.deviceKey = selectedDevice.value // 设备ID
          ctrlPtForm.deviceName = selectedDevice.label // 设备名称
        }
      }

      // 解析JSON字符串字段
      if (data.onOff) {
        const onOffData = JSON.parse(data.onOff)
        ctrlPtForm.onOffCode = onOffData.code || ''
        ctrlPtForm.onOffAddress = onOffData.address || ''
      }

      if (data.frequency) {
        const frequencyData = JSON.parse(data.frequency)
        ctrlPtForm.frequencyCode = frequencyData.code || ''
        ctrlPtForm.frequencyAddress = frequencyData.address || ''
      }

      if (data.workModel) {
        const workModelData = JSON.parse(data.workModel)
        ctrlPtForm.workModelCode = workModelData.code || ''
        ctrlPtForm.workModelAddress = workModelData.address || ''
      }

      if (data.workError) {
        const workErrorData = JSON.parse(data.workError)
        ctrlPtForm.workErrorCode = workErrorData.code || ''
        ctrlPtForm.workErrorAddress = workErrorData.address || ''
      }

      if (data.onOffSet) {
        const onOffSetData = JSON.parse(data.onOffSet)
        ctrlPtForm.onOffSetCode = onOffSetData.code || ''
        ctrlPtForm.onOffSetAddress = onOffSetData.address || ''
      }

      if (data.workModelSet) {
        const workModelSetData = JSON.parse(data.workModelSet)
        ctrlPtForm.workModelSetCode = workModelSetData.code || ''
        ctrlPtForm.workModelSetAddress = workModelSetData.address || ''
      }

      if (data.frequencySet) {
        const frequencySetData = JSON.parse(data.frequencySet)
        ctrlPtForm.frequencySetCode = frequencySetData.code || ''
        ctrlPtForm.frequencySetAddress = frequencySetData.address || ''
      }

      // 如果有productKey，加载对应的物模型数据
      if (data.productKey) {
        await loadThingModelForProduct(data.productKey)
        const options = await getThingModelData(data.productKey)
        currentThingModelOptions.value = options
      }

      // 设置表单类型为编辑
      ctrlPtFormType.value = 'edit'
    } else {
      // 如果没有数据或data为null，则为新增模式
      ctrlPtFormType.value = 'add'
      resetCtrlPtForm()
      ctrlPtForm.configId = configId
    }
  } catch (error) {
    console.error('获取风机控制点位配置失败:', error)
    // 如果获取失败，也设置为新增模式
    ctrlPtFormType.value = 'add'
    resetCtrlPtForm()
    ctrlPtForm.configId = configId
  }
}

// 获取物模型列表 - 用于表单下拉框
const getThingModelData = (productKey: string) => {
  return getThingModelList(productKey).then((res) => {
    if (res.code === 200 && res.data) {
      const options = res.data.model.properties.map((item: any) => ({
        label: item.name || item.identifier,
        value: item.identifier, // 使用 identifier 作为 value
      }))
      return options
    }
    return []
  })
}

// 重置控制点位表单
const resetCtrlPtForm = () => {
  ctrlPtForm.id = ''
  ctrlPtForm.configId = ''
  ctrlPtForm.deviceKey = ''
  ctrlPtForm.deviceName = ''
  ctrlPtForm.productKey = ''
  ctrlPtForm.ip = ''
  ctrlPtForm.communicationType = 1
  ctrlPtForm.port = ''
  ctrlPtForm.slaveId = ''
  ctrlPtForm.assistWorkMode = null
  ctrlPtForm.onOffCode = ''
  ctrlPtForm.onOffAddress = ''
  ctrlPtForm.frequencyCode = ''
  ctrlPtForm.frequencyAddress = ''
  ctrlPtForm.workModelCode = ''
  ctrlPtForm.workModelAddress = ''
  ctrlPtForm.workErrorCode = ''
  ctrlPtForm.workErrorAddress = ''
  ctrlPtForm.onOffSetCode = ''
  ctrlPtForm.onOffSetAddress = ''
  ctrlPtForm.workModelSetCode = ''
  ctrlPtForm.workModelSetAddress = ''
  ctrlPtForm.frequencySetCode = ''
  ctrlPtForm.frequencySetAddress = ''
  currentThingModelOptions.value = []
}

// 保存控制点位配置
const handleCtrlPtSave = async () => {
  // 表单验证
  if (!ctrlPtForm.deviceKey) {
    ElMessage.warning('请选择设备')
    return
  }
  if (!ctrlPtForm.ip) {
    ElMessage.warning('请输入IP地址')
    return
  }
  if (!ctrlPtForm.port) {
    ElMessage.warning('请输入端口')
    return
  }
  // 构建保存数据
  const saveData: any = {
    configId: ctrlPtForm.configId,
    productKey: ctrlPtForm.productKey,
    deviceType: 1, // 风机设备类型为1
    communicationType: ctrlPtForm.communicationType,
    ip: ctrlPtForm.ip,
    port: parseInt(ctrlPtForm.port),
    slaveId: ctrlPtForm.slaveId ? parseInt(ctrlPtForm.slaveId) : null,
    onOff: ctrlPtForm.onOffCode
      ? JSON.stringify({
          code: ctrlPtForm.onOffCode,
          address: ctrlPtForm.onOffAddress,
        })
      : null,
    frequency: ctrlPtForm.frequencyCode
      ? JSON.stringify({
          code: ctrlPtForm.frequencyCode,
          address: ctrlPtForm.frequencyAddress,
        })
      : null,
    workModel: ctrlPtForm.workModelCode
      ? JSON.stringify({
          code: ctrlPtForm.workModelCode,
          address: ctrlPtForm.workModelAddress,
        })
      : null,
    workError: ctrlPtForm.workErrorCode
      ? JSON.stringify({
          code: ctrlPtForm.workErrorCode,
          address: ctrlPtForm.workErrorAddress,
        })
      : null,
    onOffSet: ctrlPtForm.onOffSetCode
      ? JSON.stringify({
          code: ctrlPtForm.onOffSetCode,
          address: ctrlPtForm.onOffSetAddress,
        })
      : null,
    workModelSet: ctrlPtForm.workModelSetCode
      ? JSON.stringify({
          code: ctrlPtForm.workModelSetCode,
          address: ctrlPtForm.workModelSetAddress,
        })
      : null,
    frequencySet: ctrlPtForm.frequencySetCode
      ? JSON.stringify({
          code: ctrlPtForm.frequencySetCode,
          address: ctrlPtForm.frequencySetAddress,
        })
      : null,
    projectId: cachedProjects.id,
  }

  // 如果是编辑模式，需要添加id字段
  if (ctrlPtFormType.value === 'edit' && ctrlPtForm.id) {
    saveData.id = ctrlPtForm.id
  }

  try {
    let res
    if (ctrlPtFormType.value === 'add') {
      // 新增时调用新增接口
      res = await addFanCtrl(saveData)
    } else {
      // 编辑时调用编辑接口
      res = await editFanCtrl(saveData)
    }
    
    if (res.code === 200) {
      ElMessage.success(ctrlPtFormType.value === 'add' ? '新增成功' : '编辑成功')
      ctrlPtFormVisible.value = false
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 控制点位配置
const CtrlPtConfig = async (row: any) => {
  // 设置配置ID
  const configId = row.id || row.configId || ''

  // 首先加载设备列表，确保 deviceOptions 有数据
  await getDevicedata()

  // 然后加载控制点位数据和物模型数据
  await getFanCtrlData(configId)

  // 打开表单弹窗
  ctrlPtFormVisible.value = true
}
</script>

<style scoped lang="scss">
.card-list {
  display: flex;
  flex-wrap: wrap;
  margin: -8px;

  .card-item {
    // background: #fff;
    border: 1px solid rgba(33, 148, 255, 0.5);
    border-radius: 3px;
    flex: 0 0 calc(25% - 16px);
    margin: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: box-shadow 0.3s;

    &.success-box {
      /* 在线时可以有轻微高亮背景 */
      // .text-box { background: rgba(238, 250, 255, 0.4); }
    }

    &.error-box {
      /* 离线时用红色底的提示 */
      .text-box {
        background: rgba(255, 241, 241, 0.4);
      }
    }

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .text-box {
      padding: 16px;

      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;

        .title-l {
          display: flex;
          align-items: center;

          .icon {
            margin-right: 8px;
            display: flex;
            align-items: center;
          }
        }
      }

      .text {
        display: flex;
        align-items: flex-start;
        font-size: 14px;

        .txt {
          flex: 1;

          .txt-item {
            display: flex;
            margin-bottom: 10px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              width: 80px;
              color: #717c8e;
            }

            .value {
              color: #0070ff;

              &.active {
                color: #0070ff;
              }
            }
          }
        }

        .img {
          width: 100px;
          height: 100px;
          margin-left: 16px;
          text-align: right;

          img {
            object-fit: contain;
          }
        }
      }
    }

    .btn-group {
      padding: 12px 16px;
      border-top: 1px solid rgba(33, 148, 255, 0.5);
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .cu-btn {
        width: calc((100% - 48px) / 4);
        margin-left: 8px;
      }

      .el-button {
        padding: 8px;
      }

      /* 如果加了分隔线 */
      ::v-deep(.el-divider--vertical) {
        margin: 0 8px;
      }
    }
  }
}

/* 分组样式 */
.card-groups-container {
  .card-group {
    margin-bottom: 24px;

    .group-header {
      background: rgba(7, 53, 92, 0.8);
      border: 1px solid rgba(33, 148, 255, 0.5);
      border-radius: 4px;
      padding: 12px 16px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(7, 53, 92, 1);
        border-color: rgba(33, 148, 255, 0.8);
      }

      .group-title {
        display: flex;
        align-items: center;
        color: #fff;
        font-size: 16px;
        font-weight: 600;

        .collapse-icon {
          margin-right: 8px;
          transition: transform 0.3s ease;
          color: rgba(33, 148, 255, 1);
        }

        .group-count {
          margin-left: 8px;
          color: rgba(204, 204, 204, 0.8);
          font-size: 14px;
          font-weight: 400;
        }
      }
    }

    .card-list {
      margin-left: 16px;
    }
  }
}

/* 折叠动画 */
.group-collapse-enter-active, .group-collapse-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.group-collapse-enter-from, .group-collapse-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.group-collapse-enter-to, .group-collapse-leave-from {
  opacity: 1;
  max-height: 1000px;
  transform: translateY(0);
}

:deep(.el-select__wrapper) {
  width: 180px;
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}

:deep(.el-select__placeholder) {
  color: #fff;
}
:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}
:deep(.el-pagination) {
  // 页码按钮的间距
  .el-pager li {
    margin: 0 4px; // 调整页码之间的间距
  }
  // 上一页下一页按钮的间距
  .btn-prev {
    margin-right: 4px; // 上一页按钮右侧间距
  }
  .btn-next {
    margin-left: 4px; // 下一页按钮左侧间距
  }
}
</style>
