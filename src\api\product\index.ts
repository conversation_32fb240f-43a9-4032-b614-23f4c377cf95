import request from '@/utils/request'
import { AxiosPromise, AxiosResponse } from 'axios'
import { ThirdPartyConfigVO, ThirdPartyConfigForm, ThirdPartyConfigQuery } from './type'

// 查询产品第三方请求对接配置列表
export const listThirdPartyConfig = (data?: ThirdPartyConfigQuery): AxiosPromise<AxiosResponse<ThirdPartyConfigVO[]>> => {
  return request({
    url: '/product/thirdPartyConfig/list',
    method: 'post',
    data,
  })
}

//新增产品第三方请求对接配置
export const addThirdPartyConfig = (data: ThirdPartyConfigForm) => {
  return request({
    url: '/product/thirdPartyConfig/add',
    method: 'post',
    data,
  })
}

// 修改产品第三方请求对接配置
export const updateThirdPartyConfig = (data: ThirdPartyConfigForm) => {
  return request({
    url: '/product/thirdPartyConfig/edit',
    method: 'post',
    data,
  })
}

// 删除产品第三方请求对接配置
export const deleteThirdPartyConfig = (ids: Array<string | number>) => {
  return request({
    url: '/product/thirdPartyConfig/delete',
    method: 'post',
    data: ids,
  })
}

// 拷贝产品第三方请求对接配置
export const copyThirdPartyConfig = (id: number) => {
  return request({
    url: '/product/thirdPartyConfig/copy',
    method: 'post',
    data: id,
  })
}
