<template>
  <yt-crud
    ref="crudRef"
    :data="data"
    :column="column"
    :fun-props="{
        addBtn: false,
      }"
    :table-props="{
        Selection: false,
        delBtn: false,
        editBtn: false,
        dialogBtn:false,
        menuSlot: true,
      }"
    @onLoad="getData"
    :loading="state.loading"
    :total="state.total"
    v-model:page="state.page"
    v-model:query="state.query"
  >
    <template #menuSlot="scope">
      <el-tooltip class="box-item" effect="dark" content="查看历史报警" placement="top">
        <el-button link type="primary" icon="Operation" @click="historicalAlarm(scope.row)" />
      </el-tooltip>
    </template>
    <!-- 自定义 reason 列的渲染方式 -->
    <template #reason="{ row }">
      <div v-html="formatReason(row.reason)"></div>
    </template>
    <template #recommend="{ row }">
      <div v-html="formatRecommend(row.recommend)"></div>
    </template>
    <template #level="{ row }">
      <el-tag v-if="row.level == 1" type="warning">低限预警</el-tag>
      <el-tag v-if="row.level == 2" type="warning">低限报警</el-tag>
      <el-tag v-if="row.level == 3" effect="dark" type="danger">低限严重警告</el-tag>
      <el-tag v-if="row.level == 4" type="warning">高限预警</el-tag>
      <el-tag v-if="row.level == 5" type="danger">高限报警</el-tag>
      <el-tag v-if="row.level == 6" effect="dark" type="danger">高限严重警告</el-tag>
      <el-tag v-if="row.level == -1" type="primary">正常值</el-tag>
    </template>
    <template #dealFlag="{ row }">
      <el-tag v-if="row.dealFlag == 1">已处理</el-tag>
      <el-tag v-if="row.dealFlag == 0" effect="dark" type="danger">未处理</el-tag>
    </template>
    <template #lastOccurTime="{ row }">
      {{ row.lastOccurTime }}
      <!-- <el-tooltip class="box-item" effect="dark" content="最后一次告警发生时间" placement="top">
        <el-date-picker type="date" v-model="row.lastOccurTime" format="yyyy-MM-dd HH:mm:ss" />
      </el-tooltip> -->
    </template>
  </yt-crud>
  <el-dialog
    v-model="historicalAlarVisible"
    title="历史告警信息"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
  >
    <div class="alarmMaintenanceLIist">
      <el-table :data="historicalData">
        <el-table-column prop="name" label="告警名称" />
        <el-table-column prop="createTime" label="告警时间" />
        <el-table-column prop="level" label="告警等级">
          <template #default="{ row }">
            <el-tag v-if="row.level==1" type="warning">低限预警</el-tag>
            <el-tag v-if="row.level==2" type="warning">低限报警</el-tag>
            <el-tag v-if="row.level==3" effect="dark" type="danger">低限严重警告</el-tag>
            <el-tag v-if="row.level==4" type="warning">高限预警</el-tag>
            <el-tag v-if="row.level==5" type="danger">高限报警</el-tag>
            <el-tag v-if="row.level==6" effect="dark" type="danger">高限严重警告</el-tag>
            <el-tag v-if="row.level==-1">正常值</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <yt-table :data="historicalData" :column="historicalColumn" :view-btn="false"> </yt-table> -->
  </el-dialog>
</template>
<script lang="ts" setup>
import { IColumn } from '@/components/common/types/tableCommon'
import { getMsgList, getAlertList, belongingUnit, getHistoryAlarm } from './api/alarm.api'

import YtCrud from '@/components/common/yt-crud.vue'

import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
const historicalAlarVisible = ref(false)
const historicalData = ref([])
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const column: IColumn[] = [
  {
    label: '名称',
    key: 'name',
    search: true,
    rules: [{ required: true, message: '告警名称不能为空' }],
  },
  {
    label: '所属设备',
    key: 'productName',
    // search: true,
  },
  {
    label: '告警值',
    key: 'value',

  },
  {
    label: '告警等级',
    key: 'level',
    tableWidth: 120,
    search: true,
    type: 'select',
    slot: true,
    rules: [{ required: true, message: '请选择告警等级' }],
    componentProps: {
      options: [
        {
          label: '低限预警',
          value: 1,
        },
        {
          label: '低限报警',
          value: 2,
        },
        {
          label: '低限严重警告',
          value: 3,
        },
        {
          label: '高限预警',
          value: 4,
        },
        {
          label: '高限报警',
          value: 5,
        },
        {
          label: '高限严重警告',
          value: 6,
        },
        {
          label: '正常值',
          value: -1,
        },
      ],
    },
  },
  {
    label: '状态',
    key: 'dealFlag',
    tableWidth: 180,
    search: true,
    type: 'select',
    slot: true,
    rules: [{ required: true, message: '请选择状态' }],
    componentProps: {
      options: [
        {
          label: '未处理',
          value: 0,
        },
        {
          label: '已处理',
          value: 1,
        },
      ],
    },
  },
  // {
  //   label: '首次告警时间',
  //   key: 'createTime',
  //   type: 'date',
  //   tableWidth: 180,
  // },
  {
    label: '最近告警时间',
    key: 'lastOccurTime',
    type: 'datetime',
    tableWidth: 180,
    slot: true,
  },
  {
    label: '分析过程',
    key: 'process',
    hide: true,
    componentProps: {
      type: 'textarea',
      rows: 6,
      placeholder: '',
    },
  },
  {
    label: '告警原因',
    key: 'reason',
    tableWidth: 250,
    slot: true,
    componentProps: {
      type: 'textarea',
      rows: 6,
    },
  },
  {
    label: '告警建议',
    key: 'recommend',
    tableWidth: 250,
    slot: true,
    componentProps: {
      type: 'textarea',
      rows: 6,
    },
  },
]

const historicalAlarm = (row: any) => {
  // console.log(row, 'row')
  historicalAlarVisible.value = true
  const params = {
    deviceId: row.deviceId,
    identifier: row.identifier,
  }
  getHistoryAlarm(params).then((res) => {
    if (res.data.rows.length > 0) {
      // console.log(res.data, '历史告警')
      historicalData.value = res.data.rows
    } else {
      // console.log('没有历史告警')
    }
  })
}
const data = ref([])
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})
// const getData =  () => {
//   state.loading = true
//   getMsgList({
//     ...state.page,
//     ...state.query,
//   }).then((res) => {
//     data.value = res.data.rows
//     state.total = res.data.total
//   }).finally(() => {
//     state.loading = false
//   })
// }
// 查询机组数据
// const getbelongingUnit=()=>{
//   const param={
//     configType:4,
//     projectId:cachedProjects.id
//   }
//   belongingUnit(param).then((res)=>{
//     const data = res.data
//   })
// }

const getData = (e) => {
  const { id: projectId } = cachedProjects
  const project = { ...state.query, projectId }
  state.loading = true
  getAlertList({
    ...state.page,
    ...project,
  })
    .then((res) => {
      data.value = res.data.rows
      state.total = res.data.total
    })
    .finally(() => {
      state.loading = false
    })
}
const formatReason = (reason: string) => {
  if (!reason) {
    // reason 为空就直接返回空字符串，防止后续 .replace 报错
    return ''
  }
  // 如果后端已经返回带有 <br> 标签的字符串，可以直接返回
  // 如果需要转换其他换行符（如 \n）为 <br>，可以使用如下代码：
  return reason.replace(/\n/g, '<br>')
  // return reason
}
const formatRecommend = (recommend: string) => {
  // 如果后端已经返回带有 <br> 标签的字符串，可以直接返回
  // 如果需要转换其他换行符（如 \n）为 <br>，可以使用如下代码：
  if (!recommend) {
    return ''
  }
  return recommend.replace(/\n/g, '<br>')
  // return recommend
}
</script>
<style scoped>
 :deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  /* box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5); */
  border: none;
}
:deep(.el-card__header) {
  border: none;
}
:deep(.el-table,
.el-table__expanded-cell ){
  background-color: transparent !important;
}
:deep(.el-table__body tr,
.el-table__body td) {
  padding: 0;
  height: 40px;
}
:deep(.el-table tr ){
  border: none;
  background-color: transparent;
}
:deep(.el-table th) {
  /* background-color: transparent; */
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}
:deep(.el-table) {
  --el-table-border-color: none;
}
:deep(.el-table__cell) {
  /* color: rgba(204, 204, 204, 1) !important; */
}
/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row) {
  /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
  /* 取消单元格背景色，确保整行背景色生效 */
}
:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;

  color: #ffffff;
}
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}
:deep(.el-select__wrapper){
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
.el-tree {
  background-color: transparent;
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #07355c;
}
.el-tree-node__expand-icon {
  color: #fff;
}
.el-tree-node__label {
  color: #fff;
}
:deep(.el-tree-node__content) {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}
.el-select__tags .el-tag--info {
  background-color: #153059 !important;
}
.el-tag.el-tag--info {
  color: #fff !important;
}
</style>
