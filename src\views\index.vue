<template>
  <div class="app-container home">
    <el-space direction="vertical" :fill="true" size="large" wrap style="width: 100%;">
      <div v-for="(group, index) in groupedUnitsDisplayData" :key="index" class="box-jz">
        <div class="box-cs" v-for="(item, idx) in group.groupDatas" :key="idx">
          <div class="title">{{ group.unitName }} {{ item.name }}</div>
          <div class="val-unit">
            <p class="val">{{ item.value ?? "--" }}</p>
            <p class="unit">{{ item.unit ?? "" }}</p>
          </div>
        </div>
      </div>
      <div class="box-img" v-if="!statsDataNull">
        <img src="@/assets/images/noData.png" />
        <p class="box-text">暂无数据</p>
        <!-- <p class="box-text1">暂无数据</p> -->
      </div>
      <el-row :gutter="20">
        <el-col :span="14">
          <div class="charcls" v-if="cachedProjects.medicalInclude===0">
            <el-card class="box-card2" shadow="never">
              <template #header>
                <div class="card-header">
                  <img src="@/assets/images/zx.png" alt="icon" class="header-icon" />
                  <h3>汽耗率</h3>
                </div>
              </template>
              <div class="chart-msg-stat" ref="chartMsgStat"></div>
            </el-card>
          </div>
          <div class="charclss" v-if="cachedProjects.medicalInclude===1">
            <FullCalendar :options="calendarOptions" />

            <!-- 全屏遮罩 + 小弹层 -->
            <div v-if="popoverVisible && selectedTodos.length > 0" class="popup-overlay" @click="popoverVisible = false">
              <div class="popup-content" @click.stop :style="{ top: popoverY + 'px', left: popoverX + 'px' }">
                <div class="popup-header">
                  {{ selectedDate }} 的加药待办
                  <button @click="popoverVisible = false">×</button>
                </div>
                <!-- 新增：横向布局的待办容器 -->
                <div class="todo-container">
                  <div class="todo-item" v-for="(todo, idx) in selectedTodos" :key="idx">
                    <h4 class="todo-title" :style="{ backgroundColor: statusColor(todo.status) }">{{ todo.name }}</h4>
                    <ul class="detail-list">
                      <li>
                        <span class="label">药剂浓度：</span>{{ todo.addConcentration }} mg/L <span class="label"><br />加药量：</span
                        >{{ todo.addNumber }} KG
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="popup-footer">
                  <el-button type="primary" size="small" :disabled="isAllDone" @click="openForm"> 加药完成 </el-button>
                </div>
              </div>
            </div>
            <div v-else-if="popoverVisible && selectedTodos.length === 0"></div>
            <div v-if="formVisible" class="form-overlay" @click="closeForm">
              <!-- 弹窗容器 -->
              <div class="form-container" @click.stop>
                <div class="popup-header">
                  加药完成信息
                  <button class="close-btn" @click="closeForm">×</button>
                </div>
                <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
                  <el-form-item label="加药人" prop="operator">
                    <el-input v-model="form.operator" placeholder="请输入加药人名称" />
                  </el-form-item>
                  <el-form-item label="联系方式" prop="contact">
                    <el-input v-model="form.contact" placeholder="请输入联系方式" />
                  </el-form-item>
                  <div class="popup-footer">
                    <el-button size="small" @click="closeForm">取消</el-button>
                    <el-button type="primary" size="small" @click="submitForm">确定</el-button>
                  </div>
                </el-form>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="10">
          <el-card class="box-card1" shadow="never">
            <template #header>
              <div class="card-header">
                <img src="@/assets/images/zx.png" alt="icon" class="header-icon" />
                <h3>告警数量统计</h3>
              </div>
            </template>
            <div class="chart-device-num" ref="chartDeviceNumStat"></div>
          </el-card>
          <el-card class="box-card3" shadow="never">
            <template #header>
              <div class="card-header">
                <img src="@/assets/images/zx.png" alt="icon" class="header-icon" />
                <h3>设备状态统计</h3>
              </div>
            </template>
            <div class="chart-device-num" ref="chartDeviceStatus"></div>
          </el-card>
        </el-col>
      </el-row>
    </el-space>
  </div>
</template>

<script setup name="Index" lang="ts">
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import interactionPlugin from '@fullcalendar/interaction'
import zhCnLocale from '@fullcalendar/core/locales/zh-cn'
import { listCalendar, saveAddHistory } from '@/views/dosing/scheme/index.api'
import PublicCharts from '@/components/publicCharts/index.vue'
import * as echarts from 'echarts/core'
import { TooltipComponent, LegendComponent, TitleComponent, ToolboxComponent, GridComponent, GraphicComponent } from 'echarts/components'
import { PieChart, LineChart, GaugeChart } from 'echarts/charts'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { pointData, Alarm, DeviceStatus, downSampling } from '@/api/index'
import { formatDate } from '@/utils/formatTime'
import emitter from '@/utils/eventBus.js'
import { ComponentInternalInstance } from 'vue'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
const displayData = ref([])

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { alert_level } = toRefs<any>(proxy?.useDict('alert_level'))
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

// 日历部分
// 模拟待办
interface Todo {
  id: number
  addMethod: string // 加药方式
  addPerson: string // 加药人
  projectId: number // 项目id
  addPersionPhone: string // 加药人电话
  createAt: string // 创建时间
  name: string // 待办名称
  addDate: string // 添加日期
  status: number // 状态 (1: 完成, 2: 待办)
  addConcentration: number // 加药浓度
  addNumber: string // 加药量
}
const todos = ref<Todo[]>([])
// 弹层状态
const popoverVisible = ref(false)
const formVisible = ref(false)
const selectedDate = ref('')
const selectedTodos = ref<{ name: string }[]>([])
const popoverX = ref(0)
const popoverY = ref(0)
const formRef = ref()
const form = reactive({ operator: '', contact: '' })
const rules = {
  operator: [{ required: true, message: '请输入加药人', trigger: 'blur' }],
  contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
}
const calendarEvents = computed(() =>
  todos.value.map((t) => ({
    title: t.name,
    start: t.addDate,
    backgroundColor:
      t.status === 1
        ? 'rgba(0, 175, 255, 1)' // "按计划加药"
        : t.status === 2
        ? 'rgba(255, 165, 0, 1)' //"等待加药"
        : 'rgba(128, 128, 128, 0.5)', //  "未按计划加药"
    borderColor: t.status === 1 ? 'rgba(0, 175, 255, 1)' : t.status === 2 ? 'rgba(255, 165, 0, 1)' : 'rgba(128, 128, 128, 0.5)',
    textColor: '#fff',
  }))
)
const statusColor = (status: number): string => {
  return status === 1
    ? 'rgba(0, 175, 255, 1)' // 按计划加药
    : status === 2
    ? 'rgba(255, 165, 0, 1)' // 等待加药
    : 'rgba(128, 128, 128, 0.5)' // 未按计划加药
}
const onDatesSet = (arg: { view: any }) => {
  // 原始起始日期
  const start: Date = arg.view.currentStart // e.g. Tue Apr 01 2025 ...

  // 1. 格式化起始日期
  const fmtDate = (d: Date) => {
    const yyyy = d.getFullYear()
    const mm = String(d.getMonth() + 1).padStart(2, '0')
    const dd = String(d.getDate()).padStart(2, '0')
    return `${yyyy}-${mm}-${dd}`
  }
  const formattedStart = fmtDate(start)
  // 2. 计算并格式化当月最后一天：下个月第 0 天
  const lastDayOfMonth = new Date(start.getFullYear(), start.getMonth() + 1, 0)
  const formattedEnd = fmtDate(lastDayOfMonth)
  const data = {
    projectId: cachedProjects.id,
    startTime: formattedStart,
    endTime: formattedEnd,
  }
  // 调用获取日历数据的 API
  listCalendar(data).then((res) => {
    todos.value = res.data
    nextTick(() => {
      calendarOptions.events = calendarEvents.value
    })
  })
}

const calendarOptions = reactive({
  plugins: [dayGridPlugin, interactionPlugin],
  locales: [zhCnLocale],
  locale: 'zh-cn',
  initialView: 'dayGridMonth',
  headerToolbar: { left: 'prev,next today', center: 'title', right: '' },
  events: calendarEvents.value,
  height: 500,
  datesSet: onDatesSet,
  dateClick: (info) => openPopover(info.dateStr, info.jsEvent),
  eventClick: (info) => openPopover(info.event.startStr.split('T')[0], info.jsEvent),
  dayMaxEventRows: 5, // 最多 5行
})

// 存拿到的药剂列表
const getpharmaceuticalList = ref([])
const openPopover = (dateStr: string) => {
  const list = todos.value.filter((t) => t.addDate === dateStr)
  if (list.length === 0) {
    ElMessage.info('当前无加药操作')
    return
  }
  getpharmaceuticalList.value = list
  selectedDate.value = dateStr
  selectedTodos.value = list
  popoverVisible.value = true

  // 等 DOM 渲染完成后，再去读弹层尺寸并计算居中坐标
  nextTick(() => {
    const pop = document.querySelector<HTMLElement>('.popup-content')
    if (!pop) return
    const { width, height } = pop.getBoundingClientRect()

    // 屏幕（可视区）宽高
    const vw = window.innerWidth
    const vh = window.innerHeight

    // 计算左上角坐标，使弹层正好居中
    popoverX.value = (vw - width) / 2
    popoverY.value = (vh - height) / 2
  })
}
// 判断是否都已完成
const isAllDone = computed(() => selectedTodos.value.every((t) => t.status === 1))
// 点击加油完成
const openForm = () => {
  form.operator = ''
  form.contact = ''
  formVisible.value = true
}
// 关闭详情
const closePopover = () => {
  popoverVisible.value = false
}
const closeForm = () => {
  formVisible.value = false
}
const submitForm = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return
    const payload = getpharmaceuticalList.value.map((item) => ({
      ...item,
      addPerson: form.operator, // 加药人
      addPersionPhone: form.contact, // 联系方式
    }))
    saveAddHistory(payload).then((res) => {
      if (res.code === 200) {
        ElMessage.success('加油完成')
        closeForm()
        closePopover()
        getCalendarData()
      }
    })
  })
  // })
  // closeForm()
  // closePopover()
  // })
}
// 获取当月第一天
function getFirstDayOfMonth(): string {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  return `${year}-${month}-01`
}

// 获取当月最后一天 (YYYY-MM-DD)
function getLastDayOfMonth(): string {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const lastDay = new Date(year, date.getMonth() + 1, 0).getDate()
  return `${year}-${month}-${String(lastDay).padStart(2, '0')}`
}
// 获取日历数据
const getCalendarData = () => {
  const firstDay = getFirstDayOfMonth()
  const lastDay = getLastDayOfMonth()
  const data = {
    projectId: cachedProjects.id,
    startTime: firstDay,
    endTime: lastDay,
  }
  // 调用获取日历数据的 API
  listCalendar(data).then((res) => {
    todos.value = res.data
    nextTick(() => {
      calendarOptions.events = calendarEvents.value
    })
  })
}

echarts.use([
  TooltipComponent,
  LegendComponent,
  PieChart,
  CanvasRenderer,
  LabelLayout,
  TitleComponent,
  ToolboxComponent,
  GridComponent,
  LineChart,
  UniversalTransition,
  GaugeChart,
  GraphicComponent,
])

const chartDeviceNumStat = ref()
const chartMsgStat = ref()
const chartDeviceStatus = ref()
const unitsDisplayData = ref([])

const groupedUnitsDisplayData = computed(() => {
  const groups = []
  unitsDisplayData.value.forEach((unit) => {
    const datas = unit.datas.map((dataItem) => ({
      ...dataItem,
      unitName: unit.unitName,
    }))
    for (let i = 0; i < datas.length; i += 6) {
      groups.push({
        unitName: unit.unitName,
        groupDatas: datas.slice(i, i + 6),
      })
    }
  })
  return groups
})

// const statsData = ref({
//   day_electricity_amount: 0,
//   vacuum_value: 0,
//   ttd: 0,
//   power_consumption_runtime: 0,
//   temperature_rise: 0,
//   vacuum_subcooling: 0,
//   neverOnlineTotal: 0,
//   deviceStatsOfCategory: [],
//   reportDataStats: [],
// })
const cardIndices = {
  day_electricity_amount: 0,
  vacuum_value: 0,
  ttd: 0,
  power_consumption_runtime: 0,
  temperature_rise: 0,
  vacuum_subcooling: 0,
}
const statsData = ref([])
const statsDataNull = ref(true) //判断有无数据
const alarmNull = ref(true) //判断告警有无数据

// 获取顶部机组信息
const getList = () => {
  if (cachedProjects !== null) {
    const data = cachedProjects.id
    pointData(data)
      .then((res) => {
        if (res.data.length > 0 && res.data != null) {
          unitsDisplayData.value = res.data
        } else {
          unitsDisplayData.value = []
          statsDataNull.value = false
        }
      })
      .catch((err) => {
        unitsDisplayData.value = []
        statsDataNull.value = false
      })
  }
}

emitter.on('projectListChanged', (e) => {
  location.reload()
})
watch(alert_level, (newVal) => {
  if (newVal) {
    alarmEcharts() // 重新获取告警数据并更新图表
  }
})
// 获取告警数量统计
const alarmEcharts = () => {
  if (cachedProjects !== null) {
    const data = cachedProjects.id
    Alarm(data).then((res) => {
      if (res.data.length > 0) {
        const alarmData = res.data.map((item) => {
          const levelInfo = alert_level.value.find((level) => level.value === String(item.level))
          // 根据告警等级设置颜色
          let color
          switch (item.level) {
            case 1: // 低限预警
              color = new echarts.graphic.RadialGradient(0.5, 0.5, 0.5, [
                { offset: 0, color: 'rgb(255, 225, 0)' },
                { offset: 1, color: 'rgb(255, 225, 0)' },
              ])
              break

            case 2: // 低限报警
              color = new echarts.graphic.RadialGradient(0.5, 0.5, 0.5, [
                { offset: 0, color: 'rgb(255, 153, 0)' },
                { offset: 1, color: 'rgb(255, 153, 0)' },
              ])
              break

            case 3: // 低限严重警告
              color = new echarts.graphic.RadialGradient(0.5, 0.5, 0.5, [
                { offset: 0, color: 'rgb(255, 51, 0)' },
                { offset: 1, color: 'rgb(255, 51, 0)' },
              ])
              break

            case 4: // 高限预警
              color = new echarts.graphic.RadialGradient(0.5, 0.5, 0.5, [
                { offset: 0, color: 'rgb(255, 225, 0)' },
                { offset: 1, color: 'rgb(255, 225, 0)' },
              ])
              break

            case 5: // 高限报警
              color = new echarts.graphic.RadialGradient(0.5, 0.5, 0.5, [
                { offset: 0, color: 'rgb(255, 153, 0)' },
                { offset: 1, color: 'rgb(255, 153, 0)' },
              ])
              break

            case 6: // 高限严重警告
              color = new echarts.graphic.RadialGradient(0.5, 0.5, 0.5, [
                { offset: 0, color: 'rgb(255, 51, 0)' },
                { offset: 1, color: 'rgb(255, 51, 0)' },
              ])
              break

            default:
              // 如果没有匹配到，给个默认的颜色或处理逻辑
              color = '#ccc'
              break
          }

          return {
            name: levelInfo ? levelInfo.label : `${item.level}`,
            value: item.cnt,
            itemStyle: {
              color: color, // 设置颜色
            },
          }
        })
        const totalAlarms = alarmData.reduce((sum, item) => sum + item.value, 0)
        echarts.init(chartDeviceNumStat.value).setOption({
          tooltip: {
            trigger: 'item',
          },
          legend: {
            top: '20%',
            right: '5%',
            align: 'left',
            orient: 'vertical',
            icon: 'circle',
            textStyle: {
              color: 'rgba(255, 255, 255, 1)', // 设置图例字体颜色
              fontSize: 14,
            },
          },
          series: [
            {
              name: '告警数量',
              type: 'pie',
              radius: ['70%', '95%'],
              avoidLabelOverlap: false,
              center: ['50%', '50%'],
              label: {
                show: false,
                position: 'outside',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 14,
                  color: 'rgba(255, 255, 255, 1)',
                },
              },
              labelLine: {
                show: false,
              },
              data: alarmData,
            },
          ],
          graphic: [
            {
              type: 'group',
              left: 'center',
              top: 'middle',
              children: [
                {
                  type: 'circle',
                  shape: {
                    cx: 0,
                    cy: 0,
                    r: 60, // 圆形的半径，可以根据需要调整
                  },
                  style: {
                    stroke: 'rgba(35, 98, 30, 1)', // 圆形边框颜色
                    lineWidth: 1, // 边框宽度
                    lineDash: [5, 5], // 虚线样式
                    fill: 'transparent', // 透明填充
                  },
                },
                {
                  type: 'text',
                  z: 100, // 确保文本在圆形背景上面
                  left: 'center',
                  top: -20, // 相对于圆心的Y轴偏移，这里设置较低的位置
                  style: {
                    text: `${totalAlarms}`,
                    textAlign: 'center',
                    fill: 'rgba(32, 124, 229, 1)', // 文本颜色
                    fontSize: 22,
                    fontWeight: 600,
                  },
                },
                {
                  type: 'text',
                  z: 100, // 确保文本在圆形背景上面
                  left: 'center',
                  top: 20, // 相对于圆心的Y轴偏移，这里设置较高的位置
                  style: {
                    text: '告警总数',
                    textAlign: 'center',
                    fill: '#ffffff', // 文本颜色
                    fontSize: 14,
                  },
                },
              ],
            },
          ],
        })
      } else {
        alarmNull.value = false
      }
    })
  }
}

// 获取设备状态
const getDevice = () => {
  if (cachedProjects !== null) {
    const data = cachedProjects.id
    DeviceStatus(data).then((res) => {
      // if(res)
      const online = res.data.online || 0
      const offline = res.data.offline || 0
      const totalDevices = online + offline

      const onlinePercentage = totalDevices > 0 ? ((online / totalDevices) * 100).toFixed(2) : '0.00'
      const deviceData = [
        {
          value: online,
          name: '在线设备',
          itemStyle: {
            color: new echarts.graphic.RadialGradient(0.5, 0.5, 0.5, [
              { offset: 0, color: 'rgb(193, 218, 230)' }, // 颜色起点
              { offset: 0.5028, color: 'rgb(193, 218, 230)' }, // 50.28%处的颜色
              { offset: 0.752, color: 'rgb(0, 175, 255)' }, // 75.2%处的颜色
              { offset: 1, color: 'rgb(0, 175, 255)' }, // 渐变终点
            ]),
          },
        },
        {
          value: offline,
          name: '离线设备',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0.5, color: 'rgba(239, 110, 35, 1)' },
              { offset: 1, color: 'rgba(255, 223, 204, 1)' },
            ]),
          },
        },
      ]

      // 使用饼图展示设备状态
      echarts.init(chartDeviceStatus.value).setOption({
        tooltip: {
          trigger: 'item',
        },
        legend: {
          top: '20%',
          right: '10%',
          align: 'left',
          orient: 'vertical',
          icon: 'circle',
          textStyle: {
            color: 'rgba(255, 255, 255, 1)', // 设置图例字体颜色
            fontSize: 14,
          },
        },
        series: [
          {
            name: '设备状态',
            type: 'pie',
            radius: ['70%', '95%'],
            avoidLabelOverlap: false,
            center: ['50%', '50%'],
            label: {
              show: false,
              position: 'outside',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                color: 'rgba(255, 255, 255, 1)',
                // fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: deviceData,
          },
        ],
        graphic: [
          {
            type: 'group',
            left: 'center',
            top: 'middle',
            children: [
              {
                type: 'circle',
                shape: {
                  cx: 0,
                  cy: 0,
                  r: 50, // 圆形的半径，可以根据需要调整
                },
                style: {
                  fill: 'rgba(14, 38, 87, 1)', // 圆形背景颜色
                },
              },
              {
                type: 'text',
                z: 100, // 确保文本在圆形背景上面
                left: 'center',
                top: -20, // 相对于圆心的Y轴偏移，这里设置较低的位置
                style: {
                  text: `${onlinePercentage}%`,
                  textAlign: 'center',
                  fill: 'rgba(32, 124, 229, 1)', // 文本颜色
                  fontSize: 22,
                  fontWeight: 600,
                },
              },
              {
                type: 'text',
                z: 100, // 确保文本在圆形背景上面
                left: 'center',
                top: 20, // 相对于圆心的Y轴偏移，这里设置较高的位置
                style: {
                  text: '在线率',
                  textAlign: 'center',
                  fill: '#ffffff', // 文本颜色
                  fontSize: 14,
                },
              },
            ],
          },
        ],
      })
    })
  }
}

// 获取汽耗率
const downSamplingdata = () => {
  if (cachedProjects !== null) {
    const getFormattedDate = (date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

    const currentDate = new Date()
    const thirtyDaysAgoDate = new Date()
    thirtyDaysAgoDate.setDate(currentDate.getDate() - 30)

    const parms = {
      projectId: cachedProjects.id,
      endTime: getFormattedDate(currentDate),
      startTime: getFormattedDate(thirtyDaysAgoDate),
    }

    downSampling(parms)
      .then((res) => {
        if (res.data.length > 0 && res.data != null) {
          let xdata = new Set()
          let seriesData = []

          res.data.forEach((unit) => {
            let unitXdata = []
            let unitYdata = []

            unit.his.forEach((msg) => {
              xdata.add(formatDate(msg.time, 'MM-DD HH:mm'))
              unitXdata.push(formatDate(msg.time, 'MM-DD HH:mm'))
              unitYdata.push(msg.value)
            })
            seriesData.push({
              name: `${unit.powerUnitName} ${unit.name} ${unit.unit}`,
              type: 'line',
              // stack: 'Total',
              data: unitYdata,
              symbol: 'none',
              lineStyle: {
                // color: 'rgba(33, 148, 255, 1)', // 设置线条颜色
                width: 2, // 可选：设置线条宽度
                type: 'solid', // 可选：设置线条类型，可以是 'solid', 'dashed', 'dotted' 等
              },
            })
          })

          echarts.init(chartMsgStat.value).setOption({
            title: {},
            tooltip: {
              trigger: 'axis',
              backgroundColor: '#142433',
              textStyle: {
                color: '#fff', // 修改字体颜色
                fontSize: 14, // 可以同时修改字体大小等其他属性
              },
            },
            legend: {
              data: seriesData.map((s) => s.name),
              textStyle: {
                fontSize: 14,
                color: 'rgba(204, 204, 204, 1)',
                fontWeight: 400,
              },
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true,
            },
            toolbox: {
              feature: {
                saveAsImage: {},
              },
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: Array.from(xdata).sort(),
              axisLine: {
                lineStyle: {
                  color: 'rgba(204, 204, 204, 1)', // 修改 x 轴轴线的颜色
                  width: 1,
                },
              },
              axisLabel: {
                lineStyle: {
                  color: 'rgba(62, 65, 77, 1)', // 修改 x 轴刻度线的颜色
                },
              },
            },
            yAxis: {
              type: 'value',
              splitLine: {
                lineStyle: {
                  color: 'rgba(62, 65, 77, 1)', // 修改与 x 轴平行的网格线颜色
                },
              },
              axisLabel: {
                color: 'rgba(204, 204, 204, 1)',
                fontSize: 14,
              },
            },
            series: seriesData,
          })
        } else {
        }
      })
      .catch((err) => {
        chartMsgStat.value = {}
      })
  }
}
const timer = ref(null)

const createTimer = () => {
  // 先清除已有的定时器
  if (timer.value) {
    clearInterval(timer.value)
  }
  // 重新生成新的定时器
  timer.value = setInterval(() => {
    getList()
    alarmEcharts()
    getDevice()
    downSamplingdata()
    // 时间到了清除定时器并重新生成一个新的定时器
    clearInterval(timer.value)
    createTimer() // 重新创建定时器
  }, 60000) // 1分钟
}
onMounted(() => {
  getList()
  alarmEcharts()
  getDevice()
  downSamplingdata()
  createTimer() // 创建第一个定时器
  if (cachedProjects.medicalInclude === 1) {
    getCalendarData()
  }
})
onUnmounted(() => {
  // 清除定时器，避免内存泄漏
  if (timer.value !== null) {
    clearInterval(timer.value)
  }
})
</script>

<style scoped lang="scss">
.form-overlay {
  position: fixed;
  inset: 0;                        // top/right/bottom/left: 0
  background: rgba(0, 0, 0, 0.5);  // 半透明黑
  z-index: 1000;
}

/* 居中 300×500 的弹窗 */
.form-container {
  position: absolute;
  width: 300px;
  height: 200px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #213548;       // 原弹窗背景色
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

/* 如果需要，你可以复用原来的 header/footer 样式 */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  font-weight: bold;
  margin-bottom: 12px;
}
.popup-footer {
  text-align: right;
  margin-top: 16px;
}
.close-btn {
  background: transparent;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #000000;
}
.todo-title {
  // background: #00abf8;
  border-radius: 15px;
  padding: 5px 10px;
  width: fit-content;
  color: #fff;
}
:deep(.calendar-container) {
  background: rgba(2, 28, 51, 0.5) !important;
}

:deep(.fc-theme-standard .fc-popover) {
  background: #213548;
}
:deep(.fc) {
  color: #fff !important;
}

.popup-overlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
}

/* 弹层样式 */
.popup-content {
  position: absolute;
  width: 1200px; /* 根据需要调整最大宽度 */
  background: #213548;
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}
.todo-item {
  flex: 0 1 calc((100% - 5 * 8px) / 6);
  box-sizing: border-box;
  background: rgba(0, 171, 248, 0.1);
  padding: 8px;
  border-radius: 4px;
}
.detail-list {
  list-style: none;
  padding: 0;
  margin: 4px 0 0;
}

.detail-list li {
  font-size: 14px;
  color: #f8f8f8;
}

.label {
  color: #ccc;
  margin-right: 4px;
}

.popup-footer {
  text-align: right;
}
.todo-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: calc((80px + 20px) * 3); /* 每项高度约80px + 间距8px */
  overflow-y: auto;
}
/* 头部带关闭按钮 */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  margin-bottom: 6px;
  color: #fff;
}
.close-btn {
  border: none;
  background: transparent;
  font-size: 16px;
  cursor: pointer;
  background: #fff;
}

/* 列表 */
.todo-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.todo-list li + li {
  margin-top: 4px;
}
.home {
  // 其他样式
  .box-img {
    padding: 10px;
    background: rgba(2, 28, 51, 0.5);
    // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
    text-align: center;

    .box-text {
      font-size: 14px;
      font-weight: 300;
      letter-spacing: 0px;
      line-height: 18.56px;
      color: rgba(153, 153, 153, 1);
      vertical-align: top;
    }
  }

  .box-jz {
    display: flex;
    //justify-content: space-between;
    //flex-wrap: wrap;
    padding: 10px 0;
    position: relative;
    background: rgba(2, 28, 51, 0.5);
    // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);

    .box-cs {
      flex: 1;
      // flex: 1 1 calc(100% / 6 - 20px); // 每个子元素平分父容器空间
      // max-width: calc(100% / 6 - 20px); // 确保每个子元素的最大宽度
      display: flex;
      flex-direction: column;
      justify-content: center;
      // align-items: flex-start;
      align-items: center;
      // padding-left: 40px;
      text-align: left;
      box-sizing: border-box;
      height: 120px;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: -5px;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 80px;
        background: rgba(32, 73, 92, 1);
      }

      .title {
        font-size: 16px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 21.22px;
        color: rgba(204, 204, 204, 1);
        //margin-bottom: 5px;
        padding: 8px;
      }

      .val-unit {
        display: flex;
        align-items: baseline;
        justify-content: flex-start;

        .val {
          font-size: 30px;
          font-weight: 500;
          letter-spacing: 0px;
          line-height: 47.74px;
          color: rgba(33, 148, 255, 1);
          margin: 0;
          margin-right: 5px;
        }

        .unit {
          font-size: 16px;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 21.22px;
          color: rgba(255, 255, 255, 1);
        }
      }
    }
  }

  .el-card {
    opacity: 1;
    background: rgba(2, 28, 51, 0.5);
    // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
    color: rgba(255, 255, 255, 1);
    border: none;
  }

  // 使用v:deep
  :deep(.el-card__header) {
    border: none;
  }

  .card-header {
    display: flex;
    /* 使用Flexbox布局 */
    align-items: center;
    /* 垂直居中对齐图标和文字 */
  }

  .header-icon {
    margin-right: 8px;
    /* 图标与标题文字之间的间距 */
  }

  .charcls {
    height: 100%;
  }
  .charclss {
    height: 100%;
    background: rgba(2, 28, 51, 0.5);
  }
  .box-card2 {
    height: 100%;

    :first-child {
      padding: 0px 8px 0px !important;
      // min-height: 0px;
    }
  }

  .box-card3 {
    :first-child {
      padding: 0px 8px 0px !important;
      // min-height: 0px;
    }
  }

  .box-card1 {
    margin-bottom: 10px;

    :first-child {
      padding: 0px 8px 0px !important;
      // min-height: 0px;
    }
  }

  .chart-device-num,
  .chart-device-status {
    height: 200px; // 确保高度相同
    width: 100%; // 确保宽度占满父容器
  }

  .chart-device-online,
  .chart-device-offline,
  .chart-device-active {
    height: 120px;
  }

  .chart-msg-stat {
    height: 500px;
  }

  .dev-sub {
    height: 80px;
    line-height: 20px;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
  }

  .dev-sub.online {
    p {
      color: #00dd99;
    }
  }
}
</style>
