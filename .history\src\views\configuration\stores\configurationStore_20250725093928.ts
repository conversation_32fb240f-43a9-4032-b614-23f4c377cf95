import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  ConfigurationProject, 
  ConfigurationComponent, 
  EditorState,
  ComponentCategory
} from '../types'

// 组态项目存储
export const useConfigurationStore = defineStore('configuration', () => {
  // 项目列表
  const projectList = ref<ConfigurationProject[]>([])
  
  // 当前编辑的项目
  const currentProject = ref<ConfigurationProject | null>(null)
  
  // 编辑器状态
  const editorState = ref<EditorState>({
    selectedComponents: [],
    clipboard: [],
    history: {
      past: [],
      present: {} as ConfigurationProject,
      future: []
    },
    zoom: 1,
    grid: {
      show: true,
      size: 10,
      snap: true
    },
    rulers: true,
    layers: true
  })

  // 组件库
  const componentLibrary = ref<ComponentCategory[]>([
    {
      id: 'basic',
      name: '基础组件',
      icon: 'icon-basic',
      components: [
        {
          id: 'text',
          name: '文本',
          icon: 'icon-text',
          type: 'text',
          preview: '',
          defaultProps: {
            width: 120,
            height: 40,
            style: {
              fontSize: 14,
              fontColor: '#333333',
              textAlign: 'left'
            },
            data: {
              static: '文本内容'
            }
          }
        },
        {
          id: 'image',
          name: '图片',
          icon: 'icon-image',
          type: 'image',
          preview: '',
          defaultProps: {
            width: 200,
            height: 150,
            style: {
              borderRadius: 0
            },
            data: {
              static: ''
            }
          }
        },
        {
          id: 'shape',
          name: '形状',
          icon: 'icon-shape',
          type: 'shape',
          preview: '',
          defaultProps: {
            width: 100,
            height: 100,
            style: {
              backgroundColor: '#409EFF',
              borderRadius: 0
            }
          }
        },
        {
          id: 'button',
          name: '按钮',
          icon: 'icon-button',
          type: 'button',
          preview: '',
          defaultProps: {
            width: 100,
            height: 40,
            style: {
              backgroundColor: '#409EFF',
              borderRadius: 4,
              fontColor: '#FFFFFF',
              fontSize: 14,
              textAlign: 'center'
            },
            data: {
              static: '按钮'
            }
          }
        }
      ]
    },
    {
      id: 'charts',
      name: '图表组件',
      icon: 'icon-chart',
      components: [
        {
          id: 'lineChart',
          name: '折线图',
          icon: 'icon-line-chart',
          type: 'chart',
          preview: '',
          defaultProps: {
            width: 400,
            height: 300,
            data: {
              static: {
                xAxis: {
                  type: 'category',
                  data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                yAxis: {
                  type: 'value'
                },
                series: [{
                  data: [150, 230, 224, 218, 135, 147, 260],
                  type: 'line'
                }]
              }
            }
          }
        },
        {
          id: 'barChart',
          name: '柱状图',
          icon: 'icon-bar-chart',
          type: 'chart',
          preview: '',
          defaultProps: {
            width: 400,
            height: 300,
            data: {
              static: {
                xAxis: {
                  type: 'category',
                  data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                yAxis: {
                  type: 'value'
                },
                series: [{
                  data: [120, 200, 150, 80, 70, 110, 130],
                  type: 'bar'
                }]
              }
            }
          }
        },
        {
          id: 'pieChart',
          name: '饼图',
          icon: 'icon-pie-chart',
          type: 'chart',
          preview: '',
          defaultProps: {
            width: 400,
            height: 300,
            data: {
              static: {
                series: [{
                  type: 'pie',
                  radius: '50%',
                  data: [
                    { value: 1048, name: '类别A' },
                    { value: 735, name: '类别B' },
                    { value: 580, name: '类别C' },
                    { value: 484, name: '类别D' },
                    { value: 300, name: '类别E' }
                  ]
                }]
              }
            }
          }
        },
        {
          id: 'gauge',
          name: '仪表盘',
          icon: 'icon-gauge',
          type: 'gauge',
          preview: '',
          defaultProps: {
            width: 300,
            height: 300,
            data: {
              static: {
                series: [{
                  type: 'gauge',
                  progress: {
                    show: true
                  },
                  detail: {
                    valueAnimation: true,
                    formatter: '{value}%'
                  },
                  data: [{ value: 70 }]
                }]
              }
            }
          }
        }
      ]
    },
    {
      id: 'industrial',
      name: '工业组件',
      icon: 'icon-industrial',
      components: [
        {
          id: 'tank',
          name: '储罐',
          icon: 'icon-tank',
          type: 'model3d',
          preview: '',
          defaultProps: {
            width: 200,
            height: 300,
            data: {
              static: {
                model: 'tank',
                fillLevel: 50
              }
            }
          }
        },
        {
          id: 'pump',
          name: '水泵',
          icon: 'icon-pump',
          type: 'model3d',
          preview: '',
          defaultProps: {
            width: 150,
            height: 150,
            data: {
              static: {
                model: 'pump',
                status: 'running'
              }
            }
          }
        },
        {
          id: 'valve',
          name: '阀门',
          icon: 'icon-valve',
          type: 'model3d',
          preview: '',
          defaultProps: {
            width: 100,
            height: 100,
            data: {
              static: {
                model: 'valve',
                status: 'open'
              }
            }
          }
        },
        {
          id: 'pipe',
          name: '管道',
          icon: 'icon-pipe',
          type: 'model3d',
          preview: '',
          defaultProps: {
            width: 300,
            height: 50,
            data: {
              static: {
                model: 'pipe',
                flow: true,
                flowDirection: 'right',
                flowSpeed: 5
              }
            }
          }
        }
      ]
    }
  ])

  // 获取当前选中的组件
  const selectedComponents = computed(() => {
    if (!currentProject.value || editorState.value.selectedComponents.length === 0) {
      return []
    }
    
    return currentProject.value.components.filter(component => 
      editorState.value.selectedComponents.includes(component.id)
    )
  })

  // 添加新项目
  function addProject(project: ConfigurationProject) {
    projectList.value.push(project)
  }

  // 更新项目
  function updateProject(project: ConfigurationProject) {
    const index = projectList.value.findIndex(p => p.id === project.id)
    if (index !== -1) {
      projectList.value[index] = project
    }
  }

  // 删除项目
  function deleteProject(id: string) {
    const index = projectList.value.findIndex(p => p.id === id)
    if (index !== -1) {
      projectList.value.splice(index, 1)
    }
  }

  // 设置当前项目
  function setCurrentProject(project: ConfigurationProject) {
    currentProject.value = project
    // 初始化历史记录
    editorState.value.history.present = JSON.parse(JSON.stringify(project))
    editorState.value.history.past = []
    editorState.value.history.future = []
    // 清空选中状态
    editorState.value.selectedComponents = []
  }

  // 添加组件到当前项目
  function addComponent(component: ConfigurationComponent) {
    if (!currentProject.value) return
    
    currentProject.value.components.push(component)
    // 记录历史
    saveHistory()
    // 选中新添加的组件
    editorState.value.selectedComponents = [component.id]
  }

  // 更新组件
  function updateComponent(component: ConfigurationComponent) {
    if (!currentProject.value) return
    
    const index = currentProject.value.components.findIndex(c => c.id === component.id)
    if (index !== -1) {
      currentProject.value.components[index] = component
      // 记录历史
      saveHistory()
    }
  }

  // 删除组件
  function deleteComponent(id: string) {
    if (!currentProject.value) return
    
    const index = currentProject.value.components.findIndex(c => c.id === id)
    if (index !== -1) {
      currentProject.value.components.splice(index, 1)
      // 记录历史
      saveHistory()
      // 清除选中状态
      editorState.value.selectedComponents = editorState.value.selectedComponents.filter(cid => cid !== id)
    }
  }

  // 选择组件
  function selectComponent(id: string, multiple = false) {
    if (!multiple) {
      editorState.value.selectedComponents = [id]
    } else {
      if (!editorState.value.selectedComponents.includes(id)) {
        editorState.value.selectedComponents.push(id)
      }
    }
  }

  // 取消选择组件
  function deselectComponent(id: string) {
    editorState.value.selectedComponents = editorState.value.selectedComponents.filter(cid => cid !== id)
  }

  // 清空选择
  function clearSelection() {
    editorState.value.selectedComponents = []
  }

  // 复制选中组件
  function copySelectedComponents() {
    if (!currentProject.value || editorState.value.selectedComponents.length === 0) return
    
    const components = currentProject.value.components.filter(component => 
      editorState.value.selectedComponents.includes(component.id)
    )
    
    editorState.value.clipboard = JSON.parse(JSON.stringify(components))
  }

  // 粘贴组件
  function pasteComponents() {
    if (!currentProject.value || editorState.value.clipboard.length === 0) return
    
    const newComponents = JSON.parse(JSON.stringify(editorState.value.clipboard))
    const newIds: string[] = []
    
    // 为粘贴的组件生成新ID并偏移位置
    newComponents.forEach(component => {
      const oldId = component.id
      component.id = generateUniqueId()
      component.x += 20
      component.y += 20
      newIds.push(component.id)
    })
    
    // 添加到当前项目
    currentProject.value.components.push(...newComponents)
    
    // 选中新粘贴的组件
    editorState.value.selectedComponents = newIds
    
    // 记录历史
    saveHistory()
  }

  // 保存历史记录
  function saveHistory() {
    if (!currentProject.value) return
    
    // 添加当前状态到历史记录
    editorState.value.history.past.push(JSON.parse(JSON.stringify(editorState.value.history.present)))
    // 更新当前状态
    editorState.value.history.present = JSON.parse(JSON.stringify(currentProject.value))
    // 清空未来状态
    editorState.value.history.future = []
    
    // 限制历史记录数量
    if (editorState.value.history.past.length > 50) {
      editorState.value.history.past.shift()
    }
  }

  // 撤销
  function undo() {
    if (!currentProject.value || editorState.value.history.past.length === 0) return
    
    // 保存当前状态到未来记录
    editorState.value.history.future.unshift(JSON.parse(JSON.stringify(editorState.value.history.present)))
    // 获取上一个状态
    const previousState = editorState.value.history.past.pop()
    // 更新当前状态
    editorState.value.history.present = previousState as ConfigurationProject
    // 更新当前项目
    currentProject.value = JSON.parse(JSON.stringify(previousState))
  }

  // 重做
  function redo() {
    if (!currentProject.value || editorState.value.history.future.length === 0) return
    
    // 保存当前状态到历史记录
    editorState.value.history.past.push(JSON.parse(JSON.stringify(editorState.value.history.present)))
    // 获取下一个状态
    const nextState = editorState.value.history.future.shift()
    // 更新当前状态
    editorState.value.history.present = nextState as ConfigurationProject
    // 更新当前项目
    currentProject.value = JSON.parse(JSON.stringify(nextState))
  }

  // 生成唯一ID
  function generateUniqueId() {
    return 'component_' + Date.now() + '_' + Math.floor(Math.random() * 1000)
  }

  return {
    projectList,
    currentProject,
    editorState,
    componentLibrary,
    selectedComponents,
    addProject,
    updateProject,
    deleteProject,
    setCurrentProject,
    addComponent,
    updateComponent,
    deleteComponent,
    selectComponent,
    deselectComponent,
    clearSelection,
    copySelectedComponents,
    pasteComponents,
    saveHistory,
    undo,
    redo
  }
})