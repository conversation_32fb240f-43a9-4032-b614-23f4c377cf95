<template>
  <div class="data-editor">
    <el-form label-position="top" size="small">
      <el-form-item label="数据类型">
        <el-radio-group v-model="dataType" @change="switchDataType" class="dark-text">
          <el-radio-button label="static">静态数据</el-radio-button>
          <el-radio-button label="dynamic">动态数据</el-radio-button>
        </el-radio-group>
      </el-form-item>
      
      <!-- 静态数据编辑 -->
      <template v-if="dataType === 'static'">
        <!-- 文本组件 -->
        <template v-if="component.type === 'text'">
          <el-form-item label="文本内容">
            <el-input 
              v-model="localData.static" 
              type="textarea" 
              rows="3"
              @change="updateData"
              class="dark-text"
            />
          </el-form-item>
        </template>
        
        <!-- 按钮组件 -->
        <template v-else-if="component.type === 'button'">
          <el-form-item label="按钮文本">
            <el-input 
              v-model="localData.static" 
              @change="updateData"
              class="dark-text"
            />
          </el-form-item>
        </template>
        
        <!-- 图片组件 -->
        <template v-else-if="component.type === 'image'">
          <el-form-item label="图片URL">
            <el-input 
              v-model="localData.static" 
              placeholder="输入图片URL"
              @change="updateData"
              class="dark-text"
            />
          </el-form-item>
          
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :auto-upload="false"
            :on-change="handleImageChange"
          >
            <img v-if="localData.static" :src="localData.static" class="preview-image" />
            <el-icon v-else class="upload-icon"><Plus /></el-icon>
          </el-upload>
        </template>
        
        <!-- 图表组件 -->
        <template v-else-if="component.type === 'chart'">
          <el-form-item label="图表配置">
            <el-tabs>
              <el-tab-pane label="可视化编辑">
                <el-form-item label="图表类型">
                  <el-select v-model="chartType" @change="updateChartType" class="dark-text">
                    <el-option label="折线图" value="line" />
                    <el-option label="柱状图" value="bar" />
                    <el-option label="饼图" value="pie" />
                    <el-option label="散点图" value="scatter" />
                  </el-select>
                </el-form-item>
                
                <template v-if="['line', 'bar', 'scatter'].includes(chartType)">
                  <el-form-item label="X轴数据">
                    <el-input 
                      v-model="xAxisData" 
                      placeholder="用逗号分隔的值，如: 一月,二月,三月"
                      @change="updateChartData"
                      class="dark-text"
                    />
                  </el-form-item>
                  
                  <el-form-item label="Y轴数据">
                    <el-input 
                      v-model="yAxisData" 
                      placeholder="用逗号分隔的值，如: 10,20,30"
                      @change="updateChartData"
                      class="dark-text"
                    />
                  </el-form-item>
                </template>
                
                <template v-if="chartType === 'pie'">
                  <el-form-item label="饼图数据">
                    <div v-for="(item, index) in pieData" :key="index" class="pie-data-item">
                      <el-row :gutter="10">
                        <el-col :span="10">
                          <el-input 
                            v-model="item.name" 
                            placeholder="名称"
                            @change="updateChartData"
                            class="dark-text"
                          />
                        </el-col>
                        <el-col :span="10">
                          <el-input-number 
                            v-model="item.value" 
                            :min="0" 
                            controls-position="right"
                            placeholder="值"
                            @change="updateChartData"
                            class="dark-text"
                          />
                        </el-col>
                        <el-col :span="4">
                          <el-button 
                            type="danger" 
                            icon="Delete" 
                            circle
                            @click="removePieDataItem(index)"
                          />
                        </el-col>
                      </el-row>
                    </div>
                    
                    <el-button 
                      type="primary" 
                      plain 
                      icon="Plus"
                      @click="addPieDataItem"
                    >
                      添加数据项
                    </el-button>
                  </el-form-item>
                </template>
              </el-tab-pane>
              
              <el-tab-pane label="JSON编辑">
                <el-form-item>
                  <el-input 
                    v-model="chartJson" 
                    type="textarea" 
                    rows="10"
                    @change="updateChartFromJson"
                    class="dark-text"
                  />
                </el-form-item>
              </el-tab-pane>
            </el-tabs>
          </el-form-item>
        </template>
        
        <!-- 仪表盘组件 -->
        <template v-else-if="component.type === 'gauge'">
          <el-form-item label="当前值">
            <el-input-number 
              v-model="gaugeValue" 
              :min="gaugeMin" 
              :max="gaugeMax"
              controls-position="right"
              @change="updateGaugeData"
              class="dark-text"
            />
          </el-form-item>
          
          <el-form-item label="最小值">
            <el-input-number 
              v-model="gaugeMin" 
              :max="gaugeMax"
              controls-position="right"
              @change="updateGaugeData"
              class="dark-text"
            />
          </el-form-item>
          
          <el-form-item label="最大值">
            <el-input-number 
              v-model="gaugeMax" 
              :min="gaugeMin"
              controls-position="right"
              @change="updateGaugeData"
              class="dark-text"
            />
          </el-form-item>
          
          <el-form-item label="标题">
            <el-input 
              v-model="gaugeTitle" 
              @change="updateGaugeData"
              class="dark-text"
            />
          </el-form-item>
          
          <el-form-item label="单位">
            <el-input 
              v-model="gaugeUnit" 
              @change="updateGaugeData"
              class="dark-text"
            />
          </el-form-item>
        </template>
        
        <!-- 3D模型组件 -->
        <template v-else-if="component.type === 'model3d'">
          <el-form-item label="模型类型">
            <el-select v-model="modelType" @change="updateModelData" class="dark-text">
              <el-option label="储罐" value="tank" />
              <el-option label="水泵" value="pump" />
              <el-option label="阀门" value="valve" />
              <el-option label="管道" value="pipe" />
            </el-select>
          </el-form-item>
          
          <template v-if="modelType === 'tank'">
            <el-form-item label="液位百分比">
              <el-slider 
                v-model="tankFillLevel" 
                :min="0" 
                :max="100" 
                :step="1"
                show-input
                @change="updateModelData"
                class="dark-text"
              />
            </el-form-item>
          </template>
          
          <template v-if="modelType === 'pump' || modelType === 'valve'">
            <el-form-item label="状态">
              <el-radio-group v-model="deviceStatus" @change="updateModelData" class="dark-text">
                <el-radio-button 
                  :label="modelType === 'pump' ? 'running' : 'open'"
                >
                  {{ modelType === 'pump' ? '运行中' : '打开' }}
                </el-radio-button>
                <el-radio-button 
                  :label="modelType === 'pump' ? 'stopped' : 'closed'"
                >
                  {{ modelType === 'pump' ? '已停止' : '关闭' }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </template>
          
          <template v-if="modelType === 'pipe'">
            <el-form-item label="流动">
              <el-switch 
                v-model="pipeFlow" 
                @change="updateModelData"
                class="dark-text"
              />
            </el-form-item>
            
            <template v-if="pipeFlow">
              <el-form-item label="流动方向">
                <el-radio-group v-model="pipeFlowDirection" @change="updateModelData" class="dark-text">
                  <el-radio-button label="left">向左</el-radio-button>
                  <el-radio-button label="right">向右</el-radio-button>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="流动速度">
                <el-slider 
                  v-model="pipeFlowSpeed" 
                  :min="1" 
                  :max="10" 
                  :step="1"
                  show-input
                  @change="updateModelData"
                  class="dark-text"
                />
              </el-form-item>
            </template>
          </template>
        </template>
      </template>
      
      <!-- 动态数据编辑 -->
      <template v-else>
        <el-form-item label="数据源">
          <el-select 
            v-model="localData.dynamic.dataSource" 
            placeholder="选择数据源"
            @change="updateData"
            class="dark-text"
          >
            <el-option 
              v-for="binding in dataBindings" 
              :key="binding.id"
              :label="binding.name"
              :value="binding.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数据字段">
          <el-select 
            v-model="localData.dynamic.field" 
            placeholder="选择数据字段"
            @change="updateData"
            class="dark-text"
          >
            <el-option 
              v-for="field in selectedBindingFields" 
              :key="field.key"
              :label="`${field.name} (${field.key})`"
              :value="field.key"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="格式化">
          <el-input 
            v-model="localData.dynamic.format" 
            placeholder="例如: decimal:2"
            @change="updateData"
            class="dark-text"
          />
        </el-form-item>
        
        <el-form-item label="单位">
          <el-input 
            v-model="localData.dynamic.unit" 
            placeholder="例如: °C, kg, m³"
            @change="updateData"
            class="dark-text"
          />
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useConfigurationStore } from '../../stores/configurationStore'
import type { ConfigurationComponent, DataField } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
}>()

const emit = defineEmits<{
  update: [data: any]
}>()

const configStore = useConfigurationStore()

// 本地数据副本
const localData = ref({
  static: props.component.data.static,
  dynamic: props.component.data.dynamic || {
    dataSource: '',
    field: '',
    format: '',
    unit: ''
  }
})

// 数据类型
const dataType = ref(props.component.data.dynamic ? 'dynamic' : 'static')

// 获取当前项目的数据绑定
const dataBindings = computed(() => {
  return configStore.currentProject?.dataBindings || []
})

// 获取选中数据源的字段
const selectedBindingFields = computed(() => {
  if (!localData.value.dynamic?.dataSource) return []
  
  const binding = dataBindings.value.find(b => b.id === localData.value.dynamic.dataSource)
  return binding?.fields || []
})

// 图表相关
const chartType = ref('line')
const xAxisData = ref('')
const yAxisData = ref('')
const pieData = ref<{ name: string, value: number }[]>([])
const chartJson = ref('')

// 仪表盘相关
const gaugeValue = ref(0)
const gaugeMin = ref(0)
const gaugeMax = ref(100)
const gaugeTitle = ref('')
const gaugeUnit = ref('')

// 3D模型相关
const modelType = ref('tank')
const tankFillLevel = ref(50)
const deviceStatus = ref('running')
const pipeFlow = ref(true)
const pipeFlowDirection = ref('right')
const pipeFlowSpeed = ref(5)

// 初始化数据
const initData = () => {
  // 初始化图表数据
  if (props.component.type === 'chart' && props.component.data.static) {
    const chartData = props.component.data.static
    chartJson.value = JSON.stringify(chartData, null, 2)
    
    // 尝试解析图表类型和数据
    if (chartData.series && chartData.series.length > 0) {
      chartType.value = chartData.series[0].type || 'line'
      
      if (['line', 'bar', 'scatter'].includes(chartType.value)) {
        if (chartData.xAxis && chartData.xAxis.data) {
          xAxisData.value = chartData.xAxis.data.join(',')
        }
        
        if (chartData.series[0].data) {
          yAxisData.value = chartData.series[0].data.join(',')
        }
      } else if (chartType.value === 'pie') {
        if (chartData.series[0].data) {
          pieData.value = [...chartData.series[0].data]
        } else {
          pieData.value = []
        }
      }
    }
  }
  
  // 初始化仪表盘数据
  if (props.component.type === 'gauge' && props.component.data.static) {
    const gaugeData = props.component.data.static
    
    if (gaugeData.series && gaugeData.series.length > 0) {
      if (gaugeData.series[0].data && gaugeData.series[0].data.length > 0) {
        gaugeValue.value = gaugeData.series[0].data[0].value || 0
      }
      
      gaugeMin.value = gaugeData.series[0].min || 0
      gaugeMax.value = gaugeData.series[0].max || 100
    }
    
    if (gaugeData.title) {
      gaugeTitle.value = gaugeData.title.text || ''
    }
    
    // 尝试从格式化器中提取单位
    if (gaugeData.series && gaugeData.series[0].detail && gaugeData.series[0].detail.formatter) {
      const formatter = gaugeData.series[0].detail.formatter
      const match = formatter.match(/\+ ['"]([^'"]+)['"]/)
      if (match) {
        gaugeUnit.value = match[1].trim()
      }
    }
  }
  
  // 初始化3D模型数据
  if (props.component.type === 'model3d' && props.component.data.static) {
    const modelData = props.component.data.static
    
    modelType.value = modelData.model || 'tank'
    
    if (modelType.value === 'tank') {
      tankFillLevel.value = modelData.fillLevel || 50
    } else if (modelType.value === 'pump' || modelType.value === 'valve') {
      deviceStatus.value = modelData.status || (modelType.value === 'pump' ? 'running' : 'open')
    } else if (modelType.value === 'pipe') {
      pipeFlow.value = modelData.flow !== undefined ? modelData.flow : true
      pipeFlowDirection.value = modelData.flowDirection || 'right'
      pipeFlowSpeed.value = modelData.flowSpeed || 5
    }
  }
}

// 监听组件变化，更新本地副本
watch(() => props.component.data, (newData) => {
  localData.value = {
    static: newData.static,
    dynamic: newData.dynamic || {
      dataSource: '',
      field: '',
      format: '',
      unit: ''
    }
  }
  
  dataType.value = newData.dynamic ? 'dynamic' : 'static'
  
  initData()
}, { deep: true })

// 初始化
initData()

// 切换数据类型
const switchDataType = () => {
  if (dataType.value === 'static') {
    localData.value.dynamic = undefined
  } else {
    localData.value.dynamic = {
      dataSource: '',
      field: '',
      format: '',
      unit: ''
    }
  }
  
  updateData()
}

// 更新数据
const updateData = () => {
  emit('update', {...localData.value})
}

// 处理图片上传
const handleImageChange = (file: any) => {
  // 在实际应用中，这里应该上传图片到服务器
  // 这里仅作为示例，使用本地 FileReader 读取图片
  const reader = new FileReader()
  reader.onload = (e) => {
    localData.value.static = e.target?.result as string
    updateData()
  }
  reader.readAsDataURL(file.raw)
}

// 更新图表类型
const updateChartType = () => {
  // 重置数据
  if (['line', 'bar', 'scatter'].includes(chartType.value)) {
    xAxisData.value = '类别A,类别B,类别C,类别D,类别E'
    yAxisData.value = '120,200,150,80,70'
  } else if (chartType.value === 'pie') {
    pieData.value = [
      { name: '类别A', value: 1048 },
      { name: '类别B', value: 735 },
      { name: '类别C', value: 580 },
      { name: '类别D', value: 484 },
      { name: '类别E', value: 300 }
    ]
  }
  
  updateChartData()
}

// 更新图表数据
const updateChartData = () => {
  let chartData: any = {}
  
  if (['line', 'bar', 'scatter'].includes(chartType.value)) {
    const xData = xAxisData.value.split(',').map(item => item.trim())
    const yData = yAxisData.value.split(',').map(item => {
      const num = parseFloat(item.trim())
      return isNaN(num) ? 0 : num
    })
    
    chartData = {
      xAxis: {
        type: 'category',
        data: xData
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: yData,
        type: chartType.value
      }]
    }
  } else if (chartType.value === 'pie') {
    chartData = {
      series: [{
        type: 'pie',
        radius: '50%',
        data: pieData.value
      }]
    }
  }
  
  localData.value.static = chartData
  chartJson.value = JSON.stringify(chartData, null, 2)
  updateData()
}

// 从JSON更新图表
const updateChartFromJson = () => {
  try {
    const chartData = JSON.parse(chartJson.value)
    localData.value.static = chartData
    updateData()
    
    // 尝试更新可视化编辑器的值
    if (chartData.series && chartData.series.length > 0) {
      chartType.value = chartData.series[0].type || 'line'
      
      if (['line', 'bar', 'scatter'].includes(chartType.value)) {
        if (chartData.xAxis && chartData.xAxis.data) {
          xAxisData.value = chartData.xAxis.data.join(',')
        }
        
        if (chartData.series[0].data) {
          yAxisData.value = chartData.series[0].data.join(',')
        }
      } else if (chartType.value === 'pie') {
        if (chartData.series[0].data) {
          pieData.value = [...chartData.series[0].data]
        }
      }
    }
  } catch (error) {
    console.error('JSON解析失败', error)
  }
}

// 添加饼图数据项
const addPieDataItem = () => {
  pieData.value.push({ name: '新类别', value: 100 })
  updateChartData()
}

// 删除饼图数据项
const removePieDataItem = (index: number) => {
  pieData.value.splice(index, 1)
  updateChartData()
}

// 更新仪表盘数据
const updateGaugeData = () => {
  const gaugeData = {
    series: [{
      type: 'gauge',
      min: gaugeMin.value,
      max: gaugeMax.value,
      detail: {
        formatter: `{value}${gaugeUnit.value ? ' ' + gaugeUnit.value : ''}`
      },
      data: [{ value: gaugeValue.value }]
    }]
  }
  
  if (gaugeTitle.value) {
    gaugeData.title = {
      text: gaugeTitle.value
    }
  }
  
  localData.value.static = gaugeData
  updateData()
}

// 更新3D模型数据
const updateModelData = () => {
  let modelData: any = {
    model: modelType.value
  }
  
  if (modelType.value === 'tank') {
    modelData.fillLevel = tankFillLevel.value
  } else if (modelType.value === 'pump' || modelType.value === 'valve') {
    modelData.status = deviceStatus.value
  } else if (modelType.value === 'pipe') {
    modelData.flow = pipeFlow.value
    if (pipeFlow.value) {
      modelData.flowDirection = pipeFlowDirection.value
      modelData.flowSpeed = pipeFlowSpeed.value
    }
  }
  
  localData.value.static = modelData
  updateData()
}
</script>

<style scoped>
.data-editor {
  padding: 10px;
  color: #000;
}

.image-uploader {
  width: 100%;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  margin-top: 10px;
}

.image-uploader:hover {
  border-color: var(--el-color-primary);
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.preview-image {
  width: 100%;
  height: 100px;
  object-fit: contain;
}

.pie-data-item {
  margin-bottom: 10px;
}

/* 确保所有表单元素中的文字为黑色 */
:deep(.el-form-item__label) {
  color: #000 !important;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner),
:deep(.el-select-dropdown__item),
:deep(.el-radio-button__inner),
:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  color: #000 !important;
}

/* 确保下拉选项也是黑色文字 */
:deep(.el-select-dropdown__item) {
  color: #000 !important;
}

/* 确保输入框中的文字为黑色 */
.dark-text :deep(input),
.dark-text :deep(textarea) {
  color: #000 !important;
}
</style>