<template>
  <div class="container">
    <el-form
      ref="ruleFormRef"
      style="min-width: 600px"
      :model="ruleForm"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      :size="formSize"
      status-icon
    >
      <!-- 设备下拉框 -->
      <el-form-item label="设备" prop="device">
        <el-select v-model="ruleForm.device" placeholder="请选择设备" filterable clearable>
          <el-option label="设备1" value="device1" />
          <el-option label="设备2" value="device2" />
          <el-option label="设备3" value="device3" />
        </el-select>
      </el-form-item>

      <!-- 点位下拉框 -->
      <el-form-item label="点位" prop="location">
        <el-select v-model="ruleForm.location" placeholder="请选择点位" filterable clearable>
          <el-option label="点位1" value="location1" />
          <el-option label="点位2" value="location2" />
          <el-option label="点位3" value="location3" />
        </el-select>
      </el-form-item>

      <!-- 机组下拉框 -->
      <el-form-item label="机组" prop="unit">
        <el-select v-model="ruleForm.unit" placeholder="请选择机组" filterable clearable>
          <el-option label="机组1" value="unit1" />
          <el-option label="机组2" value="unit2" />
          <el-option label="机组3" value="unit3" />
        </el-select>
      </el-form-item>
      <!-- 逻辑关系单选 -->
      <el-form-item label="逻辑关系" prop="logicRelation">
        <el-radio-group v-model="ruleForm.logicRelation">
          <el-radio label="&">且</el-radio>
          <el-radio label="||">或</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 小表单 - 点位，比较，数值，依赖设备 -->
      <el-form-item label="依赖点位" prop="logicDetails">
        <div v-for="(item, index) in ruleForm.logicDetails" :key="index" class="logic-detail-row">
          <el-select
            v-model="item.location"
            placeholder="请选择点位"
            filterable
            clearable
            :rules="[{ required: true, message: '点位不能为空', trigger: 'change' }]"
          >
            <el-option label="点位1" value="location1" />
            <el-option label="点位2" value="location2" />
            <el-option label="点位3" value="location3" />
          </el-select>

          <el-select
            v-model="item.comparator"
            placeholder="请选择比较"
            filterable
            clearable
            :rules="[{ required: true, message: '比较不能为空', trigger: 'change' }]"
          >
            <el-option label=">" value=">" />
            <el-option label="<" value="<" />
            <el-option label="=" value="=" />
            <el-option label="≥" value="≥" />
            <el-option label="≤" value="≤" />
            <el-option label="≠" value="≠" />
          </el-select>

          <el-input
            v-model="item.value"
            placeholder="请输入数值"
            style="width: 150px"
            :rules="[{ required: true, message: '数值不能为空', trigger: 'blur' }]"
          />

          <el-select
            v-model="item.dependencyDevice"
            placeholder="请选择依赖设备"
            filterable
            clearable
            :rules="[{ required: true, message: '依赖设备不能为空', trigger: 'change' }]"
          >
            <el-option label="设备1" value="device1" />
            <el-option label="设备2" value="device2" />
            <el-option label="设备3" value="device3" />
          </el-select>

          <!-- 是否关联单选框 -->
          <el-form-item label="是否关联">
            <el-radio-group v-model="item.isAssociated">
              <el-radio label="yes">是</el-radio>
              <el-radio label="no">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 告警级别选择框，依赖“是否关联”的值 -->
          <el-form-item v-if="item.isAssociated === 'yes'" label="告警级别" prop="alertLevel">
            <el-select v-model="item.alertLevel" placeholder="请选择告警级别" filterable clearable>
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-form-item>

          <el-button type="danger" icon="el-icon-delete" @click="removeLogicDetail(index)" size="mini"> 删除 </el-button>
        </div>

        <el-button type="primary" icon="el-icon-plus" @click="addLogicDetail"> 新增 </el-button>
      </el-form-item>
      <el-form-item label="计算内容" prop="calculationContent">
        <el-input type="textarea" v-model="ruleForm.calculationContent" placeholder="请输入计算内容" :rows="2" style="width: 300px" />
      </el-form-item>
      <el-form-item label="结论" prop="conclusion">
        <el-input type="textarea" v-model="ruleForm.conclusion" placeholder="请输入结论" :rows="2" style="width: 300px" />
      </el-form-item>
      <el-form-item label="建议" prop="suggestion">
        <el-input type="textarea" v-model="ruleForm.suggestion" placeholder="请输入建议" :rows="2" style="width: 300px" />
      </el-form-item>

      <!-- 保存与清空按钮 -->
      <el-form-item>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="handleClear">清空</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'

interface RuleForm {
  device: string
  location: string
  unit: string
  logicRelation: string
  logicDetails: Array<{
    location: string
    comparator: string
    value: string
    dependencyDevice: string
    isAssociated: string
    alertLevel: string
  }>
  calculationContent: string,
  conclusion: string
  suggestion: string
}

const formSize = ref<ComponentSize>('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  device: '',
  location: '',
  unit: '',
  logicRelation: 'AND',
  logicDetails: [
    { location: '', comparator: '', value: '', dependencyDevice: '', isAssociated: 'no', alertLevel: '' }
  ],
  calculationContent: '',
  conclusion: '',
  suggestion: ''
})

const rules = reactive<FormRules<RuleForm>>({
  device: [{ required: true, message: '请选择设备', trigger: 'change' }],
  location: [{ required: true, message: '请选择点位', trigger: 'change' }],
  unit: [{ required: true, message: '请选择机组', trigger: 'change' }],
  logicRelation: [{ required: true, message: '请选择逻辑关系', trigger: 'change' }],
  logicDetails: [{
    required: true, message: '请填写所有逻辑条件', trigger: 'change'
  }],
  conclusion: [{ required: true, message: '请填写结论', trigger: 'change' }],

})

const addLogicDetail = () => {
  ruleForm.logicDetails.push({
    location: '',
    comparator: '',
    value: '',
    dependencyDevice: '',
    isAssociated: 'no',
    alertLevel: ''
  })
}

const removeLogicDetail = (index: number) => {
  ruleForm.logicDetails.splice(index, 1)
}

const handleSave = () => {
  ruleFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      alert('保存成功！')
    } else {
      // console.log('表单验证失败')
    }
  })
}

const handleClear = () => {
  ruleFormRef.value?.resetFields()
}
</script>

<style scoped>
.container {
  margin: 20px;
}
.logic-detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.logic-detail-row > * {
  margin-right: 10px;
}
</style>
