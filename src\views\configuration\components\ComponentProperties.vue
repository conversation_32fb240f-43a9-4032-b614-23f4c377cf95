<template>
  <div class="component-properties">
    <el-tabs>
      <el-tab-pane label="基础属性">
        <el-form label-position="top" size="small">
          <el-form-item label="组件名称">
            <el-input v-model="localComponent.name" @change="updateComponent" class="dark-text" />
          </el-form-item>
          
          <el-form-item label="位置">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-input-number 
                  v-model="localComponent.x" 
                  :min="0" 
                  controls-position="right"
                  placeholder="X"
                  @change="updateComponent"
                  class="dark-text"
                />
              </el-col>
              <el-col :span="12">
                <el-input-number 
                  v-model="localComponent.y" 
                  :min="0" 
                  controls-position="right"
                  placeholder="Y"
                  @change="updateComponent"
                  class="dark-text"
                />
              </el-col>
            </el-row>
          </el-form-item>
          
          <el-form-item label="尺寸">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-input-number 
                  v-model="localComponent.width" 
                  :min="10" 
                  controls-position="right"
                  placeholder="宽度"
                  @change="updateComponent"
                  class="dark-text"
                />
              </el-col>
              <el-col :span="12">
                <el-input-number 
                  v-model="localComponent.height" 
                  :min="10" 
                  controls-position="right"
                  placeholder="高度"
                  @change="updateComponent"
                  class="dark-text"
                />
              </el-col>
            </el-row>
          </el-form-item>
          
          <el-form-item label="旋转">
            <el-slider 
              v-model="localComponent.rotation" 
              :min="0" 
              :max="360" 
              :step="1"
              show-input
              @change="updateComponent"
              class="dark-text"
            />
          </el-form-item>
          
          <el-form-item label="透明度">
            <el-slider 
              v-model="localComponent.opacity" 
              :min="0" 
              :max="1" 
              :step="0.01"
              show-input
              @change="updateComponent"
              class="dark-text"
            />
          </el-form-item>
          
          <el-form-item label="可见性">
            <el-switch 
              v-model="localComponent.visible" 
              @change="updateComponent"
              class="dark-text"
            />
          </el-form-item>
          
          <el-form-item label="锁定">
            <el-switch 
              v-model="localComponent.locked" 
              @change="updateComponent"
              class="dark-text"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式">
        <component-style-editor 
          :component="localComponent" 
          @update="updateStyle"
        />
      </el-tab-pane>
      
      <el-tab-pane label="数据">
        <component-data-editor 
          :component="localComponent" 
          @update="updateData"
        />
      </el-tab-pane>
      
      <el-tab-pane label="事件">
        <component-event-editor 
          :component="localComponent" 
          @update="updateEvents"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import ComponentStyleEditor from './editors/ComponentStyleEditor.vue'
import ComponentDataEditor from './editors/ComponentDataEditor.vue'
import ComponentEventEditor from './editors/ComponentEventEditor.vue'
import type { ConfigurationComponent } from '../types'

const props = defineProps<{
  component: ConfigurationComponent
}>()

const emit = defineEmits<{
  update: [component: ConfigurationComponent]
}>()

// 本地组件副本
const localComponent = ref<ConfigurationComponent>({...props.component})

// 监听组件变化，更新本地副本
watch(() => props.component, (newComponent) => {
  localComponent.value = {...newComponent}
}, { deep: true })

// 更新组件
const updateComponent = () => {
  emit('update', {...localComponent.value})
}

// 更新样式
const updateStyle = (style: any) => {
  localComponent.value.style = {...style}
  updateComponent()
}

// 更新数据
const updateData = (data: any) => {
  localComponent.value.data = {...data}
  updateComponent()
}

// 更新事件
const updateEvents = (events: any[]) => {
  localComponent.value.events = [...events]
  updateComponent()
}
</script>

<style scoped>
.component-properties {
  color: #000;
}

/* 确保所有表单元素中的文字为黑色 */
:deep(.el-form-item__label) {
  color: #000 !important;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner),
:deep(.el-select-dropdown__item),
:deep(.el-radio-button__inner),
:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  color: #000 !important;
}

:deep(.el-tabs__item) {
  color: #000 !important;
}

:deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary) !important;
}

/* 确保下拉选项也是黑色文字 */
:deep(.el-select-dropdown__item) {
  color: #000 !important;
}

/* 确保输入框中的文字为黑色 */
.dark-text :deep(input),
.dark-text :deep(textarea) {
  color: #000 !important;
}
</style>