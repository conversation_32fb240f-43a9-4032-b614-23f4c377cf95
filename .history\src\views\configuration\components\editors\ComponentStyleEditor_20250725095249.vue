<template>
  <div class="style-editor">
    <el-form label-position="top" size="small">
      <!-- 背景颜色 -->
      <el-form-item label="背景颜色">
        <el-color-picker 
          v-model="localStyle.backgroundColor" 
          show-alpha
          @change="updateStyle"
        />
      </el-form-item>
      
      <!-- 边框 -->
      <el-form-item label="边框">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-input-number 
              v-model="localStyle.borderWidth" 
              :min="0" 
              :max="10"
              controls-position="right"
              placeholder="宽度"
              @change="updateStyle"
            />
          </el-col>
          <el-col :span="12">
            <el-color-picker 
              v-model="localStyle.borderColor" 
              show-alpha
              @change="updateStyle"
            />
          </el-col>
        </el-row>
      </el-form-item>
      
      <!-- 圆角 -->
      <el-form-item label="圆角">
        <el-slider 
          v-model="localStyle.borderRadius" 
          :min="0" 
          :max="50" 
          :step="1"
          show-input
          @change="updateStyle"
        />
      </el-form-item>
      
      <!-- 文本样式 (仅对文本类组件显示) -->
      <template v-if="['text', 'button'].includes(component.type)">
        <el-form-item label="字体大小">
          <el-input-number 
            v-model="localStyle.fontSize" 
            :min="8" 
            :max="72"
            controls-position="right"
            @change="updateStyle"
          />
        </el-form-item>
        
        <el-form-item label="字体颜色">
          <el-color-picker 
            v-model="localStyle.fontColor" 
            show-alpha
            @change="updateStyle"
          />
        </el-form-item>
        
        <el-form-item label="字体粗细">
          <el-select v-model="localStyle.fontWeight" @change="updateStyle">
            <el-option label="正常" value="normal" />
            <el-option label="粗体" value="bold" />
            <el-option label="细体" value="lighter" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="文本对齐">
          <el-radio-group v-model="localStyle.textAlign" @change="updateStyle">
            <el-radio-button label="left">左对齐</el-radio-button>
            <el-radio-button label="center">居中</el-radio-button>
            <el-radio-button label="right">右对齐</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </template>
      
      <!-- 阴影 -->
      <el-form-item label="阴影">
        <el-switch 
          v-model="shadowEnabled" 
          @change="updateShadow"
        />
        
        <template v-if="shadowEnabled">
          <el-row :gutter="10" class="shadow-controls">
            <el-col :span="12">
              <el-form-item label="模糊度">
                <el-slider 
                  v-model="shadowBlur" 
                  :min="0" 
                  :max="20" 
                  :step="1"
                  @change="updateShadow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="颜色">
                <el-color-picker 
                  v-model="shadowColor" 
                  show-alpha
                  @change="updateShadow"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-form-item>
      
      <!-- 渐变 -->
      <el-form-item label="渐变背景">
        <el-switch 
          v-model="gradientEnabled" 
          @change="updateGradient"
        />
        
        <template v-if="gradientEnabled">
          <el-form-item label="渐变类型">
            <el-radio-group v-model="gradientType" @change="updateGradient">
              <el-radio-button label="linear">线性</el-radio-button>
              <el-radio-button label="radial">径向</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item v-if="gradientType === 'linear'" label="方向">
            <el-slider 
              v-model="gradientDirection" 
              :min="0" 
              :max="360" 
              :step="15"
              show-input
              @change="updateGradient"
            />
          </el-form-item>
          
          <el-form-item label="颜色">
            <div class="gradient-colors">
              <div 
                v-for="(color, index) in gradientColors" 
                :key="index"
                class="color-item"
              >
                <el-color-picker 
                  v-model="gradientColors[index]" 
                  show-alpha
                  @change="updateGradient"
                />
                <el-button 
                  v-if="gradientColors.length > 2"
                  type="danger" 
                  icon="Delete" 
                  circle
                  size="small"
                  @click="removeGradientColor(index)"
                />
              </div>
              
              <el-button 
                v-if="gradientColors.length < 5"
                type="primary" 
                icon="Plus"
                circle
                size="small"
                @click="addGradientColor"
              />
            </div>
          </el-form-item>
        </template>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
}>()

const emit = defineEmits<{
  update: [style: any]
}>()

// 本地样式副本
const localStyle = ref({...props.component.style})

// 阴影设置
const shadowEnabled = ref(!!props.component.style.boxShadow)
const shadowBlur = ref(props.component.style.boxShadow ? parseInt(props.component.style.boxShadow.split('px')[0]) || 5 : 5)
const shadowColor = ref(props.component.style.boxShadow ? props.component.style.boxShadow.split(') ')[0] + ')' : 'rgba(0, 0, 0, 0.3)')

// 渐变设置
const gradientEnabled = ref(!!props.component.style.gradient)
const gradientType = ref(props.component.style.gradient?.type || 'linear')
const gradientDirection = ref(props.component.style.gradient?.direction || 90)
const gradientColors = ref(props.component.style.gradient?.colors || ['#409EFF', '#ffffff'])

// 监听组件变化，更新本地副本
watch(() => props.component.style, (newStyle) => {
  localStyle.value = {...newStyle}
  
  // 更新阴影设置
  shadowEnabled.value = !!newStyle.boxShadow
  if (newStyle.boxShadow) {
    shadowBlur.value = parseInt(newStyle.boxShadow.split('px')[0]) || 5
    shadowColor.value = newStyle.boxShadow.split(') ')[0] + ')'
  }
  
  // 更新渐变设置
  gradientEnabled.value = !!newStyle.gradient
  if (newStyle.gradient) {
    gradientType.value = newStyle.gradient.type || 'linear'
    gradientDirection.value = newStyle.gradient.direction || 90
    gradientColors.value = [...(newStyle.gradient.colors || ['#409EFF', '#ffffff'])]
  }
}, { deep: true })

// 更新样式
const updateStyle = () => {
  emit('update', {...localStyle.value})
}

// 更新阴影
const updateShadow = () => {
  if (shadowEnabled.value) {
    localStyle.value.boxShadow = `${shadowBlur.value}px ${shadowBlur.value}px ${shadowBlur.value * 2}px ${shadowColor.value}`
  } else {
    localStyle.value.boxShadow = undefined
  }
  
  updateStyle()
}

// 更新渐变
const updateGradient = () => {
  if (gradientEnabled.value) {
    localStyle.value.gradient = {
      type: gradientType.value,
      colors: [...gradientColors.value],
      direction: gradientType.value === 'linear' ? gradientDirection.value : undefined
    }
  } else {
    localStyle.value.gradient = undefined
  }
  
  updateStyle()
}

// 添加渐变颜色
const addGradientColor = () => {
  gradientColors.value.push('#ffffff')
  updateGradient()
}

// 删除渐变颜色
const removeGradientColor = (index: number) => {
  gradientColors.value.splice(index, 1)
  updateGradient()
}
</script>

<style scoped>
.style-editor {
  padding: 10px;
}

.shadow-controls {
  margin-top: 10px;
}

.gradient-colors {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 5px;
}
</style>