<template>
  <div class="performance-canvas-container" ref="containerRef">
    <div 
      class="performance-canvas"
      ref="canvasRef"
      :style="canvasStyle"
      @click="handleCanvasClick"
      @dragover="handleDragOver"
      @drop="handleDrop"
      @mousemove="handleMouseMove"
      @wheel="handleWheel"
    >
      <!-- 虚拟化渲染的组件 -->
      <template v-for="component in visibleComponents" :key="component.id">
        <component
          :is="getComponentRenderer(component.type)"
          :component="component"
          :selected="isSelected(component.id)"
          :editing="editing"
          @select="handleSelectComponent"
          @update="handleUpdateComponent"
          @start-connection="handleStartConnection"
          @finish-connection="handleFinishConnection"
        />
      </template>
      
      <!-- 连接线层 -->
      <svg 
        class="connections-layer"
        :style="svgStyle"
        v-if="visibleConnections.length > 0"
      >
        <defs>
          <!-- 箭头标记定义 -->
          <marker
            v-for="connection in visibleConnections"
            :key="`arrow-${connection.id}`"
            :id="`arrow-${connection.id}`"
            viewBox="0 0 12 8"
            refX="6"
            refY="4"
            :markerWidth="connection.style.arrowSize"
            :markerHeight="connection.style.arrowSize"
            orient="auto"
            markerUnits="strokeWidth"
          >
            <path d="M0,0 L0,8 L12,4 z" :fill="connection.style.arrowColor" />
          </marker>
        </defs>
        
        <!-- 渲染可见的连接线 -->
        <g
          v-for="connection in visibleConnections"
          :key="connection.id"
          class="connection-group"
          :class="{ 'selected': selectedConnectionId === connection.id }"
          @click="handleSelectConnection(connection)"
        >
          <path
            :d="getConnectionPath(connection)"
            :stroke="connection.style.strokeColor"
            :stroke-width="connection.style.strokeWidth"
            :stroke-dasharray="connection.style.strokeDasharray"
            fill="none"
            :marker-end="`url(#arrow-${connection.id})`"
            class="connection-line"
          />
        </g>
      </svg>
      
      <!-- 性能信息面板 -->
      <div v-if="showPerformanceInfo" class="performance-panel">
        <div class="perf-item">
          <span class="label">FPS:</span>
          <span class="value" :class="{ 'warning': currentFPS < 30, 'critical': currentFPS < 15 }">
            {{ currentFPS }}
          </span>
        </div>
        <div class="perf-item">
          <span class="label">组件:</span>
          <span class="value">{{ visibleComponents.length }}/{{ totalComponents }}</span>
        </div>
        <div class="perf-item">
          <span class="label">连接:</span>
          <span class="value">{{ visibleConnections.length }}/{{ totalConnections }}</span>
        </div>
        <div class="perf-item">
          <span class="label">内存:</span>
          <span class="value">{{ formatMemory(memoryUsage) }}</span>
        </div>
        <div class="perf-item">
          <span class="label">渲染时间:</span>
          <span class="value">{{ renderTime.toFixed(1) }}ms</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { throttle, debounce } from 'lodash-es'
import type { ConfigurationComponent, ComponentConnection } from '../types'

// 导入各种组件渲染器
import TextComponent from './components/TextComponent.vue'
import ImageComponent from './components/ImageComponent.vue'
import ShapeComponent from './components/ShapeComponent.vue'
import ChartComponent from './components/ChartComponent.vue'
import GaugeComponent from './components/GaugeComponent.vue'
import ButtonComponent from './components/ButtonComponent.vue'

const props = defineProps<{
  components: ConfigurationComponent[]
  connections: ComponentConnection[]
  canvasWidth: number
  canvasHeight: number
  backgroundColor: string
  zoom: number
  showGrid: boolean
  gridSize: number
  selectedComponents: string[]
  selectedConnectionId?: string
  editing: boolean
}>()

const emit = defineEmits([
  'canvasClick',
  'selectComponent',
  'updateComponent',
  'startConnection',
  'finishConnection',
  'selectConnection',
  'dragOver',
  'drop'
])

// 性能配置常量
const VIEWPORT_BUFFER = 100 // 视窗缓冲区大小
const MAX_VISIBLE_COMPONENTS = 500 // 最大可见组件数
const THROTTLE_INTERVAL = 16 // 约60fps
const DEBOUNCE_DELAY = 100
const FPS_SAMPLE_SIZE = 60

// DOM引用
const containerRef = ref<HTMLElement>()
const canvasRef = ref<HTMLElement>()

// 性能监控状态
const showPerformanceInfo = ref(process.env.NODE_ENV === 'development')
const currentFPS = ref(60)
const renderTime = ref(0)
const memoryUsage = ref(0)
const fpsHistory = ref<number[]>([])

// 视窗状态
const viewport = ref({
  x: 0,
  y: 0,
  width: 0,
  height: 0
})

// 组件渲染器映射
const componentRendererMap = {
  text: TextComponent,
  image: ImageComponent,
  shape: ShapeComponent,
  chart: ChartComponent,
  gauge: GaugeComponent,
  button: ButtonComponent,
  // 可以根据需要添加更多组件类型
}

// 获取组件渲染器
const getComponentRenderer = (type: string) => {
  return componentRendererMap[type as keyof typeof componentRendererMap] || ShapeComponent
}

// 计算样式
const canvasStyle = computed(() => ({
  width: `${props.canvasWidth}px`,
  height: `${props.canvasHeight}px`,
  backgroundColor: props.backgroundColor,
  transform: `scale(${props.zoom})`,
  transformOrigin: 'top left',
  backgroundImage: props.showGrid 
    ? `linear-gradient(rgba(200,200,200,0.3) 1px, transparent 1px), 
       linear-gradient(90deg, rgba(200,200,200,0.3) 1px, transparent 1px)`
    : 'none',
  backgroundSize: `${props.gridSize}px ${props.gridSize}px`,
  position: 'relative'
}))

const svgStyle = computed(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  pointerEvents: 'none',
  zIndex: 1
}))

// 计算统计信息
const totalComponents = computed(() => props.components.length)
const totalConnections = computed(() => props.connections.length)

// 更新视窗信息（高性能节流）
const updateViewport = throttle(() => {
  if (!containerRef.value) return
  
  const container = containerRef.value
  const rect = container.getBoundingClientRect()
  
  viewport.value = {
    x: (container.scrollLeft || 0) / props.zoom - VIEWPORT_BUFFER,
    y: (container.scrollTop || 0) / props.zoom - VIEWPORT_BUFFER,
    width: rect.width / props.zoom + VIEWPORT_BUFFER * 2,
    height: rect.height / props.zoom + VIEWPORT_BUFFER * 2
  }
}, THROTTLE_INTERVAL)

// 检查组件是否在视窗内（优化算法）
const isComponentInViewport = (component: ConfigurationComponent): boolean => {
  const { x, y, width, height } = component
  const { x: vx, y: vy, width: vw, height: vh } = viewport.value
  
  // 使用快速的AABB碰撞检测
  return !(x + width < vx || x > vx + vw || y + height < vy || y > vy + vh)
}

// 检查连接线是否在视窗内
const isConnectionInViewport = (connection: ComponentConnection): boolean => {
  const sourceComp = props.components.find(c => c.id === connection.sourceComponent)
  const targetComp = props.components.find(c => c.id === connection.targetComponent)
  
  if (!sourceComp || !targetComp) return false
  
  const minX = Math.min(sourceComp.x, targetComp.x)
  const maxX = Math.max(sourceComp.x + sourceComp.width, targetComp.x + targetComp.width)
  const minY = Math.min(sourceComp.y, targetComp.y)
  const maxY = Math.max(sourceComp.y + sourceComp.height, targetComp.y + targetComp.height)
  
  const { x: vx, y: vy, width: vw, height: vh } = viewport.value
  
  return !(maxX < vx || minX > vx + vw || maxY < vy || minY > vy + vh)
}

// 虚拟化可见组件（核心性能优化）
const visibleComponents = computed(() => {
  const startTime = performance.now()
  
  let result: ConfigurationComponent[]
  
  // 如果组件数量少，直接返回所有组件
  if (props.components.length <= 50) {
    result = props.components
  } else {
    // 使用视窗裁剪
    result = props.components
      .filter(isComponentInViewport)
      .slice(0, MAX_VISIBLE_COMPONENTS) // 限制最大渲染数量
  }
  
  // 记录渲染时间
  renderTime.value = performance.now() - startTime
  
  return result
})

// 虚拟化可见连接线
const visibleConnections = computed(() => {
  if (props.connections.length <= 20) {
    return props.connections
  }
  
  return props.connections.filter(isConnectionInViewport)
})

// 判断组件是否被选中
const isSelected = (componentId: string): boolean => {
  return props.selectedComponents.includes(componentId)
}

// 获取连接线路径（简化版本，提升性能）
const getConnectionPath = (connection: ComponentConnection): string => {
  const sourceComp = props.components.find(c => c.id === connection.sourceComponent)
  const targetComp = props.components.find(c => c.id === connection.targetComponent)
  
  if (!sourceComp || !targetComp) return ''
  
  // 简化的直线连接，提升性能
  const startX = sourceComp.x + sourceComp.width / 2
  const startY = sourceComp.y + sourceComp.height / 2
  const endX = targetComp.x + targetComp.width / 2
  const endY = targetComp.y + targetComp.height / 2
  
  return `M ${startX} ${startY} L ${endX} ${endY}`
}

// 事件处理（防抖和节流优化）
const handleCanvasClick = debounce((event: MouseEvent) => {
  emit('canvasClick', event)
}, DEBOUNCE_DELAY)

const handleMouseMove = throttle((event: MouseEvent) => {
  // 只在必要时更新
  if (props.editing) {
    updateViewport()
  }
}, THROTTLE_INTERVAL)

const handleWheel = throttle((event: WheelEvent) => {
  // 缩放操作后更新视窗
  nextTick(() => {
    updateViewport()
  })
}, THROTTLE_INTERVAL)

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  emit('dragOver', event)
}

const handleDrop = (event: DragEvent) => {
  emit('drop', event)
}

const handleSelectComponent = (componentId: string) => {
  emit('selectComponent', componentId)
}

const handleUpdateComponent = (component: ConfigurationComponent) => {
  emit('updateComponent', component)
}

const handleStartConnection = (componentId: string, data: any) => {
  emit('startConnection', componentId, data)
}

const handleFinishConnection = (componentId: string, data: any) => {
  emit('finishConnection', componentId, data)
}

const handleSelectConnection = (connection: ComponentConnection) => {
  emit('selectConnection', connection)
}

// FPS监控
let lastFrameTime = performance.now()
let frameCount = 0

const measureFPS = () => {
  const now = performance.now()
  frameCount++
  
  if (now - lastFrameTime >= 1000) {
    const fps = Math.round((frameCount * 1000) / (now - lastFrameTime))
    
    fpsHistory.value.push(fps)
    if (fpsHistory.value.length > FPS_SAMPLE_SIZE) {
      fpsHistory.value.shift()
    }
    
    currentFPS.value = Math.round(
      fpsHistory.value.reduce((a, b) => a + b, 0) / fpsHistory.value.length
    )
    
    frameCount = 0
    lastFrameTime = now
  }
  
  requestAnimationFrame(measureFPS)
}

// 内存监控
const updateMemoryUsage = throttle(() => {
  if ((performance as any).memory) {
    memoryUsage.value = (performance as any).memory.usedJSHeapSize
  }
}, 5000) // 每5秒更新一次

// 格式化内存显示
const formatMemory = (bytes: number): string => {
  const mb = bytes / (1024 * 1024)
  return `${mb.toFixed(1)}MB`
}

// 监听props变化，更新视窗
watch(() => [props.zoom, props.components.length], () => {
  nextTick(() => {
    updateViewport()
  })
}, { deep: false })

// 组件挂载时的初始化
onMounted(() => {
  nextTick(() => {
    updateViewport()
    
    // 添加滚动监听
    if (containerRef.value) {
      containerRef.value.addEventListener('scroll', updateViewport, { passive: true })
    }
    
    // 启动性能监控
    if (showPerformanceInfo.value) {
      measureFPS()
      updateMemoryUsage()
    }
  })
})

// 组件卸载时的清理
onUnmounted(() => {
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', updateViewport)
  }
})
</script>

<style scoped>
.performance-canvas-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
}

.performance-canvas {
  position: relative;
  min-width: 100%;
  min-height: 100%;
  user-select: none;
}

.connections-layer {
  pointer-events: none;
}

.connection-group {
  cursor: pointer;
  pointer-events: all;
}

.connection-group.selected .connection-line {
  stroke-width: 3;
  filter: drop-shadow(0 0 3px rgba(64, 158, 255, 0.8));
}

.connection-line {
  transition: stroke-width 0.2s ease;
}

.connection-line:hover {
  stroke-width: 3;
}

.performance-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 11px;
  z-index: 9999;
  min-width: 150px;
  backdrop-filter: blur(10px);
}

.perf-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  line-height: 1.2;
}

.perf-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #999;
  margin-right: 8px;
}

.value {
  color: #0f0;
  font-weight: bold;
}

.value.warning {
  color: #ff0;
}

.value.critical {
  color: #f00;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}
</style> 