<template>
  <el-card class="benefit-card" shadow="hover">
    <!-- Header部分 -->
    <template #header>
      <div class="card-header">
        <div class="header-content">
          <div class="header-title">{{ title }}</div>
          <div class="header-value">{{ headerValue }}</div>
        </div>
        <div class="header-controls">
          <!-- 图标显示：只有在没有下拉框和日期选择器时显示 -->
          <div class="header-icon" v-if="icon && (!dropdownOptions || dropdownOptions.length === 0) && !showDatePicker">
            <el-icon :size="20" :color="iconColor">
              <component :is="icon" />
            </el-icon>
          </div>

          <!-- 机组选择器 -->
          <div class="header-dropdown" v-if="dropdownOptions && dropdownOptions.length > 0">
            <el-select
              :model-value="selectedValue"
              @update:model-value="handleSelectChange"
              placeholder="请选择机组"
              size="small"
              class="custom-select"
            >
              <el-option v-for="item in dropdownOptions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </div>

          <!-- 日期选择器 -->
          <div class="header-datepicker" v-if="showDatePicker">
            <el-date-picker
              :model-value="selectedDate"
              @update:model-value="handleDateChange"
              type="month"
              placeholder="选择年月"
              size="small"
              class="custom-datepicker"
              format="YYYY年MM月"
              value-format="YYYY-MM"
            />
          </div>
        </div>
      </div>
    </template>

    <!-- Body部分 - 图表内容 -->
    <div class="card-body">
      <slot name="chart"></slot>
    </div>

    <!-- Footer部分 -->
    <div class="card-footer" v-if="footerLabel && footerValue">
      <div class="footer-item">
        <span class="footer-label">{{ footerLabel }}</span>
        <span class="footer-value">{{ footerValue }}</span>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts" name="BenefitCard">
import { ref, onMounted, onUnmounted, watch } from 'vue'

interface DropdownOption {
  id: number
  name: string
}

interface Props {
  title: string
  headerValue: string
  footerLabel?: string
  footerValue?: string
  icon?: any
  iconColor?: string
  dropdownOptions?: DropdownOption[]
  selectedValue?: number
  showDatePicker?: boolean
  selectedDate?: string
}

const props = withDefaults(defineProps<Props>(), {
  iconColor: '#409EFF'
})

const emit = defineEmits(['update:selectedValue', 'update:selectedDate'])

// 定时器相关
const autoSwitchTimer = ref<number | null>(null)
const currentIndex = ref(0)

// 开始自动切换定时器
const startAutoSwitch = () => {
  // 清除现有定时器
  if (autoSwitchTimer.value) {
    clearInterval(autoSwitchTimer.value)
  }

  // 只有当存在机组选项且数量大于1时才开启自动切换
  if (props.dropdownOptions && props.dropdownOptions.length > 1) {
    autoSwitchTimer.value = setInterval(() => {
      // 计算下一个索引
      currentIndex.value = (currentIndex.value + 1) % props.dropdownOptions!.length
      const nextOption = props.dropdownOptions![currentIndex.value]
      emit('update:selectedValue', nextOption.id)
    }, 10000) // 10秒切换一次
  }
}

// 停止自动切换
const stopAutoSwitch = () => {
  if (autoSwitchTimer.value) {
    clearInterval(autoSwitchTimer.value)
    autoSwitchTimer.value = null
  }
}

// 重置定时器（在手动操作时调用）
const resetAutoSwitch = () => {
  stopAutoSwitch()
  startAutoSwitch()
}


// 处理机组选择变化
const handleSelectChange = (value: number) => {
  // 更新当前索引
  if (props.dropdownOptions) {
    const index = props.dropdownOptions.findIndex(item => item.id === value)
    if (index !== -1) {
      currentIndex.value = index
    }
  }
  emit('update:selectedValue', value)
  // 手动切换后重置定时器
  resetAutoSwitch()
}

// 处理日期变化
const handleDateChange = (date: string) => {
  emit('update:selectedDate', date)
  // 手动切换日期后也重置定时器
  resetAutoSwitch()
}

// 监听 dropdownOptions 变化，重新初始化自动切换
watch(() => props.dropdownOptions, (newOptions) => {
  if (newOptions && newOptions.length > 0) {
    // 找到当前选中项的索引
    if (props.selectedValue) {
      const index = newOptions.findIndex(item => item.id === props.selectedValue)
      if (index !== -1) {
        currentIndex.value = index
      }
    }
    // 重新开始自动切换
    startAutoSwitch()
  } else {
    stopAutoSwitch()
  }
}, { immediate: true })

// 监听 selectedValue 变化，同步索引
watch(() => props.selectedValue, (newValue) => {
  if (newValue && props.dropdownOptions) {
    const index = props.dropdownOptions.findIndex(item => item.id === newValue)
    if (index !== -1) {
      currentIndex.value = index
    }
  }
})

// 组件挂载时开始自动切换
onMounted(() => {
  startAutoSwitch()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoSwitch()
})
</script>

<style lang="scss" scoped>
.benefit-card {
  background: rgba(8, 42, 77, 0.2);
  border: 1px solid rgba(115, 208, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 24px rgba(115, 208, 255, 0.15);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    box-shadow: 0 4px 12px rgba(51, 221, 255, 0.4);
    border-color: rgba(115, 208, 255, 0.6);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;

    .header-content {
      flex: 1;

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: transparent;
        background: linear-gradient(180deg, #fff, #73d0ff 50%);
        -webkit-background-clip: text;
        background-clip: text;
        margin-bottom: 6px;
      }

      .header-value {
        font-size: 20px;
        font-weight: 700;
        color: #73d0ff;
        font-family: 'Arial', sans-serif;
        text-shadow: 0 0 8px rgba(115, 208, 255, 0.5);
        transform: skew(-5deg);
      }
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, rgba(115, 208, 255, 0.2) 0%, rgba(77, 222, 252, 0.3) 100%);
      border-radius: 8px;
      border: 1px solid rgba(115, 208, 255, 0.4);
    }

    .header-dropdown {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 120px;

      .custom-select {
        :deep(.el-input__wrapper) {
          background: linear-gradient(135deg, rgba(115, 208, 255, 0.2) 0%, rgba(77, 222, 252, 0.3) 100%);
          border: 1px solid rgba(115, 208, 255, 0.4);
          border-radius: 6px;
          box-shadow: none;

          &:hover {
            border-color: rgba(115, 208, 255, 0.6);
          }

          &.is-focus {
            border-color: #73d0ff;
            box-shadow: 0 0 8px rgba(115, 208, 255, 0.3);
          }
        }

        :deep(.el-input__inner) {
          color: #73d0ff;
          font-size: 12px;
          font-weight: 500;
          text-align: center;

          &::placeholder {
            color: rgba(115, 208, 255, 0.6);
          }
        }
      }
    }

    .header-datepicker {
      display: flex;
      align-items: center;
      justify-content: center;
     max-width: 130px;
      .custom-datepicker {
        :deep(.el-input__wrapper) {
          background: linear-gradient(135deg, rgba(115, 208, 255, 0.2) 0%, rgba(77, 222, 252, 0.3) 100%);
          border: 1px solid rgba(115, 208, 255, 0.4);
          border-radius: 6px;
          box-shadow: none;

          &:hover {
            border-color: rgba(115, 208, 255, 0.6);
          }

          &.is-focus {
            border-color: #73d0ff;
            box-shadow: 0 0 8px rgba(115, 208, 255, 0.3);
          }
        }

        :deep(.el-input__inner) {
          color: #73d0ff;
          font-size: 12px;
          font-weight: 500;
          text-align: center;

          &::placeholder {
            color: rgba(115, 208, 255, 0.6);
          }
        }
      }
    }
  }

  .card-body {
    flex: 1;
    min-height: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .card-footer {
    flex-shrink: 0;
    padding: 12px 0 0 0;
    border-top: 1px solid rgba(115, 208, 255, 0.2);

    .footer-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .footer-label {
        margin-left: 15px;
        font-size: 14px;
        color: #b4d0fc;
        font-weight: 500;
      }

      .footer-value {
        margin-right: 15px;
        font-size: 16px;
        font-weight: 600;
        color: #4ddefc;
        font-family: 'Arial', sans-serif;
        text-shadow: 0 0 6px rgba(77, 222, 252, 0.4);
        transform: skew(-5deg);
      }
    }
  }
}

:deep(.el-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  border: none;
}

:deep(.el-card__header) {
  padding: 16px 16px 0 16px;
  flex-shrink: 0;
  background: transparent;
  border-bottom: none;
}

:deep(.el-card__body) {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: transparent;
}

:deep(.el-select-dropdown) {
  background: rgba(8, 42, 77, 0.9);
  border: 1px solid rgba(115, 208, 255, 0.3);
  backdrop-filter: blur(10px);

  .el-select-dropdown__item {
    color: #73d0ff;
    background: transparent;

    &:hover {
      background: rgba(115, 208, 255, 0.2);
    }

    &.selected {
      background: rgba(115, 208, 255, 0.3);
      color: #fff;
    }
  }
}
</style>
