<template>
  <!-- 变频循泵电流 -->
  <div class="vfc-pump-contrast">
    <div class="app-container">
      <!-- 操作按钮 -->
      <div class="mb-[20px]">
        <el-button type="primary" @click="goBack">返回</el-button>
        <el-button type="primary" @click="exportData" :disabled="!selectedDate">导出数据</el-button>
      </div>

      <!-- 日历选择区域 -->
      <div class="calendar-card ml-[20px] mb-[20px]">
        <div class="calendar-container">
          <h3>选择查询月份</h3>
          <el-date-picker
            v-model="selectedDate"
            type="month"
            placeholder="请选择月份"
            :disabled-date="disabledDate"
            :clearable="false"
            format="YYYY-MM"
            value-format="YYYY-MM"
            @change="handleDateChange"
            style="width: 200px"
          />
        </div>
      </div>

      <!-- 瀑布流表格容器 -->
      <div class="waterfall-container" ref="waterfallContainer">
        <div class="waterfall-column" v-for="(col, colIdx) in waterfallColumns" :key="colIdx">
          <el-table v-for="tbl in col" :key="tbl.id" :data="tbl.data" border stripe class="waterfall-table">
            <!-- 大表头 -->
            <el-table-column :label="tbl.title" align="center">
              <!-- 动态渲染列 -->
              <el-table-column v-for="colDef in tbl.columns" :key="colDef.prop" :prop="colDef.prop" :label="colDef.label" align="center">
                <template #default="{ row }">
                  <span>{{ row[colDef.prop] }}</span>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 空数据状态 -->
      <div v-if="complexTableData.length === 0" class="empty-state">
        <el-empty description="暂无数据" />
      </div>
    </div>
  </div>
</template>
  
  <script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import { getvariableFrequencyPumpReportView, getCalendarData } from '../dataFilterConfig/index.api'

interface ColumnDef {
  prop: string
  label: string
}
interface TableGroup {
  id: number
  title: string
  columns: ColumnDef[]
  data: Record<string, any>[]
}

const router = useRouter()
const route = useRoute()

const projectId = ref<number>(0)
const powerUnitId = ref<number>(0)
const filterType = ref<number>(0)
const unitName = ref<string>()

const selectedDate = ref<string>('')
const availableMonths = ref<string[]>([])
const complexTableData = ref<TableGroup[]>([])
const waterfallColumns = ref<TableGroup[][]>([[], []])

// 初始化路由参数
function initParams() {
  projectId.value = Number(route.query.projectId || 0)
  powerUnitId.value = Number(route.query.powerUnitId || 0)
  filterType.value = Number(route.query.filterType || 0)
  unitName.value = route.query.unitName as string
}

// 禁用不可选月份
function disabledDate(time: Date) {
  const month = `${time.getFullYear()}-${String(time.getMonth() + 1).padStart(2, '0')}`
  return !availableMonths.value.includes(month)
}

// 选择月份后重新拉取数据
function handleDateChange(val: string) {
  if (val) {
    fetchData(val)
  }
}

// 瀑布流布局
function createWaterfallLayout() {
  const columnCount = 2
  const cols: TableGroup[][] = Array.from({ length: columnCount }, () => [])
  const heights = Array(columnCount).fill(0)
  complexTableData.value.forEach((group) => {
    const h = 80 + group.data.length * 48
    const idx = heights.indexOf(Math.min(...heights))
    cols[idx].push(group)
    heights[idx] += h
  })
  waterfallColumns.value = cols
}

// 获取可选月份
async function fetchAvailableMonths() {
  try {
    const params = { projectId: projectId.value, powerUnitId: powerUnitId.value }
    const res = await getCalendarData(params)
    if (res.code === 200) {
      availableMonths.value = res.data || []
      if (availableMonths.value.length) {
        selectedDate.value = [...availableMonths.value].sort().pop()!
      }
    } else {
      ElMessage.error('获取月份失败：' + res.message)
    }
  } catch {
    ElMessage.error('请求可选月份出错')
  }
}

// 拉取表格数据
async function fetchData(month: string) {
  try {
    const params = {
      projectId: projectId.value,
      powerUnitId: powerUnitId.value,
      reportMonth: month,
    }
    const res = await getvariableFrequencyPumpReportView(params)
    if (res.code === 200) {
      complexTableData.value = res.data.map((item: any, idx: number) => ({
        id: idx,
        title: item.title,
        columns: item.headers.map((h: any) => ({ prop: h.prop, label: h.label })),
        data: item.rows,
      }))
      createWaterfallLayout()
    } else {
      ElMessage.error('获取表格失败：' + res.message)
    }
  } catch {
    ElMessage.error('请求表格出错')
  }
}

// 导出 Excel
function exportData() {
  try {
    const wb = XLSX.utils.book_new()
    const aoa: any[][] = []
    complexTableData.value.forEach((tbl, gi) => {
      if (gi) aoa.push([])
      aoa.push([tbl.title])
      aoa.push(tbl.columns.map((c) => c.label))
      tbl.data.forEach((row) => aoa.push(tbl.columns.map((c) => row[c.prop])))
    })
    const ws = XLSX.utils.aoa_to_sheet(aoa)
    ws['!cols'] = complexTableData.value[0]?.columns.map(() => ({ wch: 20 }))
    XLSX.utils.book_append_sheet(wb, ws, '变频循泵电流对比')
    saveAs(
      new Blob([XLSX.write(wb, { bookType: 'xlsx', type: 'array' })], { type: 'application/octet-stream' }),
      `${unitName.value}_变频循泵电流对比_${selectedDate.value}.xlsx`
    )
    ElMessage.success('导出成功')
  } catch {
    ElMessage.error('导出失败')
  }
}

function goBack() {
  router.go(-1)
}

onMounted(async () => {
  initParams()
  await fetchAvailableMonths()
  if (selectedDate.value) fetchData(selectedDate.value)
})
</script>
  
  <style scoped lang="scss">
.params-card {
  background-color: rgba(2, 28, 51, 0.5);
  border: 1px solid rgba(11, 56, 93, 1);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  max-width: 600px;
}
.params-card h3 {
  color: #fff;
  margin-bottom: 12px;
  font-size: 16px;
}
.params-display {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}
.params-display span {
  color: #fff;
  padding: 6px 12px;
  background-color: rgba(11, 56, 93, 0.3);
  border-radius: 4px;
  font-size: 14px;
}
.vfc-pump-contrast {
  .waterfall-container {
    display: flex;
    margin-top: 20px;
    gap: 20px;
    @media (max-width: 1200px) {
      flex-direction: column;
    }
  }
  .waterfall-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  .waterfall-table {
    overflow: hidden;
    transition: all 0.3s ease;
    background-color: rgba(2, 28, 51, 0.3) !important;
  }
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: rgba(204, 204, 204, 0.6);
  }
  @media (max-width: 768px) {
    .waterfall-container {
      flex-direction: column;
    }
  }
}
.calendar-card {
  background-color: rgba(2, 28, 51, 0.5);
  border: 1px solid rgba(11, 56, 93, 1);
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
  max-width: 350px;
  display: inline-block;
}

.calendar-card h3 {
  color: #ffffff;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.calendar-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.available-months {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* 整个表格背景色 */
:deep(.el-table) {
  background-color: rgba(2, 28, 51, 0.3) !important;
}

:deep(.el-table__body) {
  background-color: rgba(2, 28, 51, 0.3) !important;
}

:deep(.el-table td) {
  background-color: rgba(2, 28, 51, 0.3) !important;
  padding: 12px 8px;
  color: #ffffff;
  vertical-align: middle;
  min-height: 48px;
}

:deep(.el-table th) {
  background-color: rgb(11, 56, 93) !important;
  color: #ffffff;
}

/* 表头行样式 */
:deep(.el-table__body tr:has(.header-cell)) {
  background-color: rgb(11, 56, 93) !important;
}
// :deep(.el-table tr) {
//     background-color: rgb(11, 56, 93) !important;

// }

:deep(.el-table__body tr:has(.header-cell) td) {
  background-color: rgb(11, 56, 93) !important;
  color: #ffffff;
  padding: 12px 8px !important;
  vertical-align: middle;
  min-height: 48px;
}

/* 悬浮效果 */
:deep(.el-table__body tr:hover td) {
  background-color: rgb(41, 70, 93) !important;
  /* border: 2px solid rgb(22, 91, 156) !important; */
  color: #ffffff;
}

/* 表头行悬浮效果 */
:deep(.el-table__body tr:has(.header-cell):hover td) {
  background-color: rgb(11, 56, 93) !important;
  /* border: 1px solid #ebeef5 !important; */
  color: #ffffff;
}

/* 条纹效果 */
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: rgba(2, 28, 51, 0.5) !important;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover td) {
  background-color: rgb(41, 70, 93) !important;
  /* border: 2px solid rgb(22, 91, 156) !important; */
}

/* 日期选择器样式 */
:deep(.el-date-editor) {
  background-color: rgba(2, 28, 51, 0.3) !important;
  border-color: rgba(11, 56, 93, 1) !important;
}

:deep(.el-date-editor .el-input__inner) {
  color: #ffffff !important;
  background-color: transparent !important;
}

:deep(.el-date-editor .el-input__inner::placeholder) {
  color: rgba(233, 19, 19, 0.6) !important;
}
</style>
  