<template>
  <div 
    class="text-component"
    :style="componentStyle"
  >
    {{ displayText }}
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
  editing?: boolean
}>()

// 组件样式
const componentStyle = computed(() => {
  const { style } = props.component
  
  return {
    fontSize: style.fontSize ? `${style.fontSize}px` : '14px',
    color: style.fontColor || '#333333',
    fontWeight: style.fontWeight || 'normal',
    textAlign: style.textAlign || 'left',
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: getJustifyContent(style.textAlign),
    padding: '5px',
    boxSizing: 'border-box' as const,
    overflow: 'hidden',
    wordBreak: 'break-word'
  }
})

// 获取对齐方式
const getJustifyContent = (textAlign?: string) => {
  switch (textAlign) {
    case 'left':
      return 'flex-start'
    case 'center':
      return 'center'
    case 'right':
      return 'flex-end'
    default:
      return 'flex-start'
  }
}

// 显示的文本内容
const displayText = computed(() => {
  const { data } = props.component
  
  // 如果有动态数据，优先使用动态数据
  if (data.dynamic && data.dynamic.value !== undefined) {
    const value = data.dynamic.value
    const format = data.dynamic.format
    const unit = data.dynamic.unit || ''
    
    // 如果有格式化函数，应用格式化
    if (format) {
      // 这里可以实现各种格式化逻辑
      // 简单示例：保留小数位数
      if (typeof value === 'number' && format.startsWith('decimal')) {
        const decimalPlaces = parseInt(format.split(':')[1] || '2')
        return value.toFixed(decimalPlaces) + unit
      }
    }
    
    return value + unit
  }
  
  // 否则使用静态数据
  return data.static || ''
})
</script>

<style scoped>
.text-component {
  user-select: none;
}
</style>