<template>
  <div class="app-box" :class="{ 'no-sidebar': appStore.sidebar.fullHide }">
    <div class="app-main-container" :style="{ 'background': sidebar.fullHide ? 'none' : 'rgba(2, 28, 51, 0.5)' }">
      <!-- <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!settingsStore.topNav" /> -->
      <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!sidebar.topHide" />
      <section class="app-main">
        <router-view v-slot="{ Component, route }">
          <transition :enter-active-class="animante" mode="out-in">
            <div>
              <keep-alive :include="tagsViewStore.cachedViews">
                <component v-if="!route.meta.link" :is="Component" :key="route.path" />
              </keep-alive>
            </div>
          </transition>
        </router-view>
        <iframe-toggle />
      </section>
    </div>
  </div>
</template>

<script setup name="AppMain" lang="ts">
import { useAppStore } from '@/store/modules/app'
import useTagsViewStore from '@/store/modules/tagsView'
import useSettingsStore from '@/store/modules/settings'
import IframeToggle  from './IframeToggle/index.vue'
import { ComponentInternalInstance } from 'vue'

const sidebar = computed(() => useAppStore().sidebar)
const setMaskBg = computed(() => useAppStore().sidebar)
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const tagsViewStore = useTagsViewStore()
const appStore = useAppStore()
// 随机动画集合
const animante = ref<string>('')
const settingsStore = useSettingsStore()
const animationEnable = ref(useSettingsStore().animationEnable)
watch(()=> useSettingsStore().animationEnable, (val) => {
  animationEnable.value = val
  if (val) {
    animante.value = proxy?.animate.animateList[Math.round(Math.random() * proxy?.animate.animateList.length)] as string
  } else {
    animante.value = proxy?.animate.defaultAnimate as string
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.module.scss';
.app-box {
  padding-left: $base-sidebar-width;
  transition: .3s ease;
  width: 100%;
  height: calc(100vh - 50px);
  background: url('@/assets/images/mask.png');
  background-size: cover;
  background-position: center; /* 将图片中心位置对齐 */
  background-repeat: no-repeat; /* 防止重复平铺 */
  background-size: 100% 100%;
  opacity: 0.966;
  overflow-y: auto;
}
.app-main-container{
  padding: 0 16px;
  // height: 100%;
}
.no-sidebar{
  padding-left:0 !important;
  transition: .3s ease;
  width: 100%;
  height:100vh;
  // background: url('@/assets/images/bsbg.png');
  background-size: cover;
  background-position: center; /* 将图片中心位置对齐 */
  background-repeat: no-repeat; /* 防止重复平铺 */
  opacity: 0.966;
  overflow-y: auto;
}
#app .hideSidebar {
  .app-box {
    padding-left: 54px;
  }
}
.breadcrumb-container{
  color: #fff !important;
}
.app-main {
  /* 50= navbar  50  */
  // min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  height: 100%;
  // overflow-y: auto;
}

.fixed-header+.app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    // min-height: calc(100vh - 84px);
  }

  .fixed-header+.app-main {
     padding-top: 84px;
  }
}
</style>
<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>
