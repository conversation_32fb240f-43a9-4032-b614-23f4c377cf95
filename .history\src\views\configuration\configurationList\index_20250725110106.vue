<template>
  <div class="configuration-list">
    <div class="list-header">
      <h2>组态项目列表</h2>
      <div class="header-actions">
        <el-input
          v-model="searchText"
          placeholder="搜索项目"
          prefix-icon="Search"
          clearable
          style="width: 250px"
        />
        <el-button type="primary" @click="createProject">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
      </div>
    </div>
    
    <div class="list-content">
      <el-empty v-if="filteredProjects.length === 0" description="暂无组态项目" />
      
      <el-row v-else :gutter="20">
        <el-col 
          v-for="project in filteredProjects" 
          :key="project.id"
          :xs="24" 
          :sm="12" 
          :md="8" 
          :lg="6" 
          :xl="4"
        >
          <el-card class="project-card" shadow="hover">
            <div class="project-thumbnail" @click="openProject(project.id)">
              <img 
                v-if="project.thumbnail" 
                :src="project.thumbnail" 
                alt="项目缩略图"
              />
              <div v-else class="no-thumbnail">
                <el-icon><Picture /></el-icon>
              </div>
            </div>
            
            <div class="project-info">
              <h3 class="project-name">{{ project.name }}</h3>
              <p class="project-desc">{{ project.description || '暂无描述' }}</p>
              
              <div class="project-meta">
                <span class="project-date">
                  更新于: {{ formatDate(project.updatedAt) }}
                </span>
                <el-tag :type="getStatusType(project.status)" size="small">
                  {{ getStatusText(project.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="project-actions">
              <el-tooltip content="编辑" placement="top">
                <el-button 
                  type="primary" 
                  circle 
                  @click="openProject(project.id)"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip content="预览" placement="top">
                <el-button 
                  type="success" 
                  circle 
                  @click="previewProject(project.id)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip content="删除" placement="top">
                <el-button 
                  type="danger" 
                  circle 
                  @click="confirmDelete(project)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useConfigurationStore } from '../stores/configurationStore'
import type { ConfigurationProject } from '../types'

const router = useRouter()
const configStore = useConfigurationStore()

// 搜索文本
const searchText = ref('')

// 项目列表
const projects = ref<ConfigurationProject[]>([])

// 过滤后的项目列表
const filteredProjects = computed(() => {
  if (!searchText.value) {
    return projects.value
  }
  
  const search = searchText.value.toLowerCase()
  return projects.value.filter(project => 
    project.name.toLowerCase().includes(search) || 
    (project.description && project.description.toLowerCase().includes(search))
  )
})

// 加载项目列表
onMounted(async () => {
  await loadProjects()
})

// 加载项目列表
const loadProjects = async () => {
  try {
    // 这里应该从API加载项目列表
    // 临时使用示例数据
    projects.value = [
      {
        id: 'project_1',
        name: '生产线监控',
        description: '工厂生产线实时监控组态',
        width: 1920,
        height: 1080,
        backgroundColor: '#f0f0f0',
        components: [],
        dataBindings: [],
        createdAt: '2023-05-15T08:00:00.000Z',
        updatedAt: '2023-05-20T10:30:00.000Z',
        createdBy: 'admin',
        status: 'published'
      },
      {
        id: 'project_2',
        name: '设备状态监控',
        description: '设备运行状态实时监控',
        width: 1920,
        height: 1080,
        backgroundColor: '#f5f5f5',
        components: [],
        dataBindings: [],
        createdAt: '2023-06-10T09:15:00.000Z',
        updatedAt: '2023-06-12T14:20:00.000Z',
        createdBy: 'admin',
        status: 'draft'
      },
      {
        id: 'project_3',
        name: '能源消耗监控',
        description: '工厂能源消耗实时监控',
        width: 1920,
        height: 1080,
        backgroundColor: '#e8f4f8',
        components: [],
        dataBindings: [],
        createdAt: '2023-07-05T11:30:00.000Z',
        updatedAt: '2023-07-08T16:45:00.000Z',
        createdBy: 'admin',
        status: 'archived'
      }
    ]
  } catch (error) {
    ElMessage.error('加载项目列表失败')
  }
}

// 创建新项目
const createProject = () => {
  router.push('/configuration/configurationEditing')
}

// 打开项目
const openProject = (id: string) => {
  router.push({
    path: '/configuration/configurationEditing',
    query: { id }
  })
}

// 预览项目
const previewProject = (id: string) => {
  router.push({
    path: '/configuration/demonstrate',
    query: { id }
  })
}

// 确认删除
const confirmDelete = (project: ConfigurationProject) => {
  ElMessageBox.confirm(
    `确定要删除项目"${project.name}"吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    deleteProject(project.id)
  }).catch(() => {})
}

// 删除项目
const deleteProject = async (id: string) => {
  try {
    // 这里应该调用API删除项目
    // 临时模拟删除
    const index = projects.value.findIndex(p => p.id === id)
    if (index !== -1) {
      projects.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'warning'
    case 'archived':
      return 'info'
    default:
      return 'default'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    case 'archived':
      return '已归档'
    default:
      return '未知'
  }
}
</script>

<style scoped>
.configuration-list {
  padding: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.list-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.list-content {
  margin-top: 20px;
}

.project-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.project-card:hover {
  transform: translateY(-5px);
}

.project-thumbnail {
  height: 150px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 10px;
}

.project-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-thumbnail {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 48px;
  color: #dcdfe6;
}

.project-info {
  margin-bottom: 15px;
}

.project-name {
  margin: 0 0 5px 0;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.project-desc {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: #606266;
  height: 36px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.project-actions {
  display: flex;
  justify-content: space-around;
  padding-top: 10px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>