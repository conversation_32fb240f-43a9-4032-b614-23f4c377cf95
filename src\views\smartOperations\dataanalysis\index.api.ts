import request from '@/utils/request'

enum Api {
  list = '/manager/analysisReport/main/point/analysis',
  diagnosisList = '/manager/analysisReport/intelligent/analysis',
  diffTypeDatas = '/device/deviceProperty/downSampling/diffTypeDatas',
  infoByDeviceIdAndIdentifier = '/alert/infoByDeviceIdAndIdentifier',
}

// 查询分析报告
export const getAnalysis = (data) => {
  return request({
    url: Api.list,
    method: 'post',
    data,
  })
}
// 智能诊断
export const getdiagnosisList = (data) => {
  return request({
    url: Api.diagnosisList,
    method: 'post',
    data,
  })
}
// 查询历史数据和预测数据进行对比
export const getDiffTypeDatas = (data) => {
  return request({
    url: Api.diffTypeDatas,
    method: 'post',
    data,
  })
}
// 预测结果分析结论
export const getinfoByDeviceIdAndIdentifier = (data) => {
  return request({
    url: Api.infoByDeviceIdAndIdentifier,
    method: 'post',
    data,
  })
}
