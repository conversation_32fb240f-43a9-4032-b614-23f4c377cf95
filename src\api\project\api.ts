import request from '@/utils/request'
import { AxiosPromise } from 'axios'

import { SimpleProjectVo, subSystemQuery, subSystemVO, subSystemParamQuery, subSystemParamVO, subSystemParamAddVO } from './types'

//所有项目
export function projectAll(): AxiosPromise<SimpleProjectVo[]> {
  return request({
    url: '/project/all',
    method: 'get',
  })
}

// 获取子系统列表
export function subSystemList(query: subSystemQuery): AxiosPromise<subSystemVO[]> {
  return request({
    url: '/project/subSystem/all',
    method: 'post',
    data: query,
  })
}

// 获取子系统参数设计列表
export function subSystemParamList(query: subSystemParamQuery): AxiosPromise<subSystemParamVO[]> {
  return request({
    url: 'project/subSystemParam/list',
    method: 'post',
    data: query,
  })
}
// 新增子系统设计参数
export const subSystemParamAdd = (data: subSystemParamAddVO) => {
  return request({
    url: '/project/subSystemParam/add',
    method: 'post',
    data,
  })
}
// 编辑子系统设计参数
export const subSystemParamEdit = (data: subSystemParamAddVO) => {
  return request({
    url: '/project/subSystemParam/edit',
    method: 'post',
    data,
  })
}
// 删除子系统设计参数project/subSystemParam/delete
// 删除
export const deleteCategories = (data: (string | number)[]) => {
  return request({
    url: '/project/subSystemParam/delete',
    method: 'post',
    data,
  })
}

// export const deleteCategories = (data: (string | number)[]) => {
//   return request({
//     url: Api.del,
//     method: 'post',
//     data,
//   })
// }

// // 编辑、保存
// export const saveCategories = (data: ICategoriesVO) => {
//   return request({
//     url: Api.save,
//     method: 'post',
//     data,
//   })
// }
