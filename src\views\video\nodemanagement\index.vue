<template>
  <div style="margin-top: 30px;">
    <iframe ref="myframe" width="100%" height="750px" :src="httpsurl + '#/mediaServerManger?token='+ token"></iframe>
    <button @click="getIFrameURL">获取当前页面URL</button>
  </div>
</template>

<script setup>
  import { ref } from 'vue'

  const httpsurl = ref('http://***************:23080/')
//   const httpsurl = ref('http://127.0.0.1:8080/')

  const token = ref('*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************')
</script>

<style scoped>
iframe {
  border: none;
}
</style>
