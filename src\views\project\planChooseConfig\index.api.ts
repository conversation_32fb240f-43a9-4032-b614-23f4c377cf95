import request from '@/utils/request'

enum Api {
  powerUnitAll = '/project/powerUnit/all',
  configDetailDeviceList = '/device/list',
  configDetailThingModelByProductKey = '/product/getThingModelByProductKey',
  operationConfigAdd = '/intelligent/operationConfig/add',
  operationConfigEdit = '/intelligent/operationConfig/edit',
  operationConfigDelete = '/intelligent/operationConfig/delete',
  operationConfigList = '/intelligent/operationConfig/list',
}

// 获取机组数据
export const powerUnitAllList = (data) => {
  return request({
    url: Api.powerUnitAll,
    method: 'post',
    data,
  })
}
// 获取设备对应点位
export const getDevicePoint = (data) => {
  return request({
    url: Api.configDetailThingModelByProductKey,
    method: 'post',
    data,
  })
}
// 查询设备列表
export const getDeviceList = (data) => {
  return request({
    url: Api.configDetailDeviceList,
    method: 'post',
    data,
  })
}
// 新增方案选择配置
export const addPlanChooseConfig = (data) => {
  return request({
    url: Api.operationConfigAdd,
    method: 'post',
    data,
  })
}

// 编辑方案选择配置
export const editPlanChooseConfig = (data) => {
  return request({
    url: Api.operationConfigEdit,
    method: 'post',
    data,
  })
}

// 删除方案选择配置
export const deletePlanChooseConfig = (data) => {
  return request({
    url: Api.operationConfigDelete,
    method: 'post',
    data,
  })
}
// 查询方案选择配置列表
export const getPlanChooseConfigList = (data) => {
  return request({
    url: Api.operationConfigList,
    method: 'post',
    data,
  })
}
