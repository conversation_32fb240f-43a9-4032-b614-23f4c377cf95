import { onMounted, onUnmounted } from 'vue'

export interface KeyboardShortcut {
  key: string
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  action: () => void
  description: string
}

export function useKeyboardShortcuts() {
  const shortcuts = new Map<string, KeyboardShortcut>()
  
  // 生成快捷键标识符
  const getShortcutKey = (event: KeyboardEvent): string => {
    const parts: string[] = []
    if (event.ctrlKey) parts.push('ctrl')
    if (event.shiftKey) parts.push('shift')
    if (event.altKey) parts.push('alt')
    parts.push(event.key.toLowerCase())
    return parts.join('+')
  }
  
  // 注册快捷键
  const registerShortcut = (shortcut: KeyboardShortcut) => {
    const parts: string[] = []
    if (shortcut.ctrl) parts.push('ctrl')
    if (shortcut.shift) parts.push('shift')
    if (shortcut.alt) parts.push('alt')
    parts.push(shortcut.key.toLowerCase())
    
    const key = parts.join('+')
    shortcuts.set(key, shortcut)
  }
  
  // 注销快捷键
  const unregisterShortcut = (key: string, ctrl?: boolean, shift?: boolean, alt?: boolean) => {
    const parts: string[] = []
    if (ctrl) parts.push('ctrl')
    if (shift) parts.push('shift')
    if (alt) parts.push('alt')
    parts.push(key.toLowerCase())
    
    const shortcutKey = parts.join('+')
    shortcuts.delete(shortcutKey)
  }
  
  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent) => {
    // 忽略在输入框中的按键
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return
    }
    
    const shortcutKey = getShortcutKey(event)
    const shortcut = shortcuts.get(shortcutKey)
    
    if (shortcut) {
      event.preventDefault()
      event.stopPropagation()
      shortcut.action()
    }
  }
  
  // 获取所有快捷键
  const getAllShortcuts = (): KeyboardShortcut[] => {
    return Array.from(shortcuts.values())
  }
  
  // 生命周期管理
  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown)
  })
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
  })
  
  return {
    registerShortcut,
    unregisterShortcut,
    getAllShortcuts
  }
}

// 预定义的常用快捷键
export const DEFAULT_SHORTCUTS = {
  COPY: { key: 'c', ctrl: true, description: '复制选中组件' },
  PASTE: { key: 'v', ctrl: true, description: '粘贴组件' },
  CUT: { key: 'x', ctrl: true, description: '剪切选中组件' },
  DELETE: { key: 'Delete', description: '删除选中组件' },
  UNDO: { key: 'z', ctrl: true, description: '撤销操作' },
  REDO: { key: 'y', ctrl: true, description: '重做操作' },
  SELECT_ALL: { key: 'a', ctrl: true, description: '全选组件' },
  SAVE: { key: 's', ctrl: true, description: '保存项目' },
  DUPLICATE: { key: 'd', ctrl: true, description: '复制选中组件' },
  GROUP: { key: 'g', ctrl: true, description: '组合选中组件' },
  UNGROUP: { key: 'g', ctrl: true, shift: true, description: '取消组合' },
  ZOOM_IN: { key: '=', ctrl: true, description: '放大画布' },
  ZOOM_OUT: { key: '-', ctrl: true, description: '缩小画布' },
  ZOOM_FIT: { key: '0', ctrl: true, description: '适应画布' },
  TOGGLE_GRID: { key: 'g', description: '切换网格显示' },
  TOGGLE_RULERS: { key: 'r', description: '切换标尺显示' }
} as const
