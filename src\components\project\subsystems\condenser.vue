<!-- 凝汽器 -->

<template>
  <div>
    <!-- 弹框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="800px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="dialog.visible" ref="condenserData" :model="subData" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="换热面积" prop="">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入换热面积</span>
                </template>
                <template #suffix>
                  <span>㎡</span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="换热管参数" prop="">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入换热管参数</span>
                </template>
                <template #suffix>
                  <span></span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设计冷却水量" prop="">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入设计冷却水量</span>
                </template>
                <template #suffix>
                  <span>t/h</span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="进水温度" prop="">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入进水温度</span>
                </template>
                <template #suffix>
                  <span>℃</span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="凝汽器背压" prop="">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入凝汽器背压</span>
                </template>
                <template #suffix>
                  <span>kPa</span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="circulatingPump" lang="ts">
import { ref } from 'vue'

const props = defineProps({
  dialog: {
    type: Object,
    required: true
  }
})
const submitForm = () => {}
const cancel = () => {
  props.dialog.visible = false
}

const subData=ref<[]>([])
</script>
