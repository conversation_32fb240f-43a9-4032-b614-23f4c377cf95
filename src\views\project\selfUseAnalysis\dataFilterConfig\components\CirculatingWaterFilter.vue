<template>
  <div class="circulating-water-filter">
    <el-form :model="configData" label-width="120px">
      <!-- 条件配置 -->
      <!-- <el-divider content-position="left">条件配置</el-divider> -->

      <el-form-item label="泵组合方式">
        <el-input v-model="configData.cond.type" placeholder="请输入泵组合方式" style="width: 100%" />
      </el-form-item>

      <el-form-item label="运行台数">
        <el-input v-model="configData.cond.data.num" placeholder="请输入运行台数" style="width: 100%" />
      </el-form-item>

      <el-form-item label="筛选条件">
        <el-input v-model="configData.cond.title" placeholder="请输入筛选条件描述" style="width: 100%" />
      </el-form-item>

      <!-- 范围配置 -->
      <!-- <el-divider content-position="left">范围配置</el-divider> -->

      <div v-for="(range, index) in configData.range" :key="index" class="range-item">
        <el-card shadow="never" class="range-card">
          <template #header>
            <div class="range-header">
              <span>范围配置 {{ index + 1 }}</span>
              <el-button v-if="configData.range.length > 1" type="danger" size="small" icon="Delete" @click="removeRange(index)">删除</el-button>
            </div>
          </template>

          <el-form-item label="数据状态">
            <el-select v-model="range.type" placeholder="请选择数据状态" style="width: 100%">
              <el-option v-for="item in dataStateOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-row :gutter="20" v-if="range.data">
            <el-col :span="12">
              <el-form-item label="最小值">
                <el-input-number v-model="range.data.min" :precision="2" placeholder="请输入最小值" style="width: 100%" :controls="false" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大值">
                <el-input-number v-model="range.data.max" :precision="2" placeholder="请输入最大值" style="width: 100%" :controls="false" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-else>
            <el-col :span="24">
              <el-form-item>
                <el-text type="info">数据为空，请点击初始化</el-text>
                <el-button size="small" @click="range.data = { min: null, max: null }">初始化数据</el-button>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="取值范围">
            <el-input v-model="range.title" placeholder="请输入取值范围描述" style="width: 100%" />
          </el-form-item>
        </el-card>
      </div>

      <el-button type="primary" @click="addRange" icon="Plus" style="width: 100%; margin-top: 10px;">新增范围配置</el-button>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, toRefs, getCurrentInstance, nextTick } from 'vue'
import { ComponentInternalInstance } from 'vue'
import { debounce } from 'lodash-es'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { intelligent_data_filter_state } = toRefs<any>(proxy?.useDict('intelligent_data_filter_state'))

// 数据状态选项 - 从字典获取
const dataStateOptions = computed(() => {
  return (
    intelligent_data_filter_state.value || [
      {
        label: '正常',
        value: '0',
        elTagType: 'default',
        elTagClass: '',
      },
      {
        label: '低限异常',
        value: '1',
        elTagType: 'default',
        elTagClass: '',
      },
      {
        label: '低限故障',
        value: '2',
        elTagType: 'default',
        elTagClass: '',
      },
      {
        label: '高限异常',
        value: '3',
        elTagType: 'default',
        elTagClass: '',
      },
      {
        label: '高限故障',
        value: '4',
        elTagType: 'default',
        elTagClass: '',
      },
    ]
  )
})

interface RangeItem {
  type: string
  data: {
    min: number | null
    max: number | null
  } | null
  title: string
}

interface CirculatingWaterConfig {
  cond: {
    type: string
    data: {
      num: string
    } | null
    title: string
  }
  range: RangeItem[]
}

interface InternalCirculatingWaterConfig {
  cond: {
    type: string
    data: {
      num: string
    }
    title: string
  }
  range: {
    type: string
    data: {
      min: number | null
      max: number | null
    }
    title: string
  }[]
}

// 定义props
const props = defineProps<{
  modelValue: CirculatingWaterConfig
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: CirculatingWaterConfig): void
}>()

// 配置数据
const configData = reactive<InternalCirculatingWaterConfig>({
  cond: {
    type: '',
    data: {
      num: '',
    },
    title: '',
  },
  range: [
    {
      type: '',
      data: {
        min: null,
        max: null,
      },
      title: '',
    },
  ],
})

// 标记是否正在更新数据，防止循环触发
const isUpdating = ref(false)

// 重置数据到默认状态
const resetData = () => {
  Object.assign(configData, {
    cond: {
      type: '',
      data: {
        num: '',
      },
      title: '',
    },
    range: [
      {
        type: '',
        data: {
          min: null,
          max: null,
        },
        title: '',
      },
    ],
  })
}

// 初始化数据
const initData = () => {
  if (!props.modelValue || Object.keys(props.modelValue).length === 0 || !props.modelValue.cond) {
    resetData()
  } else {
    isUpdating.value = true

    // 使用更安全的数据映射，确保数据类型正确
    const condData = props.modelValue.cond.data
    const rangeData = props.modelValue.range || []

    console.log('CirculatingWaterFilter 初始化数据:', {
      condType: props.modelValue.cond.type,
      rangeTypes: rangeData.map(r => r.type)
    })

    Object.assign(configData, {
      cond: {
        type: props.modelValue.cond.type !== null && props.modelValue.cond.type !== undefined ? String(props.modelValue.cond.type) : '',
        data: {
          num: condData ? String(condData.num || '') : '',
        },
        title: props.modelValue.cond.title || '',
      },
      range: rangeData.map(range => ({
        type: range.type !== null && range.type !== undefined ? String(range.type) : '',
        data: range.data ? {
          min: range.data.min,
          max: range.data.max,
        } : {
          min: null,
          max: null,
        },
        title: range.title || '',
      }))
    })

    console.log('CirculatingWaterFilter 处理后数据:', {
      condType: configData.cond.type,
      rangeTypes: configData.range.map(r => r.type)
    })

    // 确保至少有一个范围配置
    if (configData.range.length === 0) {
      configData.range.push({
        type: '',
        data: {
          min: null,
          max: null,
        },
        title: '',
      })
    }

    nextTick(() => {
      isUpdating.value = false
    })
  }
}

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    // 强制更新数据，确保外部数据变化时能正确渲染
    initData()
  },
  { immediate: true, deep: true }
)

// 添加范围配置
const addRange = () => {
  configData.range.push({
    type: '',
    data: {
      min: null,
      max: null,
    },
    title: '',
  })
}

// 删除范围配置
const removeRange = (index: number) => {
  if (configData.range.length > 1) {
    configData.range.splice(index, 1)
  }
}

// 优化的数据转换函数
const transformData = (data: InternalCirculatingWaterConfig): CirculatingWaterConfig => {
  const result: CirculatingWaterConfig = {
    cond: {
      type: data.cond.type,
      data: data.cond.data.num === '' ? null : {
        num: data.cond.data.num,
      },
      title: data.cond.title,
    },
    range: data.range.map(range => ({
      type: range.type,
      data: range.data,
      title: range.title,
    }))
  }

  return result
}

// 使用防抖优化性能 - 减少延迟时间
const debouncedEmit = debounce((data: InternalCirculatingWaterConfig) => {
  if (!isUpdating.value) {
    const result = transformData(data)
    emit('update:modelValue', result)
  }
}, 150) // 从300ms减少到150ms

// 监听数据变化 - 使用防抖和减少深拷贝
watch(
  () => configData,
  (newVal) => {
    debouncedEmit(newVal)
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.circulating-water-filter {
  padding: 20px;
  background: rgba(3, 43, 82, 0.3);
  border-radius: 8px;
  margin-top: 20px;
}

.range-item {
  margin-bottom: 15px;
}

.range-card {
  background: rgba(3, 43, 82, 0.2);
  border: 1px solid rgba(33, 148, 255, 0.3);

  :deep(.el-card__header) {
    background: rgba(7, 53, 92, 0.5);
    border-bottom: 1px solid rgba(33, 148, 255, 0.3);
  }

  :deep(.el-card__body) {
    background: rgba(3, 43, 82, 0.1);
  }
}

.range-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  font-weight: 500;
}

:deep(.el-divider__text) {
  color: #fff;
  font-weight: 500;
}

:deep(.el-form-item__label) {
  color: rgba(204, 204, 204, 1);
}
</style>
