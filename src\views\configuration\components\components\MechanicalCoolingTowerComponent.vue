<template>
  <div 
    class="mechanical-cooling-tower"
    :style="componentStyle"
  >
    <!-- 机械通风冷却塔 -->
    <svg
      :width="component.width"
      :height="component.height"
      viewBox="0 0 140 180"
      class="tower-svg"
      preserveAspectRatio="xMidYMid meet"
    >
      <!-- 渐变定义 -->
      <defs>
        <linearGradient id="towerBodyGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" :style="`stop-color:${towerColor};stop-opacity:0.8`" />
          <stop offset="50%" :style="`stop-color:${towerColor};stop-opacity:1`" />
          <stop offset="100%" :style="`stop-color:${towerColor};stop-opacity:0.8`" />
        </linearGradient>
        
        <!-- 风扇渐变 -->
        <radialGradient id="fanGradient" cx="50%" cy="50%" r="50%">
          <stop offset="0%" style="stop-color:#e0e0e0;stop-opacity:1" />
          <stop offset="80%" style="stop-color:#b0b0b0;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#808080;stop-opacity:1" />
        </radialGradient>
        
        <!-- 水蒸气渐变 -->
        <radialGradient id="steamGradient" cx="50%" cy="50%" r="50%">
          <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8" />
          <stop offset="70%" style="stop-color:#e6f3ff;stop-opacity:0.4" />
          <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
        </radialGradient>
      </defs>
      
      <!-- 塔基础 -->
      <rect x="25" y="160" width="90" height="15" :fill="baseColor" stroke="#666" stroke-width="1"/>
      
      <!-- 塔体主结构 -->
      <rect x="30" y="80" width="80" height="80" fill="url(#towerBodyGradient)" stroke="#666" stroke-width="2" rx="4"/>
      
      <!-- 塔顶风扇罩 -->
      <rect x="35" y="40" width="70" height="40" :fill="fanHousingColor" stroke="#666" stroke-width="2" rx="8"/>
      
      <!-- 风扇 -->
      <g class="fan-assembly" transform="translate(70, 60)">
        <!-- 风扇外圈 -->
        <circle r="25" fill="url(#fanGradient)" stroke="#444" stroke-width="2"/>
        
        <!-- 风扇叶片 -->
        <g class="fan-blades" :class="{ 'rotating': isRunning }">
          <g v-for="i in 6" :key="i" :transform="`rotate(${i * 60})`">
            <path 
              d="M 0 -20 Q 8 -15 12 -8 Q 8 -2 0 0 Q -8 -2 -12 -8 Q -8 -15 0 -20 Z" 
              :fill="fanBladeColor" 
              stroke="#333" 
              stroke-width="1"
            />
          </g>
        </g>
        
        <!-- 风扇中心 -->
        <circle r="6" :fill="fanCenterColor" stroke="#333" stroke-width="1"/>
      </g>
      
      <!-- 进风格栅 -->
      <g class="air-intake">
        <line v-for="i in 8" :key="i" 
          :x1="35" :y1="85 + i * 8" 
          :x2="105" :y2="85 + i * 8" 
          stroke="#888" stroke-width="1" opacity="0.6"/>
      </g>
      
      <!-- 水蒸气效果 -->
      <g v-if="isRunning" class="steam-effect">
        <ellipse 
          v-for="(steam, index) in steamClouds" 
          :key="index"
          :cx="steam.x" 
          :cy="steam.y" 
          :rx="steam.rx" 
          :ry="steam.ry" 
          fill="url(#steamGradient)"
          :opacity="steam.opacity"
          class="steam-cloud"
          :style="`animation-delay: ${steam.delay}s`"
        />
      </g>
      
      <!-- 管道连接 -->
      <rect x="10" y="120" width="20" height="10" :fill="pipeColor" stroke="#666" stroke-width="1"/>
      <text x="5" y="118" font-size="8" fill="#666">进水</text>
      
      <rect x="110" y="140" width="20" height="10" :fill="pipeColor" stroke="#666" stroke-width="1"/>
      <text x="112" y="138" font-size="8" fill="#666">出水</text>
      
      <!-- 电机 -->
      <rect x="60" y="25" width="20" height="15" :fill="motorColor" stroke="#666" stroke-width="1" rx="2"/>
      <text x="70" y="22" text-anchor="middle" font-size="8" fill="#666">电机</text>
      
      <!-- 状态指示器 -->
      <circle 
        cx="120" 
        cy="50" 
        r="5" 
        :fill="statusColor" 
        stroke="#fff" 
        stroke-width="2"
        class="status-indicator"
      />
      
      <!-- 参数显示 -->
      <text x="70" y="110" text-anchor="middle" font-size="10" :fill="textColor" class="parameter-text">
        {{ displayTemperature }}°C
      </text>
      <text x="70" y="125" text-anchor="middle" font-size="9" :fill="textColor" class="parameter-text">
        {{ fanSpeed }}rpm
      </text>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
}>()

// 动画状态
const animationFrame = ref(0)
let animationId: number | null = null

// 组件样式
const componentStyle = computed(() => ({
  width: `${props.component.width}px`,
  height: `${props.component.height}px`,
  transform: `rotate(${props.component.rotation || 0}deg)`,
  opacity: props.component.opacity || 1
}))

// 获取数据值
const getValue = (key: string, defaultValue: any = 0) => {
  const data = props.component.data
  if (data?.dynamic?.[key] !== undefined) {
    return data.dynamic[key]
  }
  return data?.static?.[key] ?? defaultValue
}

// 运行状态和参数
const isRunning = computed(() => getValue('isRunning', true))
const temperature = computed(() => getValue('temperature', 32))
const fanSpeed = computed(() => getValue('fanSpeed', 1200))
const efficiency = computed(() => getValue('efficiency', 88))

// 显示参数
const displayTemperature = computed(() => Math.round(temperature.value * 10) / 10)

// 颜色配置
const towerColor = computed(() => getValue('towerColor', '#a8c8ec'))
const baseColor = computed(() => getValue('baseColor', '#a0a0a0'))
const fanHousingColor = computed(() => getValue('fanHousingColor', '#d0d0d0'))
const fanBladeColor = computed(() => getValue('fanBladeColor', '#e8e8e8'))
const fanCenterColor = computed(() => getValue('fanCenterColor', '#666'))
const motorColor = computed(() => getValue('motorColor', '#4a4a4a'))
const pipeColor = computed(() => getValue('pipeColor', '#4a90e2'))
const textColor = computed(() => getValue('textColor', '#333'))

// 状态颜色
const statusColor = computed(() => {
  if (!isRunning.value) return '#ff4444'
  if (efficiency.value > 85) return '#44ff44'
  if (efficiency.value > 70) return '#ffaa44'
  return '#ff4444'
})

// 水蒸气云朵
const steamClouds = computed(() => {
  if (!isRunning.value) return []
  
  return [
    { x: 60, y: 30, rx: 10, ry: 5, opacity: 0.6, delay: 0 },
    { x: 75, y: 25, rx: 14, ry: 7, opacity: 0.4, delay: 0.3 },
    { x: 65, y: 20, rx: 18, ry: 9, opacity: 0.3, delay: 0.6 },
    { x: 80, y: 22, rx: 12, ry: 6, opacity: 0.5, delay: 0.9 },
    { x: 55, y: 18, rx: 11, ry: 5, opacity: 0.4, delay: 1.2 }
  ]
})

// 动画循环
const animate = () => {
  animationFrame.value += 1
  animationId = requestAnimationFrame(animate)
}

onMounted(() => {
  if (isRunning.value) {
    animate()
  }
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped>
.mechanical-cooling-tower {
  position: relative;
  display: block;
  user-select: none;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.tower-svg {
  filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.2));
  width: 100%;
  height: 100%;
  display: block;
  /* 确保SVG能够正确缩放 */
  max-width: 100%;
  max-height: 100%;
}

.fan-blades.rotating {
  animation: fanRotation 0.5s linear infinite;
  transform-origin: center;
}

@keyframes fanRotation {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.steam-cloud {
  animation: steamRise 4s ease-in-out infinite;
}

@keyframes steamRise {
  0% {
    transform: translateY(0) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-15px) scale(1);
    opacity: 0.6;
  }
  100% {
    transform: translateY(-30px) scale(1.3);
    opacity: 0;
  }
}

.status-indicator {
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.parameter-text {
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
}

.steam-effect {
  pointer-events: none;
}
</style>
