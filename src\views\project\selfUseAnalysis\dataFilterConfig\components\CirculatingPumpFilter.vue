<template>
  <div class="circulating-pump-filter">
    <el-form :model="configData" label-width="120px">
      <el-form-item label="计算公式">
        <el-input
          v-model="configData.formula"
          type="textarea"
          :rows="6"
          placeholder="请输入计算公式，例如：#currentCircleWaterFlow*(#planTotalPumpPower/#currentTotalPower)*(1d-Math.log(#planTotalPumpPower/#currentTotalPower))/4)"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { debounce } from 'lodash-es'

interface CirculatingPumpConfig {
  formula: string
}

// 定义props
const props = defineProps<{
  modelValue: CirculatingPumpConfig
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: CirculatingPumpConfig): void
}>()

// 配置数据
const configData = reactive<CirculatingPumpConfig>({
  formula: '',
})

// 标记是否正在更新数据，防止循环触发
const isUpdating = ref(false)

// 初始化数据
const initData = () => {
  if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
    configData.formula = ''
  } else {
    isUpdating.value = true
    // 确保数据类型正确
    configData.formula = props.modelValue.formula || ''

    nextTick(() => {
      isUpdating.value = false
    })
  }
}

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    // 强制更新数据，确保外部数据变化时能正确渲染
    initData()
  },
  { immediate: true, deep: true }
)

// 使用防抖优化性能 - 减少延迟时间
const debouncedEmit = debounce((data: CirculatingPumpConfig) => {
  if (!isUpdating.value) {
    emit('update:modelValue', { ...data })
  }
}, 150) // 从300ms减少到150ms

// 监听数据变化 - 使用防抖
watch(
  () => configData,
  (newVal) => {
    debouncedEmit(newVal)
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.circulating-pump-filter {
  padding: 20px;
  background: rgba(3, 43, 82, 0.3);
  border-radius: 8px;
  margin-top: 20px;
}

:deep(.el-divider__text) {
  color: #fff;
  font-weight: 500;
}

:deep(.el-form-item__label) {
  color: rgba(204, 204, 204, 1);
}

:deep(.el-text) {
  color: rgba(204, 204, 204, 0.8);

  p {
    margin: 0 0 8px 0;
  }

  ul {
    margin: 8px 0;

    li {
      margin-bottom: 4px;
    }
  }
}
</style>
