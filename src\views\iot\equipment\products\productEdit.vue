<template>
  <div class="edit py-[20px]" v-if="state.visible" v-loading="state.loading">
    <div class="ml-[50px] mb-[20px]">
      <el-button type="info" @click="goBack">返回列表</el-button>
    </div>
    <el-tabs :tab-position="tabPosition">
      <el-tab-pane>
        <template #label>
          <span>
            <span class="color-[#FF0000]">*</span>
            <span>基本信息</span>
          </span>
        </template>
        <el-form ref="ruleFormRef" :model="form" :rules="rules" label-width="auto" class="w-[600px]">
          <el-form-item v-if="type != 'create'" label="产品密钥" prop="productSecret">
            <el-input v-model="form.productSecret" :disabled="disabled" />
          </el-form-item>
          <el-form-item label="产品KEY" prop="productKey">
            <el-input v-model="form.productKey" :disabled="disabled" />
          </el-form-item>
          <el-form-item label="产品名称" prop="name">
            <el-input v-model="form.name" :disabled="disabledView" />
          </el-form-item>
          <el-form-item label="产品品类" prop="categoryId">
            <el-select v-model="form.categoryId" :disabled="disabledView">
              <el-option v-for="(item, index) in categoryOptions" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="节点类型" prop="nodeType">
            <el-radio-group v-model="form.nodeType" :disabled="disabledView" @change="changeNodeType">
              <el-radio v-for="(item, index) in nodeTypeOptions" :key="index" :label="item.value">{{ item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="设备协议">
            <el-select v-model="form.protocol" :disabled="disabledView">
              <el-option v-for="item in sys_device_agreement" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属机组" prop="powerUnitIds">
            <el-select v-model="form.powerUnitIds" :disabled="disabledView" multiple>
              <el-option v-for="item in plantUnitData" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="透传设备" prop="transparent">
            <el-radio-group v-model="form.transparent" :disabled="disabledView">
              <el-radio v-for="(item, index) in transparentOptions" :key="index" :label="item.value">{{ item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="保活时长(秒)" prop="keepAliveTime">
            <el-input-number v-model="form.keepAliveTime" :disabled="disabledView" />
          </el-form-item>
          <el-form-item label="设备定位" prop="isOpenLocate">
            <el-radio-group v-model="form.isOpenLocate" :disabled="disabledView">
              <el-radio v-for="(item, index) in isOpenLocateOptions" :key="index" :label="item.value">{{ item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="form.isOpenLocate == true" label="定位方式" prop="isOpenLocate">
            <el-radio-group v-model="form.locateUpdateType" :disabled="disabledView">
              <el-radio v-for="(item, index) in locateUpdateTypeOptions" :key="index" :label="item.value">{{ item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="产品图片">
            <ImageUpload v-model="form.img" :modelValue="form.img" :disabled="disabledView" uploadType="url" />
          </el-form-item>

          <div class="flex justify-center" v-if="type != 'view'">
            <el-button type="primary" @click="submitForm">{{ type == 'create' ? '新增' : '保存' }}</el-button>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="产品配置" :disabled="!disabled">
        <div class="mt-[20px]">
          <el-button type="primary" @click="submitConfigChange()">保存</el-button>
        </div>
        <el-form ref="configForm" class="customform">
          <div v-for="item in allConfigItems" :key="item.value" :label="item.label">
            <span>{{ item.label }}</span>
            <span> {{ item.value }}: </span>>
            <span>
              <el-switch
                :key="item"
                :label="item"
                v-model="productConfigCheck[item.value]"
                @change="changeConfigItemSwitch($event, item)"
              ></el-switch>
            </span>
            <vue-json-editor
              v-if="productConfigCheck[item.value]"
              v-model:json="editConfig[item.value]"
              :showBtns="false"
              :mode="'text'"
              :escapeControlCharacters="true"
              lang="zh"
              height="400"
              @change="changeConfigItem($event, item)"
            />
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane v-if="form.nodeType == 0" label="子设备默认配置" :disabled="!disabled">子设备默认配置</el-tab-pane>
      <el-tab-pane label="物模型" :disabled="!disabled">
        <ThingModel :productInfo="form" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import ImageUpload from '@/components/ImageUpload/index.vue'
import { getCategoriesAll } from '../api/categories.api'
import { saveProducts, editProductConfig, getProductConfig } from '@/views/iot/equipment/api/products.api'
import ThingModel from './modules/thingModel.vue'
import vueJsonEditor from 'vue3-ts-jsoneditor'
import { ComponentInternalInstance, toRaw, onMounted } from 'vue'
import { getpowerUnit } from '@/api/powerUnit/index'
import { useCache, CACHE_KEY,useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'

const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const router = useRouter()
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const type = ref(undefined)

const ruleFormRef = ref()
const disabledView = ref(false)
const disabled = ref(false)
const plantUnitData = ref()
const tabPosition = ref('left')
const { sys_device_agreement } = toRefs<any>(proxy?.useDict('sys_device_agreement'))
const isOpenLocateOptions = [
  {
    value: true,
    label: '开启',
  },
  {
    value: false,
    label: '关闭',
  },
]

const transparentOptions = [
  {
    value: true,
    label: '开启',
  },
  {
    value: false,
    label: '关闭',
  },
]

const nodeTypeOptions = [
  {
    value: 0,
    label: '网关设备',
  },
  {
    value: 1,
    label: '网关子设备',
  },
  {
    value: 2,
    label: '直连设备',
  },
]

const locateUpdateTypeOptions = [
  {
    value: 'manual',
    label: '手动定位',
  },
  {
    value: 'device',
    label: '设备上报',
  },
]


const rules = reactive({
  productKey: [
    { required: true, message: '产品KEY不能为空', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '产品名称不能为空', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '产品品类不能为空', trigger: 'blur' }
  ],
  keepAliveTime: [
    { required: true, message: '保活时长(秒)不能为空', trigger: 'blur' }
  ],
  powerUnitIds: [
    {
      required: true, message: '所属机组不能为空', trigger: 'blur'
    }
  ]
})

const state = reactive({
  loading: false,
  visible: false,
})

const form = ref({
  categoryId: undefined,
  isOpenLocate: false,
  locateUpdateType: 'manual',
  name: undefined,
  nodeType: 0,
  productKey: '',
  transparent: true,
  config: undefined,
  protocol: undefined,
  powerUnitIds: undefined,
  // powerUnitNames: []
})

// const getpowerUnitList=()=>{
//   const projectId=cachedProjects.id
//   getpowerUnit(projectId).then((res)=>{
//     if (res.data.length>0) {
//       plantUnitData.value=res.data.map((item)=>{
//         return{
//           value:item.id,
//           label:item.name
//         }
//       })
//     }
//   })
// }
const getpowerUnitList=()=>{
  const projectId=cachedProjects.id
  getpowerUnit({projectId}).then(
    (res)=>{
      plantUnitData.value = res.data.map((item)=>{
        return{
          value:item.id,
          label:item.name
        }
      })
    }
  )
}
emitter.on('projectListChanged', (e) => {
  location.reload()
})
// onUnmounted(() => {
//   console.log('销毁')
//   emitter.off('projectListChanged')
// })
const configInit = () => {
  getProductConfig(form.value.productKey).then(
    (res) => {
      if (res) {
        editConfig.value = res.data?.config ? JSON.parse(res.data?.config) : {}
        const configItems = res.data?.configItems ? res.data?.configItems : ''
        for (const item of allConfigItems) {
          productConfigCheck.value[item.value] = configItems.includes(item.value)
        }
      }
    }
  )
}

const editConfig = ref({})
const allConfigItems = [{ value: 'basic', label: '基础配置' }, { value: 'interface', label: '接口配置' },
{ value: 'model', label: '物模型配置' }, { value: 'subBasic', label: '子设备基础配置' },
{ value: 'subInterface', label: '子设备接口配置' }, { value: 'subModel', label: '子设备物模型配置' }]

const productConfigCheck = ref({})

const changeConfigItem = (e, item) => {
  if (e.text) {
    editConfig.value[item.value] = JSON.parse(e.text)
  } else {
    editConfig.value[item.value] = null
  }
}

const changeConfigItemSwitch = (val, item) => {
}

const submitConfigChange = () => {
  let items = Object.entries(productConfigCheck.value).filter(e => e[1] == true).map(en => en[0]).join(',')

  editProductConfig({ productKey: form.value.productKey, config: editConfig.value ? JSON.stringify(editConfig.value) : '', configItems: items ? items : '' })
    .then((res) => {
      ElMessage.success('编辑成功')
    })
    .finally(() => {

    })
}


const keyMode = ref(import.meta.env.VITE_PRODUCT_KEY_MODE)

const categoryOptions = ref([])


const getDict = async () => {
  state.visible = true
  state.loading = true
  await getCategoriesAll().then((res) => {
    res = res || {}
    categoryOptions.value = res.data as [] || []
  })
}

const goBack = () => {
  router.go(-1)
}

const randomString = (len: number): string => {
  len = len || 32
  var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  if (keyMode.value === 'uppercase') {
    $chars = 'ABCDEFGHJKMNPQRSTWXYZ2345678'
  } else if (keyMode.value === 'lowercase') {
    $chars = 'abcdefhijkmnprstwxyz2345678'
  }
  var maxPos = $chars.length
  var pwd = ''
  for (var i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
  }
  // if (data.value.findIndex((f) => f.productKey === pwd) !== -1) {
  //   return randomString(len)
  // }
  return pwd
}

const submitForm = async () => {
  await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      onSave()
    } else {
      console.log('error submit!', fields)
    }
  })
}

const onSave = () => {
  // console.log(form.value, 'form.value')

  const { id: projectId } = cachedProjects
  const project = {
    ...form.value,
    powerUnitIds: form.value.powerUnitIds.join(','),
    projectId,
  }

  saveProducts(toRaw(project))
    .then((res) => {
      ElMessage.success(type.value === 'create' ? '添加成功' : '编辑成功')
    })
    .finally(() => {

    })
}

const changeNodeType = (e) => {
  console.log(e)
}

const initData = async () => {
  const formType = window.history.state.type
  if (formType == 'create') {
    form.value.productKey = randomString(16)
  } else {
    form.value = window.history.state.data
  }
  configInit()
  await getDict()
  getpowerUnitList()
  type.value = formType
  disabledView.value = formType == 'view' ? true : false
  disabled.value = formType != 'create' ? true : false
  state.loading = false
}

onMounted(async () => {
  await initData(),getpowerUnitList()
})

console.log(window.history.state)
</script>

<style scoped>
:deep(.el-select__wrapper){
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
.c {
  color: red;
}
.edit{
  background: rgba(2, 28, 51, 0.5);
  box-shadow:inset 0px 2px 28px  rgba(33, 148, 255, 0.5);

}
:deep(.el-tabs__item){
  color: #a1a0a0 !important;
}
:deep(.el-select__tags .el-tag--info){
  background-color:#153059 !important;
}
:deep(.el-input-number__decrease){
  background: #051933 !important;
  color: #fff !important;
}
:deep(.el-input-number__increase){
  background: #051933 !important;
  color: #fff !important;
}
.customform{
  color: #ddd;
}
:deep(.el-tabs--border-card){
  background: transparent !important;
}
:deep(.el-tabs__item:hover) {
  color: #fff; /* 悬停时的文字颜色 -------------*/
}
:deep(.el-tabs--border-card > .el-tabs__header) {
  background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important;
  border-bottom: 1px solid rgba(33, 148, 255, 1);
}
:deep(.el-tabs--border-card) {
  border: none;
}
:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {
  border-radius: 2px;
  background: linear-gradient(180deg, rgb(6, 101, 243) 0%, rgba(119, 122, 126, 0.1) 100%);
  opacity: 0.8;
  color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(0, 0, 0, 1);
}
</style>
