<template>
  <yt-crud
    ref="crudRef"
    :data="data"
    :column="column"
    v-model:page="state.page"
    v-model:query="state.query"
    :total="state.total"
    :loading="state.loading"
    @onLoad="getData"
    :fun-props="{
        addBtn: false,
      }"
    :table-props="{
        editBtn: false,
        delBtn: false,
        dialogBtn:false,
        menuSlot: true,//自定义操作按钮
      }"
  >
    <template #status="{ row }">
      <el-switch v-model="row.status" disabled />
    </template>
    <template #menuSlot="scope">
      <el-tooltip v-if="scope.row.messageType === 1" class="box-item" effect="dark" content="方案下载" placement="top">
        <el-button link type="primary" icon="Download" @click="downloadPlan(scope.row)" />
      </el-tooltip>
    </template>
  </yt-crud>
</template>

<script lang="ts" setup>
import { IColumn } from '@/components/common/types/tableCommon'

import YtCrud from '@/components/common/yt-crud.vue'
import { getMsgs,INotifyMessagesVO } from '../channel/api/configs.api'
import { ComponentInternalInstance } from 'vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance

const data = ref<INotifyMessagesVO[]>([])
const column: IColumn[] = [
  {
    label:'标题',
    key:'title',
    tableWidth: 180,
  },
  {
  label: '消息类型',
  key: 'messageType',
  search: true,
  type: 'select',
  componentProps: {
    options: [{
          label: '告警',
          value: 0,
        },
        {
          label: '方案',
          value: 1,
    }],
  }
}, {
  label: '消息内容',
  key: 'content',
  type: 'string',
  componentProps: {
    type: 'textarea',
    rows: 4,
  }
}, {
  label:'是否已读',
  key:'readFlag',
  tableWidth: 180,
  search: true,
  type: 'select',
  rules: [{ required: true, message: '请选择是否已读' }],
  componentProps: {
    options: [{
          label: '未读',
          value: 0,
        },
        {
          label: '已读',
          value: 1,
        }],
  }
}, {
  label: '创建时间',
  key: 'createTime',
  type: 'date',
},{
  label: '读取时间',
  key: 'readTime',
  type: 'date',
  hide: true,
}]
const state = reactive({
  total: 0,
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  query: {},
  loading: false
})
// 下载方案
const downloadPlan = (row) => {
  proxy?.$download.oss(row.dataId)

}
const getData = () => {
  state.loading = true
  getMsgs({
    ...state.page,
    ...state.query,
  }).then(res => {
    data.value = res.data.rows || []
    state.total = res.data.total
  }).finally(() => {
    state.loading = false
  })
}
</script>

<style lang="scss" scoped>
:deep(.el-select__wrapper){
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
</style>
