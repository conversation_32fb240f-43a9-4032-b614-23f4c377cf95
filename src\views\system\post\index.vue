<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="70">
          <el-form-item label="岗位编码" prop="postCode">
            <el-input v-model="queryParams.postCode" placeholder="请输入岗位编码" clearable style="width: 200px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="岗位名称" prop="postName">
            <el-input v-model="queryParams.postName" placeholder="请输入岗位名称" clearable style="width: 200px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="岗位状态" clearable style="width: 200px">
              <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:post:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:post:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:post:remove']">
              删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:post:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
        </el-row>
      </template>

      <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="岗位编号" align="center" prop="id" v-if="false" />
        <el-table-column label="岗位编码" align="center" prop="postCode" />
        <el-table-column label="岗位名称" align="center" prop="postName" />
        <el-table-column label="岗位排序" align="center" prop="postSort" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:post:edit']" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:post:remove']" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改岗位对话框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="500px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="dialog.visible" ref="postFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="岗位名称" prop="postName">
          <el-input v-model="form.postName" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="岗位编码" prop="postCode">
          <el-input v-model="form.postCode" placeholder="请输入编码名称" />
        </el-form-item>
        <el-form-item label="岗位顺序" prop="postSort">
          <el-input-number v-model="form.postSort" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="岗位状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Post" lang="ts">
import { listPost, addPost, delPost, getPost, updatePost } from '@/api/system/post'
import { PostForm, PostQuery, PostVO } from '@/api/system/post/types'
import { FormInstance } from 'element-plus'
import { ComponentInternalInstance } from 'vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'))

const postList = ref<PostVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<number | string>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const postFormRef = ref<FormInstance>()
const queryFormRef = ref<FormInstance>()

const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
})

const initFormData: PostForm = {
  id: undefined,
  postCode: '',
  postName: '',
  postSort: 0,
  status: '0',
  remark: '',
}

const data = reactive<PageData<PostForm, PostQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    postCode: '',
    postName: '',
    status: '',
  },
  rules: {
    postName: [{ required: true, message: '岗位名称不能为空', trigger: 'blur' }],
    postCode: [{ required: true, message: '岗位编码不能为空', trigger: 'blur' }],
    postSort: [{ required: true, message: '岗位顺序不能为空', trigger: 'blur' }],
  },
})

const { queryParams, form, rules } = toRefs<PageData<PostForm, PostQuery>>(data)

/** 查询岗位列表 */
const getList = async () => {
  loading.value = true
  const res: any = await listPost(queryParams.value)
  postList.value = res.data.rows
  total.value = res.total
  loading.value = false
}
/** 取消按钮 */
const cancel = () => {
  reset()
  dialog.visible = false
}
/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData }
  postFormRef.value?.resetFields()
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
/** 多选框选中数据 */
const handleSelectionChange = (selection: PostVO[]) => {
  ids.value = selection.map((item) => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}
/** 新增按钮操作 */
const handleAdd = () => {
  dialog.visible = true
  dialog.title = '添加岗位'
  nextTick(() => {
    reset()
  })
}
/** 修改按钮操作 */
const handleUpdate = (row?: PostVO) => {
  dialog.visible = true
  dialog.title = '修改岗位'
  nextTick(async () => {
    reset()
    const postId = row?.id || ids.value[0]
    const res = await getPost(postId)
    form.value = res.data
  })
}
/** 提交按钮 */
const submitForm = () => {
  postFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      form.value.id ? await updatePost(form.value) : await addPost(form.value)
      proxy?.$modal.msgSuccess('操作成功')
      dialog.visible = false
      getList()
    }
  })
}
/** 删除按钮操作 */
const handleDelete = async (row?: PostVO) => {
  const postIds = row ? [row.id] : ids.value
  await proxy?.$modal.confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
  await delPost(postIds)
  getList()
  proxy?.$modal.msgSuccess('删除成功')
}
/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/post/export',
    {
      ...queryParams.value,
    },
    `post_${new Date().getTime()}.xlsx`
  )
}

onMounted(() => {
  getList()
})
</script>
<style scoped>
:deep(.el-card){
    background: rgba(2, 28, 51, 0.5);
    /* box-shadow:inset 0px 2px 28px  rgba(33, 148, 255, 0.5); */
    border:none;
}
:deep(.el-card__body){
    border: none;
}
:deep(.el-table, .el-table__expanded-cell ){
    background-color: transparent !important;
  }
:deep(.el-table__body tr, .el-table__body td) {
    padding: 0;
    height: 40px;
  }
:deep(.el-table tr) {
    border: none;
    background-color: transparent;
  }
:deep(.el-table th) {
    /* background-color: transparent; */
    background-color: rgba(7, 53, 92, 1);
    color: rgba(204, 204, 204, 1) !important;
    font-size: 14px;
    font-weight: 400;
  }
:deep(.el-table){
    --el-table-border-color: none;
  }
  /*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
:deep(.el-table__body-wrapper .el-table__row){
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
:deep(.el-table__body-wrapper .el-table__row:hover td ){
    background: none !important;
    /* 取消单元格背景色，确保整行背景色生效 */
  }
:deep(.el-table__header thead tr th) {
    background: rgba(7, 53, 92, 1) !important;
    color: #ffffff;
  }
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
    color: #fff;
  }
:deep(.el-tree){
    background-color: transparent;
  }
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content){
    background-color:   #07355c;
  }
  :deep(.el-tree-node__expand-icon){
    color: #fff;
  }
  :deep(.el-tree-node__label){
    color: #fff;
  }
  :deep(.el-tree-node__content) {
    &:hover {
      background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    }
  }
:deep(.el-select__tags .el-tag--info){
    background-color:#153059 !important;
}
:deep(.el-tag.el-tag--info){
  color: #fff !important;
}
:deep(.el-select__wrapper){

color: #fff!important;
background: rgb(3, 43, 82) !important;
box-shadow:0 0 0 0px #034374 inset !important;
border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
color: #fff;
}
</style>
