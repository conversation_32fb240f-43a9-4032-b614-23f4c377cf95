export interface ThirdPartyConfigVO {
  /**
   * id
   */
  id: string | number

  /**
   * 产品名称
   */
  productName: string

  /**
   * 产品key
   */
  productKey: string

  /**
   * 0:传感器厂家-weilian
   */
  thirdPartyType: number

  /**
   * 请求地址
   */
  url: string

  /**
   * 请求体
   */
  body: string

  /**
   * 描述
   */
  description: string

  /**
   * 创建时间
   */
  createTime: number

  /**
   * 项目id
   */
  projectId: string | number
}

export interface ThirdPartyConfigForm {
  /**
   * id
   */
  id?: string | number

  /**
   * 项目id
   */
  projectId: string | number

  /**
   * 产品key
   */
  productKey: string

  /**
   * 0:传感器厂家-weilian
   */
  thirdPartyType?: number

  /**
   * 请求地址
   */
  url: string

  /**
   * 请求体
   */
  body?: string

  /**
   * 描述
   */
  description?: string
}

export interface ThirdPartyConfigQuery extends PageQuery {
  projectId?: number
  productKeyLike?: string
}
