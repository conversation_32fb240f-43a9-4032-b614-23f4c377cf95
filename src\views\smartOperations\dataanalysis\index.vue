<template>
  <div class="dataanalysis">
    <!-- 时间选择 -->
    <div class="time-control">
      <!-- 选择时间 -->
      <div class="time-control__select">
        <span class="time-control__label">选择时间</span>
        <el-date-picker
          v-model="selectedDate"
          type="datetime"
          placeholder="请选择时间"
          :disabled-date="disabledFutureDates"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="onSelectedTimeChange"
        />
      </div>
      <!-- 自动生成的开始时间 -->
      <div class="time-control__select">
        <span class="time-control__label">开始时间</span>
        <el-date-picker v-model="startTime" placeholder="开始时间自动生成" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" disabled />
      </div>
      <!-- 自动生成的结束时间 -->
      <div class="time-control__select">
        <span class="time-control__label">结束时间</span>
        <el-date-picker v-model="endTime" placeholder="结束时间自动生成" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" disabled />
      </div>
    </div>

    <!-- Tab切换 -->
    <el-tabs v-model="activeName" type="border-card" class="demo-tabs" @tab-click="handleTabsClick">
      <el-tab-pane v-for="tab in waterTabData" :key="tab.name" :label="tab.label" :name="tab.name"></el-tab-pane>
    </el-tabs>

    <div class="tab-content">
      <!-- 智能诊断 -->
      <div class="tab-content_diagnosis" v-if="activeName === 'diagnosis'">
        <div v-for="(section, index) in diagnosisData" :key="index" class="diagnosis-box">
          <h1>{{ section.mainType }}</h1>
          <div v-for="(item, itemIndex) in section.analysisList" :key="item.id" class="sub-content-container">
            <h2>
              <span class="pseudo-block"></span>
              {{ item.label }}--{{ item.title }}
            </h2>
            <div class="content-container">
              <div class="content-row">
                <div class="content-box">
                  <div class="content-name">诊断结果</div>
                  <div class="content-value">{{ item.leftContent }}</div>
                </div>
                <div class="content-box">
                  <div class="content-name">当前值</div>
                  <div class="content-value">
                    {{ item.centerContent === null ? "--" : item.centerContent }}{{  item.centerContent===null ? "" : item.unit }}
                  </div>
                </div>
                <div class="content-box">
                  <div class="content-name">合理区间</div>
                  <div class="content-value">
                    {{ item.rightContent === "null" ? "--" : item.rightContent }}
                  </div>
                </div>
              </div>
              <div class="suggestion-row">
                <!-- <h2>
                  <span class="pseudo-block"></span>
                  分析及建议
                </h2> -->
                <span class="analysis-title">分析及建议</span>
                <div class="suggestion-content">
                  <el-table :data="item.reportList" style="margin: 10px; width: 100%;">
                    <el-table-column label="原因分析">
                      <template #default="scope">
                        <div>
                          <div v-for="(reason, index) in scope.row.reason" :key="index">
                            {{ reason }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="结论">
                      <template #default="scope">
                        <div>
                          <div v-for="(conclusion, index) in scope.row.conclusion" :key="index">
                            {{ conclusion }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="建议">
                      <template #default="scope">
                        <div>
                          <div v-for="(suggestion, index) in scope.row.suggestion" :key="index">
                            {{ suggestion }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
            <analysis-echarts
              :chart-data="item.chart"
              :show-legend="true"
              :id="`chart-${index}-${itemIndex}`"
              axis-tick-color="#ff0000"
              axis-label-color="#00ff00"
            ></analysis-echarts>
          </div>
        </div>
      </div>

      <!-- 关键指标分析 -->
      <div class="tab-content_indicators" v-if="activeName === 'indicators'">
        <div class="indicators-container">
          <el-tabs v-model="activeConclusion" type="border-card" class="demo-tabs" @tab-click="handleTabClickConclusion">
            <el-tab-pane v-for="conclusion in tabskeyIndicatorsData" :key="conclusion" :label="conclusion.name" :name="conclusion.label">
              <div class="indicators-table">
                <el-table :data="forecastingTableData.conclusionList" class="indicator-table" @row-click="handleRowClick">
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="currentValue" label="当前值">
                    <template #default="scope">
                      <div class="current-value-container">
                        <span style="padding-right: 7px;">
                          {{ scope.row.dataStatus == null
                          ? "点位缺失"
                          : scope.row.currentValue }}
                        </span>
                        <el-icon v-if="scope.row.dataStatus === '1'">
                          <img :src="high" alt="偏高" />
                        </el-icon>
                        <el-icon v-else-if="scope.row.dataStatus === '3'">
                          <img :src="low" alt="偏低" />
                        </el-icon>
                        <el-icon v-else-if="scope.row.dataStatus === null">
                          <img :src="alarm" alt="告警" />
                        </el-icon>
                        <el-icon v-else-if="scope.row.dataStatus === '2'">
                          <span>&nbsp;</span>
                        </el-icon>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="suggestedValue" label="建议值" />
                  <el-table-column prop="conclusion" label="结论" />
                </el-table>
                <div class="prediction">
                  <!-- 预测的结论和建议 -->
                  <div class="prediction-container">
                    <!-- 结论 -->
                    <div class="conclusion-row">
                      <h4 class="conclusion-title">预测结论</h4>
                      <!-- DeviceIdAndIdentifierdata.value.reason -->
                      <ul
                        style="color: #fdfeff; font-size: 15px; list-style-type: none;"
                        v-for="(item, index) in DeviceIdAndIdentifierdata.reason"
                        :key="index"
                      >
                        <span>{{ index + 1 }}.</span>
                        <TypewriterText :key="index" :text="item"> </TypewriterText>
                        <!-- <li>{{ index+1 }}.{{ item }}</li> -->
                      </ul>
                    </div>
                    <!-- 建议 -->
                    <div class="suggestion-row">
                      <h4 class="suggestion-title">预测建议</h4>
                      <ul
                        style="color: #fdfeff; font-size: 15px; list-style-type: none;"
                        v-for="(item, index) in DeviceIdAndIdentifierdata.recommend"
                        :key="index"
                      >
                        <span>{{ index + 1 }}.</span>
                        <TypewriterText :key="index" :text="item"> </TypewriterText>
                        <!-- <li>{{ index+1 }}.{{ item }}</li> -->
                      </ul>
                    </div>
                  </div>
                  <!-- 预测的图表 -->
                  <div class="prediction-chart">
                    <PublicCharts :seriesData="seriesData" :xAxisData="xAxisData" />
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TypewriterText from '@/components/TypewriterText/TypewriterText.vue'
import { ref, onMounted } from 'vue'
import PublicCharts from '@/components/publicCharts/indexCopy.vue'
import AnalysisEcharts from '@/components/analysisEcharts/index.vue'
import { getAnalysis, getdiagnosisList, getDiffTypeDatas, getinfoByDeviceIdAndIdentifier } from './index.api'
import { useLocalCache, CACHE_KEY } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
import high from '@/assets/icons/svg/high.svg'
import low from '@/assets/icons/svg/low.svg'
import alarm from '@/assets/icons/svg/alarm_1.svg'

const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const keyIndicatorsData = ref([])
const tabskeyIndicatorsData = ref([])
const selectedDate = ref<string | null>(null)
const startTime = ref<string | null>(null)
const endTime = ref<string | null>(null)
const diagnosisData = ref([])
const seriesData = ref<any[]>([])
const xAxisData = ref<string[]>([])
const disabledFutureDates = (time: Date) => {
  const now = new Date()
  return time.getTime() > now.getTime()
}
const activeConclusion = ref()
// 点击切换存储
const forecastingTableData = ref([])
//点击切换
const handleTabClickConclusion = (tab: any, event: any) => {
  forecastingTableData.value = keyIndicatorsData.value.find((item: any) => item.powerUnitId === tab.props.name)
  const params = {
    deviceId: forecastingTableData.value.conclusionList[0].deviceId,
    identifier: forecastingTableData.value.conclusionList[0].identifier,
  }
  getDiffTypeDatasecharts(params)
}
// 点击表格当前行
const handleRowClick = (row: any) => {
  const params = {
    deviceId: row.deviceId,
    identifier: row.identifier,
  }
  getDiffTypeDatasecharts(params)
}
function transformHisData(his: any[], name: string, unit: string) {
  // xAxisData.value = his.map((item) => item.time)
  xAxisData.value = his.map((item) => item.time.replace(/^\d{4}-/, ''))
  const seriesName = `${name} ${unit ? unit : ''}`.trim()
  // const stackName = 'Total'
  seriesData.value = [
    {
      name: seriesName,
      type: 'line',
      // smooth: true,
      // stack: stackName, // 添加堆叠属性
      // areaStyle: {}, // 添加面积样式属性
      data: his.map((item) => item.value),
    },
    {
      name: `${name}预测值`,
      type: 'line',
      // smooth: true,
      // stack: stackName, // 添加堆叠属性
      // areaStyle: {}, // 添加面积样式属性
      data: his.map((item) => item.predicate),
    },
  ]
}
const onSelectedTimeChange = (val: string) => {
  if (!val) return
  const dateTime = new Date(val)
  const year = dateTime.getFullYear()
  const month = dateTime.getMonth()
  const date = dateTime.getDate()
  const hour = dateTime.getHours()
  const minute = dateTime.getMinutes()

  let startMinute = 0
  let endMinute = 30
  let endSecond = 0

  if (minute < 30) {
    startMinute = 0
    endMinute = 30
    endSecond = 0
  } else {
    startMinute = 30
    endMinute = 59
    endSecond = 59
  }

  startTime.value = `${year}-${String(month + 1).padStart(2, '0')}-${String(date).padStart(2, '0')} ${String(hour).padStart(2, '0')}:${String(
    startMinute
  ).padStart(2, '0')}:00`
  endTime.value = `${year}-${String(month + 1).padStart(2, '0')}-${String(date).padStart(2, '0')} ${String(hour).padStart(2, '0')}:${String(
    endMinute
  ).padStart(2, '0')}:${String(endSecond).padStart(2, '0')}`

  if (activeName.value === 'indicators') {
    keyIndicatorsList()
  } else {
    IntelligentDiagnosis()
  }
}

emitter.on('projectListChanged', (e) => {
  location.reload()
})

const activeName = ref<string>('diagnosis')
const waterTabData = ref([
  { name: 'diagnosis', label: '智能诊断' },
  { name: 'indicators', label: '关键指标预测分析' },
])

const handleTabsClick = (tab: any, event: any) => {
  if (tab.props.name === 'indicators') {
    // 调用关键指标分析
    keyIndicatorsList()
  } else {
    // 调用智能诊断
    IntelligentDiagnosis()
  }
}

const keyIndicatorsList = () => {
  const params = {
    start: startTime.value,
    end: endTime.value,
    configType: 7,
    projectId: cachedProjects.id,
  }
  getAnalysis(params).then((res) => {
    if (res.data.length > 0) {
      keyIndicatorsData.value = res.data
      tabskeyIndicatorsData.value = res.data.map((item: any) => {
        return {
          name: item.powerUnitName,
          label: item.powerUnitId,
        }
      })
      activeConclusion.value = tabskeyIndicatorsData.value[0].label
      forecastingTableData.value = keyIndicatorsData.value[0]
      const params = {
        deviceId: forecastingTableData.value.conclusionList[0].deviceId,
        identifier: forecastingTableData.value.conclusionList[0].identifier,
      }
      getDiffTypeDatasecharts(params)
    }
  })
}
const DeviceIdAndIdentifierdata = ref({
  reason: [],
  recommend: [],
})
const getDiffTypeDatasecharts = (data: any) => {
  getDiffTypeDatas(data).then((res) => {
    if (res.code !== 200) {
      return
    }
    if (!Array.isArray(res.data.his) || res.data.his.length === 0) {
      return
    }
    transformHisData(res.data.his, res.data.name, res.data.unit)
  })
  const params = {
    ...data,
    dataType: 2,
  }
  getinfoByDeviceIdAndIdentifier(params).then((res) => {
    if (res.code === 200) {
      const reasonlist = res.data.reason || ''
      const recommendlist = res.data.recommend || ''
      DeviceIdAndIdentifierdata.value.reason = reasonlist.includes('<br>') ? reasonlist.split('<br>') : reasonlist ? [reasonlist] : []

      DeviceIdAndIdentifierdata.value.recommend = recommendlist.includes('<br>') ? recommendlist.split('<br>') : recommendlist ? [recommendlist] : []
    }
  })
}
const IntelligentDiagnosis = () => {
  const params = {
    start: startTime.value,
    end: endTime.value,
    configType: 7,
    projectId: cachedProjects.id,
  }
  getdiagnosisList(params).then((res) => {
    if (res.data.length > 0) {
      const processedData = JSON.parse(JSON.stringify(res.data))
      processedData.forEach((mainTypeItem: any) => {
        mainTypeItem.analysisList.forEach((analysisItem: any) => {
          if (analysisItem.reportList === null) {
            analysisItem.reportList = []
            return
          } else {
            analysisItem.reportList.forEach((report: any) => {
              report.reason = JSON.parse(report.reason)
              report.conclusion = JSON.parse(report.conclusion)
              report.suggestion = JSON.parse(report.suggestion)
              if (report.reason != null) {
                report.reason = report.reason.map((item: string, index: number) => `${index + 1}. ${item}`)
              } else {
                report.reason = []
              }
              if (report.conclusion != null) {
                report.conclusion = report.conclusion.map((item: string, index: number) => `${index + 1}. ${item}`)
              } else {
                report.conclusion = []
              }
              if (report.suggestion != null) {
                report.suggestion = report.suggestion.map((item: string, index: number) => `${index + 1}. ${item}`)
              } else {
                report.suggestion = []
              }
            })
          }
        })
      })

      diagnosisData.value = processedData
    }
  })
}

// 定时器
const timer = ref<number | null>(null) // 定时器ID

const startTimer = () => {
  // 每10秒调用一次
  timer.value = window.setInterval(() => {
    if (activeName.value === 'indicators') {
      // keyIndicatorsList()
    } else if (activeName.value === 'diagnosis') {
      IntelligentDiagnosis()
    }
  }, 10000) // 10000毫秒 = 10秒
}

const stopTimer = () => {
  if (timer.value !== null) {
    clearInterval(timer.value)
    timer.value = null
  }
}
onUnmounted(() => {
  stopTimer() // 清除定时器
})

onMounted(() => {
  const now = new Date()
  selectedDate.value = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(
    now.getHours()
  ).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`
  onSelectedTimeChange(selectedDate.value!)

  if (waterTabData.value.length > 0) {
    activeName.value = waterTabData.value[0].name
    IntelligentDiagnosis()
  }
  startTimer() // 启动定时器
})
</script>

<style scoped lang="scss">
:deep(.conclusion-row ul) {
  padding-left: 0; /* 去掉默认的左边距 */
  margin: 0; /* 去掉默认的外边距 */
  list-style-type: none; /* 去掉默认的项目符号 */
  overflow: hidden; /* 隐藏滚动条 */
}

:deep(.conclusion-row li) {
  padding-left: 0; /* 确保每个 li 紧贴左边 */
}

:deep(.conclusion-row .prediction-container) {
  overflow: hidden; /* 确保父容器不出现滚动条 */
}
/* 深层选择器和表格样式 */
:deep(.el-table__row > :nth-child(2) > .cell) {
  color: #eab50a;
}
:deep(.el-table__row > :nth-child(3) > .cell) {
  color: #eab50a;
}
:deep(.el-table__row > :nth-child(4) > .cell) {
  color: #eab50a;
}
:deep(.el-table__inner-wrapper) {
  width: 100% !important;
}
:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}
:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table) {
  --el-table-border-color: none;
}
:deep(.el-table__cell) {
  color: #fff;
}
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background: #07355c !important;
}
:deep(.el-table__body-wrapper .el-table__row:nth-child(odd)) {
  background-color: #15334c !important;
  font-size: 16px !important;
}
:deep(.el-table__body-wrapper .el-table__row) {
}
:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}
:deep(.el-table__header thead tr th) {
  color: #b08805;
}
:deep(.el-table .cell) {
  line-height: 45px;
}

:deep(.el-tabs__item:hover) {
  color: #fff;
}


:deep(.el-tabs--border-card) {
  background: rgba(2, 28, 51, 0.5);

}

:deep(.el-tabs--border-card > .el-tabs__header) {
  background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important;
  border-bottom: 1px solid rgba(33, 148, 255, 1);
}

:deep(.el-tabs--border-card) {
  border: none;
}

:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {
  border-radius: 2px;
  background: linear-gradient(180deg, rgb(6, 101, 243) 0%, rgba(119, 122, 126, 0.1) 100%);
  opacity: 0.8;
  color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(0, 0, 0, 1);
}
:deep(.el-tabs--border-card > .el-tabs__content) {
  padding: 5px !important;
}

/* 父容器样式 */
.dataanalysis {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);

  .time-control {
    height: 70px;
    margin: 16px 0;
    display: flex;
    flex-direction: row;
    gap: 8px;

    .time-control__select {
      line-height: 70px;
    }

    .time-control__label {
      color: #fff;
      margin: 0 8px 0 25px;
    }
  }

  .tab-content {
    padding: 0 15px;

    /* 智能诊断部分样式保持不变 */
    .tab-content_diagnosis {
      .diagnosis-box {
        margin-bottom: 20px;
        border-radius: 8px;

        h1 {
          font-size: 24px;
          color: #12cefb;
          display: inline-block;
          margin-bottom: 10px;
          border-radius: 5px;
          padding: 5px;
        }

        .sub-content-container {
          padding: 10px;
          border-radius: 5px;

          h2 {
            font-size: 18px;
            color: #0fd2d3;
            display: inline-block;
            position: relative;
            padding-left: 8px;

            .pseudo-block {
              position: absolute;
              left: 0;
              top: 50%;
              width: 2px;
              height: 20px;
              background-color: #07a1e9;
              transform: translateY(-50%);
            }
          }

          .content-container {
            margin-top: 10px;
          }

          .content-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 10px;
            border-radius: 5px;

            .content-box {
              flex: 1;
              padding: 16px;
              margin: 0 10px;
              border-radius: 5px;
              text-align: justify;
              background: #103862;
              color: #fff;
              display: flex;
              justify-content: space-between;
              .content-value {
                font-size: 22px;
              }
              .content-name {
                color: #ccc;
              }
            }
          }

          .suggestion-row {
            margin-top: 10px;
            padding: 10px 10px;

            .analysis-title {
              position: relative;
              font-size: 16px;
              font-weight: bold;
              margin-bottom: 10px;
              display: block;
              color: burlywood;
              padding-left: 8px;
              margin-left: 10px;

              &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 2px;
                height: 100%;
                background-color: rgb(219, 133, 21);
              }
            }

            .suggestion-content {
              width: 100%;
              max-height: 200px; /* 限制最大高度 */

              ::-webkit-scrollbar {
                width: 0px; /* 隐藏滚动条 */
                height: 0px;
              }

              ::-webkit-scrollbar-thumb {
                background-color: transparent; /* 设置滚动条为透明 */
              }
            }
          }
        }
      }
    }

    /* 关键指标分析部分样式 */
    .tab-content_indicators {
      .indicators-container {
        width: 100%;
        .demo-tabs {
          width: 100%;
          .indicators-table {
            display: flex;
            .indicator-table {
              width: 50%;
            }
            .prediction {
              width: 50%;
              .prediction-container {
                height: 230px;
                width: 100%;
                display: flex;
                justify-content: center;

                .conclusion-row,
                .suggestion-row {
                  width: 50%;
                  height: 230px;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  overflow-y: auto; /* 使内容可以垂直滚动 */

                  ::-webkit-scrollbar {
                    width: 0px; /* 隐藏滚动条 */
                    height: 0px;
                  }

                  ::-webkit-scrollbar-thumb {
                    background-color: transparent; /* 设置滚动条为透明 */
                  }
                }

                .conclusion-row {
                  text-align: center;
                  .conclusion-title {
                    font-size: 16px;
                    font-weight: bold;
                    color: burlywood;
                    margin-bottom: 10px;
                  }
                }

                .suggestion-row {
                  text-align: center;
                  .suggestion-title {
                    font-size: 16px;
                    font-weight: bold;
                    color: burlywood;
                    margin-bottom: 10px;
                  }
                }
              }

              .prediction-chart {
                height: 400px;
                padding: 20px;
              }
            }
          }
        }

        @media (max-width: 800px) {
          .indicators-container {
            grid-template-columns: repeat(2, 1fr); /* 保持两列布局 */
          }
        }

        @media (max-width: 600px) {
          .indicators-container {
            grid-template-columns: 1fr; /* 单列布局 */
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
// @import '@/assets/styles/ctable.scss';
/* 全局样式 */
@import '@/assets/styles/datapicker.scss';
/* 全局样式 */
</style>
