<template>
  <div class="tableWrap">
    <el-button @click="addLedger" type="primary" style="margin-bottom: 5px;">新增</el-button>
    <el-table
      :data="tableData"
      border
      v-loading="loading"
      element-loading-text="数据查询中"
      :span-method="spanMethod"
      height="620"
      size="small"
      style="width: 100%; table-layout: fixed;"
    >
      <el-table-column label="盘点日期" prop="date" width="150" align="center" />
      <el-table-column label="药剂名称" prop="name" width="180" align="center" />
      <el-table-column label="规格" prop="spec" width="120" align="center" />
      <el-table-column label="单位" prop="unit" width="80" align="center" />
      <el-table-column label="入库日期" prop="inDate" width="150" align="center" />
      <el-table-column label="入库数量" prop="inAmount" width="120" align="center" />
      <el-table-column label="库存数量" prop="stockAmount" width="120" align="center" />
      <el-table-column label="月度用量" prop="monthlyAmount" width="120" align="center" />
      <el-table-column label="备注" prop="remark" width="200" align="center" />
    </el-table>
    <!-- <RichTextEditor v-model="editorContent" v-bind="editorProps" />  -->
  </div>
</template>

<script lang="ts" setup>
import RichTextEditor from '@/components/Editor/index.vue'
const tableData = ref([
  { date: '2024/9/30', name: 'HJ-612非氧化性杀菌剂', spec: '25kg/桶', unit: '桶', inDate: '2024/9/23', inAmount: 48, stockAmount: 30, monthlyAmount: 18, remark: '' },
  { date: '2024/9/30', name: 'HJ-622盐泥剖高剂', spec: '25kg/桶', unit: '桶', inDate: '2024/8/28', inAmount: 6, stockAmount: 36, monthlyAmount: 0, remark: '' },
  { date: '2024/9/30', name: 'HJ-852阻垢剂', spec: '25kg/桶', unit: '桶', inDate: '2024/9/18', inAmount: 24, stockAmount: 12, monthlyAmount: 0, remark: '' },
  { date: '2024/10/31', name: 'HJ-612非氧化性杀菌剂', spec: '25kg/桶', unit: '桶', inDate: '2024/10/14', inAmount: 200, stockAmount: 130, monthlyAmount: 137, remark: '' },
  { date: '2024/10/31', name: 'HJ-622盐泥剖高剂', spec: '25kg/桶', unit: '桶', inDate: '2024/10/14', inAmount: 100, stockAmount: 12, monthlyAmount: 24, remark: '' },
  { date: '2024/11/29', name: 'HJ-612非氧化性杀菌剂', spec: '25kg/桶', unit: '桶', inDate: '2024/11/25', inAmount: 40, stockAmount: 24, monthlyAmount: 12, remark: '' },
  { date: '2024/12/31', name: 'HJ-612非氧化性杀菌剂', spec: '25kg/桶', unit: '桶', inDate: '2024/12/30', inAmount: 280, stockAmount: 314, monthlyAmount: 145, remark: '' },
  { date: '2024/12/31', name: 'HJ-622盐泥剖高剂', spec: '25kg/桶', unit: '桶', inDate: '2024/12/30', inAmount: 50, stockAmount: 66, monthlyAmount: 28, remark: '' },
])
// 当前行 row、当前列 column、当前行号 rowIndex、当前列号 columnIndex
tableData.value.sort((a, b) => a.name.localeCompare(b.name))

const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 1) {
    if (rowIndex === 0 || row.name !== tableData.value[rowIndex - 1].name) {
      const rowCount = tableData.value.filter(i => i.name === row.name).length
      return { rowspan: rowCount, colspan: 1 }
    }
    return { rowspan: 0, colspan: 0 }
  }
  return { rowspan: 1, colspan: 1 }
}



const editorContent = ref('')
const editorProps = {
  height: 400, // 设置编辑器的高度
  minHeight: 200, // 设置最小高度
  readOnly: false, // 设置是否只读
  fileSize: 5, // 设置文件大小限制 (MB)
}

// 新增台账
const addLedger = () => {

}
</script>

<style scoped></style>
