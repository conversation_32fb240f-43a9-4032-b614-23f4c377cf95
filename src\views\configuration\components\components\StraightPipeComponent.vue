<template>
  <div 
    class="straight-pipe-component"
    :style="componentStyle"
    @click="handleClick"
    @contextmenu="handleContextMenu"
  >
    <!-- 3D管道容器 -->
    <div class="pipe-container" ref="pipeContainer">
      <!-- 管道主体 -->
      <div class="pipe-body" :style="pipeBodyStyle">
        <!-- 管道内壁 -->
        <div class="pipe-inner" :style="pipeInnerStyle"></div>
        
        <!-- 流体流动效果 -->
        <div 
          v-if="showFlow" 
          class="flow-animation"
          :style="flowAnimationStyle"
        >
          <div 
            v-for="i in flowParticles" 
            :key="i" 
            class="flow-particle"
            :style="getParticleStyle(i)"
          ></div>
        </div>
        
        <!-- 管道高光效果 -->
        <div class="pipe-highlight" :style="highlightStyle"></div>
        
        <!-- 管道阴影效果 -->
        <div class="pipe-shadow" :style="shadowStyle"></div>
      </div>
      
      <!-- 管道端口 -->
      <div class="pipe-port pipe-port-left" :style="leftPortStyle"></div>
      <div class="pipe-port pipe-port-right" :style="rightPortStyle"></div>
    </div>
    
    <!-- 数据显示面板 -->
    <div v-if="showDataPanel" class="data-panel" :style="dataPanelStyle">
      <div class="data-item">
        <span class="data-label">流速:</span>
        <span class="data-value">{{ flowSpeed }} m/s</span>
      </div>
      <div class="data-item">
        <span class="data-label">压力:</span>
        <span class="data-value">{{ pressure }} MPa</span>
      </div>
      <div class="data-item">
        <span class="data-label">流体:</span>
        <span class="data-value">{{ fluidTypeText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
  selected?: boolean
  editing?: boolean
}>()

const emit = defineEmits(['select', 'update', 'contextmenu'])

// 组件引用
const pipeContainer = ref<HTMLElement>()

// 计算属性
const componentStyle = computed(() => ({
  width: '100%',
  height: '100%',
  position: 'relative'
}))

// 管道数据
const pipeData = computed(() => props.component.data?.static || {})
const pipeStyle = computed(() => props.component.style || {})
const fluidType = computed(() => pipeData.value.fluidType || 'water')
const flowDirection = computed(() => pipeData.value.flowDirection || 'right')
const flowSpeed = computed(() => pipeData.value.flowSpeed || 2.5)
const pressure = computed(() => pipeData.value.pressure || 0.6)
const temperature = computed(() => pipeData.value.temperature || 25)

// 显示控制
const showFlow = computed(() => flowSpeed.value > 0)
const showDataPanel = computed(() => props.selected || props.editing)

// 流体类型文本
const fluidTypeText = computed(() => {
  const types = {
    water: '水',
    steam: '蒸汽',
    oil: '油',
    gas: '气体'
  }
  return types[fluidType.value as keyof typeof types] || '未知'
})

// 管道样式
const pipeBodyStyle = computed(() => {
  const isVertical = props.component.height > props.component.width
  const diameter = Math.min(props.component.width, props.component.height) * 0.8

  // 确保最小直径
  const minDiameter = 20
  const finalDiameter = Math.max(diameter, minDiameter)

  // 支持自定义管道颜色 - 使用any类型避免类型检查
  const styleAny = pipeStyle.value as any
  const pipeColor = styleAny.pipeColor || '#c0c0c0'
  const pipeDarkColor = styleAny.pipeDarkColor || '#8a8a8a'
  const pipeLightColor = styleAny.pipeLightColor || '#e0e0e0'

  return {
    width: isVertical ? `${finalDiameter}px` : '100%',
    height: isVertical ? '100%' : `${finalDiameter}px`,
    background: styleAny.pipeGradient || `linear-gradient(${isVertical ? '90deg' : '0deg'},
      ${pipeDarkColor} 0%,
      ${pipeColor} 20%,
      ${pipeLightColor} 50%,
      ${pipeColor} 80%,
      ${pipeDarkColor} 100%)`,
    borderRadius: `${finalDiameter / 2}px`,
    position: 'relative',
    margin: 'auto',
    border: styleAny.pipeBorder || '1px solid #666',
    boxShadow: styleAny.pipeShadow || `
      inset 0 ${isVertical ? '0' : '2px'} ${isVertical ? '2px' : '0'} rgba(0,0,0,0.3),
      inset 0 ${isVertical ? '0' : '-2px'} ${isVertical ? '-2px' : '0'} rgba(255,255,255,0.3),
      0 2px 4px rgba(0,0,0,0.2)
    `
  }
})

// 管道内壁样式
const pipeInnerStyle = computed(() => {
  const isVertical = props.component.height > props.component.width
  const innerDiameter = Math.min(props.component.width, props.component.height) * 0.6
  const minInnerDiameter = 16
  const finalInnerDiameter = Math.max(innerDiameter, minInnerDiameter)

  return {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: isVertical ? `${finalInnerDiameter}px` : '85%',
    height: isVertical ? '85%' : `${finalInnerDiameter}px`,
    background: getFluidColor(),
    borderRadius: `${finalInnerDiameter / 2}px`,
    boxShadow: 'inset 0 0 8px rgba(0,0,0,0.3)'
  }
})

// 流体颜色
const getFluidColor = () => {
  const styleAny = pipeStyle.value as any

  // 优先使用自定义流体颜色
  if (styleAny.fluidColor) {
    return styleAny.fluidColor
  }

  // 默认流体颜色
  const colors = {
    water: '#4A90E2',
    steam: '#E8E8E8',
    oil: '#8B4513',
    gas: '#90EE90'
  }
  return colors[fluidType.value as keyof typeof colors] || '#4A90E2'
}

// 流动动画样式
const flowParticles = computed(() => Math.floor(props.component.width / 30))

const flowAnimationStyle = computed(() => ({
  position: 'absolute',
  top: '0',
  left: '0',
  width: '100%',
  height: '100%',
  overflow: 'hidden',
  borderRadius: 'inherit'
}))

// 流动粒子样式
const getParticleStyle = (index: number) => {
  const isVertical = props.component.height > props.component.width
  const size = Math.random() * 4 + 2
  const delay = index * 0.5
  
  return {
    position: 'absolute',
    width: `${size}px`,
    height: `${size}px`,
    background: 'rgba(255,255,255,0.6)',
    borderRadius: '50%',
    top: isVertical ? '0%' : '50%',
    left: isVertical ? '50%' : '0%',
    transform: 'translate(-50%, -50%)',
    animation: `flowAnimation${isVertical ? 'Vertical' : 'Horizontal'} ${3 / Math.max(flowSpeed.value, 0.5)}s linear infinite`,
    animationDelay: `${delay}s`
  }
}

// 高光效果
const highlightStyle = computed(() => {
  const isVertical = props.component.height > props.component.width
  
  return {
    position: 'absolute',
    top: isVertical ? '0' : '10%',
    left: isVertical ? '10%' : '0',
    width: isVertical ? '80%' : '100%',
    height: isVertical ? '100%' : '30%',
    background: `linear-gradient(${isVertical ? '90deg' : '0deg'}, 
      transparent 0%, 
      rgba(255,255,255,0.4) 50%, 
      transparent 100%)`,
    borderRadius: 'inherit',
    pointerEvents: 'none'
  }
})

// 阴影效果
const shadowStyle = computed(() => {
  const isVertical = props.component.height > props.component.width
  
  return {
    position: 'absolute',
    bottom: isVertical ? '0' : '10%',
    right: isVertical ? '10%' : '0',
    width: isVertical ? '80%' : '100%',
    height: isVertical ? '100%' : '30%',
    background: `linear-gradient(${isVertical ? '90deg' : '0deg'}, 
      transparent 0%, 
      rgba(0,0,0,0.2) 50%, 
      transparent 100%)`,
    borderRadius: 'inherit',
    pointerEvents: 'none'
  }
})

// 端口样式
const leftPortStyle = computed(() => {
  const isVertical = props.component.height > props.component.width
  const portSize = Math.max(Math.min(props.component.width, props.component.height) * 0.25, 8)
  const styleAny = pipeStyle.value as any

  return {
    position: 'absolute',
    width: `${portSize}px`,
    height: `${portSize}px`,
    background: styleAny.portColor || '#666',
    borderRadius: '50%',
    border: styleAny.portBorder || '1px solid #333',
    top: '50%',
    left: isVertical ? '50%' : '2px',
    transform: isVertical ? 'translate(-50%, -50%)' : 'translate(-50%, -50%)',
    zIndex: 10
  }
})

const rightPortStyle = computed(() => {
  const isVertical = props.component.height > props.component.width
  const portSize = Math.max(Math.min(props.component.width, props.component.height) * 0.25, 8)
  const styleAny = pipeStyle.value as any

  return {
    position: 'absolute',
    width: `${portSize}px`,
    height: `${portSize}px`,
    background: styleAny.portColor || '#666',
    borderRadius: '50%',
    border: styleAny.portBorder || '1px solid #333',
    top: isVertical ? 'auto' : '50%',
    bottom: isVertical ? '2px' : 'auto',
    right: isVertical ? '50%' : '2px',
    transform: isVertical ? 'translate(50%, 50%)' : 'translate(50%, -50%)',
    zIndex: 10
  }
})

// 数据面板样式
const dataPanelStyle = computed(() => ({
  position: 'absolute',
  top: '-60px',
  left: '0',
  background: 'rgba(0,0,0,0.8)',
  color: 'white',
  padding: '8px 12px',
  borderRadius: '4px',
  fontSize: '12px',
  whiteSpace: 'nowrap',
  zIndex: 1000,
  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
}))

// 事件处理
const handleClick = (event: MouseEvent) => {
  event.stopPropagation()
  emit('select', props.component.id)
}

const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()
  emit('contextmenu', event, props.component.id)
}

// 生命周期
onMounted(() => {
  // 添加CSS动画
  addFlowAnimations()
})

onUnmounted(() => {
  // 清理动画
  removeFlowAnimations()
})

// 添加流动动画CSS
const addFlowAnimations = () => {
  const style = document.createElement('style')
  style.id = 'pipe-flow-animations'
  
  if (!document.getElementById('pipe-flow-animations')) {
    style.textContent = `
      @keyframes flowAnimationHorizontal {
        0% { left: 0%; opacity: 0; }
        10% { opacity: 1; }
        90% { opacity: 1; }
        100% { left: 100%; opacity: 0; }
      }
      
      @keyframes flowAnimationVertical {
        0% { top: 0%; opacity: 0; }
        10% { opacity: 1; }
        90% { opacity: 1; }
        100% { top: 100%; opacity: 0; }
      }
    `
    document.head.appendChild(style)
  }
}

// 移除动画CSS
const removeFlowAnimations = () => {
  const style = document.getElementById('pipe-flow-animations')
  if (style) {
    style.remove()
  }
}
</script>

<style scoped>
.straight-pipe-component {
  position: relative;
  user-select: none;
}

.pipe-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pipe-body {
  position: relative;
  overflow: hidden;
}

.data-panel {
  font-family: 'Courier New', monospace;
}

.data-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.data-item:last-child {
  margin-bottom: 0;
}

.data-label {
  margin-right: 8px;
  opacity: 0.8;
}

.data-value {
  font-weight: bold;
}

.pipe-port {
  box-shadow: 
    inset 0 2px 4px rgba(0,0,0,0.3),
    0 1px 2px rgba(0,0,0,0.2);
}

.pipe-port:hover {
  background: #777;
}
</style>
