import request from '@/utils/request'

enum Api {
  list = '/medicalDevice/list',
  analysisList = '/medicalDevice/analysisDetails',
  dosing = '/medicalDevice/downSampling/list',
}

// 查询设备点位
export const queryequipmentLocationList = (data) => {
  return request({
    url: Api.list,
    method: 'post',
    data,
  })
}
export const queryPointAnalysis = (data) => {
  return request({
    url: Api.analysisList,
    method: 'post',
    data,
  })
}
export const queryDosing = (data) => {
  return request({
    url: Api.dosing,
    method: 'post',
    data,
  })
}
