<template>
  <div class="water-maintenance">
    <!-- 上 -->
    <div class="water-maintenance-top">
      <div class="water-maintenance-top-left">
        <div class="water-maintenance-top-left-left" v-if="waterMaintenanceData.length > 0">
          <div class="item" v-for="(item, index) in waterMaintenanceData" :key="index" @click="getpointdata(item)">
            <div class="item-title">{{ item.name !== null ? item.name : '--' }}</div>
            <div class="item-value">
              <span class="value">{{ item.value !== null ? item.value : '--' }}</span>
              <span class="unit">{{  item.unit }}</span>
            </div>
          </div>
        </div>
        <div class="water-maintenance-top-left-left-empty" v-else>
          <EmptyData :imageSrc="emptyImagePath" />
        </div>
        <div class="water-maintenance-top-left-right">
          <div class="water-maintenance-top-left-right-img">
            <img :src="waterMaintenanceImg" alt="" />
          </div>
          <div class="water-maintenance-top-left-right-value">
            <div class="equipmentLocation" v-for="(item, index) in equipmentLocationData" :key="index">
              <div class="equipmentLocation-Number">{{ item.order }}</div>
              <div :class="item.status === 1 ? 'equipmentLocation-Name-off' : 'equipmentLocation-Name'" @click="changeDeviceInformation(item, index)">
                {{ item.name }}
              </div>
              <div v-if="selectedIndex === index" style="margin-left: 5px;">
                <img style="width: 30px; height: 30px;" :src="hand" />
              </div>
            </div>
          </div>
          <div class="waterrefresh">
            <el-button style="margin-top: 5px;padding: auto;" type="primary" icon="Refresh" circle @click="refreshData()" />
          </div>
        </div>
      </div>
      <div class="water-maintenance-top-right">
        <div class="water-maintenance-top-right-top" v-if="processList.length > 0">
          <div class="water-maintenance-top-right-top-content">
            <h4>分析过程</h4>
            <ul style="color: #fdfeff; font-size: 15px;" v-for="(item, index) in displayedProcesses" :key="index">
              <li>
                <span>{{ index + 1 }}.</span>
                <TypewriterText :key="item.id" :text="item.process" @done="handleProcessDone">
                  <span style="margin-left: 5px;">
                    <el-icon :color="item.status === 1 ? '#e70f0f' : '#288ae7'">
                      <Select v-if="item.status === 0" />
                      <CloseBold v-else-if="item.status === 1" />
                    </el-icon>
                  </span>
                </TypewriterText>
              </li>
            </ul>
          </div>
          <div class="water-Dependency">
            <h4>依赖指标</h4>
            <ul style="color: #fdfeff; font-size: 15px;" v-for="(item, index) in relationList" :key="index">
              <li>
                <span>{{ index + 1 }}.</span>
                <span>{{ item.name!==null?item.name:'' }}：{{ item.value!==null?item.value:''}}</span>
              </li>
            </ul>
          </div>
        </div>
        <div class="water-maintenance-top-right-top-empty" v-else>
          <!-- <EmptyData :imageSrc="emptyImagePath" /> -->
          <img style="height: 100%;" :src="emptyImagePath" alt="" />
        </div>
        <!-- 分析过程及建议 -->
        <div class="water-maintenance-top-right-bottom">
          <div class="water-maintenance-top-right-bottom-right">
            <span style="color: #2c91ee; text-align: center; display: block; margin-top: 5px;">结论</span>
            <ul
              v-if="allProcessesDone"
              style="color: #fdfeff; font-size: 15px; list-style-type: none;"
              v-for="(item, index) in conclusionList"
              :key="index"
            >
              <li>{{ index + 1 }}.{{ item}}</li>
            </ul>
          </div>
          <div class="water-maintenance-top-right-bottom-left">
            <!-- <h4>结论</h4> -->
            <span style="color: #2c91ee; text-align: center; display: block; margin-top: 5px;">建议</span>
            <ul
              v-if="allProcessesDone"
              style="color: #fdfeff; font-size: 15px; list-style-type: none;"
              v-for="(item, index) in suggestListList"
              :key="index"
            >
              <li>{{ index + 1 }}.{{ item }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!-- 下 -->
    <div class="water-maintenance-bottom">
      <div class="water-maintenance-bottom-left">
        <!-- 图表时间 -->
        <div class="echats-time">
          <el-date-picker
            v-model="waterVal"
            type="datetimerange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            date-format="YYYY/MM/DD ddd"
            time-format="A hh:mm:ss"
            :disabled-date="disabledDate"
            @change="changeTime"
            class="customdatapicker"
            size="large"
          />
        </div>
        <div class="echarts">
          <PublicCharts :seriesData="seriesData" :xAxisData="xAxisData" />
        </div>
      </div>
      <div class="water-maintenance-bottom-right">
        <div class="watertime"><span>24h</span></div>
        <div class="echarts">
          <PublicCharts :seriesData="DosinseriesData" :xAxisData="DosinxAxisData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import TypewriterText from '@/components/TypewriterText/TypewriterText.vue'
import { queryequipmentLocationList, queryPointAnalysis, queryDosing } from './index.api'
import EmptyData from '@/components/emptyData/index.vue'
import emptyImagePath from '@/assets/images/noData.png'
import hand from '@/assets/images/hand3.png'
import waterMaintenanceImg from '@/assets/images/waterder.png'
import { formatDate } from '@/utils/formatTime'
import { getTabsList, getDataList, getechartsData } from '../pEcharts/index.api'
import emitter from '@/utils/eventBus.js'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import PublicCharts from '@/components/publicCharts/indexCopy.vue'
import { el } from 'element-plus/es/locale'
import { log } from 'console'

// 缓存和初始数据
const { wsCache } = useLocalCache()
emitter.on('projectListChanged', (e) => {
  location.reload()
})

const waterVal = ref<[Date, Date]>([
  new Date(new Date().setDate(new Date().getDate() - 7)),
  new Date()
])

const disabledDate = (time: Date) => {
  const oneYearAgo = new Date()
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)
  return time.getTime() > Date.now() || time.getTime() < oneYearAgo.getTime()
}

const cachedProjects = wsCache.get(CACHE_KEY.projectList)

const waterMaintenanceData = ref<any[]>([])
const equipmentLocationData = ref<any[]>([])
const cilwaterValueData = ref<any>()
const xAxisData = ref<string[]>([])
const seriesData = ref<any[]>([])
const DosinxAxisData = ref<string[]>([])
const DosinseriesData = ref<any[]>([])

function transformHisData(his: any[], name: string, unit: string) {
  xAxisData.value = his.map((item) => item.time)
  const seriesName = `${name} ${unit ? unit : ''}`.trim()
  // const stackName = 'Total'
  seriesData.value = [
    {
      name: seriesName,
      type: 'line',
      // smooth: true,
      // stack: stackName, // 添加堆叠属性
      areaStyle: {}, // 添加面积样式属性
      data: his.map((item) => item.value)
    },
    {
      name: '环比',
      type: 'line',
      // smooth: true,
      // stack: stackName, // 添加堆叠属性
      areaStyle: {}, // 添加面积样式属性
      data: his.map((item) => item.hbValue)
    },
    {
      name: '同比',
      type: 'line',
      smooth: true,
      // stack: stackName, // 添加堆叠属性
      // areaStyle: {}, // 添加面积样式属性
      data: his.map((item) => item.tbValue)
    }
  ]
}

const refreshData =() => {
  const params = {
    projectId: cachedProjects.id
  }
  queryequipmentLocationList(params).then((res) => {
    if (res.data.length > 0) {
      // 使用order排序
      equipmentLocationData.value = res.data.sort((a, b) => a.order - b.order)
    } else {
      equipmentLocationData.value = []
    }
  })
}
const getwaterMaintenanceData = () => {
  getTabsList(cachedProjects.id).then((res) => {
    if (res.data.length > 0) {
      // 筛选出model为TempHumidityWaterSensor的设备
      const tempHumidityWaterSensor = res.data.filter(
        (item) => item.model === 'TempHumidityWaterSensor'
      )
      tempHumidityWaterSensor.forEach((item) => {
        const params = {
          deviceId: item.deviceId,
          pointType: 0
        }
        getDataList(params).then((res) => {
          if (res.data.datas.length > 0) {
            waterMaintenanceData.value = res.data.datas
            const startTime = formatDate(waterVal.value[0])
            const endTime = formatDate(waterVal.value[1])
            getparms(waterMaintenanceData.value[0], startTime, endTime)
          } else {
            waterMaintenanceData.value = []
          }
        })
      })
    } else {
      waterMaintenanceData.value = []
    }
  })
  const params = {
    projectId: cachedProjects.id
  }
  queryequipmentLocationList(params).then((res) => {
    if (res.data.length > 0) {
      // 使用order排序
      equipmentLocationData.value = res.data.sort((a, b) => a.order - b.order)
      const PointParams = {
        projectId: cachedProjects.id,
        deviceId: equipmentLocationData.value[0].deviceId,
        identifier: equipmentLocationData.value[0].identifier,
        name: equipmentLocationData.value[0].name
      }
      // getDosingecharts(equipmentLocationData.value[0])
      changeDeviceInformation(equipmentLocationData.value[0], 0)
    } else {
      equipmentLocationData.value = []
    }
  })
}

const selectedIndex = ref(0)
// 点击点位数据获取参数
const getpointdata = (value: any) => {
  const params = {
    deviceId: value.deviceId,
    identifier: value.identifier,
    startTime: formatDate(waterVal.value[0]),
    endTime: formatDate(waterVal.value[1]),
    displayStats: true,
    displayGrowth: true
  }
  changewaterEch(params)
}

// 接收图表params数据
const getparms = (value: any, startTime: string, endTime: string) => {
  // 存储当前点位
  cilwaterValueData.value = value

  const params = {
    deviceId: cilwaterValueData.value.deviceId,
    identifier: cilwaterValueData.value.identifier,
    startTime: startTime,
    endTime: endTime,
    displayStats: true,
    displayGrowth: true
  }
  changewaterEch(params)
}

// 图表时间选择
const changeTime = (value: [Date, Date]) => {
  // 接收时间和当前选中的点位
  const params = {
    deviceId: cilwaterValueData.value.deviceId,
    identifier: cilwaterValueData.value.identifier,
    startTime: formatDate(value[0]),
    endTime: formatDate(value[1]),
    displayStats: true,
    displayGrowth: true
  }
  changewaterEch(params)
}

// 查看点位图表信息
const changewaterEch = (value: any) => {
  getechartsData(value).then((res) => {
    if (res.code !== 200) {
      return
    }
    if (!Array.isArray(res.data.his) || res.data.his.length === 0) {
      return
    }
    transformHisData(res.data.his, res.data.name, res.data.unit)
  })
}
function DosingetransformHisData(his: any[], name: string, unit: string) {
  DosinxAxisData.value = his.map((item) => {
  const match = item.time.match(/(\d{2}):(\d{2}):\d{2}$/)
  return match ? `${match[1]}:${match[2]}` : ''
})
  const seriesName = `${name} ${unit ? unit : ''}`.trim()
  const stackName = 'Total'
  DosinseriesData.value = [
    {
      name: seriesName,
      type: 'line',
      // smooth: true,
      // stack: stackName, // 添加堆叠属性
      areaStyle: {}, // 添加面积样式属性
      data: his.map((item) => item.value)
    },
    // {
    //   name: '环比',
    //   type: 'line',
    //   smooth: true,
    //   stack: stackName, // 添加堆叠属性
    //   areaStyle: {}, // 添加面积样式属性
    //   data: his.map((item) => item.hbValue)
    // },
    // {
    //   name: '同比',
    //   type: 'line',
    //   smooth: true,
    //   stack: stackName, // 添加堆叠属性
    //   areaStyle: {}, // 添加面积样式属性
    //   data: his.map((item) => item.tbValue)
    // }
  ]
}
const getDosingecharts =()=>{
  const params = {
    projectId: cachedProjects.id,
    identifier: 'scale_inhibitor_quantity',
  }
  queryDosing(params).then((res) => {
    // console.log(res,'---加药设备走势')
    if (res.code !== 200) {
     return
    }
    if (!Array.isArray(res.data.his) || res.data.his.length === 0) {
      return
    }
    DosingetransformHisData(res.data.his, res.data.name, res.data.unit)
  })
}

// 点击查看设备信息
const processCounter = ref(0) // 用于生成唯一的 id
const allProcessesDone = ref(false)
const changeDeviceInformation = async (value: any, index: number) => {
  selectedIndex.value = index // 更新选中的索引
  const PointParams = {
    projectId: cachedProjects.id,
    deviceId: value.deviceId,
    identifier: value.identifier,
    name: value.name
  }

  // 清空 displayedProcesses 和重置索引
  displayedProcesses.value = []
  currentProcessIndex.value = 0
  allProcessesDone.value = false
  await getPointAnalysis(PointParams)
  // await getDosingecharts(value)

  // 如果有新的 processList，添加第一个项到 displayedProcesses
  if (processList.value.length > 0) {
    displayedProcesses.value = [{
      ...processList.value[0],
      id: processCounter.value++ // 为每个项添加唯一的 id
    }]
  }
}

// 查询点位分析数据
const processList = ref<any[]>([])//处理过程
const conclusionList = ref<any[]>([])//结论
const suggestListList = ref<any[]>([])//建议
const relationList = ref<any[]>([])//依赖指标
const getPointAnalysis = async (value: any) => {
  try {
    const res = await queryPointAnalysis(value)
    if (res.code === 200) {
      processList.value = res.data.processList
      conclusionList.value = res.data.conclusionList
      suggestListList.value = res.data.suggestList
      relationList.value = res.data.relationList
      // console.log(suggestListList.value, 'suggestListList.value')
    } else {
      processList.value = []
      conclusionList.value = []
      suggestListList.value = []
      relationList.value = []
    }
  } catch (error) {
    console.error('获取点位分析数据失败:', error)
    processList.value = []
    conclusionList.value = []
    suggestListList.value = []
    relationList.value = []
  }
}


// 控制逐行逐字显示
const currentProcessIndex = ref(0) // 当前正在显示的processList索引
const displayedProcesses = ref<any[]>([]) // 已显示的processList项

const handleProcessDone = () => {
  if (currentProcessIndex.value < processList.value.length - 1) {
    currentProcessIndex.value += 1
    const nextProcess = processList.value[currentProcessIndex.value]
    displayedProcesses.value.push({
      ...nextProcess,
      id: processCounter.value++ // 为每个项添加唯一的 id
    })
  } else {
    // 所有分析过程完成
    allProcessesDone.value = true
  }
}



// 监听processList的变化
// watch(
//   () => processList.value,
//   (newList) => {
//     if (newList.length > 0) {
//       displayedProcesses.value = [newList[0]]
//       currentProcessIndex.value = 0
//     } else {
//       displayedProcesses.value = []
//     }
//   },
//   { immediate: true }
// )

// 自动点击第一个点位
// watch(
//   () => equipmentLocationData.value,
//   (newData) => {
//     if (newData.length > 0 && selectedIndex.value === 0) {
//       const firstItem = newData[0]
//       getpointdata(firstItem, 0) // 自动点击第一个 item
//     }
//   },
//   { immediate: true }
// )

onMounted(() => {
  getwaterMaintenanceData()
  getDosingecharts()
})
</script>

<style lang="scss">
// @import '@/assets/styles/ctable.scss';
/* 全局样式 */
@import '@/assets/styles/datapicker.scss';
/* 全局样式 */
</style>
<style scoped lang="scss">
.water-maintenance {
  background: rgba(2, 28, 51, 0.5);
  height: calc(100vh - 90px);
  .water-maintenance-top {
    margin-bottom: 5px;
    height: 55%;
    display: flex;

    .water-maintenance-top-left {
      width: 70%;
      display: flex;

      .water-maintenance-top-left-left-empty {
        width: 45%;
        height: 100%;
        // border: 2px solid #268eee;
        border-radius: 10px;

        // background-color: #ffcccc;
        /* Flexbox 布局 */
        // display: flex;
        // flex-wrap: wrap;
        // row-gap: 5px; /* 行间隔 5px */
        // overflow-y: auto;
      }
      .water-maintenance-top-left-left {
        width: 45%;
        height: 100%;
        // border: 2px solid #268eee;
        border-radius: 10px;

        // background-color: #ffcccc;
        /* Flexbox 布局 */
        display: flex;
        flex-wrap: wrap;
        row-gap: 5px; /* 行间隔 5px */
        overflow-y: auto;

        /* 隐藏滚动条 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE 和 Edge */

        /* 隐藏 Webkit 浏览器的滚动条 */
        &::-webkit-scrollbar {
          display: none;
        }

        .item {
          flex: 0 0 calc((100% - (2 * 3px)) / 3); /* 每行三个，减去两次3px列间隔 */
          height: 90px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          // border: 1px solid #ccc;
          box-sizing: border-box;
          position: relative;
        }

        .item-title {
          font-weight: bold;
          margin-bottom: 5px;
          color: rgba(255, 255, 255, 1);
        }

        .item-value {
          display: flex;
          justify-content: center;
          align-items: center;
          .value {
            font-size: 20px;
            font-weight: 500;
            line-height: 47.74px; /* 确保value的高度一致 */
            color: rgba(0, 160, 233, 1);
            margin-right: 5px;
          }
          .unit {
            font-size: 16px;
            font-weight: 400;
            line-height: 1; /* 确保unit内容高度与其字体大小一致 */
            color: rgba(255, 255, 255, 1);
            align-self: center; /* 垂直居中对齐 */
          }
        }

        /* 选择每行的中间 item */
        .item:nth-child(3n + 2) {
          /* 选择器针对每行第二个 item */
          &::after {
            content: '';
            position: absolute;
            top: 10%;
            bottom: 10%;
            left: 100%;
            width: 2px;
            background-color: #264d61;
            /* 调整竖线位置以适应列间隔 */
            transform: translateX(1.5px); /* 半个列间隔 */
          }

          &::before {
            content: '';
            position: absolute;
            top: 10%;
            bottom: 10%;
            right: 100%; /* 放置在左侧 */
            width: 2px;
            background-color: #264d61; /* 竖线颜色 */
            /* 调整竖线位置以适应列间隔 */
            transform: translateX(-1.5px); /* 半个列间隔 */
          }
        }
      }

      .water-maintenance-top-left-right {
        margin: 0 5px;
        width: 55%;
        height: 100%;
        // background-color: #ccffcc;
        // border: 2px solid #268eee;
        // border-radius: 10px;
        // 虚线
        // border-top: 1px dashed #1f5774;
        border-right: 1px dashed #1f5774;
        border-left: 1px dashed #1f5774;
        display: flex;
        .water-maintenance-top-left-right-img {
          // display: flex;
          width: 50%;
          height: 100%;
          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        .water-maintenance-top-left-right-value {
          margin-left: 20px;
          width: 40%;
          height: 100%;
          overflow-y: auto;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */
          &::-webkit-scrollbar {
            display: none; /* Webkit browsers */
          }
        }
        .waterrefresh{
          width: 10%;
          text-align: center;
        }
      }
    }

    .equipmentLocation {
      display: flex;
      align-items: center;
      margin-top: 10px;
      margin-bottom: 13px;
      .equipmentLocation-Number {
        width: 30px;
        height: 30px;
        background-color: #2b4f63;
        border-radius: 50%;
        margin-right: 5px;
        text-align: center;
        line-height: 30px;
        color: white;
      }

      .equipmentLocation-Name {
        padding: 5px 10px;
        background-color: #0493d6;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      }
      .equipmentLocation-Name-off{
        padding: 5px 10px;
        background-color: #f56c6c;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      }
    }

    .water-maintenance-top-right {
      width: 30%;
      height: 100%;
      display: flex;
      flex-direction: column;

      .water-maintenance-top-right-top {
        height: 50%;
        display: flex;
        margin-bottom: 5px;
        border-bottom: 1px dashed #1f5774;
        // h4标签内容居中
        h4{
          text-align: center;
          color: #2c91ee;
          // margin: 0;
        }
        .water-maintenance-top-right-top-content {
          width: 50%;
          height: calc(100% - 65px);
          overflow-y: auto;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */
        }
        .water-Dependency{
          width: 50%;
          height: calc(100% - 65px);
          overflow-y: auto;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */
        }
        .water-Dependency::-webkit-scrollbar {
          display: none; /* Webkit browsers */
        }
        .water-Dependency ul {
          list-style: none;
        }
        .water-maintenance-top-right-top-content::-webkit-scrollbar {
          display: none; /* Webkit browsers */
        }
        .water-maintenance-top-right-top-content ul {
          list-style: none;
        }
      }
      .water-maintenance-top-right-top-empty{
        height: 50%;
        margin-bottom: 5px;
        text-align: center;
        // height: 100%;
      }

      .water-maintenance-top-right-bottom {
        height: 50%;
        // background-color: #ccccff;
        //
        // border-top: 1px solid #268eee;
        // border-radius: 10px;
        display: flex;
        .water-maintenance-top-right-bottom-left{
          width: 50%;
          height: 100%;
          overflow-y: auto;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */
        }
        .water-maintenance-top-right-bottom-left::-webkit-scrollbar {
          display: none; /* Webkit browsers */
        }
        .water-maintenance-top-right-bottom-right{
          width: 50%;
          height: 100%;

          overflow-y: auto;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */
        }
        .water-maintenance-top-right-bottom-right::-webkit-scrollbar {
          display: none; /* Webkit browsers */
        }
      }
    }
  }

  .water-maintenance-bottom {
    height: 45%;
    display: flex;

    .water-maintenance-bottom-left {
      width: 50%;
      height: 100%;
      // background-color: #ffffcc;
      // border: 2px solid #268eee;
      // border-radius: 10px;


      .echarts {
        height: calc(100% - 40px);
      }
    }

    .water-maintenance-bottom-right {
      margin-left: 5px;
      width: 50%;
      height: 100%;
      // background-color: #ffccff;
      // border: 1px solid #268eee;
      border-left: 1px dashed #1f5774;
      // border-radius: 10px;
      .echarts {
        height: calc(100% - 40px);
      }
      .watertime{
        height: 30px;
        width: 15%;
        margin: 3px 5px;
        background: linear-gradient(to right, rgba(14, 78, 131, 0.8), rgba(7, 49, 88, 0.3));
        border-radius: 10px;
        color: #fff;
        text-align: center;
      }
    }
  }
}
</style>
