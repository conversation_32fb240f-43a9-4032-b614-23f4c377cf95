/* 大屏适配样式 */
body, html {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 适配不同分辨率的媒体查询 */

/* 1680*1050 适配 */
@media screen and (width: 1680px) and (height: 1050px) {
  .container1 {
    transform: scale(0.875);
    transform-origin: top left;
    margin-left: -120px;
    margin-top: -15px;
  }
}

/* 1600*900 适配 */
@media screen and (width: 1600px) and (height: 900px) {
  .container1 {
    transform: scale(0.833);
    transform-origin: top left;
    margin-left: -160px;
    margin-top: -90px;
  }
}

/* 1440*900 适配 */
@media screen and (width: 1440px) and (height: 900px) {
  .container1 {
    transform: scale(0.75);
    transform-origin: top left;
    margin-left: -240px;
    margin-top: -90px;
  }
}

/* 1400*1050 适配 */
@media screen and (width: 1400px) and (height: 1050px) {
  .container1 {
    transform: scale(0.729);
    transform-origin: top left;
    margin-left: -260px;
    margin-top: -15px;
  }
}

/* 通用适配 - 用于动态计算的情况 */
.screen-adaptive {
  transition: transform 0.3s ease;
}

/* 确保容器在不同尺寸下的显示效果 */
.container1 {
  box-sizing: border-box;
  min-width: 1920px;
  min-height: 1080px;
} 