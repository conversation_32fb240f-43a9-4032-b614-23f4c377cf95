<template>
  <div class="self-use-analysis">
    <el-table :data="tableData" border>
      <!-- 机组名称列 -->
      <el-table-column prop="unitName" label="机组名称" align="center" />

      <!-- 操作列 -->
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            v-for="action in scope.row.actions"
            :key="action.id"
            :type="action.type"
            :size="action.size"
            :disabled="action.disabled"
            @click="handleAction(action, scope.row)"
          >
            {{ action.label }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { updateConfig } from '@/api/system/config'
import { ComponentInternalInstance } from 'vue'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
const { wsCache } = useLocalCache()
import emitter from '@/utils/eventBus.js'
import { getDiagnosisDashboard, DiagnosisDashboardResponseDTO } from './dataFilterConfig/index.api'
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
emitter.on('projectListChanged', (e) => {
  location.reload()
})
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { intelligent_data_filter_type} = toRefs<any>(
  proxy?.useDict('intelligent_data_filter_type')
)
console.log(intelligent_data_filter_type.value)

// 定义操作按钮的类型
interface ActionButton {
  id: string
  label: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  size: 'large' | 'default' | 'small'
  disabled: boolean
  action: string
  filterType: number
}

// 定义表格数据的类型
interface TableRow {
  id: string
  unitName: string
  powerUnitId: number
  projectId: number
  actions: ActionButton[]
}

// 路由实例
const router = useRouter()

// 表格数据
const tableData = ref<TableRow[]>([])

// 创建操作按钮的函数
const createActionButtons = (filterTypes: number[], unitId: number): ActionButton[] => {
  const buttons: ActionButton[] = []

  filterTypes.forEach((filterType) => {
    const dictItem = intelligent_data_filter_type.value?.find(item => item.value === filterType.toString())
    if (dictItem) {
      let actionType: string
      let buttonType: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'

      switch (filterType) {
        case 1:
          actionType = 'water_flow'
          buttonType = 'primary'
          break
        case 2:
          actionType = 'clean_coefficient'
          buttonType = 'success'
          break
        case 3:
          actionType = 'pump_current'
          buttonType = 'warning'
          break
        case 4:
          actionType = 'fan_current'
          buttonType = 'info'
          break
        default:
          actionType = 'unknown'
          buttonType = 'text'
      }

      buttons.push({
        id: `${actionType}_${unitId}`,
        label: dictItem.label,
        type: buttonType,
        size: 'small',
        disabled: false,
        action: actionType,
        filterType: filterType
      })
    }
  })

  return buttons
}

// 获取数据
const fetchDiagnosisData = async () => {
  try {
    if (!cachedProjects?.id) {
      ElMessage.error('未找到缓存的项目ID')
      return
    }

    const response = await getDiagnosisDashboard(cachedProjects.id)
    const data: DiagnosisDashboardResponseDTO[] = response.data

    // 转换数据格式为表格数据
    const transformedData: TableRow[] = data.map((item) => ({
      id: item.powerUnitId.toString(),
      unitName: item.powerUnitName,
      powerUnitId: item.powerUnitId,
      projectId: item.projectId,
      actions: createActionButtons(item.filterType, item.powerUnitId)
    }))

    tableData.value = transformedData
  } catch (error) {
    console.error('获取诊断仪表板数据失败:', error)
  }
}

// 处理按钮点击事件
const handleAction = (action: ActionButton, row: TableRow) => {
console.log(action,row)

  switch (action.action) {
    case 'water_flow':
      handleWaterFlow(row, action)
      break
    case 'clean_coefficient':
      handleCleanCoefficient(row, action)
      break
    case 'pump_current':
      handlePumpCurrent(row, action)
      break
    case 'fan_current':
      handleFanCurrent(row, action)
      break
    default:
      console.log('未知操作:', action.action)
  }
}

const handleWaterFlow = (row: TableRow, action: ActionButton) => {
  // 查看循环水量
  router.push({
    path: '/project/cwatervolume',
    query: {
      projectId: row.projectId,
      powerUnitId: row.powerUnitId,
      filterType: action.filterType,
      unitName: row.unitName
    }
  })
}

const handleCleanCoefficient = (row: TableRow, action: ActionButton) => {
  // 查看清洁系数
  router.push({
    path: '/project/factorcontrast',
    query: {
      projectId: row.projectId,
      powerUnitId: row.powerUnitId,
      filterType: action.filterType,
      unitName: row.unitName

    }
  })
}

const handlePumpCurrent = (row: TableRow, action: ActionButton) => {
  // 查看变频循泵电流
  router.push({
    path: '/project/vfcpumpcontrast',
    query: {
      projectId: row.projectId,
      powerUnitId: row.powerUnitId,
      filterType: action.filterType,
      unitName: row.unitName

    }
  })
}

const handleFanCurrent = (row: TableRow, action: ActionButton) => {
  // 查看风机电流
  router.push({
    path: '/project/currentcombinedfan',
    query: {
      projectId: row.projectId,
      powerUnitId: row.powerUnitId,
      filterType: action.filterType,
      unitName: row.unitName

    }
  })
}

// 初始化数据
onMounted(() => {
  fetchDiagnosisData()
})
</script>

<style scoped>
.self-use-analysis {
  padding: 20px;
}

.el-button {
  margin-right: 8px;
}

.el-button:last-child {
  margin-right: 0;
}
:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}
</style>
