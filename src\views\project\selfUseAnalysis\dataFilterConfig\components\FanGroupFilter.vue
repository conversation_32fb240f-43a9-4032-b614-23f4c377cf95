<template>
  <div class="fan-group-filter">
    <el-card shadow="never" class="info-card">
      <template #header>
        <div class="card-header">
          <el-icon><InfoFilled /></el-icon>
          <span>风机组合风机电流</span>
        </div>
      </template>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { InfoFilled } from '@element-plus/icons-vue'

// 定义props - 接受null值
const props = defineProps<{
  modelValue: null
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: null): void
}>()

// 标记是否正在更新数据，防止循环触发
const isUpdating = ref(false)

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    // 强制更新数据，确保外部数据变化时能正确渲染
    isUpdating.value = true
    // 始终emit null
    emit('update:modelValue', null)
    nextTick(() => {
      isUpdating.value = false
    })
  },
  { immediate: true }
)

// 初始化时直接emit null，确保值始终为null
if (!isUpdating.value) {
  emit('update:modelValue', null)
}
</script>

<style scoped lang="scss">
.fan-group-filter {
  padding: 20px;
  background: rgba(3, 43, 82, 0.3);
  border-radius: 8px;
  margin-top: 20px;
}

.info-card {
  background: rgba(3, 43, 82, 0.2);
  border: 1px solid rgba(33, 148, 255, 0.3);

  :deep(.el-card__header) {
    background: rgba(7, 53, 92, 0.5);
    border-bottom: 1px solid rgba(33, 148, 255, 0.3);
  }

  :deep(.el-card__body) {
    background: rgba(3, 43, 82, 0.1);
  }
}

.card-header {
  display: flex;
  align-items: center;
  color: #fff;
  font-weight: 500;

  .el-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

.info-content {
  text-align: center;
  padding: 20px 0;
}

:deep(.el-text) {
  color: rgba(204, 204, 204, 0.8);

  p {
    margin: 0;
    font-size: 14px;
    line-height: 1.6;
  }
}
</style>
