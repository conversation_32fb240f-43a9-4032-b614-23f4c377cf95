import { to as tos } from 'await-to-js'
import router from './router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp } from '@/utils/validate'
import { isRelogin } from '@/utils/request'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'
import { useAppStore } from '@/store/modules/app'

NProgress.configure({ showSpinner: false })
const whiteList = ['/login', '/register']
const isValidProjectList = () => {
  try {
    const projectList = localStorage.getItem('projectList')
    return projectList !== null && typeof JSON.parse(projectList) === 'object'
  } catch (e) {
    return false
  }
}
const isValidProjectLists = () => {
  try {
    const projectList = localStorage.getItem('projectLists')
    return projectList !== null && typeof JSON.parse(projectList) === 'object'
  } catch (e) {
    return false
  }
}
// 添加一个标记，用于防止重复调用免登录
let isNoTokenLoginProcessing = false

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const appStore = useAppStore()
  //  根据目标路由来决定sideBar是否隐藏
  if (to.path === '/dataScreen') {
    appStore.setSideBarFullHide(true)
  } else {
    appStore.setSideBarFullHide(false)
  }

  // 检查URL中是否包含appCode参数
  const appCode = to.query.appCode as string
  if (appCode && !isNoTokenLoginProcessing) {
    isNoTokenLoginProcessing = true
    // 执行免登录逻辑
    const [err, res] = await tos(useUserStore().noTokenLoginp(appCode))
    if (!err && res?.code === 200) {
      isNoTokenLoginProcessing = false
      next()
      return
    }
    isNoTokenLoginProcessing = false
    // 如果免登录失败，继续执行正常的登录流程
  }

  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title as string)
    /* has token */
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (useUserStore().roles.length === 0) {
        isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        const [err] = await tos(useUserStore().getInfo())
        if (err) {
          await useUserStore().logout()
          ElMessage.error(err)
          next({ path: '/' })
        } else {
          isRelogin.show = false
          const accessRoutes = await usePermissionStore().generateRoutes()
          // 根据roles权限生成可访问的路由表
          accessRoutes.forEach((route) => {
            if (!isHttp(route.path)) {
              router.addRoute(route) // 动态添加可访问路由表
            }
          })
          next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
        }
      } else {
        next()
      }
    }
  } else {
    // 没有token，走免登录逻辑
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
