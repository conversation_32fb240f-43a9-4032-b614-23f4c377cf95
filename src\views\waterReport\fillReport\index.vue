<template>
  <div class="app-container home">
    <!--报告填写 -->
    <EmptyDataPage :imageSrc="emptyImagePath" v-if="waterTabData.length === 0" />
    <div class="report-tabs" v-else>
      <el-row :gutter="20">
        <el-col :span="8" class="col-with-margin">
          <div class="report-tbs">
            <el-tabs type="border-card" v-model="activeTab" @tab-click="handleTabsClick">
              <el-tab-pane v-for="tab in waterTabData" :key="tab.name" :label="tab.label" :name="tab.name">
                <div class="export">
                  <el-button class="export-button" @click="handleExport()" type="primary">导出</el-button>
                </div>
                <el-table
                  :data="waterFillData"
                  style="width: 100%"
                  height="550"
                  @row-click="getRowData"
                  :header-cell-style="{ fontSize: '14px', color: '#fff' }"
                >
                  <el-table-column prop="name" label="指标" width="180" />
                  <el-table-column prop="value" label="值" />
                  <el-table-column prop="unit" label="单位" />
                  <!-- <el-table-column fixed="right" label="操作" min-width="120">
                    <template #default="scope">
                      <el-button size="small" type="primary" plain @click="getRowData(scope.row)">查看历史曲线</el-button>
                    </template>
</el-table-column> -->
                </el-table>
                <div class="divider">
                  <div class="divider-title">更新时间&nbsp;&nbsp;{{ currentDate }}</div>
                  <!-- <el-divider content-position="center">{{ currentDate }}</el-divider> -->
                </div>
                <div class="btn">
                  <el-button type="primary" icon="Edit" @click="fillReport" v-hasPermi="['project:powerPointConfig:device:data:add']"
                    >填写水质报告</el-button
                  >
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
        <el-col :span="16">
          <div class="report-ech">
            <div class="selectdate">
              <el-date-picker
                v-model="selectDateTime"
                type="daterange"
                :shortcuts="shortcuts"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled-date="disabledDate"
                format="YYYY-MM-DD"
                date-format="YYYY/MM/DD ddd"
                @change="selectpicker"
                class="customdatapicker"
              />
            </div>
            <div class="echarts">
              <div class="chart-msg-stat" ref="chartMsgStat"></div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <el-dialog
      v-model="dialogWaterVisible"
      title="水质报告填写"
      width="500px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      destroy-on-close
      class="customDialog"
    >
      <el-form :model="fillReportForm" ref="fillReportFormRef" :rules="rules">
        <el-form-item
          v-for="(model, index) in fillReportForm.models"
          :key="model"
          :label="model.name"
          :prop="`models.${index}.value`"
          label-width="100px"
          class="aligned-form-item"
        >
          <div class="input-container">
            <el-input v-model="model.value" placeholder="请输入" class="input-field">
              <template #append>
                <span class="unit">{{ model.unit }}</span>
              </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="填写时间" label-width="100px" prop="fillTime">
          <el-date-picker v-model="fillTime" type="date" placeholder="请选择时间" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelFillReport">取消</el-button>
        <el-button type="primary" @click="saveFillReport">保存</el-button>
      </div>
    </el-dialog>

    <!-- 导出 -->
    <el-dialog v-model="exportDialogVisible" title="数据导出" width="430" align-center :before-close="handleClose">
      <span class="spanTitle">请选择点位:</span>
      <div>
        <el-select
          v-model="selectedTreeOptionsvalue"
          multiple
          clearable
          collapse-tags
          placeholder="请选择需要导出的点位"
          popper-class="custom-header"
          :max-collapse-tags="1"
          style="width: 240px"
        >
          <template #header>
            <el-checkbox v-model="checkAll" :indeterminate="indeterminate" @change="handleCheckAll"> 全选 </el-checkbox>
          </template>
          <el-option v-for="item in selectedTreeOptions" :key="item.identifier" :label="item.name" :value="item.identifier" />
        </el-select>
        <div class="block1">
          <span class="spanTitle">请选择时间:</span>
          <el-date-picker
            v-model="datePicker"
            type="daterange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            date-format="YYYY/MM/DD ddd"
            :disabled-date="disabledDates"
            :picker-options="pickerOptions"
            @change="handleDateChange"
          />
        </div>
        <div class="leixing">
          <span class="spanTitle">请选择时间粒度:</span>
          <el-row :gutter="20">
            <!-- 输入框部分 -->
            <el-col :span="12">
              <el-input disabled v-model="inputValue" placeholder="请输入数字" />
            </el-col>

            <!-- el-select 下拉选择部分 -->
            <el-col :span="12">
              <el-select v-model="selectedUnit" placeholder="选择时间粒度" @change="handleSelectChange" style="width: 100%;">
                <el-option label="分" value="m" disabled />
                <el-option label="时" value="h" disabled />
                <el-option label="天" value="d" />
              </el-select>
            </el-col>
          </el-row>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseExport">取消</el-button>
          <el-button type="primary" @click="confirmExport"> 下载 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import emptyImagePath from '@/assets/images/noData.png'
import { getWaterReport, getWaterFill, getHisData, addWaterReport } from './api/fillReport.api'
import { CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
import * as echarts from 'echarts/core'
import { TooltipComponent, LegendComponent, TitleComponent, ToolboxComponent, GridComponent } from 'echarts/components'
import { PieChart, LineChart, GaugeChart } from 'echarts/charts'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { formatDate } from '@/utils/formatTime'
import type { FormInstance } from 'element-plus'
import { symbol } from 'vue-types'
import type { CheckboxValueType } from 'element-plus'
import { exportPoint } from '@/api/exportPoint/exportPoint'
import { tr } from 'element-plus/es/locale'

echarts.use([
  TooltipComponent,
  LegendComponent,
  PieChart,
  CanvasRenderer,
  LabelLayout,
  TitleComponent,
  ToolboxComponent,
  GridComponent,
  LineChart,
  UniversalTransition,
  GaugeChart,
])
// 导出
const exportDialogVisible = ref(false)
const selectedTreeOptions = ref([])
const checkAll = ref(false)
const indeterminate = ref(false)
const selectedTreeOptionsvalue = ref<CheckboxValueType[]>([])
const inputValue = ref('1') // 输入框的值
const selectedUnit = ref('d') // 默认选择 '时'
const currentDate1 = new Date()
// 报警提示
function playAlarmSound(): void {
  // 创建 AudioContext
  const audioContext = new (window.AudioContext || window.webkitAudioContext)()

  // 创建振荡器节点（OscillatorNode）
  const oscillator: OscillatorNode = audioContext.createOscillator()
  oscillator.type = 'sine' // 设置波形类型（正弦波）

  // 创建增益节点（GainNode）用于控制音量
  const gainNode: GainNode = audioContext.createGain()
  gainNode.gain.setValueAtTime(0, audioContext.currentTime) // 初始音量为 0

  // 振荡器连接到增益节点，再连接到输出设备
  oscillator.connect(gainNode)
  gainNode.connect(audioContext.destination)

  // 设置频率变化和音量震荡
  const startTime: number = audioContext.currentTime // 当前时间
  const duration = 2 // 告警音持续时间（秒）

  // 配置频率震荡：从 440Hz 到 880Hz 来回变化
  oscillator.frequency.setValueAtTime(440, startTime) // 起始频率 440Hz
  oscillator.frequency.exponentialRampToValueAtTime(880, startTime + 0.1) // 升到 880Hz
  oscillator.frequency.exponentialRampToValueAtTime(440, startTime + 0.2) // 降回 440Hz

  // 循环频率震荡
  for (let i = 0; i < duration * 5; i++) {
    const time = startTime + i * 0.2 // 每 0.2 秒重复一次
    oscillator.frequency.setValueAtTime(440, time)
    oscillator.frequency.exponentialRampToValueAtTime(880, time + 0.1)
  }

  // 音量渐变
  gainNode.gain.setValueAtTime(0.5, startTime) // 提升音量到 50%
  gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration) // 渐变至静音

  // 启动振荡器并设置停止时间
  oscillator.start(startTime)
  oscillator.stop(startTime + duration)
}



// 计算当前时间之前三个月的日期
const threeMonthsAgo = new Date(currentDate1)
threeMonthsAgo.setMonth(currentDate1.getMonth() - 3)

// 初始化日期选择器的值
const datePicker = ref([threeMonthsAgo, currentDate1])
const handleExport = () => {
  // playAlarmSound()
  exportDialogVisible.value = true
  // let valueCounter = 1
  const currentDate = new Date()
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(currentDate.getDate() - 7) // 设置为一周前
  inputValue.value = '1'
  datePicker.value = [oneWeekAgo, currentDate]
  // console.log(waterFillData.value,'------------')
  // 打开导出对话框
  // // 2. 遍历 item.datas，将每一项转化为目标格式并自增长 value
  // const transformedData = item.contents.datas.flatMap((group) =>
  //   group.map((data) => ({
  //     value: valueCounter++, // 自增长的 value
  //     identifier: data.identifier,
  //     label: data.name,
  //     deviceId: data.deviceId,
  //   }))
  // )

  selectedTreeOptions.value = waterFillData.value
}
const handleClose = (done: () => void) => {
  ElMessageBox.confirm('您确定要关闭对话框吗？')
    .then(() => {
      done()
      exportDialogVisible.value = false
      inputValue.value = ''
      // Add these lines to clear selected points
      selectedTreeOptionsvalue.value = []
      checkAll.value = false
      indeterminate.value = false
    })
    .catch(() => {
      // catch error
    })
}
// 限制日期选择：禁用大于当前时间的日期
const disabledDates = (date: Date) => {
  return date > currentDate1 // 禁用未来日期
}

// 设置最大时间范围选择（快捷选项） - 最近三个月

const pickerOptions = {
  shortcuts: [
    {
      text: '最近三个月',
      value: () => {
        return [threeMonthsAgo, currentDate]
      },
    },
  ],
}
watch(selectedTreeOptionsvalue, (val) => {
  if (val.length === 0) {
    checkAll.value = false
    indeterminate.value = false
  } else if (val.length === selectedTreeOptions.value.length) {
    checkAll.value = true
    indeterminate.value = false
  } else {
    indeterminate.value = true
  }
})
// 监听时间范围选择变化
const handleDateChange = (val: any) => {
  const [startDate, endDate] = val
  // 判断选择的日期区间是否大于3个月
  const diffInMonths = (endDate.getFullYear() - startDate.getFullYear()) * 12 + endDate.getMonth() - startDate.getMonth()
  if (diffInMonths > 3) {
    // 如果选择的区间超过3个月，弹出提示
    ElMessageBox.confirm('选择的时间范围不能超过3个月')
    // 可以将日期选择器的值重置为一个合理的时间范围（比如三个月前至当前时间）
    datePicker.value = [threeMonthsAgo, currentDate1]
  }
}
const handleSelectChange = (value: string) => {
}
const handleCloseExport = () => {
  exportDialogVisible.value = false
  inputValue.value = ''
  // Add these lines to clear selected points
  selectedTreeOptionsvalue.value = []
  checkAll.value = false
  indeterminate.value = false
}
const confirmExport = () => {
  // 校验表单数据
  if (!datePicker.value || !selectedTreeOptionsvalue.value.length || !inputValue.value || !selectedUnit.value) {
    ElMessage.error('请确保所有字段都已填写')
    return
  }

  // 获取选中的数据点
  const selectedData = selectedTreeOptionsvalue.value.map((val) => {
    return selectedTreeOptions.value.find((option) => option.identifier === val)
  })

  // 构建请求参数
  const parms = {
    // displayLog:true,
    points: selectedData,
    startTime: formatDate(datePicker.value[0]),
    endTime: formatDate(datePicker.value[1]),
    interval: inputValue.value + selectedUnit.value,
  }
  const findProductName = (tabs, activeName) => {
    const item = tabs.value.find((tab) => tab. name === activeName)
    return item ? item.label : '水质报告' // 如果匹配到则返回productName，否则返回null
  }
  //  activeTab
  const productName = findProductName(waterTabData, activeTab.value)
  // const ExcelName=tabs.value
  // 调用 API 导出数据
  exportPoint(parms)
    .then((res) => {
      const link = document.createElement('a')
      let blob = new Blob([res], { type: 'application/vnd.ms-excel' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      // `${activeName.value}.xlsx`
      link.download = `${productName}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    })
    .catch((error) => {
      ElMessage.error('导出过程中发生错误')
      console.error(error)
    })

  // 关闭对话框和清空表单
  exportDialogVisible.value = false
  inputValue.value = ''
  selectedTreeOptionsvalue.value = [] // 清空选择的数据点
  checkAll.value = false // 重置全选框
  indeterminate.value = false // 重置半选状态
}
// 全选
const handleCheckAll = (val: boolean) => {
  indeterminate.value = false
  if (val) {
    // 全选时，将所有选项的 identifier 添加到 selectedTreeOptionsvalue
    selectedTreeOptionsvalue.value = selectedTreeOptions.value.map(item => item.identifier)
  } else {
    // 取消全选时，清空 selectedTreeOptionsvalue
    selectedTreeOptionsvalue.value = []
  }
}


//
const fillTime = ref()
// 用于保存表单数据
const fillReportForm = ref({
  models: [],
})
// 定义校验规则
const rules = {
  models: {
    type: 'array',
    required: true,
    validator: (rule, value, callback) => {
      for (let i = 0; i < value.length; i++) {
        const model = value[i]
        if (model.value === null || model.value === '') {
          callback(new Error(`${model.name}是必填项`))
          return
        }
        if (isNaN(Number(model.value))) {
          callback(new Error(`${model.name}必须是数字`))
          return
        }
      }
      callback()
    },
    trigger: 'blur',
  },
  // fillTime:{ required: true, message: '请选择时间' }
}
const fillReportFormRef = ref<FormInstance>()
const fillReportData = ref([])
// 初始化图表
const chartMsgStat = ref<(HTMLElement | null)[]>([])
// 弹框初始化
const dialogWaterVisible = ref(false)
// 定义时间
const selectDateTime = ref<[Date, Date]>([new Date(new Date().setDate(new Date().getDate() - 7)), new Date()])
// 日期快捷简
const shortcuts = [
  {
    text: '上周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    },
  },
  {
    text: '上个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    },
  },
  {
    text: '3个月前',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
]
// 获取当前年月日
const currentDate = ref()

// 定义tabs表格选择数据
const selectRowData = ref()
// 选择时间不能超过一年
// 时间选择不能超过当前时间并且时间不能超过一年
const disabledDate = (time: Date) => {
  const oneYearAgo = new Date()
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)
  return time.getTime() > Date.now() || time.getTime() < oneYearAgo.getTime()
}
const { wsCache } = useLocalCache()

const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const activeTab = ref()
// 定义tbas切换
const waterTabData = ref([])
// 定义tabs内容
const waterFillData = ref([])
// 获取tabs切换数据
const getTabs = () => {
  const parms = {
    projectId: cachedProjects.id,
    configType: 1,
  }
  getWaterReport(parms).then((res) => {
    if (res.data.length > 0) {
      fillReportData.value = res.data
      waterTabData.value = res.data.map((item) => {
        return {
          label: item.name,
          name: item.id,
        }
      })
      if (waterTabData.value.length > 0) {
        activeTab.value = waterTabData.value[0].name
      }
      getWaterFillData(waterTabData.value[0].name)
    }
  })
}
// 获取当天数据
const getWaterFillData = (id) => {
  getWaterFill(id).then((res) => {
    if (res.data.datas.length > 0) {
      waterFillData.value = res.data.datas
      // formatDate(fillTime.value, 'YYYY-MM-DD')
      currentDate.value = formatDate(res.data.time, 'YYYY-MM-DD')
      // console.log(waterFillData, '这是tsb切换下面的数据')
      getRowData(waterFillData.value[0])
      // getEcharsHisData(selectRowData.value)
    }
  })
}
// 点击切换tabs
const handleTabsClick = (tab, event) => {
  // 拿到切换的id
  getWaterFillData(tab.props.name)
}
// 填写报告
const fillReport = () => {
  dialogWaterVisible.value = true
  fillTime.value = new Date()
  // 根据activeTab.value的id去匹配fillReportData里面的id拿到该数据
  const data = fillReportData.value.find((item) => item.id === activeTab.value)
  // console.log(data, 'data=====')
  //   fillReportForm.value=data
  //   console.log(fillReportForm.value,'fillReportForm')
  if (data) {
    fillReportForm.value.models = data.models.map((model) => ({
      ...model,
      value: model.value || '', // 确保 value 初始值不是 null
    }))
    // console.log(fillReportForm.value, 'fillReportForm')
  }
}
// 取消填写报告
const cancelFillReport = () => {
  dialogWaterVisible.value = false
  fillReportForm.value.models = [] // 清空表单数据
  fillTime.value = null // ��空时间数据
}
// 保存填写的报告
const saveFillReport = () => {
  const formRef = fillReportFormRef.value // 获取 ElForm 实例
  const hasValue = fillReportForm.value.models.some(item => item.value !== '' && item.value !== null && item.value !== undefined)
  if (fillTime.value === undefined) {
    ElMessage({
      message: '请选择时间',
      type: 'warning',
    })
    return
  }
  if (!hasValue) {
    ElMessage({
      message: '至少填写一项指标数据',
      type: 'warning',
    })
    return
  }
  if (formRef) {
    formRef.validate((valid: boolean) => {
      // console.log(valid, '-valid--')
      if (valid) {
        const savedData = fillReportForm.value.models.map((model) => ({
          identifier: model.identifier,
          deviceId: model.deviceId,
          value: model.value,
        }))
        // 做时间限制
        const formattedTime = formatDate(fillTime.value, 'YYYY-MM-DD')
        // 将填写时间添加到请求数据中
        const requestData = {
          time: formattedTime, // 添加时间
          datas: savedData, // 添加表单数据
        }
        // 调用接口保存数据
        addWaterReport(requestData).then(() => {
          // 保存成功后关闭弹框，并清空数据
          dialogWaterVisible.value = false
          fillReportForm.value.models = []
          fillTime.value = null // 清空时间数据
          getTabs()
        })
      } else {
        // getTabs()
      }
    })
  }
}
// 那tbas.row值
const getRowData = (row) => {
  selectRowData.value = row
  getEcharsHisData(row)
}
// 获取图表数据
const getEcharsHisData = (data) => {
  const [startDate, endDate] = selectDateTime.value
  endDate.setHours(23, 59, 59, 999)
  const params = {
    deviceId: data.deviceId,
    identifier: data.identifier,
    name: data.name,
    startTime: formatDate(startDate),
    endTime: formatDate(endDate),
    displayStats: true,
    displayLog:data.displayLog?data.displayLog:null
  }

  getHisData(params).then((res) => {
    // console.log(res, '=========', res.data.his.map(i => i.value))
    const hisData = res.data.his.filter(i => i.value !== null)
    const xdata = []
    if (hisData.length > 0) {
      // 提取时间和对应的值

      hisData.forEach((msg) => {
        // msg.value = msg.value ? msg.value : Math.floor(Math.random() * 10) + 1
        xdata.push(formatDate(msg.time, 'MM-DD'))
      })
      const seriesData = [
        {
          name: res.data.name,
          type: 'line',
          data: hisData.map((item) => item.value),
          smooth: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(33, 148, 255, 0.4)' }, // 渐变起始颜色
              { offset: 1, color: 'rgba(33, 148, 255, 0.04)' }, // 渐变结束颜色
            ]),
          },
          lineStyle: {
            color: 'rgba(33, 148, 255, 1)', // 设置线条颜色
            width: 2, // 可选：设置线条宽度
            type: 'solid', // 可选：设置线条类型，可以是 'solid', 'dashed', 'dotted' 等
          },
        },
      ]
      echarts.init(chartMsgStat.value).setOption({
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#142433',
          textStyle: {
            color: '#fff', // 修改字体颜色
            fontSize: 14, // 可以同时修改字体大小等其他属性
          },
        },
        legend: {
          data: seriesData.map((s) => s.name),
          textStyle: {
            fontSize: 14,
            color: 'rgba(204, 204, 204, 1)',
            fontWeight: 400,
          },
          left: 'center',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xdata,
          axisLine: {
            lineStyle: {
              color: 'rgba(204, 204, 204, 1)', // 修改 x 轴轴线的颜色
              width: 1,
            },
          },
          axisLabel: {
            lineStyle: {
              color: 'rgba(62, 65, 77, 1)', // 修改 x 轴刻度线的颜色
            },
            // interval: 0, //全部显示
            // rotate: 45, //旋转
            // formatter: function (value, index) { //自定义横坐标,index%18==0就显示
            //   if (index % 25 === 0) {
            //     return value
            //   } else {
            //     return ''
            //   }
            // }
          },
          axisTick: {
            show: false, // 隐藏 x 轴刻度,
          }
        },
        yAxis: {
          type: 'value',
          splitLine: {
            lineStyle: {
              color: 'rgba(62, 65, 77, 1)', // 修改与 x 轴平行的网格线颜色
            },
          },
          axisLabel: {
            color: 'rgba(204, 204, 204, 1)',
            fontSize: 14,
          },
        },
        series: seriesData,
      })
    } else {
      // 如果 hisData 为空，则清空图表
      echarts.init(chartMsgStat.value).clear()
      echarts.init(chartMsgStat.value).setOption({
        title: {
          text: '',
          left: 'center',
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [],
      })
    }
  })
}

// 获取选择的时间
const selectpicker = (value: string) => {
  // const start = formatDate()
  // const end = formatDate()
  // pickerVal.value
  selectDateTime.value = [value[0], value[1]]
  getEcharsHisData(selectRowData.value)
}
emitter.on('projectListChanged', (e) => {
  location.reload()
})
onMounted(() => {
  getTabs()
})
</script>

<style lang="scss">
/* 全局样式 */
@import '@/assets/styles/datapicker.scss';
/* 全局样式 */
</style>
<style scoped lang="scss">
 :deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  border: none;
}
:deep(.el-card__header) {
  border: none;
}
:deep(.el-table,
.el-table__expanded-cell ){
  background-color: transparent !important;
}
:deep(.el-table__body tr,
.el-table__body td) {
  padding: 0;
  height: 40px;
}
:deep(.el-table tr ){
  border: none;
  background-color: transparent;
}
:deep(.el-table th) {
  /* background-color: transparent; */
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}
:deep(.el-table) {
  --el-table-border-color: none;
}
:deep(.el-table__cell) {
  // color: rgba(204, 204, 204, 1) !important;
}
/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row) {
  /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
  /* 取消单元格背景色，确保整行背景色生效 */
}
:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;

  color: #ffffff;
}
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}
.el-tree {
  background-color: transparent;
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #07355c;
}
.el-tree-node__expand-icon {
  color: #fff;
}
.el-tree-node__label {
  color: #fff;
}
.el-tree-node__content {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}
.el-select__tags .el-tag--info {
  background-color: #153059 !important;
}
.el-tag.el-tag--info {
  color: #fff !important;
}
.export {
  width: 100%;
  margin-bottom: 5px;
  display: flex;
  flex-direction: row-reverse;
}
.home {
  background: rgba(2, 28, 51, 0.5);
}

.divider {
  border-top: 1px solid #2b343d;
}

.el-button {
  height: 27px;
  padding: 6px 9px;
}

.divider-title {
  margin-top: 10px;
  color: rgba(204, 204, 204, 1);
  font-size: 14px;
}

.el-col {
  padding: 10px;
}

/* .el-row{
  height: 70%;
} */
.empty {
  height: 100vh;
}

.report-tabs {
  margin-bottom: 20px;
  /* display: flex; */
  flex-direction: row;
  height: calc(100vh - 180px);
}

.el-col {
  flex: 1;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.report-tbs,
.report-ech {
  flex-grow: 1;
  padding: 10px;
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  min-height: 0;
}

/* tbas样式 */
:deep(.el-tabs__item:hover) {
  color: #fff;
  /* 悬停时的文字颜色 -------------*/
}

:deep(.el-tabs__item) {
  /* opacity: 0; */
  /* background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important; */
}

:deep(.el-tabs--border-card) {
  background: rgba(2, 28, 51, 0.5);
  /* padding-bottom: 140px; */
}

:deep(.el-tabs--border-card > .el-tabs__header) {
  background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important;
  border-bottom: 1px solid rgba(33, 148, 255, 1);
}

:deep(.el-tabs--border-card) {
  border: none;
}

:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {
  border-radius: 2px;
  background: linear-gradient(180deg, rgb(6, 101, 243) 0%, rgba(119, 122, 126, 0.1) 100%);
  opacity: 0.8;
  color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(0, 0, 0, 1);
}
:deep(.el-select__wrapper){
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:none !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
/* ----------------- */

/* 表格样式 */

// :deep(.el-table, .el-table__expanded-cell) {
//   background-color: transparent !important;
// }

// :deep(.el-table__body tr, .el-table__body td) {
//   padding: 0;
//   height: 40px;
// }

// :deep(.el-table tr) {
//   border: none;

//   background-color: transparent;
// }

// :deep(.el-table th) {
//   background-color: rgba(7, 53, 92, 1);
// }

// :deep(.el-table) {
//   --el-table-border-color: none;
// }

// :deep(.el-table__cell) {
//   color: #fff;
// }

// :deep(.el-table__body-wrapper .el-table__row:hover) {
//   background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
//   outline: 2px solid rgba(19, 89, 158, 1);
//   color: #fff;
// }

// :deep(.el-table__body-wrapper .el-table__row) {
//   color: #fff;
// }

// :deep(.el-table__body-wrapper .el-table__row:hover td) {
//   background: none !important;
// }

// :deep(.el-table__header thead tr th) {
//   background: rgba(7, 53, 92, 1) !important;

//   color: #ffffff;
// }

// :deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
//   color: #fff;
// }

.report-ech,
.report-tbs {
  padding: 10px;
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
}

.echarts {}

.waterfill {
  display: flex;
  justify-content: space-between;
}

.water-item {
  width: 33%;
  text-align: center;
}

.water-item.left {
  text-align: left;
}

.water-item.center {
  text-align: center;
}

.water-item.right {
  text-align: right;
}

.btn {
  text-align: right;
  margin-top: 10px;
}

.chart-msg-stat {
  height: 500px;
  margin: 5px;
}

.selectdate {
  padding: 5px;
}

.el-form-item {
  display: flex;
  align-items: center;
}

.aligned-form-item :deep(.el-form-item__label) {
  text-align: left;
  /* 确保标签左对齐 */
  color: rgba(255, 255, 255, 1) !important;
}

:deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 1) !important;

}

:deep(.el-input-group__append) {
  // border: 1px solid rgba(72, 86, 102, 1) !important;
  border: none;
  background: rgba(74, 83, 97, 0.5) !important;
  color: rgba(204, 204, 204, 1);
  font-size: 16px;
  box-shadow: none;
}

.input-with-unit {
  display: flex;
  align-items: center;
  width: 100%;
}

.input-field {
  flex-grow: 1;
  // margin-right: 10px; /* 为单位部分留出一些间隔 */
  // border: 1px solid rgba(72, 86, 102, 1);
  border-radius: 2px 0px, 0px, 2px;

}

// :deep(.el-input__inner){
//   color: rgba(255, 255, 255, 1);
//   font-size: 14px;
// }
// :deep(.el-input__wrapper){
//     border: 1px solid #034374 !important;
//     box-shadow: 0 0 0 0px #034374 inset !important;
//     background: transparent !important;
//     color: #FFFFFF !important;
//   }
.unit {
  width: 50px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
  /* 使按钮组靠右对齐 */
  padding: 10px 20px;
  /* 为底部留出适当的间距 */
  margin-top: 20px;
  /* 与上面的内容拉开距离 */
}

/* 弹框 */
// :deep(.el-dialog){
//   background: rgba(3, 43, 82, 1) !important;
//   box-shadow:inset 0px 0px 16px  rgba(33, 148, 255, 0.5);

// }
// :deep(.el-dialog__title){
//   color: #fff !important;
//   font-size: 18px;
// }
</style>
