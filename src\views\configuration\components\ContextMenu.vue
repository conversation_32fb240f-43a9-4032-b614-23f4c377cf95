<template>
  <teleport to="body">
    <div
      v-if="visible"
      class="context-menu"
      :style="menuStyle"
      @click.stop
      @contextmenu.prevent
    >
      <div class="context-menu-content">
        <div
          v-for="item in menuItems"
          :key="item.id"
          class="context-menu-item"
          :class="{ 
            'disabled': item.disabled,
            'divider': item.type === 'divider'
          }"
          @click="handleItemClick(item)"
        >
          <div v-if="item.type === 'divider'" class="menu-divider"></div>
          <template v-else>
            <el-icon v-if="item.icon" class="menu-icon">
              <component :is="item.icon" />
            </el-icon>
            <span class="menu-label">{{ item.label }}</span>
            <span v-if="item.shortcut" class="menu-shortcut">{{ item.shortcut }}</span>
          </template>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'

export interface ContextMenuItem {
  id: string
  label?: string
  icon?: string
  shortcut?: string
  disabled?: boolean
  type?: 'item' | 'divider'
  action?: () => void
}

const props = defineProps<{
  visible: boolean
  x: number
  y: number
  items: ContextMenuItem[]
}>()

const emit = defineEmits<{
  close: []
  itemClick: [item: ContextMenuItem]
}>()

// 菜单样式
const menuStyle = computed(() => ({
  position: 'fixed',
  left: `${props.x}px`,
  top: `${props.y}px`,
  zIndex: 9999
}))

// 过滤后的菜单项
const menuItems = computed(() => {
  return props.items.filter(item => item.type === 'divider' || item.label)
})

// 处理菜单项点击
const handleItemClick = (item: ContextMenuItem) => {
  if (item.disabled || item.type === 'divider') return
  
  emit('itemClick', item)
  if (item.action) {
    item.action()
  }
  emit('close')
}

// 点击外部关闭菜单
const handleClickOutside = (event: MouseEvent) => {
  if (props.visible) {
    emit('close')
  }
}

// 按ESC关闭菜单
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.visible) {
    emit('close')
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.context-menu {
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  box-shadow: var(--el-box-shadow);
  padding: 4px 0;
  min-width: 160px;
  user-select: none;
}

.context-menu-content {
  max-height: 300px;
  overflow-y: auto;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: var(--el-text-color-primary);
  transition: background-color 0.2s;
}

.context-menu-item:hover:not(.disabled):not(.divider) {
  background-color: var(--el-fill-color-light);
}

.context-menu-item.disabled {
  color: var(--el-text-color-disabled);
  cursor: not-allowed;
}

.context-menu-item.divider {
  padding: 0;
  margin: 4px 0;
  cursor: default;
}

.menu-divider {
  height: 1px;
  background-color: var(--el-border-color-lighter);
  margin: 0 8px;
}

.menu-icon {
  margin-right: 8px;
  font-size: 16px;
}

.menu-label {
  flex: 1;
}

.menu-shortcut {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-left: 16px;
}
</style>
