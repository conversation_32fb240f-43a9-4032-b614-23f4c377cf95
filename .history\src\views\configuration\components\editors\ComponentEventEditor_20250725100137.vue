<template>
  <div class="event-editor">
    <div v-if="events.length === 0" class="no-events">
      <el-empty description="暂无事件" />
    </div>
    
    <el-collapse v-else>
      <el-collapse-item 
        v-for="(event, index) in events" 
        :key="index"
        :title="getEventTitle(event)"
      >
        <el-form label-position="top" size="small">
          <el-form-item label="事件类型">
            <el-select v-model="event.type" @change="updateEvents" class="dark-text">
              <el-option label="点击" value="click" />
              <el-option label="悬停" value="hover" />
              <el-option label="值变化" value="change" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="动作类型">
            <el-select v-model="event.action" @change="updateEvents" class="dark-text">
              <el-option label="页面导航" value="navigate" />
              <el-option label="弹窗提示" value="popup" />
              <el-option label="调用API" value="api" />
              <el-option label="执行脚本" value="script" />
            </el-select>
          </el-form-item>
          
          <!-- 页面导航参数 -->
          <template v-if="event.action === 'navigate'">
            <el-form-item label="目标URL">
              <el-input 
                v-model="event.params.url" 
                placeholder="输入URL或路径"
                @change="updateEvents"
                class="dark-text"
              />
            </el-form-item>
            
            <el-form-item label="打开方式">
              <el-radio-group v-model="event.params.target" @change="updateEvents" class="dark-text">
                <el-radio-button label="_self">当前窗口</el-radio-button>
                <el-radio-button label="_blank">新窗口</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </template>
          
          <!-- 弹窗提示参数 -->
          <template v-if="event.action === 'popup'">
            <el-form-item label="消息内容">
              <el-input 
                v-model="event.params.message" 
                type="textarea"
                rows="3"
                placeholder="输入提示消息"
                @change="updateEvents"
                class="dark-text"
              />
            </el-form-item>
            
            <el-form-item label="消息类型">
              <el-select v-model="event.params.type" @change="updateEvents" class="dark-text">
                <el-option label="成功" value="success" />
                <el-option label="警告" value="warning" />
                <el-option label="错误" value="error" />
                <el-option label="信息" value="info" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="显示时长(毫秒)">
              <el-input-number 
                v-model="event.params.duration" 
                :min="1000" 
                :step="1000"
                controls-position="right"
                @change="updateEvents"
                class="dark-text"
              />
            </el-form-item>
          </template>
          
          <!-- API调用参数 -->
          <template v-if="event.action === 'api'">
            <el-form-item label="API地址">
              <el-input 
                v-model="event.params.url" 
                placeholder="输入API地址"
                @change="updateEvents"
                class="dark-text"
              />
            </el-form-item>
            
            <el-form-item label="请求方法">
              <el-select v-model="event.params.method" @change="updateEvents" class="dark-text">
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="请求头">
              <div v-for="(value, key, index) in event.params.headers" :key="index" class="header-item">
                <el-row :gutter="10">
                  <el-col :span="10">
                    <el-input 
                      v-model="headerKeys[index]" 
                      placeholder="名称"
                      @change="updateHeader(index, $event)"
                      class="dark-text"
                    />
                  </el-col>
                  <el-col :span="10">
                    <el-input 
                      v-model="headerValues[index]" 
                      placeholder="值"
                      @change="updateHeader(index, $event)"
                      class="dark-text"
                    />
                  </el-col>
                  <el-col :span="4">
                    <el-button 
                      type="danger" 
                      icon="Delete" 
                      circle
                      @click="removeHeader(index)"
                    />
                  </el-col>
                </el-row>
              </div>
              
              <el-button 
                type="primary" 
                plain 
                icon="Plus"
                @click="addHeader"
              >
                添加请求头
              </el-button>
            </el-form-item>
            
            <el-form-item v-if="event.params.method !== 'GET'" label="请求数据">
              <el-input 
                v-model="apiDataJson" 
                type="textarea"
                rows="5"
                placeholder="输入JSON格式的请求数据"
                @change="updateApiData"
                class="dark-text"
              />
            </el-form-item>
          </template>
          
          <!-- 脚本执行参数 -->
          <template v-if="event.action === 'script'">
            <el-form-item label="JavaScript代码">
              <el-input 
                v-model="event.params.code" 
                type="textarea"
                rows="10"
                placeholder="输入JavaScript代码"
                @change="updateEvents"
                class="dark-text"
              />
            </el-form-item>
            
            <el-alert
              title="安全提示"
              type="warning"
              description="执行自定义脚本可能存在安全风险，请确保代码来源可信。"
              show-icon
            />
          </template>
          
          <el-form-item>
            <el-button 
              type="danger" 
              @click="removeEvent(index)"
            >
              删除事件
            </el-button>
          </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>
    
    <div class="add-event">
      <el-button 
        type="primary" 
        icon="Plus"
        @click="addEvent"
      >
        添加事件
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ConfigurationComponent, ComponentEvent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
}>()

const emit = defineEmits<{
  update: [events: ComponentEvent[]]
}>()

// 事件列表
const events = ref<ComponentEvent[]>([])

// API请求头键值对
const headerKeys = ref<string[]>([])
const headerValues = ref<string[]>([])

// API请求数据JSON
const apiDataJson = ref('')

// 初始化事件
const initEvents = () => {
  events.value = props.component.events ? [...props.component.events] : []
  
  // 初始化请求头键值对
  headerKeys.value = []
  headerValues.value = []
  
  events.value.forEach(event => {
    if (event.action === 'api' && event.params.headers) {
      Object.entries(event.params.headers).forEach(([key, value]) => {
        headerKeys.value.push(key)
        headerValues.value.push(value as string)
      })
    }
    
    // 初始化API数据JSON
    if (event.action === 'api' && event.params.data) {
      apiDataJson.value = JSON.stringify(event.params.data, null, 2)
    }
  })
}

// 监听组件变化，更新本地副本
watch(() => props.component.events, (newEvents) => {
  if (newEvents) {
    events.value = [...newEvents]
    initEvents()
  } else {
    events.value = []
  }
}, { deep: true })

// 初始化
initEvents()

// 获取事件标题
const getEventTitle = (event: ComponentEvent) => {
  const typeMap = {
    click: '点击',
    hover: '悬停',
    change: '值变化'
  }
  
  const actionMap = {
    navigate: '页面导航',
    popup: '弹窗提示',
    api: '调用API',
    script: '执行脚本'
  }
  
  return `${typeMap[event.type as keyof typeof typeMap]} - ${actionMap[event.action as keyof typeof actionMap]}`
}

// 添加事件
const addEvent = () => {
  const newEvent: ComponentEvent = {
    type: 'click',
    action: 'popup',
    params: {
      message: '这是一条提示消息',
      type: 'info',
      duration: 3000
    }
  }
  
  events.value.push(newEvent)
  updateEvents()
}

// 删除事件
const removeEvent = (index: number) => {
  events.value.splice(index, 1)
  updateEvents()
}

// 更新事件
const updateEvents = () => {
  emit('update', [...events.value])
}

// 添加请求头
const addHeader = () => {
  headerKeys.value.push('')
  headerValues.value.push('')
  
  // 更新事件中的请求头
  updateHeadersInEvents()
}

// 更新请求头
const updateHeader = (index: number, event: any) => {
  // 更新事件中的请求头
  updateHeadersInEvents()
}

// 删除请求头
const removeHeader = (index: number) => {
  headerKeys.value.splice(index, 1)
  headerValues.value.splice(index, 1)
  
  // 更新事件中的请求头
  updateHeadersInEvents()
}

// 更新事件中的请求头
const updateHeadersInEvents = () => {
  events.value.forEach(event => {
    if (event.action === 'api') {
      const headers: Record<string, string> = {}
      
      headerKeys.value.forEach((key, index) => {
        if (key && headerValues.value[index]) {
          headers[key] = headerValues.value[index]
        }
      })
      
      event.params.headers = headers
    }
  })
  
  updateEvents()
}

// 更新API数据
const updateApiData = () => {
  try {
    const data = JSON.parse(apiDataJson.value)
    
    events.value.forEach(event => {
      if (event.action === 'api') {
        event.params.data = data
      }
    })
    
    updateEvents()
  } catch (error) {
    console.error('JSON解析失败', error)
  }
}
</script>

<style scoped>
.event-editor {
  padding: 10px;
  color: #000;
}

.no-events {
  padding: 20px 0;
}

.add-event {
  margin-top: 20px;
  text-align: center;
}

.header-item {
  margin-bottom: 10px;
}

/* 确保所有表单元素中的文字为黑色 */
:deep(.el-form-item__label) {
  color: #000 !important;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner),
:deep(.el-select-dropdown__item),
:deep(.el-radio-button__inner),
:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  color: #000 !important;
}

:deep(.el-collapse-item__header),
:deep(.el-collapse-item__content) {
  color: #000 !important;
}

/* 确保下拉选项也是黑色文字 */
:deep(.el-select-dropdown__item) {
  color: #000 !important;
}

/* 确保输入框中的文字为黑色 */
.dark-text :deep(input),
.dark-text :deep(textarea) {
  color: #000 !important;
}
</style>