<template>
  <div class="custom-3d-model-component" ref="containerRef">
    <div v-if="!loaded && !error" class="loading-container">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>加载模型中...</span>
    </div>
    <div v-if="error" class="error-container">
      <el-icon class="error-icon"><WarningFilled /></el-icon>
      <span>{{ errorMessage }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader'
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader'
import type { ConfigurationComponent } from '../../types'
import { modelService } from '../../services/modelService'
import { Loading, WarningFilled } from '@element-plus/icons-vue'

const props = defineProps<{
  component: ConfigurationComponent
  editing?: boolean
}>()

// 容器引用
const containerRef = ref<HTMLElement | null>(null)

// 状态
const loaded = ref(false)
const error = ref(false)
const errorMessage = ref('加载失败')

// Three.js 对象
let scene: THREE.Scene | null = null
let camera: THREE.PerspectiveCamera | null = null
let renderer: THREE.WebGLRenderer | null = null
let controls: OrbitControls | null = null
let animationFrameId: number | null = null
let mixer: THREE.AnimationMixer | null = null

// 模型对象
let model: THREE.Object3D | null = null
let resizeObserver: ResizeObserver | null = null

// 初始化 Three.js
const initThree = () => {
  if (!containerRef.value) return
  
  try {
    // 创建场景
    scene = new THREE.Scene()
    scene.background = new THREE.Color(0xf0f0f0)
    
    // 创建相机
    let width = containerRef.value.clientWidth
    let height = containerRef.value.clientHeight

    // 如果容器尺寸为0，使用组件的尺寸
    if (width <= 0) width = props.component.width
    if (height <= 0) height = props.component.height

    camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
    camera.position.set(5, 5, 5)
    camera.lookAt(0, 0, 0)

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
    renderer.setSize(width, height)
    renderer.setPixelRatio(window.devicePixelRatio)
    renderer.shadowMap.enabled = true
    renderer.shadowMap.type = THREE.PCFSoftShadowMap
    containerRef.value.appendChild(renderer.domElement)
    
    // 添加控制器
    if (camera && renderer) {
      controls = new OrbitControls(camera, renderer.domElement)
      controls.enableDamping = true
      controls.dampingFactor = 0.25
      controls.enableZoom = true
      controls.enablePan = !props.editing // 编辑模式下禁用平移
      controls.autoRotate = false
    }
    
    // 添加灯光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
    scene.add(ambientLight)
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(10, 10, 5)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 2048
    directionalLight.shadow.mapSize.height = 2048
    scene.add(directionalLight)

    // 添加点光源
    const pointLight = new THREE.PointLight(0xffffff, 0.5, 100)
    pointLight.position.set(-10, 10, -10)
    scene.add(pointLight)
    
    // 加载模型
    loadCustomModel()
    
    // 开始动画循环
    animate()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)

    // 使用ResizeObserver监听容器尺寸变化
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        handleResize()
      })
      resizeObserver.observe(containerRef.value)
    }
  } catch (err) {
    console.error('初始化 Three.js 失败', err)
    error.value = true
    errorMessage.value = '初始化失败'
  }
}

// 加载自定义3D模型
const loadCustomModel = async () => {
  if (!scene) return
  
  const { data } = props.component
  const modelId = data.static?.modelId
  
  if (!modelId) {
    error.value = true
    errorMessage.value = '未指定模型ID'
    return
  }

  try {
    // 清除现有模型
    if (model) {
      scene.remove(model)
      model = null
    }

    // 从模型服务获取模型文件
    const arrayBuffer = await modelService.getModelFile(modelId)
    const models = modelService.getModels()
    const modelInfo = models.find(m => m.id === modelId)
    
    if (!modelInfo) {
      throw new Error('模型信息不存在')
    }

    // 根据文件类型选择加载器
    const fileType = modelInfo.fileType.toLowerCase()
    
    if (fileType === 'glb' || fileType === 'gltf') {
      await loadGLTFModel(arrayBuffer, fileType)
    } else if (fileType === 'obj') {
      await loadOBJModel(arrayBuffer)
    } else if (fileType === 'fbx') {
      await loadFBXModel(arrayBuffer)
    } else {
      throw new Error(`不支持的模型格式: ${fileType}`)
    }
    
    loaded.value = true
  } catch (err) {
    console.error('加载模型失败', err)
    error.value = true
    errorMessage.value = err instanceof Error ? err.message : '加载模型失败'
  }
}

// 加载GLTF/GLB模型
const loadGLTFModel = async (arrayBuffer: ArrayBuffer, fileType: string) => {
  const loader = new GLTFLoader()
  
  return new Promise<void>((resolve, reject) => {
    loader.parse(arrayBuffer, '', (gltf) => {
      model = gltf.scene
      
      // 设置模型属性
      model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.castShadow = true
          child.receiveShadow = true
        }
      })

      // 居中模型
      const box = new THREE.Box3().setFromObject(model)
      const center = box.getCenter(new THREE.Vector3())
      model.position.sub(center)

      // 缩放模型以适应视口
      const size = box.getSize(new THREE.Vector3())
      const maxDim = Math.max(size.x, size.y, size.z)
      const scale = 2 / maxDim
      model.scale.setScalar(scale)

      scene!.add(model)

      // 设置动画
      if (gltf.animations && gltf.animations.length > 0) {
        mixer = new THREE.AnimationMixer(model)
        gltf.animations.forEach((clip) => {
          mixer!.clipAction(clip).play()
        })
      }

      resolve()
    }, reject)
  })
}

// 加载OBJ模型
const loadOBJModel = async (arrayBuffer: ArrayBuffer) => {
  const loader = new OBJLoader()
  const text = new TextDecoder().decode(arrayBuffer)
  
  model = loader.parse(text)
  
  // 设置材质
  model.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      child.material = new THREE.MeshPhongMaterial({ color: 0x888888 })
      child.castShadow = true
      child.receiveShadow = true
    }
  })

  // 居中和缩放
  const box = new THREE.Box3().setFromObject(model)
  const center = box.getCenter(new THREE.Vector3())
  model.position.sub(center)

  const size = box.getSize(new THREE.Vector3())
  const maxDim = Math.max(size.x, size.y, size.z)
  const scale = 2 / maxDim
  model.scale.setScalar(scale)

  scene!.add(model)
}

// 加载FBX模型
const loadFBXModel = async (arrayBuffer: ArrayBuffer) => {
  const loader = new FBXLoader()
  
  return new Promise<void>((resolve, reject) => {
    loader.parse(arrayBuffer, '', (fbx) => {
      model = fbx
      
      // 设置模型属性
      model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.castShadow = true
          child.receiveShadow = true
        }
      })

      // 居中和缩放
      const box = new THREE.Box3().setFromObject(model)
      const center = box.getCenter(new THREE.Vector3())
      model.position.sub(center)

      const size = box.getSize(new THREE.Vector3())
      const maxDim = Math.max(size.x, size.y, size.z)
      const scale = 2 / maxDim
      model.scale.setScalar(scale)

      scene!.add(model)

      // 设置动画
      if (fbx.animations && fbx.animations.length > 0) {
        mixer = new THREE.AnimationMixer(model)
        fbx.animations.forEach((clip) => {
          mixer!.clipAction(clip).play()
        })
      }

      resolve()
    }, reject)
  })
}

// 动画循环
const animate = () => {
  if (!renderer || !scene || !camera) return

  animationFrameId = requestAnimationFrame(animate)

  // 更新控制器
  if (controls) {
    controls.update()
  }

  // 更新动画混合器
  if (mixer) {
    mixer.update(0.016) // 约60fps
  }

  renderer.render(scene, camera)
}

// 处理窗口大小变化
const handleResize = () => {
  if (!containerRef.value || !camera || !renderer) return

  const width = containerRef.value.clientWidth || props.component.width
  const height = containerRef.value.clientHeight || props.component.height

  camera.aspect = width / height
  camera.updateProjectionMatrix()
  renderer.setSize(width, height)
}

// 清理资源
const cleanup = () => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }

  if (mixer) {
    mixer.stopAllAction()
    mixer = null
  }

  if (controls) {
    controls.dispose()
    controls = null
  }

  if (renderer) {
    renderer.dispose()
    if (containerRef.value && renderer.domElement.parentNode === containerRef.value) {
      containerRef.value.removeChild(renderer.domElement)
    }
    renderer = null
  }

  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }

  window.removeEventListener('resize', handleResize)

  scene = null
  camera = null
  model = null
}

// 监听组件尺寸变化
watch(() => [props.component.width, props.component.height], () => {
  nextTick(() => {
    handleResize()
  })
}, { immediate: false })

// 监听模型ID变化
watch(() => props.component.data.static?.modelId, () => {
  if (loaded.value) {
    loaded.value = false
    error.value = false
    loadCustomModel()
  }
}, { immediate: false })

// 组件挂载时初始化
onMounted(() => {
  nextTick(() => {
    initThree()
  })
})

// 组件卸载时清理
onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
.custom-3d-model-component {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
  font-size: 14px;
}

.loading-icon,
.error-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.loading-icon {
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.error-icon {
  color: #f56c6c;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
