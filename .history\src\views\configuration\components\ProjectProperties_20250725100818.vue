<template>
  <div class="project-properties">
    <el-form label-position="top" size="small">
      <el-form-item label="项目名称">
        <el-input v-model="localProject.name" @change="updateProject" class="dark-text" />
      </el-form-item>
      
      <el-form-item label="项目描述">
        <el-input 
          v-model="localProject.description" 
          type="textarea" 
          rows="3"
          @change="updateProject"
          class="dark-text"
        />
      </el-form-item>
      
      <el-form-item label="画布尺寸">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-input-number 
              v-model="localProject.width" 
              :min="800" 
              :max="3840"
              controls-position="right"
              placeholder="宽度"
              @change="updateProject"
              class="dark-text"
            />
          </el-col>
          <el-col :span="12">
            <el-input-number 
              v-model="localProject.height" 
              :min="600" 
              :max="2160"
              controls-position="right"
              placeholder="高度"
              @change="updateProject"
              class="dark-text"
            />
          </el-col>
        </el-row>
      </el-form-item>
      
      <el-form-item label="背景颜色">
        <el-color-picker 
          v-model="localProject.backgroundColor" 
          show-alpha
          @change="updateProject"
        />
      </el-form-item>
      
      <el-form-item label="数据绑定">
        <div v-if="localProject.dataBindings.length === 0" class="no-bindings">
          <el-empty description="暂无数据绑定" />
        </div>
        
        <el-collapse v-else>
          <el-collapse-item 
            v-for="(binding, index) in localProject.dataBindings" 
            :key="binding.id"
            :title="binding.name"
          >
            <el-form label-position="top" size="small">
              <el-form-item label="数据源名称">
                <el-input 
                  v-model="binding.name" 
                  @change="updateProject"
                  class="dark-text"
                />
              </el-form-item>
              
              <el-form-item label="数据源类型">
                <el-select v-model="binding.type" @change="updateProject" class="dark-text">
                  <el-option label="API" value="api" />
                  <el-option label="WebSocket" value="websocket" />
                  <el-option label="MQTT" value="mqtt" />
                  <el-option label="静态数据" value="static" />
                </el-select>
              </el-form-item>
              
              <template v-if="binding.type === 'api'">
                <el-form-item label="API地址">
                  <el-input 
                    v-model="binding.config.url" 
                    placeholder="例如: https://api.example.com/data"
                    @change="updateProject"
                    class="dark-text"
                  />
                </el-form-item>
                
                <el-form-item label="请求方法">
                  <el-select v-model="binding.config.method" @change="updateProject" class="dark-text">
                    <el-option label="GET" value="GET" />
                    <el-option label="POST" value="POST" />
                    <el-option label="PUT" value="PUT" />
                    <el-option label="DELETE" value="DELETE" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="刷新间隔 (毫秒)">
                  <el-input-number 
                    v-model="binding.config.interval" 
                    :min="1000" 
                    :step="1000"
                    controls-position="right"
                    @change="updateProject"
                    class="dark-text"
                  />
                </el-form-item>
              </template>
              
              <template v-if="binding.type === 'websocket'">
                <el-form-item label="WebSocket地址">
                  <el-input 
                    v-model="binding.config.url" 
                    placeholder="例如: ws://example.com/socket"
                    @change="updateProject"
                    class="dark-text"
                  />
                </el-form-item>
              </template>
              
              <template v-if="binding.type === 'mqtt'">
                <el-form-item label="MQTT代理地址">
                  <el-input 
                    v-model="binding.config.url" 
                    placeholder="例如: mqtt://broker.example.com"
                    @change="updateProject"
                    class="dark-text"
                  />
                </el-form-item>
                
                <el-form-item label="主题">
                  <el-input 
                    v-model="binding.config.topic" 
                    placeholder="例如: sensors/temperature"
                    @change="updateProject"
                    class="dark-text"
                  />
                </el-form-item>
              </template>
              
              <el-form-item label="数据字段">
                <div v-for="(field, fieldIndex) in binding.fields" :key="fieldIndex" class="field-item">
                  <el-row :gutter="10">
                    <el-col :span="8">
                      <el-input 
                        v-model="field.key" 
                        placeholder="字段键"
                        @change="updateProject"
                        class="dark-text"
                      />
                    </el-col>
                    <el-col :span="8">
                      <el-input 
                        v-model="field.name" 
                        placeholder="显示名称"
                        @change="updateProject"
                        class="dark-text"
                      />
                    </el-col>
                    <el-col :span="6">
                      <el-select v-model="field.type" @change="updateProject" class="dark-text">
                        <el-option label