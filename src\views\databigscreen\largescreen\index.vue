<template>
  <div style="margin-top: 30px;">
    <iframe ref="myframe" width="100%" height="750px" :src="url"></iframe>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {getToken} from '@/utils/auth'

const getCookie = (name) => {
  const cookieValue = document.cookie
    .split('; ')
    .find(row => row.startsWith(name + '='))
    ?.split('=')[1]
  return cookieValue ? decodeURIComponent(cookieValue) : null
}

const token = ref(getToken('token'))
const baseUrl = import.meta.env.VITE_BIG_SCREEN_API
const url = baseUrl +'/#/big-screen-list?token='+ token.value
</script>

<style scoped>
iframe {
  border: none;
}
</style>
