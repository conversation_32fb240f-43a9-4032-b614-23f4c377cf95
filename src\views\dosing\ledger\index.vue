<template>
  <div class="tableWrap">
    <el-card shadow="never">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <!-- 新增按钮：打开弹窗并切换为 "add" 模式 -->
          <el-button type="primary" plain icon="Plus" @click="addLedger">新增</el-button>
        </el-col>
        <right-toolbar @queryTable="getList" />
      </el-row>
      <el-table :data="tableData" style="width: 100%" :span-method="spanMethod">
        <el-table-column prop="ledgerDate" label="盘点日期" align="center" />
        <!-- 药剂信息列 -->
        <el-table-column label="药剂名称" prop="medicalName" align="center" />
        <el-table-column label="规格" prop="medicalSpec" align="center" />
        <el-table-column label="单位" prop="medicalUnit" align="center" />
        <el-table-column label="库存数量" prop="stockNum" align="center" />
        <el-table-column label="月度用量" prop="monthNum" align="center" />
        <el-table-column label="入库数量" prop="inNum" align="center" />
        <el-table-column label="入库时间" prop="inTime" align="center" />

        <el-table-column label="备注" prop="remarkDetail" align="center">
          <template #default="scope">
            <div>
              <!-- 显示备注文字 -->
              <div>{{ scope.row.remarkDetail.remarkDetail }}</div>
              <!-- 如果存在附件，则遍历展示缩略图，并支持点击预览 -->
              <div v-if="scope.row.remarkDetail.ossList && scope.row.remarkDetail.ossList.length">
                <el-image
                  v-for="(oss, index) in scope.row.remarkDetail.ossList"
                  :key="index"
                  style="width: 30px; height: 30px; margin-right: 5px; cursor: pointer;"
                  :src="oss.url"
                  :preview-src-list="[oss.url]"
                  fit="cover"
                />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="action" label="操作" align="center">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" size="small" @click="handleEdit(scope.row)"></el-button>
            <el-button link type="danger" icon="Delete" size="small" @click="handleDelete(scope.row)"></el-button>
            <el-button link type="primary" icon="View" size="small" @click="handleView(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="state.total > 0"
        v-model:page="state.page.pageNum"
        v-model:limit="state.page.pageSize"
        :total="state.total"
        layout="total, prev, pager, next, jumper"
        @pagination="getpagination"
      />
    </el-card>
  </div>

  <el-dialog
    v-model="dialogVisible"
    title="台账"
    width="1200"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
  >
    <!-- 通过 :disabled="dialogMode === 'view'" 控制表单整体是否可编辑 -->
    <el-form
      ref="ruleFormRef"
      :size="formSize"
      :model="ruleForm"
      label-width="auto"
      :rules="rules"
      class="demo-form-inline"
      :disabled="dialogMode === 'view'"
    >
      <el-form-item label="盘点日期" prop="ledgerDate">
        <el-date-picker v-model="ruleForm.ledgerDate" type="datetime" placeholder="选择日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
      </el-form-item>

      <el-form-item label="">
        <el-row style="margin-bottom: 10px;">
          <el-col :span="24">
            <!-- 查看模式下禁用或隐藏「新增药剂」按钮 -->
            <!-- 这里用 disabled 让按钮不可点击，如果想不显示就换成 v-if="dialogMode !== 'view'" -->
            <el-button type="primary" size="small" @click="addarrform" :disabled="dialogMode === 'view'"> 新增药剂 </el-button>
          </el-col>
          <el-col :span="24">
            <div v-for="(item, index) in ruleForm.detailList" :key="index" class="logic-detail-row" style="margin-bottom: 10px;">
              <el-row gutter="20">
                <el-col :span="6">
                  <el-form-item label="药剂名称" prop="medicalId">
                    <el-select v-model="item.medicalId" placeholder="请选择药剂名称" filterable clearable>
                      <el-option v-for="option in medicalList" :key="option.id" :label="option.name" :value="option.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="规格" prop="medicalSpec">
                    <el-input v-model="item.medicalSpec" placeholder="请输入规格" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="单位" prop="medicalUnit">
                    <el-input v-model="item.medicalUnit" placeholder="请输入单位" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="月度用量" prop="monthNum">
                    <el-input v-model="item.monthNum" placeholder="请输入月度用量" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row gutter="20">
                <el-col :span="6">
                  <el-form-item label="库存数量" prop="stockNum">
                    <el-input v-model="item.stockNum" placeholder="请输入库存数量" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-button type="danger" size="small" @click="delPharmaceutical(index)" :disabled="dialogMode === 'view'"> 删除药剂信息 </el-button>
                </el-col>
              </el-row>
              <el-row gutter="20">
                <el-col :span="24">
                  <el-button type="primary" size="small" @click="addInventoryInformation(index)" :disabled="dialogMode === 'view'">
                    新增入库信息
                  </el-button>
                </el-col>
                <el-col :span="24">
                  <div v-for="(inItem, inIndex) in item.inBaseList" :key="inIndex" class="logic-detail-row" style="margin-bottom: 10px;">
                    <el-row gutter="20">
                      <el-col :span="6">
                        <el-form-item label="入库日期" prop="inTime">
                          <el-date-picker
                            v-model="inItem.inTime"
                            type="datetime"
                            placeholder="选择日期"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            format="YYYY-MM-DD HH:mm:ss"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item label="入库数量" prop="inNum">
                          <el-input v-model="inItem.inNum" placeholder="请输入入库数量" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-button type="danger" size="small" @click="removearrform(index, inIndex)" :disabled="dialogMode === 'view'">
                          入库日期删除
                        </el-button>
                      </el-col>
                    </el-row>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="备注" prop="remarkDetail">
        <div class="remark-container">
          <el-input type="textarea" v-model="ruleForm.remarkDetail.remarkDetail" placeholder="请输入备注文字" rows="4" />
          <ImageUpload v-model="ruleForm.remarkDetail.ossList" />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>

        <!-- 提交 & 重置按钮：仅在非查看模式下显示，可根据需求使用禁用或隐藏 -->
        <template v-if="dialogMode !== 'view'">
          <el-button type="primary" @click="submitForm(ruleFormRef)">确 定</el-button>
          <el-button @click="resetForm(ruleFormRef)">重置</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import ImageUpload from '@/components/ImageUpload/index.vue'
import { getledgerList, addledger, editledger, deletelLedger } from './index.api'
import { getAllPharmaceuticalList } from '../category/index.api'
import RichTextEditor from '@/components/Editor/index.vue'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import type { TableColumnCtx } from 'element-plus'
import emitter from '@/utils/eventBus.js'
import { useLocalCache, CACHE_KEY } from '@/hooks/web/useCache'

const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

// 如果有项目切换的逻辑，这里保持原有写法
emitter.on('projectListChanged', (e) => {
  location.reload()
})

// 定义接口
interface RuleForm {
  ledgerDate: string
  id: string
  detailList: Array<{
    medicalId: number | string
    medicalSpec: string
    medicalUnit: string
    stockNum: string
    monthNum: string
    inBaseList: Array<{
      inTime: string
      inNum: string
    }>
    medicalName?: string
  }>
  remarkDetail: {
    ossList: Array<any>
    remarkDetail: string
  }
}

// 表单数据
const ruleForm = reactive<RuleForm>({
  id: '',
  ledgerDate: '',
  detailList: [
    {
      medicalId: '',
      medicalSpec: '',
      medicalUnit: '',
      stockNum: '',
      monthNum: '',
      inBaseList: [{ inTime: '', inNum: '' }],
    },
  ],
  remarkDetail: {
    ossList: [],
    remarkDetail: '',
  },
})

// 新增一个 dialogMode，用来区分「新增 / 编辑 / 查看」
const dialogMode = ref<'add' | 'edit' | 'view'>('add')

const rules = reactive<FormRules<RuleForm>>({})
const formSize = ref<ComponentSize>('default')
const ruleFormRef = ref<FormInstance>()
const dialogVisible = ref(false)
const tableData = ref<any[]>([])
const medicalList = ref<any[]>([])

const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})

// 从缓存中获取项目 ID
const { id: projectId } = cachedProjects || { id: '' }

// 打开弹窗：新增
const addLedger = () => {
  dialogMode.value = 'add'
  dialogVisible.value = true
  // 重置表单
  resetForm()
}

// 编辑
const handleEdit = (row: any) => {
  dialogMode.value = 'edit'
  const originalData = row._origin
  loadFormData(originalData)
  dialogVisible.value = true
}

// 查看
const handleView = (row: any) => {
  dialogMode.value = 'view'
  const originalData = row._origin
  loadFormData(originalData)
  dialogVisible.value = true
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定删除该条记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      deletelLedger([row._origin.id]).then((response) => {
        if (response.code === 200) {
          ElMessage.success('删除成功!')
          getList()
        }
      })
    })
    .catch(() => {
      ElMessage.info('已取消删除')
      getList()
    })
}

// 加载表单数据：编辑 / 查看 共用
function loadFormData(data: any) {
  ruleForm.id = data.id
  ruleForm.ledgerDate = data.ledgerDate
  ruleForm.detailList = data.detailList.map((detail: any) => ({
    ...detail,
    // 将字符串类型转为 number，以便后续 select 正常使用
    medicalId: Number(detail.medicalId),
  }))
  // 备注
  ruleForm.remarkDetail =
    data.remarkDetail || { ossList: [], remarkDetail: data.remark || '' }
}

// 提交
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (!valid) {
      console.log('error submit!', fields)
      return
    }
    // 处理备注里的 ossList
    if (typeof ruleForm.remarkDetail.ossList === 'string') {
      ruleForm.remarkDetail.ossList = ruleForm.remarkDetail.ossList.split(',')
    }
    // 转换成 { id: ? } 结构
    ruleForm.remarkDetail.ossList = ruleForm.remarkDetail.ossList.map((item: any) => {
      return typeof item === 'object'
        ? { id: item.id || item.ossId, url: item.url }
        : { id: item }
    })

    // 添加药剂名称
    ruleForm.detailList.forEach((item) => {
      const medical = medicalList.value.find((med) => +med.id === +item.medicalId)
      if (medical) {
        item.medicalName = medical.name
      }
    })

    const params = {
      projectId,
      ...ruleForm,
    }
    // 根据 dialogMode 判断是新增还是编辑
    if (dialogMode.value === 'add') {
      addledger(params).then((response) => {
        if (response.code === 200) {
          dialogVisible.value = false
          getList()
        }
      })
    } else if (dialogMode.value === 'edit') {
      editledger(params).then((response) => {
        if (response.code === 200) {
          dialogVisible.value = false
          getList()
        }
      })
    }
  })
}

// 重置
const resetForm = (formEl?: FormInstance) => {
  // 如果需要校验规则一起重置，可以用 formEl.resetFields()
  // 此处直接手动重置数据
  ruleForm.id = ''
  ruleForm.ledgerDate = ''
  ruleForm.detailList = [
    {
      medicalId: '',
      medicalSpec: '',
      medicalUnit: '',
      stockNum: '',
      monthNum: '',
      inBaseList: [{ inTime: '', inNum: '' }],
    },
  ]
  ruleForm.remarkDetail = {
    ossList: [],
    remarkDetail: '',
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 获取列表
const getList = () => {
  state.loading = true
  const params = {
    projectId,
    ...state.page,
    ...state.query,
  }
  getledgerList(params).then((res) => {
    if (res.code === 200) {
      tableData.value = flattenLedgerData(res.data.rows)
      state.total = res.data.total
    }
    state.loading = false
  })
}

// 扁平化处理
function flattenLedgerData(ledgerRows: any[]): any[] {
  const flatData: any[] = []
  ledgerRows.forEach((ledger) => {
    // detailList 的入库记录条数之和
    const ledgerTotalRows = ledger.detailList.reduce(
      (acc: number, detail: any) => acc + detail.inBaseList.length,
      0
    )
    // 备注信息
    const remarkData = ledger.remarkDetail
      ? ledger.remarkDetail
      : { ossList: [], remarkDetail: ledger.remark || '' }

    ledger.detailList.forEach((detail: any, detailIndex: number) => {
      const detailRowCount = detail.inBaseList.length
      detail.inBaseList.forEach((inItem: any, inIndex: number) => {
        const isFirstRowInDetail = inIndex === 0
        const isFirstRowInLedger = detailIndex === 0 && inIndex === 0
        flatData.push({
          _origin: ledger,
          ledgerDate: ledger.ledgerDate,
          remarkDetail: remarkData,
          medicalId: detail.medicalId,
          medicalName: detail.medicalName,
          medicalSpec: detail.medicalSpec,
          medicalUnit: detail.medicalUnit,
          stockNum: detail.stockNum,
          monthNum: detail.monthNum,
          inTime: inItem.inTime,
          inNum: inItem.inNum,
          ledgerRowSpan: ledgerTotalRows,
          detailRowSpan: isFirstRowInDetail ? detailRowCount : 0,
          isFirstRowInLedger,
          isFirstRowInDetail,
        })
      })
    })
  })
  return flatData
}

// 合并单元格
const spanMethod = ({ row, column }: { row: any; column: any }) => {
  // 处理台账公共字段：盘点日期、备注、操作列
  if (column.property === 'ledgerDate' || column.property === 'remarkDetail' || column.property === 'action') {
    return row.isFirstRowInLedger
      ? { rowspan: row.ledgerRowSpan, colspan: 1 }
      : { rowspan: 0, colspan: 0 }
  }
  // 处理药剂信息：药剂名称、规格、单位、库存数量、月度用量
  if (['medicalName', 'medicalSpec', 'medicalUnit', 'stockNum', 'monthNum'].includes(column.property)) {
    return row.isFirstRowInDetail
      ? { rowspan: row.detailRowSpan, colspan: 1 }
      : { rowspan: 0, colspan: 0 }
  }
  // 入库记录字段不合并
  return { rowspan: 1, colspan: 1 }
}

const getpagination = (pagination: { page: number; limit: number }) => {
  state.page.pageNum = pagination.page
  state.page.pageSize = pagination.limit
  getList()
}

// 获取药剂列表
const getPharmaceuticalList = () => {
  if (!projectId) return
  const project = { projectId }
  return getAllPharmaceuticalList(project).then((res) => {
    if (res.code === 200) {
      medicalList.value = res.data
    }
  })
}

// 新增药剂行
const addarrform = () => {
  ruleForm.detailList.push({
    medicalId: '',
    medicalSpec: '',
    medicalUnit: '',
    stockNum: '',
    monthNum: '',
    inBaseList: [{ inTime: '', inNum: '' }],
  })
}

// 删除药剂行
const delPharmaceutical = (index: number) => {
  if (ruleForm.detailList.length <= 1) {
    ElMessage.warning('至少保留一条药剂信息')
    return
  }
  ruleForm.detailList.splice(index, 1)
}

// 新增入库信息
const addInventoryInformation = (index: number) => {
  ruleForm.detailList[index].inBaseList.push({
    inTime: '',
    inNum: '',
  })
}

// 删除入库信息
const removearrform = (index: number, inIndex: number) => {
  if (ruleForm.detailList[index].inBaseList.length <= 1) {
    ElMessage.warning('至少保留一条入库记录')
    return
  }
  ruleForm.detailList[index].inBaseList.splice(inIndex, 1)
}

onMounted(async () => {
  await getPharmaceuticalList()
  getList()
})
</script>

<style scoped lang="scss">
.tableWrap {
  padding: 20px;
}
:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  border: none;
}

:deep(.el-card__body) {
  // border: none;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  // border: 1px solid rgba(19, 89, 158, 1);
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  // color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
  border: 1px solid rgba(19, 89, 158, 1);
}

:deep(.el-table) {
  border: none;
  --el-table-border-color: rgba(19, 89, 158, 1) !important;
  // border: 1px solid rgba(19, 89, 158, 1);
  border-right: 1px solid rgba(19, 89, 158, 1);
}

:deep(.el-table__cell) {
  color: rgb(255, 255, 255) !important;
  // border: 1px solid rgba(19, 89, 158, 1);
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  // color: #ffffff;
  // border: 1px solid rgba(19, 89, 158, 1);
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  // color: #fff;
}

:deep(.el-tree) {
  background-color: transparent;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #07355c;
}
:deep(.el-select__wrapper){
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
:deep(.el-tree-node__expand-icon) {
  // color: #fff;
}

:deep(.el-tree-node__label) {
  // color: #fff;
}

:deep(.el-table__body-wrapper table) {
  border-collapse: collapse;
}
:deep(.el-table__body-wrapper table th),
:deep(.el-table__body-wrapper table td) {
  border: 1px solid rgba(19, 89, 158, 1);
}
/* 移除鼠标悬停时行的背景色 */
:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-select__tags .el-tag--info) {
  background-color: #153059 !important;
}

:deep(.el-tag.el-tag--info) {
  // color: #fff !important;
}
</style>
<style lang="scss">
// @import '@/assets/styles/ctable.scss';
/* 全局样式 */
@import '@/assets/styles/datapicker.scss';
/* 全局样式 */
</style>
