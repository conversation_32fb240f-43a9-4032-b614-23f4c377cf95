<template>
  <div class="model-library-manager">
    <!-- 头部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h3>模型组件库管理</h3>
        <el-tag type="info">{{ modelStats.total }} 个模型</el-tag>
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="showUploadDialog = true">
          <el-icon><Upload /></el-icon>
          上传模型
        </el-button>
        <el-button @click="refreshModels">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-bar">
      <el-input
        v-model="searchQuery"
        placeholder="搜索模型名称、描述或标签..."
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-select v-model="selectedCategory" placeholder="选择分类" @change="handleSearch">
        <el-option label="全部分类" value="all" />
        <el-option label="工业设备" value="industrial" />
        <el-option label="建筑结构" value="building" />
        <el-option label="机械零件" value="mechanical" />
        <el-option label="其他" value="general" />
      </el-select>
    </div>

    <!-- 模型列表 -->
    <div class="model-grid">
      <div
        v-for="model in filteredModels"
        :key="model.id"
        class="model-card"
        @click="selectModel(model)"
        :class="{ selected: selectedModel?.id === model.id }"
      >
        <div class="model-thumbnail">
          <img
            v-if="model.thumbnail"
            :src="model.thumbnail"
            :alt="model.name"
            @error="handleImageError"
          />
          <div v-else class="thumbnail-placeholder">
            <el-icon><Box /></el-icon>
            <span>{{ model.fileType.toUpperCase() }}</span>
          </div>
        </div>
        <div class="model-info">
          <h4 class="model-name">{{ model.name }}</h4>
          <p class="model-description">{{ model.description || '暂无描述' }}</p>
          <div class="model-meta">
            <el-tag size="small" type="info">{{ model.fileType.toUpperCase() }}</el-tag>
            <span class="file-size">{{ formatFileSize(model.fileSize) }}</span>
          </div>
          <div class="model-tags" v-if="model.tags && model.tags.length > 0">
            <el-tag
              v-for="tag in model.tags.slice(0, 3)"
              :key="tag"
              size="small"
              effect="plain"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
        <div class="model-actions">
          <el-button
            size="small"
            type="primary"
            @click.stop="addToCanvas(model)"
          >
            添加到画布
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click.stop="confirmDelete(model)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredModels.length === 0" class="empty-state">
      <el-icon><Box /></el-icon>
      <p>{{ searchQuery ? '未找到匹配的模型' : '暂无模型，请上传模型文件' }}</p>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传3D模型"
      width="600px"
      :close-on-click-modal="false"
    >
      <ModelUploadForm
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
        @cancel="showUploadDialog = false"
      />
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="showDeleteDialog"
      title="确认删除"
      width="400px"
    >
      <p>确定要删除模型 "{{ modelToDelete?.name }}" 吗？此操作不可恢复。</p>
      <template #footer>
        <el-button @click="showDeleteDialog = false">取消</el-button>
        <el-button type="danger" @click="deleteModel" :loading="deleting">删除</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Refresh, Search, Box } from '@element-plus/icons-vue'
import { modelService, type Model3DInfo } from '../services/modelService'
import ModelUploadForm from './ModelUploadForm.vue'

const emit = defineEmits(['model-selected', 'add-to-canvas'])

// 响应式数据
const models = modelService.getModelsRef()
const searchQuery = ref('')
const selectedCategory = ref('all')
const selectedModel = ref<Model3DInfo | null>(null)
const showUploadDialog = ref(false)
const showDeleteDialog = ref(false)
const modelToDelete = ref<Model3DInfo | null>(null)
const deleting = ref(false)

// 计算属性
const modelStats = computed(() => modelService.getModelStats())

const filteredModels = computed(() => {
  return modelService.searchModels(searchQuery.value, selectedCategory.value)
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已在 computed 中处理
}

const selectModel = (model: Model3DInfo) => {
  selectedModel.value = model
  emit('model-selected', model)
}

const addToCanvas = (model: Model3DInfo) => {
  emit('add-to-canvas', model)
  ElMessage.success(`已添加模型 "${model.name}" 到画布`)
}

const confirmDelete = (model: Model3DInfo) => {
  modelToDelete.value = model
  showDeleteDialog.value = true
}

const deleteModel = async () => {
  if (!modelToDelete.value) return

  const modelIdToDelete = modelToDelete.value.id
  deleting.value = true
  try {
    await modelService.deleteModel(modelIdToDelete)
    ElMessage.success('模型删除成功')
    showDeleteDialog.value = false

    // 如果删除的是当前选中的模型，清除选中状态
    if (selectedModel.value?.id === modelIdToDelete) {
      selectedModel.value = null
    }

    modelToDelete.value = null
  } catch (error) {
    ElMessage.error('删除模型失败: ' + (error as Error).message)
  } finally {
    deleting.value = false
  }
}

const refreshModels = () => {
  // 重新加载模型列表
  location.reload() // 简单的刷新方式
}

const handleUploadSuccess = (model: Model3DInfo) => {
  showUploadDialog.value = false
  ElMessage.success(`模型 "${model.name}" 上传成功`)
  selectModel(model)
}

const handleUploadError = (error: string) => {
  ElMessage.error('上传失败: ' + error)
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  // 组件挂载时可以执行一些初始化操作
})
</script>

<style scoped>
.model-library-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-left h3 {
  margin: 0;
  color: #303133;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.search-bar {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.search-bar .el-input {
  flex: 1;
}

.search-bar .el-select {
  width: 150px;
}

.model-grid {
  flex: 1;
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  overflow-y: auto;
}

.model-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.model-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.model-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.model-thumbnail {
  height: 160px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.model-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #909399;
}

.thumbnail-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.model-info {
  padding: 12px;
}

.model-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model-description {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.model-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.model-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.model-actions {
  padding: 12px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  gap: 8px;
}

.model-actions .el-button {
  flex: 1;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.empty-state .el-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}
</style>
