<template>
  <div class="smart-operation-container">
    <!-- 机组切换 Segmented -->
    <div class="pumpsegmented" v-if="segOptions.length > 1">
      <el-segmented v-model="selectedUnit" :options="segOptions" />
    </div>

    <!-- 顶部统计：正常 / 过载 / 未运行 -->
    <div class="runningState">
      <div class="title">
        <span class="icon">
          <img src="@/assets/images/normal.png" alt="" />
        </span>
        <span class="status">
          正常运行&nbsp;
          <span style="color: #00CC88">
            {{ selectedData?.runCount ?? 0 }}
            &nbsp;</span
          >
          <span style="color: #CCCCCC; font-size: 12px;">台</span>
        </span>
      </div>

      <div class="title">
        <span class="icon">
          <img src="@/assets/images/overload.png" alt="" />
        </span>
        <span class="status">
          过载运行&nbsp;
          <span style="color: #DD2C00">
            {{ selectedData?.overloadCount ?? 0 }}
            &nbsp;</span
          >
          <span style="color: #CCCCCC; font-size: 12px;">台</span>
        </span>
      </div>

      <div class="title">
        <span class="icon">
          <img src="@/assets/images/notRunning.png" alt="" />
        </span>
        <span class="status">
          未运行&nbsp;
          <span style="color: #999999">
            {{ selectedData?.notRunCount ?? 0 }}
            &nbsp;</span
          >
          <span style="color: #CCCCCC; font-size: 12px;">台</span>
        </span>
      </div>
    </div>

    <!-- 每台泵信息展示 -->
    <div class="runningPump">
      <!-- 循环 pumpVOS 数据 -->
      <div
        class="pump"
        v-for="pump in selectedData?.pumpVOS ?? []"
        :key="pump.name"
        :style="{ background: `url(${getPumpBackground(pump.status)}) no-repeat center/100% 100%` }"
      >
        <!-- 水泵名称 -->
        <div class="pupmname">{{ pump.name }}</div>

        <!-- 泵内部展示多个测点数据（datas 数组） -->
        <div class="pupmcontent">
          <div class="content" v-for="dataItem in pump.datas" :key="dataItem.identifier">
            <div class="label">{{ dataItem.label }}</div>
            <div class="value-box">
              <span class="value">{{ dataItem.value?dataItem.value:'--' }}</span>
              <span class="unit">{{ dataItem.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, toRefs } from 'vue'

// 3张不同状态的背景图
import normalpump from '@/assets/images/normalpump.png'
import overloadpump from '@/assets/images/overloadpump.png'
import notRunningpump from '@/assets/images/notRunningpump.png'

const props = defineProps({
  dialogData: {
    type: Array,
    required: true
  }
})
const { dialogData } = toRefs(props)

// Segmented 当前选中的机组下标
const selectedUnit = ref(0)

// 生成 Segmented 选项： label 为机组名称、value 为索引
const segOptions = computed(() =>
  dialogData.value.map((item: any, index: number) => ({
    label: item.powerUnitName,
    value: index
  }))
)

// 当前选中机组的数据
const selectedData = computed(() => {
  return dialogData.value?.[selectedUnit.value] || null
})

// 根据 status 返回不同的泵背景图
function getPumpBackground(status: number) {
  switch (status) {
    case 0:
      return notRunningpump
    case 1:
      return normalpump
    case 2:
      return overloadpump
    default:
      return notRunningpump
  }
}
</script>

<style lang="scss" scoped>
.smart-operation-container {
  height: 100%;
  width: 100%;

  /* 机组切换 */
  .pumpsegmented {
    text-align: center;
  }

  /* 统计数据：正常 / 过载 / 未运行 */
  .runningState {
    display: flex;
    justify-content: space-around;
    align-items: center; /* 整体垂直居中 */

    .title {
      display: flex;
      align-items: center; /* 每个标题内部元素垂直居中 */
      .icon {
        height: 40px;
        width: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          height: 25px;
          width: 25px;
        }
      }
      .status {
        height: 25px;
        display: flex;
        align-items: center;
        margin-left: 5px;
        color: #fff;
      }
    }
  }

  /* 每台泵卡片 */
  .runningPump {
    margin-top: 10px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    grid-auto-rows: 170px;

    .pump {
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
      color: #fff;

      .pupmname {
        line-height: 36px;
        height: 36px;
        padding: 0 5px;
        text-align: right;
        font-size: 16px;
        font-weight: 500;
      }

      .pupmcontent {
        height: calc(100% - 36px);
        display: grid;
        gap: 5px;
        grid-template-rows: repeat(3, 1fr);

        .content {
          display: grid;
          grid-template-columns: 60px 1fr;
          align-items: center;
          padding: 0 10px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;

          .label {
            font-size: 14px;
          }
          .value-box {
            display: flex;
            align-items: center;
            gap: 4px;
            .value {
              font-size: 16px;
              color: #00cc88;
            }
            .unit {
              font-size: 12px;
              color: #ccc;
              line-height: 1;
              position: relative;
              bottom: -2px;
            }
          }
        }
      }
    }
  }
}
</style>
