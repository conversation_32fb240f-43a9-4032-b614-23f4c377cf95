<template>
  <div class="app-container home">
    <div class="content-top">
      <div class="mb listcas">
        <el-button icon="DArrowLeft" @click="goBack">返回列表</el-button>
      </div>
      <div class="picker">
        <el-date-picker
          v-model="pickerVal"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          date-format="YYYY/MM/DD ddd"
          time-format="A hh:mm:ss"
          :disabled-date="disabledDate"
          @change="selectpicker"
          size="large"
          class="customdatapicker"
        />
      </div>
    </div>

    <div>
      <div class="chart-device-offline" ref="chartRefer"></div>
    </div>
  </div>
</template>

<script setup lang="ts" name="boardEcharts">
import { echatsData } from '../../historicalcurve/index.ts'
import * as echarts from 'echarts/core'
import { TooltipComponent, LegendComponent, TitleComponent, ToolboxComponent, GridComponent } from 'echarts/components'
import { LineChart } from 'echarts/charts'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { formatDate } from '@/utils/formatTime'

echarts.use([
  TooltipComponent,
  LegendComponent,
  CanvasRenderer,
  LabelLayout,
  TitleComponent,
  ToolboxComponent,
  GridComponent,
  LineChart,
  UniversalTransition,
])

const chartRefer = ref()
const router = useRouter()
const pickerVal = ref<[Date, Date]>([new Date(new Date().setDate(new Date().getDate() - 7)), new Date()])
const disabledDate = (time: Date) => {
  const oneYearAgo = new Date()
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)
  return time.getTime() > Date.now() || time.getTime() < oneYearAgo.getTime()
}

let chartInstances = [] // 用于存储图表实例

const initEcharts = () => {
  const charts = []
  for (let i = 0; i < 3; i++) {
    const chartDiv = document.createElement('div')
    chartDiv.style.height = '380px'
    chartDiv.style.marginBottom = '20px'
    chartRefer.value.appendChild(chartDiv)

    const chartInstance = echarts.init(chartDiv)
    chartInstance.setOption({
      // 初始化图表的基本配置
      tooltip: {
        trigger: 'axis',
        backgroundColor:'#142433',
            textStyle: {
            color: '#fff',  // 修改字体颜色
            fontSize: 14,   // 可以同时修改字体大小等其他属性
            }
      },
      legend: {
        data: [],
        textStyle: {
          fontWeight: 'bolder',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {},
        },
      },

      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: [],
        axisLine: {
          lineStyle: {
            color: 'rgba(204, 204, 204, 1)', // 修改 x 轴轴线的颜色
            width: 1,
          },
        },
        axisLabel: {
          lineStyle: {
            color: 'rgba(62, 65, 77, 1)', // 修改 x 轴刻度线的颜色
          },
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: { type: 'dashed', color: '#c6c7c8' },
        },
        axisLabel: {
          color: 'rgba(204, 204, 204, 1)',
          fontSize: 14,
        },
      },
      series: [
        {
          name: '',
          type: 'line',
          stack: 'Total',
          connectNulls: false, // 保留 null 以显示断点
          symbol: 'circle', // 可以改为 'circle' 或其他符号来突出显示数据点
          symbolSize: 3, // 增大符号的尺寸提高可见性
          sampling: 'lttb',
          data: [],
          smooth: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(33, 148, 255, 0.4)' }, // 渐变起始颜色
              { offset: 1, color: 'rgba(33, 148, 255, 0.04)' }, // 渐变结束颜色
            ]),
          },
          lineStyle: {
            color: 'rgba(33, 148, 255, 1)', // 设置线条颜色
            width: 2, // 可选：设置线条宽度
            type: 'solid', // 可选：设置线条类型，可以是 'solid', 'dashed', 'dotted' 等
          },
        },
      ],
    })
    charts.push(chartInstance)
  }
  chartInstances = charts // 保存实例引用
}

const updateCharts = (responses) => {
  responses.forEach((response, index) => {
    const eDdat = response.data.his
    const xdata = []
    const upData = []

    eDdat.forEach((msg) => {
      xdata.push(formatDate(msg.time, 'MM-DD HH:mm'))
      upData.push(msg.value)
    })

    // 更新图表数据
    chartInstances[index].setOption({
      // title: {
      //   text: response.data.name,
      // },
      legend: {
        data: [response.data.name], // 动态设置图例名称
        textStyle: {
          color: '#ffffff',
          fontSize: 12,
        },
      },
      xAxis: {
        data: xdata,
      },
      series: [
        {
          name: response.data.name,
          data: upData,
        },
      ],
    })
  })
}

const selectpicker = async (value: [Date, Date]) => {
  pickerVal.value = [formatDate(value[0]), formatDate(value[1])]
  await initData() // 在选择时间后重新加载数据并刷新图表
}

const goBack = () => {
  router.go(-1)
}

const initData = async () => {
  const formType = window.history.state.data
  const requests = formType.map((item) => {
    const params = {
      identifier: item.identifier,
      deviceId: item.deviceId,
      name: item.name,
      displayStats: true,
      startTime: formatDate(pickerVal.value[0]),
      endTime: formatDate(pickerVal.value[1]),
    }
    return echatsData(params)
  })

  const responses = await Promise.all(requests)
  updateCharts(responses)
}

onMounted(async () => {
  initEcharts() // 初始化图表实例
  await initData() // 加载初始数据
})
</script>

<style lang="scss">
:deep(.el-select__wrapper){
  background: rgb(3, 43, 82) !important;
  box-shadow:none !important;
  border: 1px solid #034374 !important;
}
@import '@/assets/styles/datapicker.scss'; /* 全局样式 */
</style>
<style scoped>
:deep(.el-select__wrapper){
  background: rgb(3, 43, 82) !important;
  box-shadow:none !important;
  border: 1px solid #034374 !important;
}
/* 引入css*/
.listcas .picker {
  /* 给这个盒子一个下边框 */
  border-bottom: 1px solid #e1e3e1;
  padding: 20px;
}
.picker {
  margin-left: 20px;
}
.home {
  min-height: 140vh;
  padding: 20px;
  background: rgba(2, 28, 51, 0.5);
  /* box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5); */
}
/* 图表 */
.chart-device-offline {
  height: 380px;
}
.content-top {
  display: flex;
}
.el-button {
  height: 40px;
  background: rgba(33, 148, 255, 1);
  color: #fff;
  border: none;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 1px;
  padding: 8px 8px;
}
</style>
