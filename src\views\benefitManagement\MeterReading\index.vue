<template>
  <yt-crud
    ref="crudRef"
    :data="data"
    :column="column"
    :table-props="{
          selection: false,//多选
          dialogBtn:false,
          menuSlot: true,//自定义操作按钮
        }"
    :form-props="{
          width: 550,
          labelWidth:220
        }"
    @save-fun="onSave"
    @del-fun="handleDelete"
    @onLoad="getData"
    :loading="state.loading"
    :total="state.total"
    v-model:page="state.page"
    v-model:query="state.query"
  >
    <template #belongDate="{ row }">
      {{ formatDate(row.belongDate) }}
    </template>
  </yt-crud>
</template>

<script lang="ts" setup>
import { addbenifit, getbenifitList, editbenifit, deletebenifit } from './index.api'
import { IColumn } from '@/components/common/types/tableCommon'
import { formatDateymd } from '@/utils/index'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

emitter.on('projectListChanged', (e) => {
  location.reload()
})

const data = ref([])

// 格式化日期函数
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const column = ref<IColumn[]>([
  {
    label: '日期',
    key: 'belongDate',
    search: true,
    type: 'date',
    align: 'center',
    slot: true,
    rules: [{ required: true, message: '请选择日期', trigger: 'change' }],
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    label: '当天发电总量(kW/h)',
    key: 'dayElectricityAmount',
    // search: true,
    align: 'center',
  },
  {
    label: '当前发电机组运行时长(小时)',
    key: 'dayElectricityDuration',
    // search: true,
    align: 'center',
  },
  {
    label: '当天焚烧垃圾量(吨)',
    key: 'dayGarbageAmout',
    // search: true,
    align: 'center',
  },
])
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})
const onSave = ({ type, data, cancel }: any) => {
  const { id: projectId } = cachedProjects
  const modifiedData = {
    ...data,
    projectId,
  }
  if (type == 'add') {
    addbenifit(modifiedData).then((res) => {
      if (res.code == 200) {
        ElMessage.success('添加成功')
        cancel()
        getData()
      }
    })
  } else if (type == 'update') {
    editbenifit(modifiedData).then((res) => {
      if (res.code == 200) {
        ElMessage.success('修改成功')
        cancel()
        getData()
      }
    })
  }
}
const { id: projectId } = cachedProjects
const handleDelete = (row: any) => {
  console.log(row, '===')
  deletebenifit([row.id]).then((res) => {
    if (res.code == 200) {
      ElMessage.success('删除成功')
      getData()
    }
  })
}
const getData = () => {
  state.loading = true
  const project: any = { ...state.query, projectId }
  if (project.belongDate) {
    project.belongDate = formatDateymd(project.belongDate)
  }
  getbenifitList(project).then((res) => {
    state.loading = false
    state.total = res.data.total
    data.value = res.data.rows
  })
}
</script>
<style scoped>
:deep(.el-select__wrapper) {
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
</style>
