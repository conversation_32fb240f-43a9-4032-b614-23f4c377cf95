<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="菜单名称" prop="menuName">
            <el-input v-model="queryParams.deptName" placeholder="请输入公司名称" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="公司状态" clearable>
              <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd()" v-hasPermi="['system:dept:add']">新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        :data="deptList"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        ref="deptTableRef"
        :default-expand-all="isExpandAll"
      >
        <el-table-column prop="deptName" label="公司名称" width="260" />
        <el-table-column prop="orderNum" align="center" label="排序" width="200" />
        <el-table-column prop="status" align="center" label="状态" width="100">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="200">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:dept:edit']" />
            </el-tooltip>
            <el-tooltip content="新增" placement="top">
              <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['system:dept:add']" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:dept:remove']" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="600px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="dialog.visible" ref="deptFormRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24" v-if="form.parentId !== 0">
            <el-form-item label="上级公司" prop="parentId">
              <el-tree-select
                v-model="form.parentId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'deptName', children: 'children' }"
                value-key="id"
                placeholder="选择上级公司"
                check-strictly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司名称" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入公司名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <el-input v-model="form.leader" placeholder="请输入负责人" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">{{ dict.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Dept" lang="ts">
import { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from '@/api/system/dept'
import { ComponentInternalInstance } from 'vue'
import { DeptForm, DeptQuery, DeptVO } from '@/api/system/dept/types'
import { FormInstance, TableInstance } from 'element-plus'

interface DeptOptionsType {
  id: number | string
  deptName: string
  children: DeptOptionsType[]
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'))

const deptList = ref<DeptVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const deptOptions = ref<DeptOptionsType[]>([])
const isExpandAll = ref(true)

const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
})

const deptTableRef = ref<TableInstance>()
const queryFormRef = ref<FormInstance>()
const deptFormRef = ref<FormInstance>()

const initFormData: DeptForm = {
  id: undefined,
  parentId: undefined,
  deptName: undefined,
  orderNum: 0,
  leader: undefined,
  phone: undefined,
  email: undefined,
  status: '0',
}
const data = reactive<PageData<DeptForm, DeptQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deptName: undefined,
    status: undefined,
  },
  rules: {
    parentId: [{ required: true, message: '上级公司不能为空', trigger: 'blur' }],
    deptName: [{ required: true, message: '公司名称不能为空', trigger: 'blur' }],
    orderNum: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
    phone: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  },
})

const { queryParams, form, rules } = toRefs<PageData<DeptForm, DeptQuery>>(data)

/** 查询菜单列表 */
const getList = async () => {
  loading.value = true
  const res = await listDept(queryParams.value)
  const data = proxy?.handleTree<DeptVO>(res.data, 'id')
  if (data) {
    deptList.value = data
  }
  loading.value = false
}
/** 取消按钮 */
const cancel = () => {
  reset()
  dialog.visible = false
}
/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData }
  deptFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
/** 新增按钮操作 */
const handleAdd = (row?: DeptVO) => {
  listDept().then((res) => {
    const data = proxy?.handleTree<DeptOptionsType>(res.data, 'id')
    if (data) {
      deptOptions.value = data
      dialog.visible = true
      dialog.title = '添加公司'
      nextTick(() => {
        reset()
        if (row && row.id) {
          form.value.parentId = row?.id
        }
      })
    }
  })
}
/** 展开/折叠操作 */
const handleToggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value
  toggleExpandAll(deptList.value, isExpandAll.value)
}
/** 展开/折叠所有 */
const toggleExpandAll = (data: DeptVO[], status: boolean) => {
  data.forEach((item) => {
    deptTableRef.value?.toggleRowExpansion(item, status)
    if (item.children && item.children.length > 0) toggleExpandAll(item.children, status)
  })
}

/** 修改按钮操作 */
const handleUpdate = async (row: DeptVO) => {
  const res = await getDept(row.id)
  dialog.visible = true
  dialog.title = '修改公司'
  nextTick(async () => {
    reset()
    form.value = res.data
    const response = await listDeptExcludeChild(row.id)
    const data = proxy?.handleTree<DeptOptionsType>(response.data, 'id')
    if (data) {
      deptOptions.value = data
      if (data.length === 0) {
        const noResultsOptions: DeptOptionsType = { id: res.data.parentId, deptName: res.data.parentName, children: [] }
        deptOptions.value.push(noResultsOptions)
      }
    }
  })
}
/** 提交按钮 */
const submitForm = () => {
  deptFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      form.value.id ? await updateDept(form.value) : await addDept(form.value)
      proxy?.$modal.msgSuccess('操作成功')
      dialog.visible = false
      getList()
    }
  })
}
/** 删除按钮操作 */
const handleDelete = async (row: DeptVO) => {
  await proxy?.$modal.confirm('是否确认删除名称为"' + row.deptName + '"的数据项?')
  await delDept(row.id)
  getList()
  proxy?.$modal.msgSuccess('删除成功')
}

onMounted(() => {
  getList()
})
</script>
<style scoped>
:deep(.el-card){
    background: rgba(2, 28, 51, 0.5);
    /* box-shadow:inset 0px 2px 28px  rgba(33, 148, 255, 0.5); */
    border:none;
}
:deep(.el-card__body){
    border: none;
}
:deep(.el-table, .el-table__expanded-cell ){
    background-color: transparent !important;

  }
:deep(.el-table__body tr, .el-table__body td) {
    padding: 0;
    height: 40px;
  }
:deep(.el-table tr) {
    border: none;
    background-color: transparent;
  }
:deep(.el-table th) {
    /* background-color: transparent; */
    background-color: rgba(7, 53, 92, 1);
    color: rgba(204, 204, 204, 1) !important;
    font-size: 14px;
    font-weight: 400;
  }
:deep(.el-table){
    --el-table-border-color: none;
  }
:deep(.el-table__cell) {
    color: rgba(204, 204, 204, 1) !important;
  }
  /*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
:deep(.el-table__body-wrapper .el-table__row){
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
:deep(.el-table__body-wrapper .el-table__row:hover td ){
    background: none !important;
    /* 取消单元格背景色，确保整行背景色生效 */
  }
:deep(.el-table__header thead tr th) {
    background: rgba(7, 53, 92, 1) !important;
    color: #ffffff;
  }
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
    color: #fff;
  }
:deep(.el-tree){
    background-color: transparent;
  }
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content){
    background-color:   #07355c;
  }
  :deep(.el-tree-node__expand-icon){
    color: #fff;
  }
  :deep(.el-tree-node__label){
    color: #fff;
  }
  :deep(.el-tree-node__content) {
    &:hover {
      background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    }
  }
:deep(.el-select__tags .el-tag--info){
    background-color:#153059 !important;
}
:deep(.el-tag.el-tag--info){
  color: #fff !important;
}
:deep(.el-select__wrapper){

color: #fff!important;
background: rgb(3, 43, 82) !important;
box-shadow:0 0 0 0px #034374 inset !important;
border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
color: #fff;
}
</style>
