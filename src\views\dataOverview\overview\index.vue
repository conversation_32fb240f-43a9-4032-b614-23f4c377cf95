<template>
  <div class="overview">
    <el-row :gutter="5">
      <el-col :span="8">
        <!-- 左边 -->
        <div class="allLeft">
          <div class="curveChart" v-for="(title, index) in overviewTitle.slice(0, 3)" :key="title.id">
            <div class="card-container">
              <div class="card-header">
                <div class="title-left">
                  <span>{{ title.name }}</span>
                  <el-select
                    v-if="title.powerUnits.length >= 2"
                    v-model="jzvalue[title.id]"
                    @change="handleJzChange(title.id, $event)"
                    placeholder="机组切换"
                    size="small"
                    style="width: 160px"
                  >
                    <el-option v-for="item in title.powerUnits" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </div>
                <span class="title-right">24H</span>
              </div>
              <div class="card-content">
                <overviewLine
                  v-if="allChartData[title.id] && chartLegendData[title.id]?.length"
                  :data="allChartData[title.id]"
                  :legendData="chartLegendData[title.id]"
                  :lineColors="chartColors"
                />
                <div class="box-img" v-else>
                  <img src="@/assets/images/noDatax.svg" alt="暂无数据" />
                  <p class="box-text">暂无数据</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <!-- 中间 -->
      <el-col :span="8">
        <div class="allCenter">
          <div class="image-bg">
            <img src="@/assets/icons/svg/moxing1.svg" alt="模型" />
          </div>
          <div class="conclusionTable">
            <div class="card-container">
              <div class="card-header">
                <div>
                  <span>结论</span>
                  <el-select
                    v-if="conclusionOption.length >= 2"
                    v-model="jzconclusionvalue"
                    @change="jzconclusionChange"
                    placeholder="机组切换"
                    size="small"
                    style="width: 160px"
                  >
                    <el-option v-for="item in conclusionOption" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </div>
              </div>
              <div class="card-content">
                <el-table :data="tableData" :header-cell-style="{ textAlign: 'center' }">
                  <el-table-column label="类别" width="180" align="center">
                    <template #default="scope">
                      <span
                        >{{ scope.row.type }}
                        <span style="font-size: 12px;color: #ddd;line-height: 12px;">{{ scope.row.unit? scope.row.unit:'' }}</span></span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column label="当前值" width="100">
                    <template #default="scope">
                      <div style="display: flex; justify-content: flex-end; align-items: center;">
                        <span style="margin: 0 2px 0 10px">{{ scope.row.currentValue }}</span>
                        <el-icon v-if="scope.row.dataStatus === '1'">
                          <img :src="high" alt="偏高" />
                        </el-icon>
                        <el-icon v-else-if="scope.row.dataStatus === '3'">
                          <img :src="low" alt="偏低" />
                        </el-icon>
                        <el-icon v-else-if="scope.row.dataStatus === null">
                          <img :src="alarm" alt="告警" />
                        </el-icon>
                        <el-icon v-else-if="scope.row.dataStatus === '2'">
                          <span>&nbsp;</span>
                        </el-icon>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="suggestedValue" label="建议值" width="100" align="center" />
                  <el-table-column prop="conclusion" label="结论" align="center" />
                </el-table>
              </div>
            </div>
          </div>
          <div class="recommendation">
            <div class="card-container">
              <div class="card-header"><span>建议</span></div>
              <div class="card-content">
                <div class="icon-group">
                  <div class="icon-item" v-for="(suggestion, index) in suggestionList" :key="index" v-if="suggestionList.length > 0">
                    <div class="icon-box">
                      <img :src="getIconForSuggestion(suggestion)" alt="" />
                    </div>
                    <div class="info-box">
                      <div class="info-title">{{ suggestion.name }}</div>
                      <div class="info-value" v-if="suggestion.runStatus != 3">
                        {{ suggestion.value }}&nbsp;
                        <span
                          v-if="suggestion.unit"
                          style="
                            font-size: 14px;
                            color: rgb(204, 204, 204);
                            font-weight: 600;
                          "
                        >
                          {{ suggestion.unit }}
                        </span>
                      </div>
                      <div class="info-value-error" v-else>分析异常&nbsp;</div>
                    </div>
                  </div>
                  <div v-else>暂无数据</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <!-- 右边 -->
      <el-col :span="8">
        <div class="allRight">
          <div class="curveChart" v-if="overviewTitle.length > 4">
            <div class="card-container">
              <div class="card-header">
                <div>
                  <span>{{ overviewTitle[4].name }}</span>
                  <el-select
                    v-if="overviewTitle[4].powerUnits.length >= 2"
                    v-model="jzvalue[overviewTitle[4].id]"
                    @change="handleSelectChange(overviewTitle[4].id, $event)"
                    placeholder="机组切换"
                    size="small"
                    style="width: 160px"
                  >
                    <el-option v-for="item in overviewTitle[4].powerUnits" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </div>
              </div>
              <div class="card-content">
                <div class="waterpump">
                  <div class="waterpump-value" v-for="(index, idx) in waterpumpData" :key="idx">
                    <div class="title">{{ index.name }}</div>
                    <div class="title-value">
                      {{ index.value !== null ? index.value : '--' }}&nbsp;<span class="title-unit">{{ index.unit }}</span>
                    </div>
                  </div>
                </div>
                <div class="shuibeng" :class="{ 'shuibeng-wrap': overviewTitle.length > 4 && overviewTitle[4].powerUnits.length >= 2 }">
                  <div class="beng" v-for="(pumpData, index) in waterpumpArr" :key="index">
                    <div class="beng-value" v-for="(data, idx) in pumpData" :key="idx">
                      <div class="beng-value-title">{{ data.label }}</div>
                      <div class="beng-value-unit">
                        <!-- 针对索引为0的项，直接显示data.value；否则应用原有的条件判断 -->
                        {{ idx === 0 ? data.value : (data.value !== null ? data.value : '--') }}&nbsp;<span class="title-unit">{{ data.unit }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="curveChart" v-if="overviewTitle.length > 5">
            <div class="card-container">
              <div class="card-header">
                <span>{{ overviewTitle[5].name }}</span>
                <span style="margin-right: 10px;font-size: 13px;">24H</span>
              </div>
              <div class="card-content" style="width: 100%; height: 100%; overflow: hidden;">
                <swiper :modules="modules" :autoplay="{ delay: 10000, disableOnInteraction: false }" loop style="width: 100%; height:270px;">
                  <swiper-slide>
                    <div
                      class="waterTable"
                      style="
                        width: 100%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      <el-table :data="watertableData.slice(1)" border>
                        <el-table-column
                          show-overflow-tooltip
                          v-for="(label, key) in watertableData[0]"
                          :key="key"
                          :prop="key"
                          :label="label"
                          :width="key === 'unit' ? 68 : null"
                        />
                      </el-table>
                    </div>
                  </swiper-slide>
                  <swiper-slide>
                    <div
                      class="waterEcharts"
                      style="
                        width: 100%;
                        height: 100%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      <overviewLine
                        v-if="
                          allChartData[overviewTitle[5].id] &&
                          chartLegendData[overviewTitle[5].id]?.length
                        "
                        :data="allChartData[overviewTitle[5].id]"
                        :legendData="chartLegendData[overviewTitle[5].id]"
                        :lineColors="chartColors"
                      />
                      <div class="box-img" style="width: 100%;" v-else>
                        <img src="@/assets/images/noDatax.svg" alt="暂无数据" />
                        <p class="box-text">暂无数据</p>
                      </div>
                    </div>
                  </swiper-slide>
                </swiper>
              </div>
            </div>
          </div>
          <div class="curveChart" v-if="overviewTitle.length > 3">
            <div class="card-container">
              <div class="card-header">
                <div class="title-left">
                  <span>{{ overviewTitle[3].name }}</span>
                  <el-select
                    v-if="overviewTitle[3].powerUnits.length >= 2"
                    v-model="jzvalue[overviewTitle[3].id]"
                    @change="handleJzChange(overviewTitle[3].id, $event)"
                    placeholder="机组切换"
                    size="small"
                    style="width: 160px"
                  >
                    <el-option v-for="item in overviewTitle[3].powerUnits" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </div>
                <span style="margin-right: 10px;font-size: 13px;">24H</span>
              </div>
              <div class="card-content">
                <overviewLine
                  v-if="
                    allChartData[overviewTitle[3].id] &&
                    chartLegendData[overviewTitle[3].id]?.length
                  "
                  :data="allChartData[overviewTitle[3].id]"
                  :legendData="chartLegendData[overviewTitle[3].id]"
                  :lineColors="chartColors"
                />
                <div class="box-img" v-else>
                  <img src="@/assets/images/noDatax.svg" alt="暂无数据" />
                  <p class="box-text">暂无数据</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import overviewLine from '@/components/overviewLine/index.vue'
import { overviewTitleList, downSamplingList, waterPumpList, watertableList, intelligenceList } from './api/overview.api'
import { CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import WebSocketSingleton from '@/utils/socket_service'
import backIcon from '@/assets/icons/svg/back.svg'
import abnormalBackIcon from '@/assets/icons/svg/back_y.svg'
import suggestionBackIcon from '@/assets/icons/svg/back_j.svg'
import pumpIcon from '@/assets/icons/svg/icon_xhb_r.svg'
import abnormalPumpIcon from '@/assets/icons/svg/icon_xhb_r_y.svg'
import suggestionPumpIcon from '@/assets/icons/svg/icon_xhb_r_j.svg'
import fanIcon from '@/assets/icons/svg/icon_gwsyfj_r.svg'
import abnormalFanIcon from '@/assets/icons/svg/icon_gwsyfj_r_y.svg'
import suggestionFanIcon from '@/assets/icons/svg/icon_gwsyfj_r_j.svg'
import high from '@/assets/icons/svg/high.svg'
import low from '@/assets/icons/svg/low.svg'
import alarm from '@/assets/icons/svg/alarm_1.svg'

// 返回上一级
import { useRouter } from 'vue-router'
const router = useRouter()
const handleClick = (item: any) => {
  // router.back()//返回上一级
  router.push('/')//返回首页
}
// 轮播
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'
import 'swiper/css/autoplay'
import { Autoplay } from 'swiper/modules'

// 定义响应式数据
const jzvalue = ref<Record<string, number | string>>({})
const jzconclusionvalue = ref('')
const conclusionOption = ref([])

const modules = [Autoplay]
const { wsCache } = useLocalCache()

// 图表数据结构初始化
const allChartData = ref<Record<string, Record<string, { value: number; time: string }[]>>>({})
const chartLegendData = ref<Record<string, string[]>>({})
const chartColors = ref<string[]>(['#73c0de', '#fac858', '#91cc75', '#5470c6', '#3ba272', '#EB96F2', '#E03B47', '#049188', '#FFC300'])

const watertableData = ref([])
const tableData = ref([])
const suggestionList = ref([])

// 建议图标选择函数
const getIconForSuggestion = (suggestion: any) => {
  const runStatus = suggestion.runStatus === null ? 3 : suggestion.runStatus
  switch (runStatus) {
    case 1:
      return (
        {
          recommendUnitPressure: backIcon,
          recommendPump: pumpIcon,
          recommendFan: fanIcon,
        }[suggestion.code] || ''
      )
    case 2:
      return (
        {
          recommendUnitPressure: suggestionBackIcon,
          recommendPump: suggestionPumpIcon,
          recommendFan: suggestionFanIcon,
        }[suggestion.code] || ''
      )
    case 3:
      return (
        {
          recommendUnitPressure: abnormalBackIcon,
          recommendPump: abnormalPumpIcon,
          recommendFan: abnormalFanIcon,
        }[suggestion.code] || ''
      )
    default:
      return ''
  }
}
const showTwoPerRow = computed(() => {
  return overviewTitle.value.length > 4 && overviewTitle.value[4].powerUnits.length >= 2
})

// 获取数据概览大屏各系统标题
const overviewTitle = ref<any[]>([])
const waterpumpArr = ref<any[]>([])
const waterpumpData = ref<any[]>([])

// 从缓存中获取项目列表
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

// 获取概览标题和相关数据
const getOverviewTitle = () => {
  const params = {
    projectId: cachedProjects.id,
    configType: 5,
  }

  overviewTitleList(params)
    .then((res) => {
      if (res.code === 200) {
        overviewTitle.value = res.data.rows
        conclusionOption.value = overviewTitle.value[0]?.powerUnits || []

        // 保留当前的 jzconclusionvalue，如果不存在则设置默认值
        if (!jzconclusionvalue.value && conclusionOption.value.length > 0) {
          jzconclusionvalue.value = conclusionOption.value[0].id
        }

        // 设置 jzvalue 的默认值，保留已有的选项
        overviewTitle.value.forEach((title: any) => {
          if (title.powerUnits && title.powerUnits.length > 0) {
            if (!jzvalue.value[title.id]) {
              // 如果当前没有选中值，则设置为第一个 powerUnit 的 id
              jzvalue.value[title.id] = title.powerUnits[0].id
            } else {
              // 检查当前选中的值是否仍在新的 powerUnits 中
              const currentValue = jzvalue.value[title.id]
              const exists = title.powerUnits.some((unit: any) => unit.id === currentValue)
              if (!exists) {
                // 如果当前选中的值不再存在，则设置为第一个 powerUnit 的 id
                jzvalue.value[title.id] = title.powerUnits[0].id
              }
            }
          }
        })
        // 处理每个标题的数据
        const promises = overviewTitle.value.map((item: any) => {
          if (item.displayType === 99) {
            const downSamplingParams = { configId: item.id, powerUnitId: jzvalue.value[item.id] }
            return downSamplingList(downSamplingParams)
              .then((response) => {
                if (response.code === 200) {
                  const chartDataProcessed: Record<string, { value: number; time: string; unit: string }[]> = {}
                  const legendData: string[] = []
                  response.data.forEach((dataItem: any) => {
                    const time = dataItem.time
                    dataItem.datas.forEach((data: any) => {
                      if (!chartDataProcessed[data.label]) {
                        chartDataProcessed[data.label] = []
                        legendData.push(data.label)
                      }
                      chartDataProcessed[data.label].push({
                        value: data.value,
                        time: time,
                        unit: data.unit,
                      })
                    })
                  })

                  // 使用展开运算符重新分配对象，确保 Vue 能检测到变化
                  allChartData.value = {
                    ...allChartData.value,
                    [item.id]: chartDataProcessed,
                  }
                  chartLegendData.value = {
                    ...chartLegendData.value,
                    [item.id]: legendData,
                  }
                }
              })
              .catch(() => {
                allChartData.value = {}
                chartLegendData.value = {}
              })
          }
          if (item.displayType === 0) {
            const waterpumpParams = { configId: item.id, powerUnitId: jzvalue.value[item.id] }
            return waterPumpList(waterpumpParams).then((res) => {
              waterpumpArr.value = res.data.datas
              waterpumpData.value = res.data.pubItem
            })
          }
        })

        // 等待所有请求完成
        Promise.all(promises).then(() => {
          // 确保 overviewTitle.value 有数据并且第一个项有 powerUnits
          if (overviewTitle.value.length > 0 && overviewTitle.value[0].powerUnits.length > 0) {
            const powerUnitId = jzconclusionvalue.value || overviewTitle.value[0].powerUnits[0].id
            const intelligenceParams = {
              projectId: cachedProjects.id,
              configType: 7,
              powerUnitId: powerUnitId,
            }
            intelligenceList(intelligenceParams)
              .then((res) => {
                if (res.code === 200) {
                  tableData.value = res.data.conclusionList || []
                  suggestionList.value = res.data.suggestionList || []
                }
              })
              .catch(() => {
                tableData.value = []
                suggestionList.value = []
              })
          } else {
            // 处理没有 powerUnits 的情况
            tableData.value = []
            suggestionList.value = []
            console.warn('没有找到可用的 powerUnitId')
          }
        })
      }
    })
    .catch(() => {
      overviewTitle.value = []
      console.error('获取 overviewTitle 失败')
    })

  // 获取 watertableData
  const waterTableParams = {
    projectId: cachedProjects.id,
    configType: 6,
  }
  watertableList(waterTableParams)
    .then((res) => {
      if (res.code === 200) {
        watertableData.value = res.data
      } else {
        watertableData.value = []
      }
    })
    .catch(() => {
      watertableData.value = []
    })
}

// 结论切换
const jzconclusionChange = (e: number | string) => {
  const params = {
    configType: 7,
    projectId: cachedProjects.id,
    powerUnitId: e,
  }
  intelligenceList(params)
    .then((res) => {
      if (res.code === 200) {
        tableData.value = res.data.conclusionList || []
        suggestionList.value = res.data.suggestionList || []
      }
    })
    .catch(() => {
      tableData.value = []
      suggestionList.value = []
    })
}

// 水泵机组切换
const handleSelectChange = (id: string, value: number | string) => {
  jzvalue.value[id] = value
  handleJzsbChange(id, value)
}
const handleJzsbChange = (id: string, value: number | string) => {
  const params = {
    configId: id,
    powerUnitId: value,
  }
  waterPumpList(params)
    .then((res) => {
      if (res.data.datas.length > 0) {
        waterpumpArr.value = res.data.datas
        waterpumpData.value = res.data.pubItem
      }
    })
    .catch(() => {
      waterpumpArr.value = []
      waterpumpData.value = []
    })
}

// 图表机组切换调用的数据
const handleJzChange = (id: string, value: number | string) => {
  const params = {
    configId: id,
    powerUnitId: value,
  }

  downSamplingList(params)
    .then((res) => {
      if (res.data.length > 0) {
        const newChartData: Record<string, { value: number; time: string }[]> = {}
        const legendData: string[] = []

        res.data.forEach((entry: any) => {
          const time = entry.time
          const datas = entry.datas

          datas.forEach((dataItem: any) => {
            const label = dataItem.label
            const value = dataItem.value

            if (!newChartData[label]) {
              newChartData[label] = []
              legendData.push(label)
            }

            newChartData[label].push({
              value: value,
              time: time,
            })
          })
        })

        // 替换掉 allChartData.value[id] 的数据
        allChartData.value[id] = newChartData

        // 替换或更新 chartLegendData.value[id]
        chartLegendData.value[id] = legendData
      } else {
        console.warn(`下采样数据为空，无法更新 id: ${id} 的图表数据`)
      }
    })
    .catch((error) => {
      console.error('调用 downSamplingList 时出错:', error)
    })
}

// websocket 接收数据并且进行处理
const handleMessage = (event: MessageEvent) => {
  let dataString = event.data
  let dataObject: any
  // 检查数据类型
  if (typeof dataString === 'string') {
    const fixedDataString = fixJsonString(dataString)
    try {
      dataObject = JSON.parse(fixedDataString)
    } catch (e) {
      console.error('无法将消息数据解析为JSON', e)
      return
    }
  } else if (typeof dataString === 'object') {
    dataObject = dataString
  } else {
    console.error('不支持的数据类型', typeof dataString)
    return
  }

  // 检查消息类型并处理 "waterPumb" 类型的消息
  if (dataObject.MESSAGETYPE === 'property' && dataObject.MESSAGECONTENT && dataObject.MESSAGECONTENT.type === 'waterPumb') {
    const messageContent = dataObject.MESSAGECONTENT
    const newValues = messageContent.waterPumbleDataList

    newValues.forEach((group: any, groupIndex: number) => {
      group.forEach((newItem: any) => {
        // 忽略 identifier 为空的项
        if (!newItem.identifier) return
        // 更新 waterpumpArr.value 中的 value 值
        const existingGroup = waterpumpArr.value[groupIndex]
        if (!existingGroup) return

        const existingItem = existingGroup.find((item: any) => item.identifier === newItem.identifier)

        if (existingItem) {
          existingItem.value = newItem.value // 仅更新 value
        }
      })
    })
  }
}

// 辅助函数：修复 JSON 字符串
function fixJsonString(str: string): string {
  return str
    .replace(/([{,]\s*)([A-Za-z0-9_]+)\s*:/g, '$1"$2":') // 给键添加双引号
    .replace(/'/g, '"') // 将单引号替换为双引号
}
// 特殊性循环水泵系统2秒刷新 (无法使用socket)
watch(
  overviewTitle,
  (newVal) => {
    if (newVal.length > 4) {
      createTimer1()
    }
  },
  { immediate: false }
)
watch(
  () => jzvalue.value[overviewTitle.value[4]?.id],
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      createTimer1()
    }
  }
)

const timer1 = ref<NodeJS.Timeout | null>(null)

const createTimer1 = () => {
  if (timer1.value) {
    clearInterval(timer1.value)
  }

  if (overviewTitle.value.length > 4) {
    const id = overviewTitle.value[4].id

    if (id) {
      timer1.value = setInterval(() => {
        const currentValue = jzvalue.value[id]
        handleJzsbChange(id, currentValue)
      }, 3000) // 每2秒刷新一次
    }
  }
}

const timer = ref<NodeJS.Timeout | null>(null)
const createTimer = () => {
  if (timer.value) {
    clearInterval(timer.value)
  }
  timer.value = setInterval(() => {
    getOverviewTitle()
  }, 10 * 60 * 1000) // 每10分钟刷新一次
}

onMounted(() => {
  // const webSocketInstance = WebSocketSingleton.getInstance()
  // webSocketInstance.connect()
  // webSocketInstance.ws?.addEventListener('message', handleMessage)
  getOverviewTitle()
  createTimer()
  // createTimer1()
})

onBeforeUnmount(() => {
  // 获取 WebSocket 实例，并断开连接
  // const webSocketInstance = WebSocketSingleton.getInstance()
  // webSocketInstance.ws?.removeEventListener('message', handleMessage)
  // webSocketInstance.disconnect()
  // 清除定时器
  if (timer.value !== null) {
    clearInterval(timer.value)
  }
  if (timer1.value !== null) {
    clearInterval(timer1.value)
  }
})
</script>

<style scoped lang="scss">
:deep(.el-table__row > :nth-child(1) > .cell) {
  color: #b08805;
}
:deep(.el-table__inner-wrapper) {
  width: 100% !important;
}
:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}
:deep(.el-table__body td) {
  padding: 0;
  height: 33px !important;
}
:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}
:deep(.el-table th) {

}
:deep(.el-table) {
  --el-table-border-color: none;

}
:deep(.el-table__cell) {
  color: #fff;
}

:deep(.el-table__body tr) {
  height: 33px !important;
}
:deep(.el-table .el-table__cell) {
  padding: 0 !important;
}
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background: #07355c !important;
}
:deep(.el-table__body-wrapper .el-table__row:nth-child(odd)) {
  background-color: #15334c !important;
}
:deep(.el-table__body-wrapper .el-table__row) {
}
:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;

}
:deep(.el-table__header thead tr th) {

  color: #b08805;
}
:deep(.el-col) {

}
:deep(.el-input, .el-cascader, .el-select, .el-autocomplete) {
}
:deep(.el-select .el-input__inner){
  text-align: center;
}
:deep(.el-select__selection){
    // width: 120px!important;
  }
  :deep(.el-select__wrapper){
    background-color: #063057 !important;
    box-shadow: #2090f7 0 0 0 1px inset !important;
  }
  :deep(.el-select__placeholder){
    color: #fff   ;
  }
:deep(.el-input__wrapper){
  border: none !important;
}

.overview {
  width: 100%;
  background: rgba(2, 28, 51, 0.5);
  // 正常项目情况
  // height: calc(100vh - 90px);
  // 惠州项目
  height:100vh;
  overflow: hidden;
  .el-row {
    .is-guttered {
      height: 100% !important;
    }
    height: 100% !important;

    .allLeft,
    .allRight,
    .allCenter {
      height: 100%;
      display: flex;
      flex-direction: column;

      .curveChart,
      .image-bg,
      .conclusionTable,
      .recommendation {
        flex: 1;
        // margin: 5px 0;
        background: rgba(2, 28, 51, 0.5);
        // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;

        .card-container {
          width: 100%;
          height: 100%;
          background: transparent;
          border-radius: 8px;
          display: flex;
          // padding-left: 10px;
          color: rgba(204, 204, 204, 1);
          flex-direction: column;

          .card-header {
            letter-spacing: 2px;
            padding: 1px 0;
            font-weight: bold;
            // margin-bottom: 5px;
            border-left: 10px solid rgba(33, 148, 255, 0.8);
            // 背景左右渐变
            background: linear-gradient(to right, rgba(14, 78, 131, 0.8), rgba(7, 49, 88, 0.3));
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title-left {
              margin-left: 5px;
            }

            .title-right {
              margin-right: 10px;
              font-size: 13px;
            }
          }

          .card-content {
            .waterpump {
              display: flex;
              justify-content: space-evenly;
              background: rgba(12, 55, 97, 0.5);
              // margin: 0 15px;
              margin: 5px 10px 0 5px;
              padding-bottom: 5px;
              border-radius: 15px;
              height: 100%;
              align-items:center;

              .waterpump-value {
                text-align: center;

                .title {
                  font-size: 18px;
                  font-weight: 700;
                }

                .title-value {
                  font-size: 18px;
                  font-weight: 700;
                  color: rgba(0, 160, 233, 1);
                }

                .title-unit {
                  font-size: 14px;
                  font-weight: 600;
                  color: rgb(204, 204, 204);
                }
              }
            }

            .box-img {
              padding: 10px;
              background: rgba(2, 28, 51, 0.5);
              // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
              text-align: center;

              .box-text {
                font-size: 14px;
                font-weight: 300;
                letter-spacing: 0px;
                line-height: 18.56px;
                color: rgba(153, 153, 153, 1);
                vertical-align: top;
              }
            }

            // padding: 0 10px;
            width: 100%;
            height: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;

            .water {
              display: flex;
              flex: 1;
              width: 100%;
              height: 100%;

              .waterTable {
                width: 50%; // 固定宽度为父元素 water 的 50%
                display: flex;
                justify-content: center;
                align-items: center;
              }

              .waterEcharts {
                width: 50%; // 水图的宽度也是父容器的50%
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;

                .box-img {
                  padding: 10px;
                  background: rgba(2, 28, 51, 0.5);
                  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
                  text-align: center;

                  .box-text {
                    font-size: 14px;
                    font-weight: 300;
                    letter-spacing: 0px;
                    line-height: 18.56px;
                    color: rgba(153, 153, 153, 1);
                    vertical-align: top;
                  }
                }
              }
            }

            .icon-group {
              display: flex;
              justify-content: space-evenly;
              align-items: center;
              height: 100%;

              .icon-item {
                padding: 0 10px;
                text-align: center;

                .icon-box {
                  flex: 1;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                }

                .info-box {
                  flex: 3;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;

                  .info-value {
                    font-size: 18px;
                    color: rgba(0, 160, 233, 1);
                    font-weight: 700;
                  }

                  .info-value-error {
                    font-size: 18px;
                    color: #fc3d3d;
                    font-weight: 700;
                  }

                  .info-title {
                    font-size: 17px;
                    font-weight: 500;
                    padding-bottom: 2px;
                  }
                }
              }
            }

            .shuibeng {
              display: flex;
              flex-wrap: nowrap;
              width: 100%;
              height: 100%;
              justify-content: flex-start;
              padding-left: 10px;

              &.shuibeng-wrap {
                flex-wrap: wrap;
                justify-content: flex-start;
              }

              &.shuibeng-wrap .beng {
                flex: 1 1 calc(33.333% - 10px);
                margin: 1px 5px;
                box-sizing: border-box;
                display: flex;
                justify-content: flex-start; // 确保内容左对齐
                align-items: flex-start; // 确保内容顶部对齐
              }

              /* 响应式优化 */
              @media (max-width: 768px) {
                &.shuibeng-wrap .beng {
                  flex: 1 1 calc(50% - 10px); // 在中等屏幕上每行2个
                }
              }

              @media (max-width: 480px) {
                &.shuibeng-wrap .beng {
                  flex: 1 1 100%; // 在小屏幕上每行1个
                }
              }

              .beng {
                flex: 1 1 100%; // 默认每个 .beng 占100%宽度
                box-sizing: border-box;
                padding: 1px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                // align-items: center;

                .icon-beng {
                  // text-align: center;

                  div {
                    /* 确保标签居中 */
                    // text-align: center;
                  }

                  /* img {
                    width: 24px;
                    height: 24px;
                  } */
                }

                .beng-value {
                  display: flex;
                  justify-content: space-between;
                  width: 100%;
                  &:nth-child(1){
                    .beng-value-title{
                      // text-align: center;
                    padding-right: 5px;
                    font-size: 16px;
                    font-weight: 700;
                    }
                  }
                  .beng-value-title {
                    // flex: 1;
                    text-align: right;
                    padding-right: 5px;
                    font-size: 12px;
                    font-weight: 700;
                  }

                  .beng-value-unit {
                    flex: 1;
                    text-align: left;
                    padding-left: 5px;
                    font-size: 14px;
                    font-weight: 700;
                    color: rgba(0, 160, 233, 1);

                    .title-unit {
                      font-size: 12px;
                      font-weight: 600;
                      color: rgb(204, 204, 204);
                    }
                  }
                }

              }
            }
          }

          .curveChart:first-child,
          .image-bg:first-child {
            margin-top: 0;
          }

          .curveChart:last-child,
          .recommendation:last-child {
            margin-bottom: 0;
          }
        }

        .allCenter {
          display: flex;
          flex-direction: column;
          height: 100%;

          .image-bg {
            // 正常项目
            // flex: 1;
            // 惠州项目
            flex: 0.5;
          }

          .conclusionTable {
            flex: 0;
          }

          .recommendation {
             // 正常项目
            // flex: 4;
            // 惠州项目
            flex: 4.5;
          }
        }

        .allLeft {
          .curveChart {
            &:nth-child(2) {
              margin: 2px 0;
            }
          }
        }

        .allRight {
          display: flex;
          flex-direction: column;
          height: 100%;

          .curveChart {
            &:first-child {
              flex: 2; // 第一个盒子占2份高度（约20%）
            }

            &:nth-child(2) {
              margin: 1px 0;
              flex: 3;
            }

            &:nth-child(3) {
              flex: 5; // 第二个和第三个盒子各占5份高度（约50%）
            }
          }

          .curveChart:last-child {
            margin-bottom: 0; // 移除最后一个盒子的底部间距
          }

          .card-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between; // 让内容均匀分布
          }
        }
      }

      /* 其他可能的全局样式或组件样式 */
    }
  }
}
</style>
