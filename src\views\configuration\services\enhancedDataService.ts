// 增强的数据服务 - 支持多协议数据源
import { ref, computed } from 'vue'

// 数据源协议类型
export type DataSourceProtocol = 'simulation' | 'http' | 'websocket' | 'mqtt' | 'opcua' | 'modbus'

// 数据点类型
export interface EnhancedDataPoint {
  id: string
  name: string
  value: any
  unit?: string
  timestamp: number
  quality: 'good' | 'bad' | 'uncertain'
  min?: number
  max?: number
  dataType: 'number' | 'string' | 'boolean' | 'object'
  address?: string // 设备地址或寄存器地址
}

// 数据源配置
export interface DataSourceConfig {
  // HTTP配置
  url?: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  params?: Record<string, any>
  interval?: number
  
  // WebSocket配置
  wsUrl?: string
  protocols?: string[]
  
  // MQTT配置
  brokerUrl?: string
  clientId?: string
  username?: string
  password?: string
  topics?: string[]
  qos?: 0 | 1 | 2
  
  // OPC UA配置
  endpointUrl?: string
  securityMode?: string
  securityPolicy?: string
  nodeIds?: string[]
  
  // Modbus配置
  host?: string
  port?: number
  unitId?: number
  registers?: Array<{
    address: number
    type: 'coil' | 'discrete' | 'holding' | 'input'
    dataType: 'int16' | 'uint16' | 'int32' | 'uint32' | 'float32'
  }>
}

// 增强的数据源
export interface EnhancedDataSource {
  id: string
  name: string
  protocol: DataSourceProtocol
  config: DataSourceConfig
  dataPoints: EnhancedDataPoint[]
  status: 'connected' | 'disconnected' | 'connecting' | 'error'
  lastUpdate: number
  errorMessage?: string
}

// 数据转换规则
export interface DataTransformRule {
  id: string
  sourceField: string
  targetField: string
  transform: 'none' | 'scale' | 'offset' | 'formula' | 'mapping'
  params?: {
    scale?: number
    offset?: number
    formula?: string
    mapping?: Record<string, any>
  }
}

// 报警规则
export interface AlarmRule {
  id: string
  dataPointId: string
  name: string
  type: 'threshold' | 'range' | 'change' | 'timeout'
  condition: {
    operator: '>' | '<' | '>=' | '<=' | '==' | '!=' | 'between' | 'outside'
    value: any
    value2?: any // 用于范围检查
  }
  severity: 'info' | 'warning' | 'error' | 'critical'
  enabled: boolean
  message: string
}

// 报警事件
export interface AlarmEvent {
  id: string
  ruleId: string
  dataPointId: string
  timestamp: number
  severity: 'info' | 'warning' | 'error' | 'critical'
  message: string
  value: any
  acknowledged: boolean
}

class EnhancedDataService {
  private dataSources = new Map<string, EnhancedDataSource>()
  private subscribers = new Map<string, Set<(data: EnhancedDataPoint) => void>>()
  private alarmSubscribers = new Set<(alarm: AlarmEvent) => void>()
  private connections = new Map<string, any>() // 存储各种连接实例
  private intervals = new Map<string, number>()
  private transformRules = new Map<string, DataTransformRule[]>()
  private alarmRules = new Map<string, AlarmRule[]>()
  private alarmEvents = ref<AlarmEvent[]>([])
  
  // 获取所有数据源
  getDataSources(): EnhancedDataSource[] {
    return Array.from(this.dataSources.values())
  }
  
  // 获取数据源
  getDataSource(id: string): EnhancedDataSource | undefined {
    return this.dataSources.get(id)
  }
  
  // 添加数据源
  async addDataSource(dataSource: EnhancedDataSource): Promise<boolean> {
    this.dataSources.set(dataSource.id, dataSource)
    return await this.connectDataSource(dataSource.id)
  }
  
  // 连接数据源
  async connectDataSource(id: string): Promise<boolean> {
    const dataSource = this.dataSources.get(id)
    if (!dataSource) return false
    
    try {
      dataSource.status = 'connecting'
      
      switch (dataSource.protocol) {
        case 'simulation':
          return this.connectSimulation(dataSource)
        case 'http':
          return this.connectHttp(dataSource)
        case 'websocket':
          return this.connectWebSocket(dataSource)
        case 'mqtt':
          return this.connectMqtt(dataSource)
        case 'opcua':
          return this.connectOpcUa(dataSource)
        case 'modbus':
          return this.connectModbus(dataSource)
        default:
          throw new Error(`Unsupported protocol: ${dataSource.protocol}`)
      }
    } catch (error) {
      dataSource.status = 'error'
      dataSource.errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return false
    }
  }
  
  // 模拟数据源连接
  private connectSimulation(dataSource: EnhancedDataSource): boolean {
    const interval = setInterval(() => {
      dataSource.dataPoints.forEach(dataPoint => {
        if (dataPoint.dataType === 'number') {
          const min = dataPoint.min || 0
          const max = dataPoint.max || 100
          const range = max - min
          const variation = range * 0.05
          
          let newValue = (dataPoint.value as number) + (Math.random() - 0.5) * variation
          newValue = Math.max(min, Math.min(max, newValue))
          
          this.updateDataPoint(dataSource.id, dataPoint.id, Math.round(newValue * 100) / 100)
        }
      })
    }, dataSource.config.interval || 2000)
    
    this.intervals.set(dataSource.id, interval)
    dataSource.status = 'connected'
    return true
  }
  
  // HTTP数据源连接
  private async connectHttp(dataSource: EnhancedDataSource): Promise<boolean> {
    const fetchData = async () => {
      try {
        const response = await fetch(dataSource.config.url!, {
          method: dataSource.config.method || 'GET',
          headers: dataSource.config.headers,
          body: dataSource.config.method !== 'GET' ? JSON.stringify(dataSource.config.params) : undefined
        })
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const data = await response.json()
        this.processHttpData(dataSource, data)
        dataSource.status = 'connected'
        dataSource.lastUpdate = Date.now()
      } catch (error) {
        dataSource.status = 'error'
        dataSource.errorMessage = error instanceof Error ? error.message : 'HTTP request failed'
      }
    }
    
    // 立即执行一次
    await fetchData()
    
    // 设置定时器
    if (dataSource.config.interval) {
      const interval = setInterval(fetchData, dataSource.config.interval)
      this.intervals.set(dataSource.id, interval)
    }
    
    return dataSource.status === 'connected'
  }
  
  // WebSocket数据源连接
  private connectWebSocket(dataSource: EnhancedDataSource): boolean {
    try {
      const ws = new WebSocket(dataSource.config.wsUrl!, dataSource.config.protocols)
      
      ws.onopen = () => {
        dataSource.status = 'connected'
        dataSource.lastUpdate = Date.now()
      }
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.processWebSocketData(dataSource, data)
          dataSource.lastUpdate = Date.now()
        } catch (error) {
          console.error('WebSocket data parsing error:', error)
        }
      }
      
      ws.onerror = () => {
        dataSource.status = 'error'
        dataSource.errorMessage = 'WebSocket connection error'
      }
      
      ws.onclose = () => {
        dataSource.status = 'disconnected'
      }
      
      this.connections.set(dataSource.id, ws)
      return true
    } catch (error) {
      dataSource.status = 'error'
      dataSource.errorMessage = error instanceof Error ? error.message : 'WebSocket connection failed'
      return false
    }
  }
  
  // MQTT数据源连接（需要MQTT客户端库）
  private connectMqtt(dataSource: EnhancedDataSource): boolean {
    // 这里需要引入MQTT客户端库，如 mqtt.js
    // 由于这是示例，我们模拟连接过程
    console.log('MQTT connection not implemented in demo')
    dataSource.status = 'connected'
    return true
  }
  
  // OPC UA数据源连接（需要OPC UA客户端库）
  private connectOpcUa(dataSource: EnhancedDataSource): boolean {
    // 这里需要引入OPC UA客户端库
    console.log('OPC UA connection not implemented in demo')
    dataSource.status = 'connected'
    return true
  }
  
  // Modbus数据源连接（需要Modbus客户端库）
  private connectModbus(dataSource: EnhancedDataSource): boolean {
    // 这里需要引入Modbus客户端库
    console.log('Modbus connection not implemented in demo')
    dataSource.status = 'connected'
    return true
  }
  
  // 处理HTTP数据
  private processHttpData(dataSource: EnhancedDataSource, data: any) {
    dataSource.dataPoints.forEach(dataPoint => {
      const value = this.extractValueFromPath(data, dataPoint.address || dataPoint.id)
      if (value !== undefined) {
        this.updateDataPoint(dataSource.id, dataPoint.id, value)
      }
    })
  }
  
  // 处理WebSocket数据
  private processWebSocketData(dataSource: EnhancedDataSource, data: any) {
    // 类似HTTP数据处理
    this.processHttpData(dataSource, data)
  }
  
  // 从路径提取值
  private extractValueFromPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current && current[key], obj)
  }
  
  // 更新数据点
  updateDataPoint(dataSourceId: string, dataPointId: string, value: any) {
    const dataSource = this.dataSources.get(dataSourceId)
    if (!dataSource) return
    
    const dataPoint = dataSource.dataPoints.find(dp => dp.id === dataPointId)
    if (!dataPoint) return
    
    // 应用数据转换
    const transformedValue = this.applyTransforms(dataSourceId, dataPointId, value)
    
    dataPoint.value = transformedValue
    dataPoint.timestamp = Date.now()
    dataPoint.quality = 'good'
    
    // 检查报警
    this.checkAlarms(dataPointId, transformedValue)
    
    // 通知订阅者
    const callbacks = this.subscribers.get(dataPointId)
    if (callbacks) {
      callbacks.forEach(callback => callback(dataPoint))
    }
  }
  
  // 应用数据转换
  private applyTransforms(dataSourceId: string, dataPointId: string, value: any): any {
    const rules = this.transformRules.get(`${dataSourceId}.${dataPointId}`)
    if (!rules) return value
    
    let result = value
    for (const rule of rules) {
      switch (rule.transform) {
        case 'scale':
          if (typeof result === 'number' && rule.params?.scale) {
            result = result * rule.params.scale
          }
          break
        case 'offset':
          if (typeof result === 'number' && rule.params?.offset) {
            result = result + rule.params.offset
          }
          break
        case 'formula':
          if (rule.params?.formula) {
            // 简单的公式计算（实际应用中需要更安全的表达式解析器）
            try {
              result = eval(rule.params.formula.replace('x', result.toString()))
            } catch (error) {
              console.error('Formula evaluation error:', error)
            }
          }
          break
        case 'mapping':
          if (rule.params?.mapping) {
            result = rule.params.mapping[result] || result
          }
          break
      }
    }
    
    return result
  }
  
  // 检查报警
  private checkAlarms(dataPointId: string, value: any) {
    const rules = this.alarmRules.get(dataPointId)
    if (!rules) return
    
    rules.forEach(rule => {
      if (!rule.enabled) return
      
      let triggered = false
      
      switch (rule.type) {
        case 'threshold':
          triggered = this.evaluateCondition(value, rule.condition)
          break
        case 'range':
          triggered = this.evaluateCondition(value, rule.condition)
          break
        // 其他报警类型...
      }
      
      if (triggered) {
        const alarm: AlarmEvent = {
          id: `alarm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          ruleId: rule.id,
          dataPointId,
          timestamp: Date.now(),
          severity: rule.severity,
          message: rule.message.replace('{value}', value.toString()),
          value,
          acknowledged: false
        }
        
        this.alarmEvents.value.push(alarm)
        this.notifyAlarmSubscribers(alarm)
      }
    })
  }
  
  // 评估条件
  private evaluateCondition(value: any, condition: AlarmRule['condition']): boolean {
    switch (condition.operator) {
      case '>': return value > condition.value
      case '<': return value < condition.value
      case '>=': return value >= condition.value
      case '<=': return value <= condition.value
      case '==': return value == condition.value
      case '!=': return value != condition.value
      case 'between': return value >= condition.value && value <= (condition.value2 || 0)
      case 'outside': return value < condition.value || value > (condition.value2 || 0)
      default: return false
    }
  }
  
  // 订阅数据点
  subscribe(dataPointId: string, callback: (data: EnhancedDataPoint) => void) {
    if (!this.subscribers.has(dataPointId)) {
      this.subscribers.set(dataPointId, new Set())
    }
    this.subscribers.get(dataPointId)!.add(callback)
    
    return () => {
      const callbacks = this.subscribers.get(dataPointId)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          this.subscribers.delete(dataPointId)
        }
      }
    }
  }
  
  // 订阅报警
  subscribeAlarms(callback: (alarm: AlarmEvent) => void) {
    this.alarmSubscribers.add(callback)
    
    return () => {
      this.alarmSubscribers.delete(callback)
    }
  }
  
  // 通知报警订阅者
  private notifyAlarmSubscribers(alarm: AlarmEvent) {
    this.alarmSubscribers.forEach(callback => callback(alarm))
  }
  
  // 断开数据源
  disconnectDataSource(id: string) {
    const dataSource = this.dataSources.get(id)
    if (!dataSource) return
    
    // 清理连接
    const connection = this.connections.get(id)
    if (connection) {
      if (connection.close) connection.close()
      this.connections.delete(id)
    }
    
    // 清理定时器
    const interval = this.intervals.get(id)
    if (interval) {
      clearInterval(interval)
      this.intervals.delete(id)
    }
    
    dataSource.status = 'disconnected'
  }
  
  // 添加报警规则
  addAlarmRule(dataPointId: string, rule: AlarmRule) {
    if (!this.alarmRules.has(dataPointId)) {
      this.alarmRules.set(dataPointId, [])
    }
    this.alarmRules.get(dataPointId)!.push(rule)
  }

  // 删除报警规则
  removeAlarmRule(dataPointId: string, ruleId: string) {
    const rules = this.alarmRules.get(dataPointId)
    if (rules) {
      const index = rules.findIndex(r => r.id === ruleId)
      if (index > -1) {
        rules.splice(index, 1)
      }
    }
  }

  // 获取报警规则
  getAlarmRules(dataPointId?: string): AlarmRule[] {
    if (dataPointId) {
      return this.alarmRules.get(dataPointId) || []
    }

    const allRules: AlarmRule[] = []
    this.alarmRules.forEach(rules => allRules.push(...rules))
    return allRules
  }

  // 获取报警事件
  getAlarmEvents(): AlarmEvent[] {
    return this.alarmEvents.value
  }

  // 确认报警
  acknowledgeAlarm(alarmId: string) {
    const alarm = this.alarmEvents.value.find(a => a.id === alarmId)
    if (alarm) {
      alarm.acknowledged = true
    }
  }

  // 清除报警
  clearAlarm(alarmId: string) {
    const index = this.alarmEvents.value.findIndex(a => a.id === alarmId)
    if (index > -1) {
      this.alarmEvents.value.splice(index, 1)
    }
  }

  // 添加数据转换规则
  addTransformRule(dataSourceId: string, dataPointId: string, rule: DataTransformRule) {
    const key = `${dataSourceId}.${dataPointId}`
    if (!this.transformRules.has(key)) {
      this.transformRules.set(key, [])
    }
    this.transformRules.get(key)!.push(rule)
  }

  // 删除数据转换规则
  removeTransformRule(dataSourceId: string, dataPointId: string, ruleId: string) {
    const key = `${dataSourceId}.${dataPointId}`
    const rules = this.transformRules.get(key)
    if (rules) {
      const index = rules.findIndex(r => r.id === ruleId)
      if (index > -1) {
        rules.splice(index, 1)
      }
    }
  }

  // 销毁服务
  destroy() {
    this.intervals.forEach(interval => clearInterval(interval))
    this.connections.forEach(connection => {
      if (connection.close) connection.close()
    })
    this.intervals.clear()
    this.connections.clear()
    this.subscribers.clear()
    this.alarmSubscribers.clear()
    this.dataSources.clear()
  }
}

// 创建单例实例
export const enhancedDataService = new EnhancedDataService()

// 导出类型
export type { EnhancedDataService }
