<template>
  <el-config-provider :locale="appStore.locale" :size="size">
    <router-view />
    <!-- end chatgpt -->
    <Chatgpt />
  </el-config-provider>
</template>

<script setup lang="ts">
import useSettingsStore from '@/store/modules/settings'
import { handleThemeStyle } from '@/utils/theme'
import useAppStore from '@/store/modules/app'
import { getToken } from '@/utils/auth'
import { useRouter } from 'vue-router'
import WebSocketSingleton from '@/utils/socket_service'
// import { ElNotification } from 'element-plus'
// import { Chatgpt } from './components/chatgpt.vue'
const router = useRouter()
const appStore = useAppStore()
const size = computed(() => appStore.size as any)
onMounted(() => {
  // console.log('onMounted')
  const webSocketInstance = WebSocketSingleton.getInstance()
  webSocketInstance.connect()
  webSocketInstance.ws?.addEventListener('message', handleMessage)
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme)
  })
})
function fixJsonString(str: string): string {
  return str
    .replace(/([{,]\s*)([A-Za-z0-9_]+)\s*:/g, '$1"$2":') // 给键添加双引号
    .replace(/'/g, '"') // 将单引号替换为双引号
}
const handleMessage = (event: MessageEvent) => {
  let dataString = event.data
  let dataObject

  // 检查数据类型
  if (typeof dataString === 'string') {
    // 修复可能存在的问题，使其成为有效的 JSON 字符串
    const fixedDataString = fixJsonString(dataString)
    try {
      dataObject = JSON.parse(fixedDataString)
    } catch (e) {
      console.error('无法将消息数据解析为JSON', e)
      return
    }
  } else if (typeof dataString === 'object') {
    dataObject = dataString
  } else {
    console.error('不支持的数据类型', typeof dataString)
    return
  }

  const isPropertyMessage = dataObject.MESSAGETYPE === 'alarm'
  const hasMessageContent = !!dataObject.MESSAGECONTENT
  if (isPropertyMessage && hasMessageContent) {
    const { powerUnitNames, propertyName, name, level, value, lastOccurTime, alarmCount } = dataObject.MESSAGECONTENT

    let backgroundColor = '#fff' // yellow

    if (level === 3 || level === 6 ||level === 5 || level === 2) {
      backgroundColor = '#f56c6c' // red
    } else{
      backgroundColor = '#e6a23c' // green
    }

    const uniqueClass = `custom-notification-${Date.now()}`
    if (window.currentNotification) {
      window.currentNotification.close()
    }
    window.currentNotification = ElNotification({
      position: 'bottom-right',
      // offset: 100,
      dangerouslyUseHTMLString: true,
      type: 'warning',
      title: `${powerUnitNames === null ? '设备名称查找失败' : powerUnitNames} ${propertyName === null ? '' : propertyName}${value === null ? '' : (value)}`,
      // message: `${name}<br><small>${lastOccurTime}</small>`,
      message: `<div><div>${name}<span class="notification-badge">${alarmCount > 99 ? '99+' : alarmCount}</span></div><div>${lastOccurTime}</div></div> `,
      // duration: 10000,//10秒
      // group: 'myGroup',
      duration: 0,
      customClass: uniqueClass,
      onClick: () => {
        router.push({
          path: '/alarm/list',
        })
        window.currentNotification.close()
      },
    })

    const existingStyle = document.querySelector(`style[data-class="${uniqueClass}"]`)
    if (existingStyle) {
      existingStyle.remove()
    }

    const style = document.createElement('style')
    style.setAttribute('data-class', uniqueClass)
    style.innerHTML = `
      .${uniqueClass} {
        background-color: ${backgroundColor} !important;
        border: none !important;
      }
      .${uniqueClass} .el-notification__title,
      .${uniqueClass} .el-notification__content,
      .${uniqueClass} .el-notification--warning,
      .${uniqueClass} .el-notification__closeBtn {
        color: #fff !important;
      }
    `
    document.head.appendChild(style)
  }
}
// onBeforeUnmount(() => {
//   // 获取 WebSocket 实例，并断开连接
//   const webSocketInstance = WebSocketSingleton.getInstance()
//   webSocketInstance.ws?.removeEventListener('message', handleMessage)
//   webSocketInstance.disconnect()
// })
</script>

<style lang="scss">
body {
  font-family: 'Noto Sans CJK SC';
  src: url('@/assets/fonts/NotoSansSC-VariableFont_wght.ttf') format('truetype');
}
.el-table__body-wrapper .el-table-column--selection > .cell {
  display: block;
}
.notification-badge {
  background-color: red;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  text-align: center;
  align-items: center;
  position: absolute;
  top: 41px;
  right: 291px;
  // transform: translate(15%, 0);
}
</style>
<style scoped>
:deep(.el-notification) {
  border: none !important;
  background-color: #e6a23c !important;
}
</style>
