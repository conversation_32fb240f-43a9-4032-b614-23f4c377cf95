<template>
  <div class="model3d-component" ref="containerRef">
    <div v-if="!loaded && !error" class="loading-container">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    <div v-if="error" class="error-container">
      <el-icon class="error-icon"><WarningFilled /></el-icon>
      <span>{{ errorMessage }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import type { ConfigurationComponent } from '../../types'
import { dataService } from '../../services/dataService'

const props = defineProps<{
  component: ConfigurationComponent
  editing?: boolean
}>()

// 获取样式配置
const getStyleValue = (key: string, defaultValue: any) => {
  const style = props.component.style as any
  return style?.[key] || defaultValue
}

// 容器引用
const containerRef = ref<HTMLElement | null>(null)

// 状态
const loaded = ref(false)
const error = ref(false)
const errorMessage = ref('加载失败')

// Three.js 对象
let scene: THREE.Scene | null = null
let camera: THREE.PerspectiveCamera | null = null
let renderer: THREE.WebGLRenderer | null = null
let controls: OrbitControls | null = null
let animationFrameId: number | null = null

// 模型对象
let model: THREE.Object3D | null = null
let liquidMesh: THREE.Mesh | null = null
let dataUnsubscribe: (() => void) | null = null
let resizeObserver: ResizeObserver | null = null

// 初始化 Three.js
const initThree = () => {
  if (!containerRef.value) return
  
  try {
    // 创建场景
    scene = new THREE.Scene()
    scene.background = new THREE.Color(0xf0f0f0)
    
    // 创建相机
    let width = containerRef.value.clientWidth
    let height = containerRef.value.clientHeight

    // 如果容器尺寸为0，使用组件的尺寸
    if (width <= 0) width = props.component.width
    if (height <= 0) height = props.component.height

    camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
    camera.position.z = 5

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true })
    renderer.setSize(width, height)
    renderer.setPixelRatio(window.devicePixelRatio)
    containerRef.value.appendChild(renderer.domElement)
    
    // 添加控制器
    if (camera && renderer) {
      controls = new OrbitControls(camera, renderer.domElement)
      controls.enableDamping = true
      controls.dampingFactor = 0.25
      controls.enableZoom = true
      controls.enablePan = !props.editing // 编辑模式下禁用平移
    }
    
    // 添加灯光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
    scene.add(ambientLight)
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(1, 1, 1)
    scene.add(directionalLight)
    
    // 加载模型
    loadModel()
    
    // 开始动画循环
    animate()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)

    // 使用ResizeObserver监听容器尺寸变化
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        handleResize()
      })
      resizeObserver.observe(containerRef.value)
    }
  } catch (err) {
    console.error('初始化 Three.js 失败', err)
    error.value = true
    errorMessage.value = '初始化失败'
  }
}

// 加载模型
const loadModel = () => {
  if (!scene) return
  
  const { data } = props.component
  const modelType = data.static?.model || 'tank'
  
  try {
    // 清除现有模型
    if (model) {
      scene.remove(model)
      model = null
    }
    
    // 根据模型类型创建不同的几何体
    switch (modelType) {
      case 'tank':
        createTank()
        break
      case 'pump':
        createPump()
        break
      case 'valve':
        createValve()
        break
      case 'pipe':
        createPipe()
        break
      default:
        createDefaultModel()
    }
    
    loaded.value = true
  } catch (err) {
    console.error('加载模型失败', err)
    error.value = true
    errorMessage.value = '加载模型失败'
  }
}

// 创建储罐模型
const createTank = () => {
  if (!scene) return

  const { data } = props.component
  let fillLevel = data.static?.fillLevel || 50

  // 创建储罐容器
  const tankGroup = new THREE.Group()

  // 创建储罐外壳
  const tankGeometry = new THREE.CylinderGeometry(1, 1, 2, 32)
  const tankColor = getStyleValue('tankColor', '#999999')
  const tankMaterial = new THREE.MeshPhongMaterial({
    color: tankColor,
    transparent: true,
    opacity: getStyleValue('tankOpacity', 0.7)
  })
  const tank = new THREE.Mesh(tankGeometry, tankMaterial)
  tankGroup.add(tank)

  // 创建储罐顶部
  const topGeometry = new THREE.CylinderGeometry(1, 1, 0.1, 32)
  const topColor = getStyleValue('tankTopColor', '#777777')
  const topMaterial = new THREE.MeshPhongMaterial({ color: topColor })
  const top = new THREE.Mesh(topGeometry, topMaterial)
  top.position.y = 1.05
  tankGroup.add(top)

  // 创建储罐底部
  const bottomGeometry = new THREE.CylinderGeometry(1, 1, 0.1, 32)
  const bottomColor = getStyleValue('tankBottomColor', '#777777')
  const bottomMaterial = new THREE.MeshPhongMaterial({ color: bottomColor })
  const bottom = new THREE.Mesh(bottomGeometry, bottomMaterial)
  bottom.position.y = -1.05
  tankGroup.add(bottom)

  // 创建液体
  const liquidHeight = (fillLevel / 100) * 2
  const liquidGeometry = new THREE.CylinderGeometry(0.95, 0.95, liquidHeight, 32)
  const liquidColor = getStyleValue('liquidColor', '#3399ff')
  const liquidMaterial = new THREE.MeshPhongMaterial({ color: liquidColor })
  liquidMesh = new THREE.Mesh(liquidGeometry, liquidMaterial)
  liquidMesh.position.y = -1 + liquidHeight / 2
  tankGroup.add(liquidMesh)

  // 设置数据绑定
  setupDataBinding()

  // 添加到场景
  scene.add(tankGroup)
  model = tankGroup
}

// 设置数据绑定
const setupDataBinding = () => {
  const { data } = props.component

  // 如果是动态数据源，订阅数据变化
  if (data.static?.dataSource === 'dynamic' && data.static?.dataPointId) {
    const dataPointId = data.static.dataPointId

    // 订阅数据点变化
    dataUnsubscribe = dataService.subscribe(dataPointId, (dataPoint) => {
      updateLiquidLevel(dataPoint.value, data.static?.maxValue || 1000)
    })

    // 获取初始值
    const tankDataSource = dataService.getDataSource('tank-levels')
    if (tankDataSource) {
      const dataPoint = tankDataSource.dataPoints.find(dp => dp.id === dataPointId)
      if (dataPoint) {
        updateLiquidLevel(dataPoint.value, data.static?.maxValue || 1000)
      }
    }
  }
}

// 更新液位
const updateLiquidLevel = (currentValue: number, maxValue: number) => {
  if (!liquidMesh) return

  const fillPercentage = Math.min(100, Math.max(0, (currentValue / maxValue) * 100))
  const liquidHeight = (fillPercentage / 100) * 2

  // 更新液体几何体
  const newGeometry = new THREE.CylinderGeometry(0.95, 0.95, liquidHeight, 32)
  liquidMesh.geometry.dispose()
  liquidMesh.geometry = newGeometry
  liquidMesh.position.y = -1 + liquidHeight / 2

  // 根据液位改变颜色
  const hue = (fillPercentage / 100) * 0.6 // 从红色(0)到青色(0.6)
  if (liquidMesh.material instanceof THREE.MeshPhongMaterial) {
    liquidMesh.material.color.setHSL(hue, 0.8, 0.5)
  }
}

// 创建水泵模型
const createPump = () => {
  if (!scene) return
  
  const { data } = props.component
  const status = data.static?.status || 'running'
  
  // 创建水泵组
  const pumpGroup = new THREE.Group()
  
  // 创建水泵主体
  const bodyGeometry = new THREE.BoxGeometry(1.5, 0.8, 1)
  const bodyColor = getStyleValue('pumpBodyColor', '#3366cc')
  const bodyMaterial = new THREE.MeshPhongMaterial({ color: bodyColor })
  const body = new THREE.Mesh(bodyGeometry, bodyMaterial)
  pumpGroup.add(body)

  // 创建进水管
  const inletGeometry = new THREE.CylinderGeometry(0.2, 0.2, 1, 16)
  const pipeColor = getStyleValue('pumpPipeColor', '#999999')
  const inletMaterial = new THREE.MeshPhongMaterial({ color: pipeColor })
  const inlet = new THREE.Mesh(inletGeometry, inletMaterial)
  inlet.rotation.z = Math.PI / 2
  inlet.position.x = -1
  pumpGroup.add(inlet)

  // 创建出水管
  const outletGeometry = new THREE.CylinderGeometry(0.2, 0.2, 1, 16)
  const outletMaterial = new THREE.MeshPhongMaterial({ color: pipeColor })
  const outlet = new THREE.Mesh(outletGeometry, outletMaterial)
  outlet.rotation.z = Math.PI / 2
  outlet.position.x = 1
  pumpGroup.add(outlet)

  // 创建电机
  const motorGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.6, 16)
  const runningColor = getStyleValue('pumpRunningColor', '#00cc00')
  const stoppedColor = getStyleValue('pumpStoppedColor', '#cc0000')
  const motorMaterial = new THREE.MeshPhongMaterial({
    color: status === 'running' ? runningColor : stoppedColor
  })
  const motor = new THREE.Mesh(motorGeometry, motorMaterial)
  motor.rotation.z = Math.PI / 2
  motor.position.y = 0.7
  pumpGroup.add(motor)
  
  // 添加到场景
  scene.add(pumpGroup)
  model = pumpGroup
}

// 创建阀门模型
const createValve = () => {
  if (!scene) return
  
  const { data } = props.component
  const status = data.static?.status || 'open'
  const isOpen = status === 'open'
  
  // 创建阀门组
  const valveGroup = new THREE.Group()
  
  // 创建阀门主体
  const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.5, 16)
  const bodyColor = getStyleValue('valveBodyColor', '#666666')
  const bodyMaterial = new THREE.MeshPhongMaterial({ color: bodyColor })
  const body = new THREE.Mesh(bodyGeometry, bodyMaterial)
  valveGroup.add(body)

  // 创建阀门手柄
  const handleGeometry = new THREE.BoxGeometry(0.1, 0.1, 1)
  const openColor = getStyleValue('valveOpenColor', '#00ff00')
  const closedColor = getStyleValue('valveClosedColor', '#ff0000')
  const handleMaterial = new THREE.MeshPhongMaterial({
    color: isOpen ? openColor : closedColor
  })
  const handle = new THREE.Mesh(handleGeometry, handleMaterial)
  handle.position.y = 0.3
  handle.rotation.x = isOpen ? 0 : Math.PI / 2
  valveGroup.add(handle)

  // 创建管道
  const pipeGeometry = new THREE.CylinderGeometry(0.2, 0.2, 2, 16)
  const pipeColor = getStyleValue('valvePipeColor', '#999999')
  const pipeMaterial = new THREE.MeshPhongMaterial({ color: pipeColor })
  const pipe = new THREE.Mesh(pipeGeometry, pipeMaterial)
  pipe.rotation.z = Math.PI / 2
  valveGroup.add(pipe)
  
  // 添加到场景
  scene.add(valveGroup)
  model = valveGroup
}

// 创建管道模型
const createPipe = () => {
  if (!scene) return
  
  const { data } = props.component
  const flow = data.static?.flow || false
  const flowDirection = data.static?.flowDirection || 'right'
  const flowSpeed = data.static?.flowSpeed || 5
  
  // 创建管道组
  const pipeGroup = new THREE.Group()
  
  // 创建管道
  const pipeGeometry = new THREE.CylinderGeometry(0.2, 0.2, 3, 16)
  const pipeMaterial = new THREE.MeshPhongMaterial({ color: 0x999999 })
  const pipe = new THREE.Mesh(pipeGeometry, pipeMaterial)
  pipe.rotation.z = Math.PI / 2
  pipeGroup.add(pipe)
  
  // 如果有流动，添加流动指示器
  if (flow) {
    const arrowCount = 5
    const arrowSpacing = 0.5
    
    for (let i = 0; i < arrowCount; i++) {
      const arrowGeometry = new THREE.ConeGeometry(0.1, 0.2, 8)
      const arrowMaterial = new THREE.MeshPhongMaterial({ color: 0x3399ff })
      const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial)
      
      // 设置箭头方向
      if (flowDirection === 'right') {
        arrow.rotation.z = -Math.PI / 2
        arrow.position.x = -1.5 + i * arrowSpacing
      } else {
        arrow.rotation.z = Math.PI / 2
        arrow.position.x = 1.5 - i * arrowSpacing
      }
      
      arrow.position.y = 0.3
      pipeGroup.add(arrow)
    }
  }
  
  // 添加到场景
  scene.add(pipeGroup)
  model = pipeGroup
}

// 创建默认模型
const createDefaultModel = () => {
  if (!scene) return
  
  // 创建一个简单的立方体
  const geometry = new THREE.BoxGeometry(1, 1, 1)
  const material = new THREE.MeshPhongMaterial({ color: 0x00ff00 })
  const cube = new THREE.Mesh(geometry, material)
  
  scene.add(cube)
  model = cube
}

// 处理窗口大小变化
const handleResize = () => {
  if (!containerRef.value || !camera || !renderer) return

  let width = containerRef.value.clientWidth
  let height = containerRef.value.clientHeight

  // 如果容器尺寸为0，使用组件的尺寸
  if (width <= 0) width = props.component.width
  if (height <= 0) height = props.component.height

  // 确保尺寸有效
  if (width <= 0 || height <= 0) return

  camera.aspect = width / height
  camera.updateProjectionMatrix()

  renderer.setSize(width, height)

  // 立即重新渲染
  if (scene && camera) {
    renderer.render(scene, camera)
  }
}

// 动画循环
const animate = () => {
  animationFrameId = requestAnimationFrame(animate)
  
  if (controls) {
    controls.update()
  }
  
  if (renderer && scene && camera) {
    renderer.render(scene, camera)
  }
}

// 清理资源
const cleanup = () => {
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId)
  }
  
  if (renderer) {
    if (containerRef.value) {
      containerRef.value.removeChild(renderer.domElement)
    }
    renderer.dispose()
  }
  
  window.removeEventListener('resize', handleResize)

  // 清理ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }

  // 清理数据订阅
  if (dataUnsubscribe) {
    dataUnsubscribe()
    dataUnsubscribe = null
  }

  scene = null
  camera = null
  renderer = null
  controls = null
  model = null
  liquidMesh = null
}

// 监听组件数据变化
watch(() => props.component.data, () => {
  if (scene) {
    loadModel()
  }
}, { deep: true })

// 防抖定时器
let resizeTimer: number | null = null

// 监听组件尺寸变化
watch(() => [props.component.width, props.component.height], () => {
  // 防抖处理，避免频繁调用
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }

  resizeTimer = setTimeout(() => {
    nextTick(() => {
      handleResize()
    })
  }, 16) // 约60fps的更新频率
}, { immediate: false })

// 组件挂载时初始化
onMounted(() => {
  // 延迟初始化以确保DOM完全渲染
  nextTick(() => {
    initThree()
  })
})

// 组件卸载时清理
onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
.model3d-component {
  width: 100%;
  height: 100%;
  position: relative;
}

.loading-container, .error-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1;
}

.loading-icon {
  font-size: 32px;
  animation: rotate 1s linear infinite;
}

.error-icon {
  font-size: 32px;
  color: #F56C6C;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>