<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form ref="queryFormRef"  :model="queryParams" :inline="true" label-width="70">
            <el-form-item label="项目名称" prop="projectId">
              <el-select v-model="queryParams.projectId" value-key="" placeholder="请选择项目名称" clearable filterable >
                  <el-option v-for="project in projects"
                  :key="project.id"
                  :label="project.name"
                  :value="project.id">
                  </el-option>
              </el-select>               
            </el-form-item>
            <el-form-item label="产品key" prop="productKey" >
                <el-input v-model="queryParams.productKeyLike" placeholder="请输入产品key"  clearable />
            </el-form-item>
            <el-form-item>
               <el-button type="primary" icon="Search"  @click="handleQuery">搜索</el-button>
               <el-button  size="Refresh" @click="resetQuery">重置</el-button>           
            </el-form-item>
        </el-form>
      </div>  
    </transition>

    <el-card shadow="never">
        <!--操作-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
              <el-button plain size="default" icon="Plus" @click="handleAdd">新增</el-button>
          </el-col> 
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
        </el-row>

        <!-- 表格 -->
        <el-table :data="configList" border v-loading="loading">
          <el-table-column label="序号" type="index" align="center" width="80" />
          <el-table-column prop="productKey" label="产品key"   width="180"/>
          <el-table-column prop="productName" label="产品名称"  width="180"/>
          <el-table-column prop="url" label="url" />
          <el-table-column prop="description" label="描述" />
           <el-table-column label="操作" width="180" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-tooltip content="修改" placement="top" >
                  <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" />
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" />
                </el-tooltip>
                <el-tooltip content="拷贝" placement="top">
                  <el-button link type="primary" icon="CopyDocument" @click="handleCopy(scope.row)" />
                </el-tooltip>
              </template>
            </el-table-column>
        </el-table>
        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
    </el-card>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" @close="closeDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close>
      <el-form :model="form" ref="operateFromRef" :rules="rules" label-width="100px" :inline="false" size="normal">
        <el-row>
          <el-col :span="12">
              <el-form-item label="项目" prop="projectId">
                <el-select v-model="form.projectId" placeholder="请选择项目名称" clearable filterable  @change="onChangeProject">
                  <el-option v-for="project in projects" :label="project.name" :value="project.id" :key="project.id" ></el-option>
                </el-select>
              </el-form-item>
          </el-col>

          <el-col :span="12">
              <el-form-item label="产品" prop="productKey">
                <el-select v-model="form.productKey" placeholder="请选择产品" clearable filterable >
                  <el-option v-for="device in devices" :label="device.productName" :value="device.productKey" :key="device.deviceId"></el-option>
                </el-select>
              </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
              <el-form-item label="第三方类型" prop="projectId">
                <el-select v-model="form.thirdPartyType" placeholder="请选择第三方类型" clearable filterable >
                  <el-option v-for="dict in product_third_party_type" :key="dict.value" :label="dict.label" :value="Number(dict.value)" />
                </el-select>
              </el-form-item>
          </el-col>

          <el-col :span="12">
              <el-form-item label="url" prop="url">
                <el-input v-model="form.url"  placeholder="请输入url" />
              </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="请求体" prop="body">
              <el-input v-model="form.body" type="textarea" placeholder="请输入请求体" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注"  prop="description">
              <el-input v-model="form.description" type="textarea" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <template #footer>
      <span>
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </span>
      </template>
    </el-dialog>
    
  </div>
  
</template>

<script setup lang='ts' name='thirdPartyConfig'>
  import { ThirdPartyConfigQuery, ThirdPartyConfigForm, ThirdPartyConfigVO} from '@/api/product/type'
  import { listThirdPartyConfig, addThirdPartyConfig, updateThirdPartyConfig, deleteThirdPartyConfig, copyThirdPartyConfig } from '@/api/product'
  
  import { projectAll } from '@/api/project/api'
  import { SimpleProjectVo, SimpleProjects } from '@/api/project/types'

  import { FormInstance } from 'element-plus'
  import { ComponentInternalInstance } from 'vue'

  import { getTabsList } from '@/views/smartOperations/pEcharts/index.api'
  import { to } from 'await-to-js'


  const { proxy } = getCurrentInstance() as ComponentInternalInstance
  const { product_third_party_type } = toRefs<any>(proxy?.useDict('product_third_party_type'))

  const showSearch = ref(true)
  const queryFormRef = ref<FormInstance>()
  
  
  const projects = ref<SimpleProjects>()
  const loading = ref(false)
  const total = ref(0)
  const configList = ref<ThirdPartyConfigVO[]>()
  
  const getAllProject = async () => {
      const res = await projectAll()
      projects.value = res.data
      
  }

  const initFormData: ThirdPartyConfigForm = {
      id: undefined,
      projectId: '',
      productKey: '',
      thirdPartyType: undefined,
      url: '',
      body: '',
      description: '',
  }

  const data = reactive<PageData<ThirdPartyConfigForm, ThirdPartyConfigQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
    rules: {
      projectId: [
        { required: true, message: '请选择项目', trigger: 'blur' }
      ],
      productKey: [{ required: true, message: '请选择产品', trigger: 'blur' }],
      url: [{ required: true, message: '请输入url', trigger: 'blur' }],
      thirdPartyType: [{ required: true, message: '请选择第三方类型', trigger: 'blur' }]
    },
  })

  const { queryParams, form, rules } = toRefs<PageData<ThirdPartyConfigForm, ThirdPartyConfigQuery>>(data)


  const getList = async () =>{
    loading.value = true
    const res: any = await listThirdPartyConfig(queryParams.value)
    loading.value = false
    
    configList.value = res.data.rows
    total.value = res.data.total
   
  }

  const handleQuery = ()=>{
     queryParams.value.pageNum = 1
     getList()
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields()
    queryParams.value.pageNum = 1
    handleQuery()
  }

  onMounted(()=>{
    getAllProject()
    getList()
  })
  
  const devices = ref<any>([])

  const changeProject = async ()=>{
    if (!form.value.projectId){
      return;
    }

    //查询此项目下的设备列表
    const res = await getTabsList(form.value.projectId)
    devices.value = res.data 
  }

  const handleAdd = ()=>{
    dialog.visible = true
    dialog.title = '新增配置'
    nextTick(()=>{
        changeProject()
    })
  }

  const onChangeProject = ()=>{
    changeProject()
    form.value.productKey = ''

  }
  const handleUpdate = (row: ThirdPartyConfigForm)=>{
    dialog.visible = true
    dialog.title = '修改配置'
    nextTick(()=>{
      Object.assign(form.value, row)
      changeProject()
    })
  }

  /** 删除按钮操作 */
const handleDelete = async (row?: ThirdPartyConfigForm) => {
  const ids = [row?.id!]
  const [err] = await to(proxy?.$modal.confirm('是否确认删除此配置？') as any)
 
  if (!err && ids) {
    await deleteThirdPartyConfig(ids)

    queryParams.value.pageNum = 1
    await getList()
    proxy?.$modal.msgSuccess('删除成功')
  }
}

const handleCopy =  async (row?: ThirdPartyConfigForm) => {
  const id = <number>row?.id
  const [err] = await to(proxy?.$modal.confirm('是否确定拷贝此配置？') as any)
 
  if (!err && id) {
    await copyThirdPartyConfig(id)

    queryParams.value.pageNum = 1
    await getList()
    proxy?.$modal.msgSuccess('复制成功')
  }
}

  const dialog = reactive<DialogOption>({
    visible: false,
    title: '',
  })

  const operateFromRef = ref<FormInstance>()
  
  //提交
  const submitForm = ()=>{
    operateFromRef.value?.validate(async (valid: boolean)=>{
        if (valid){
          form.value.id ? await updateThirdPartyConfig(form.value) :  await addThirdPartyConfig(form.value)
          proxy?.$modal.msgSuccess('操作成功')
          dialog.visible = false

          if (!form.value.id){
            queryParams.value.pageNum = 1
          }
          await getList()
        }
    })
  }

  /** 重置操作表单 */
const reset = () => {
  form.value = { ...initFormData }
  operateFromRef.value?.resetFields()
}
/** 取消按钮 */
const cancel = () => {
  reset()
  dialog.visible = false
}
 
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  border: none;
}

:deep(.el-card__body) {
  border: none;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

:deep(.el-table__cell) {
  // color: rgba(204, 204, 204, 1) !important;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}

:deep(.el-tree) {
  background-color: transparent;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #07355c;
}

:deep(.el-tree-node__expand-icon) {
  color: #fff;
}
:deep(.el-select__wrapper){
  width: 180px;
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
:deep(.el-tree-node__label) {
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}

:deep(.el-select__tags .el-tag--info) {
  background-color: #153059 !important;
}

:deep(.el-tag.el-tag--info) {
  color: #fff !important;
}
</style>

