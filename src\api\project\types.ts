export interface SimpleProjectVo {
  id: number | string
  name: string
}

export type SimpleProjects = SimpleProjectVo[]

export interface subSystemQuery {
  data: number
}

export interface subSystemVO {
  id?: string
  name?: string
  shareFlag?: number
  powerUnitIds?: string
  powerUnitNames?: string
  projectId?: number
}

export interface subSystemParamQuery {
  pageNum: number
  pageSize: number
  subSystemId: string
}
export interface subSystemParamVO {
  id?: number
  paramName?: string
  paramIdentifier?: number
  paramValue?: number
  unitName?: string
  subSystemId?: number
  subSystemName?: string
}

export interface subSystemParamAddVO {
  paramName?: string
  paramIdentifier?: number
  paramValue?: string
  unitName?: string
  subSystemId?: number
  subSystemName?: string
}
