<template>
  <div
    class="component-wrapper"
    :class="{ 
      'is-selected': selected, 
      'is-editing': editing,
      'is-locked': component.locked,
      'connection-mode': connectionMode
    }"
    :style="wrapperStyle"
    :data-component-id="component.id"
    @click.stop="onSelect"
    @mousedown="onMouseDown"
  >
    <!-- 组件内容 -->
    <component
      :is="componentMap[component.type]"
      :component="component"
      :editing="editing"
    />
    
    <!-- 连接点 - 只在连接模式或选中时显示 -->
    <template v-if="editing && (connectionMode || selected) && !component.locked">
      <!-- 9个连接点 -->
      <div 
        v-for="(point, index) in connectionPoints" 
        :key="index"
        class="connection-point"
        :class="[point.position, { 'active': activePoint === point.position }]"
        :style="point.style"
        @mousedown.stop="onConnectionStart($event, point.position)"
        @mouseenter="highlightPoint(point.position)"
        @mouseleave="unhighlightPoint"
      ></div>
    </template>
    
    <!-- 编辑模式下的控制点 -->
    <template v-if="editing && selected && !component.locked && !connectionMode">
      <!-- 调整大小的控制点 -->
      <div 
        v-for="(handle, index) in resizeHandles" 
        :key="index"
        class="resize-handle"
        :class="handle.class"
        @mousedown.stop="onResizeStart($event, handle.cursor)"
      ></div>
      
      <!-- 旋转控制点 -->
      <div 
        class="rotate-handle"
        @mousedown.stop="onRotateStart"
      >
        <el-icon><Refresh /></el-icon>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import type { ConfigurationComponent } from '../types'
import TextComponent from './components/TextComponent.vue'
import ImageComponent from './components/ImageComponent.vue'
import ShapeComponent from './components/ShapeComponent.vue'
import ChartComponent from './components/ChartComponent.vue'
import GaugeComponent from './components/GaugeComponent.vue'
import ButtonComponent from './components/ButtonComponent.vue'
import Model3dComponent from './components/Model3dComponent.vue'

// 组件类型映射
const componentMap = {
  text: TextComponent,
  image: ImageComponent,
  shape: ShapeComponent,
  chart: ChartComponent,
  gauge: GaugeComponent,
  button: ButtonComponent,
  model3d: Model3dComponent,
}

const props = defineProps<{
  component: ConfigurationComponent
  selected?: boolean
  editing?: boolean
  connectionMode?: boolean
}>()

const emit = defineEmits(['select', 'update', 'startConnection', 'finishConnection'])

// 当前高亮的连接点
const activePoint = ref<string | null>(null)

// 组件样式
const wrapperStyle = computed(() => {
  return {
    left: `${props.component.x}px`,
    top: `${props.component.y}px`,
    width: `${props.component.width}px`,
    height: `${props.component.height}px`,
    transform: `rotate(${props.component.rotation}deg)`,
    opacity: props.component.opacity,
    zIndex: props.component.z || 0,
    display: props.component.visible ? 'block' : 'none',
    cursor: getCursor()
  }
})

// 获取光标样式
const getCursor = () => {
  if (!props.editing || props.component.locked) return 'default'
  if (props.connectionMode) return 'crosshair'
  return 'move'
}

// 连接点配置
const connectionPoints = computed(() => [
  // 四个角点
  { position: 'top-left', style: { top: '-4px', left: '-4px' } },
  { position: 'top-right', style: { top: '-4px', right: '-4px' } },
  { position: 'bottom-left', style: { bottom: '-4px', left: '-4px' } },
  { position: 'bottom-right', style: { bottom: '-4px', right: '-4px' } },
  // 四个边中点
  { position: 'top', style: { top: '-4px', left: '50%', transform: 'translateX(-50%)' } },
  { position: 'right', style: { top: '50%', right: '-4px', transform: 'translateY(-50%)' } },
  { position: 'bottom', style: { bottom: '-4px', left: '50%', transform: 'translateX(-50%)' } },
  { position: 'left', style: { top: '50%', left: '-4px', transform: 'translateY(-50%)' } },
  // 中心点
  { position: 'center', style: { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' } }
])

// 调整大小的控制点
const resizeHandles = [
  { class: 'top-left', cursor: 'nwse-resize' },
  { class: 'top', cursor: 'ns-resize' },
  { class: 'top-right', cursor: 'nesw-resize' },
  { class: 'right', cursor: 'ew-resize' },
  { class: 'bottom-right', cursor: 'nwse-resize' },
  { class: 'bottom', cursor: 'ns-resize' },
  { class: 'bottom-left', cursor: 'nesw-resize' },
  { class: 'left', cursor: 'ew-resize' }
]

// 拖拽状态
const isDragging = ref(false)
const isResizing = ref(false)
const isRotating = ref(false)
const startX = ref(0)
const startY = ref(0)
const startWidth = ref(0)
const startHeight = ref(0)
const startRotation = ref(0)
const resizeCursor = ref('')
const startComponentX = ref(0)
const startComponentY = ref(0)

// 选择组件
const onSelect = (event: MouseEvent) => {
  if (props.connectionMode) {
    // 连接模式下，触发连接完成到中心点
    const point = getConnectionPointPosition('center')
    console.log('ComponentRenderer: 组件被点击，完成连接', props.component.id, point)
    emit('finishConnection', props.component.id, { point, anchor: 'center' })
    return
  }
  
  emit('select', props.component.id, event.shiftKey)
}

// 开始拖拽
const onMouseDown = (event: MouseEvent) => {
  if (!props.editing || props.component.locked || props.connectionMode) return
  
  // 如果没有选中，先选中
  if (!props.selected) {
    emit('select', props.component.id, event.shiftKey)
    return
  }
  
  isDragging.value = true
  startX.value = event.clientX
  startY.value = event.clientY
  startComponentX.value = props.component.x
  startComponentY.value = props.component.y
  
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

// 开始连接
const onConnectionStart = (event: MouseEvent, position: string) => {
  if (!props.connectionMode) return
  
  event.stopPropagation()
  event.preventDefault()
  
  console.log('ComponentRenderer: 开始连接', props.component.id, position)
  
  // 计算连接点的实际位置
  const point = getConnectionPointPosition(position)
  
  console.log('ComponentRenderer: 连接点位置', point)
  
  // 传递锚点位置信息
  emit('startConnection', props.component.id, { point, anchor: position })
}

// 获取连接点的实际位置
const getConnectionPointPosition = (position: string) => {
  const { x, y, width, height } = props.component
  
  switch (position) {
    case 'top-left':
      return { x, y }
    case 'top':
      return { x: x + width / 2, y }
    case 'top-right':
      return { x: x + width, y }
    case 'right':
      return { x: x + width, y: y + height / 2 }
    case 'bottom-right':
      return { x: x + width, y: y + height }
    case 'bottom':
      return { x: x + width / 2, y: y + height }
    case 'bottom-left':
      return { x, y: y + height }
    case 'left':
      return { x, y: y + height / 2 }
    case 'center':
      return { x: x + width / 2, y: y + height / 2 }
    default:
      return { x: x + width / 2, y: y + height / 2 }
  }
}

// 高亮连接点
const highlightPoint = (position: string) => {
  if (props.connectionMode) {
    activePoint.value = position
  }
}

// 取消高亮连接点
const unhighlightPoint = () => {
  activePoint.value = null
}

// 拖拽移动
const onMouseMove = (event: MouseEvent) => {
  if (isResizing.value) {
    handleResize(event)
  } else if (isRotating.value) {
    handleRotate(event)
  } else if (isDragging.value) {
    const dx = event.clientX - startX.value
    const dy = event.clientY - startY.value
    
    const updatedComponent = { ...props.component }
    updatedComponent.x = startComponentX.value + dx
    updatedComponent.y = startComponentY.value + dy
    
    emit('update', updatedComponent)
  }
}

// 结束拖拽
const onMouseUp = () => {
  isDragging.value = false
  isResizing.value = false
  isRotating.value = false
  
  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', onMouseUp)
}

// 开始调整大小
const onResizeStart = (event: MouseEvent, cursor: string) => {
  if (!props.editing || props.component.locked) return
  
  isResizing.value = true
  resizeCursor.value = cursor
  startX.value = event.clientX
  startY.value = event.clientY
  startWidth.value = props.component.width
  startHeight.value = props.component.height
  startComponentX.value = props.component.x
  startComponentY.value = props.component.y
  
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

// 处理调整大小
const handleResize = (event: MouseEvent) => {
  const dx = event.clientX - startX.value
  const dy = event.clientY - startY.value
  
  const updatedComponent = { ...props.component }
  
  // 根据不同的控制点调整大小和位置
  switch (resizeCursor.value) {
    case 'nwse-resize': // 左上角或右下角
      if (resizeHandles[0].cursor === resizeCursor.value) {
        // 左上角
        updatedComponent.width = Math.max(10, startWidth.value - dx)
        updatedComponent.height = Math.max(10, startHeight.value - dy)
        updatedComponent.x = startComponentX.value + (startWidth.value - updatedComponent.width)
        updatedComponent.y = startComponentY.value + (startHeight.value - updatedComponent.height)
      } else {
        // 右下角
        updatedComponent.width = Math.max(10, startWidth.value + dx)
        updatedComponent.height = Math.max(10, startHeight.value + dy)
      }
      break
    case 'nesw-resize': // 右上角或左下角
      if (resizeHandles[2].cursor === resizeCursor.value) {
        // 右上角
        updatedComponent.width = Math.max(10, startWidth.value + dx)
        updatedComponent.height = Math.max(10, startHeight.value - dy)
        updatedComponent.y = startComponentY.value + (startHeight.value - updatedComponent.height)
      } else {
        // 左下角
        updatedComponent.width = Math.max(10, startWidth.value - dx)
        updatedComponent.height = Math.max(10, startHeight.value + dy)
        updatedComponent.x = startComponentX.value + (startWidth.value - updatedComponent.width)
      }
      break
    case 'ns-resize': // 上边或下边
      if (resizeHandles[1].cursor === resizeCursor.value) {
        // 上边
        updatedComponent.height = Math.max(10, startHeight.value - dy)
        updatedComponent.y = startComponentY.value + (startHeight.value - updatedComponent.height)
      } else {
        // 下边
        updatedComponent.height = Math.max(10, startHeight.value + dy)
      }
      break
    case 'ew-resize': // 左边或右边
      if (resizeHandles[7].cursor === resizeCursor.value) {
        // 左边
        updatedComponent.width = Math.max(10, startWidth.value - dx)
        updatedComponent.x = startComponentX.value + (startWidth.value - updatedComponent.width)
      } else {
        // 右边
        updatedComponent.width = Math.max(10, startWidth.value + dx)
      }
      break
  }
  
  emit('update', updatedComponent)
}

// 开始旋转
const onRotateStart = (event: MouseEvent) => {
  if (!props.editing || props.component.locked) return
  
  isRotating.value = true
  startX.value = event.clientX
  startY.value = event.clientY
  startRotation.value = props.component.rotation
  
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

// 处理旋转
const handleRotate = (event: MouseEvent) => {
  // 计算组件中心点
  const componentCenterX = props.component.x + props.component.width / 2
  const componentCenterY = props.component.y + props.component.height / 2
  
  // 计算鼠标相对于组件中心的角度
  const startAngle = Math.atan2(startY.value - componentCenterY, startX.value - componentCenterX)
  const currentAngle = Math.atan2(event.clientY - componentCenterY, event.clientX - componentCenterX)
  
  // 计算角度差（弧度转度）
  let angleDiff = (currentAngle - startAngle) * (180 / Math.PI)
  
  // 更新组件旋转角度
  const updatedComponent = { ...props.component }
  updatedComponent.rotation = (startRotation.value + angleDiff) % 360
  
  emit('update', updatedComponent)
}

// 组件挂载和卸载时处理事件监听
onMounted(() => {
  // 可以在这里添加其他初始化逻辑
})

onUnmounted(() => {
  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', onMouseUp)
})
</script>

<style scoped>
.component-wrapper {
  position: absolute;
  box-sizing: border-box;
}

.is-editing {
  user-select: none;
}

.is-selected {
  outline: 2px solid var(--el-color-primary);
}

.is-locked {
  cursor: not-allowed !important;
}

.connection-mode {
  cursor: crosshair !important;
}

/* 连接点样式 */
.connection-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: var(--el-color-primary);
  border: 2px solid #fff;
  border-radius: 50%;
  cursor: crosshair;
  opacity: 0;
  transition: all 0.2s;
  z-index: 1000;
}

.component-wrapper:hover .connection-point,
.component-wrapper.connection-mode .connection-point,
.component-wrapper.is-selected .connection-point {
  opacity: 1;
}

.connection-point.active {
  background-color: #67c23a;
  transform: scale(1.2);
}

/* 调整大小控制点样式 */
.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: var(--el-color-primary);
  border: 1px solid #fff;
  border-radius: 50%;
}

.top-left {
  top: -4px;
  left: -4px;
  cursor: nwse-resize;
}

.top {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: ns-resize;
}

.top-right {
  top: -4px;
  right: -4px;
  cursor: nesw-resize;
}

.right {
  top: 50%;
  right: -4px;
  transform: translateY(-50%);
  cursor: ew-resize;
}

.bottom-right {
  bottom: -4px;
  right: -4px;
  cursor: nwse-resize;
}

.bottom {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: ns-resize;
}

.bottom-left {
  bottom: -4px;
  left: -4px;
  cursor: nesw-resize;
}

.left {
  top: 50%;
  left: -4px;
  transform: translateY(-50%);
  cursor: ew-resize;
}

.rotate-handle {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background-color: var(--el-color-primary);
  border: 1px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: grab;
}

.rotate-handle:active {
  cursor: grabbing;
}
</style>