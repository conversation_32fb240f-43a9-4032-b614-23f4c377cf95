import request from '@/utils/request'
import { AxiosPromise } from 'axios'
import { powerPointConfigQuery, powerPointConfigVO, pointDataQuery, pointDataVO } from './type'
export function powerPointConfiglable(query: powerPointConfigQuery): AxiosPromise<powerPointConfigVO[]> {
  return request({
    url: '/project/powerPointConfig/all',
    method: 'post',
    data: query,
  })
}

export function pointData(query: pointDataQuery): AxiosPromise<pointDataVO[]> {
  return request({
    url: '/project/powerPointConfig/point/data',
    method: 'post',
    data: query,
  })
}
