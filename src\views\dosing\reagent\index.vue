<template>
  <yt-crud
    ref="crudRef"
    :data="data"
    :column="column"
    :table-props="{
            selection: false,//多选
            dialogBtn:false,
            menuSlot: true,//自定义操作按钮
            // editBtn: false,
            // delBtn: false,
            viewBtn: false,
            
          }"
    :form-props="{
            width: 550,
            labelWidth:150
          }"
    @save-fun="onSave"
    @del-fun="handleDelete"
    @onLoad="getData"
    :loading="state.loading"
    :total="state.total"
    v-model:page="state.page"
    v-model:query="state.query"
  >
    <template #menuSlot="scope">
      <el-tooltip class="box-item" effect="dark" content="新增药剂方案配置" placement="top">
        <el-button link type="primary" icon="Tools" @click="addReagent(scope.row)" />
      </el-tooltip>
    </template>
    <template #rightToolbar>
      <el-col :span="12" style="margin-right: 5px">
        <el-button plain @click="pushConfiguration">
          <svg-icon icon-class="table2" />
          <span style="color: rgb(0, 112, 255);">推送配置</span>
        </el-button>
      </el-col>
      <el-col :span="12" style="margin-right: 5px">
        <el-button plain @click="oneClickPush">
          <svg-icon icon-class="upload" />
          <span style="color: rgb(0, 112, 255);">一键推送</span>
        </el-button>
      </el-col>
    </template>
  </yt-crud>
  <el-dialog
    v-model="dialogVisible"
    title="药剂方案配置列表"
    width="1000"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
  >
    <div class="dialog-content">
      <el-button @click="addReagentList" type="primary" style="margin-bottom: 5px;">新增</el-button>
      <el-table :data="Reagentdata" border style="width: 100%">
        <el-table-column label="序号" width="80" type="index" />
        <el-table-column prop="preCondShow" label="前置条件" />
        <el-table-column prop="condShow" label="判断条件" />
        <!-- <el-table-column prop="remark" label="结论" /> -->
        <el-table-column fixed="right" label="操作" min-width="120">
          <template #default="scope">
            <el-button link type="primary" icon="Delete" size="small" @click="reagentDelete(scope.row)"></el-button>
            <el-button link type="primary" icon="Edit" size="small" @click="reagentEdit(scope.row)"></el-button>
            <el-button link type="primary" icon="View" size="small" @click="reagentView(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
  <el-dialog
    v-model="pharmaceuticaldialogVisible"
    title="药剂方案配置"
    width="1000"
    :before-close="handleClosepharmaceutical"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
  >
    <el-form
      ref="ruleFormRef"
      style="min-width: 600px"
      :model="ruleForm"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      :size="formSize"
      status-icon
    >
      <el-form-item label="前置条件" prop="preCondList">
        <el-row style="margin-bottom: 10px;">
          <el-col :span="24">
            <el-button type="primary" size="small" @click="addpreCondList"> 新增前置条件 </el-button>
          </el-col>
          <el-col :span="24">
            <div v-for="(item, index) in ruleForm.preCondList" :key="index" style="margin-bottom: 10px;">
              <el-row gutter="20">
                <el-col :span="6">
                  <el-form-item label="依赖设备">
                    <el-select
                      :disabled="indisabled"
                      v-model="item.deviceId"
                      placeholder="请选择依赖设备"
                      filterable
                      clearable
                      :rules="[{ required: true, message: '依赖设备不能为空', trigger: 'change' }]"
                      @change="handleDependentDevices"
                    >
                      <el-option v-for="item in alarmBasic.device" :key="item.deviceId" :label="item.productName" :value="item.deviceId" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="6">
                  <el-form-item label="点位">
                    <el-select
                      :disabled="indisabled"
                      v-model="item.identifier"
                      placeholder="请选择点位"
                      filterable
                      clearable
                      :rules="[{ required: true, message: '点位不能为空', trigger: 'change' }]"
                    >
                      <el-option v-for="item in alarmBasic.preCondPointList" :key="item.identifier" :label="item.name" :value="item.identifier" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="6">
                  <!-- 值类型 -->
                  <el-form-item label="值类型">
                    <el-select
                      :disabled="indisabled"
                      v-model="item.dataType"
                      placeholder="请选择值类型"
                      filterable
                      clearable
                      :rules="[{ required: true, message: '值类型不能为空', trigger: 'change' }]"
                    >
                      <el-option label="平均" value="avg" />
                      <el-option label="瞬时" value="instant" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="判断条件">
                    <el-select
                      :disabled="indisabled"
                      v-model="item.comparator"
                      placeholder="判断条件"
                      filterable
                      clearable
                      :rules="[{ required: true, message: '比较不能为空', trigger: 'change' }]"
                    >
                      <el-option label=">" value=">" />
                      <el-option label="<" value="<" />
                      <el-option label="=" value="=" />
                      <el-option label="≥" value="≥" />
                      <el-option label="≤" value="≤" />
                      <el-option label="≠" value="≠" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row gutter="20">
                <el-col :span="6">
                  <el-form-item label="数值">
                    <el-input
                      :disabled="indisabled"
                      v-model="item.value"
                      placeholder="请输入数值"
                      :rules="[{ required: true, message: '数值不能为空', trigger: 'blur' }]"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <!-- 删除按钮放在输入框后面 -->
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    @click="preCondremoveLogicDetail(index)"
                    :disabled="preCondredisableddielete"
                    size="mini"
                    style="margin-left: 10px;"
                  >
                    删除
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="判断条件" prop="condList">
        <!-- 新增判断条件按钮 -->
        <el-row style="margin-bottom: 10px;">
          <el-col :span="24">
            <el-button type="primary" size="small" @click="addCondList"> 新增判断条件 </el-button>
          </el-col>
          <el-col :span="24">
            <div v-for="(item, index) in ruleForm.condList" :key="index" class="logic-detail-row" style="margin-bottom: 10px;">
              <el-row gutter="20">
                <el-col :span="6">
                  <el-form-item label="依赖设备">
                    <el-select
                      :disabled="indisabled"
                      v-model="item.deviceId"
                      placeholder="请选择依赖设备"
                      filterable
                      clearable
                      :rules="[{ required: true, message: '依赖设备不能为空', trigger: 'change' }]"
                      @change="handleDependentDevices"
                    >
                      <el-option v-for="item in alarmBasic.device" :key="item.deviceId" :label="item.productName" :value="item.deviceId" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="点位">
                    <el-select
                      :disabled="indisabled"
                      v-model="item.identifier"
                      placeholder="请选择点位"
                      filterable
                      clearable
                      :rules="[{ required: true, message: '点位不能为空', trigger: 'change' }]"
                    >
                      <el-option v-for="item in alarmBasic.preCondPointList" :key="item.identifier" :label="item.name" :value="item.identifier" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="值类型">
                    <el-select
                      :disabled="indisabled"
                      v-model="item.dataType"
                      placeholder="请选择值类型"
                      filterable
                      clearable
                      :rules="[{ required: true, message: '值类型不能为空', trigger: 'change' }]"
                    >
                      <el-option label="平均" value="avg" />
                      <el-option label="瞬时" value="instant" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="判断条件">
                    <el-select
                      :disabled="indisabled"
                      v-model="item.comparator"
                      placeholder="判断条件"
                      filterable
                      clearable
                      :rules="[{ required: true, message: '比较不能为空', trigger: 'change' }]"
                    >
                      <el-option label=">" value=">" />
                      <el-option label="<" value="<" />
                      <el-option label="=" value="=" />
                      <el-option label="≥" value="≥" />
                      <el-option label="≤" value="≤" />
                      <el-option label="≠" value="≠" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row gutter="20">
                <el-col :span="6">
                  <el-form-item label="数值">
                    <el-input
                      :disabled="indisabled"
                      v-model="item.value"
                      placeholder="请输入数值"
                      :rules="[{ required: true, message: '数值不能为空', trigger: 'blur' }]"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="24">
                  <el-form-item label="目标选择" style="margin-top: 10px;">
                    <el-row>
                      <el-col :span="6">
                        <el-select
                          :disabled="indisabled"
                          v-model="item.target.deviceId"
                          placeholder="请选择设备"
                          filterable
                          clearable
                          :rules="[{ required: true, message: '设备不能为空', trigger: 'change' }]"
                          @change="handleDependentDevices"
                        >
                          <el-option
                            v-for="device in alarmBasic.device"
                            :key="device.deviceId"
                            :label="device.productName"
                            :value="device.deviceId"
                          />
                        </el-select>
                      </el-col>
                      <el-col :span="6">
                        <el-select
                          :disabled="indisabled"
                          v-model="item.target.identifier"
                          placeholder="请选择点位"
                          filterable
                          clearable
                          :rules="[{ required: true, message: '点位不能为空', trigger: 'change' }]"
                        >
                          <el-option
                            v-for="point in alarmBasic.preCondPointList"
                            :key="point.identifier"
                            :label="point.name"
                            :value="point.identifier"
                          />
                        </el-select>
                      </el-col>
                      <el-col :span="6">
                        <el-input
                          :disabled="indisabled"
                          v-model="item.target.value"
                          placeholder="请输入目标值"
                          :rules="[{ required: true, message: '目标值不能为空', trigger: 'blur' }]"
                        />
                      </el-col>
                      <el-col :span="6">
                        <el-button
                          type="danger"
                          icon="el-icon-delete"
                          @click="condremoveLogicDetail(index)"
                          :disabled="condrdisableddielete"
                          size="mini"
                          style="margin-left: 10px;"
                        >
                          删除
                        </el-button>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="水质分析" prop="waterAnalysis">
        <el-input
          :disabled="indisabled"
          v-model="ruleForm.waterAnalysis"
          :autosize="{ minRows: 2, maxRows: 4 }"
          type="textarea"
          placeholder="请输入水质分析"
        />
      </el-form-item>
      <el-form-item label="结论" prop="des">
        <el-input :disabled="indisabled" v-model="ruleForm.des" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" placeholder="请输入结论" />
      </el-form-item>
    </el-form>
    <div style="text-align: right">
      <el-button :disabled="indisabled" type="primary" @click="configurationhandleSave">保存</el-button>
      <el-button :disabled="indisabled" @click="configurationhandleClear">清空</el-button>
      <el-button @click="configurationhandleClose">关闭</el-button>
    </div>
  </el-dialog>
  <el-dialog v-model="pushdialogVisible" title="请选择推送人员" width="500" :before-close="closePushDialog">
    <template #default>
      <el-checkbox :value="isAllSelected" @change="toggleAllSelection">全选</el-checkbox>

      <!-- 多选框列表 -->
      <el-checkbox-group v-model="selectedStaff">
        <div v-for="staff in staffList" :key="staff.id" class="checkbox-item">
          <el-checkbox :label="staff.id.toString()"> {{ staff.userName }} - {{ staff.nickName }} - {{ staff.phonenumber }} </el-checkbox>
        </div>
      </el-checkbox-group>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="pushdialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmPush"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  modifyList,
  addModify,
  editModify,
  modifyListConfig,
  getDeviceList,
  getDevicePoint,
  getAllUser,
  getSelectedUser,
  addCorrectivePlan,
  deleteModify,
  deleteModifyConfig,
  editmodifyConfig,
  editPushUser,
  pushPlan
} from './index.api'
import { IColumn } from '@/components/common/types/tableCommon'
import YtCrud from '@/components/common/yt-crud.vue'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
import { log } from 'console'
import { el } from 'element-plus/es/locale'
import { ComponentInternalInstance } from 'vue'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const dialogVisible = ref(false)
const indisabled = ref(false)
const pharmaceuticaldialogVisible = ref(false)
const Reagentdata = ref([])
const formSize = ref<ComponentSize>('default')
emitter.on('projectListChanged', (e) => {
  location.reload()
})
const data = ref([])
const column = ref<IColumn[]>([
  {
    label: '药剂调整方案名称',
    key: 'name',
    search: true,
    rules: [{ required: true, message: '药剂调整方案名称不能为空' }],
    align: 'center',
  },
])
const staffList = ref([])
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})
interface RuleForm {
  preCondList: Array<{
    identifier: string
    dataType: string
    comparator: string
    deviceId: string
    target: null
    value: string
    dateNumber: -1
  }>
  condList: Array<{
    identifier: string
    dataType: string
    comparator: string
    deviceId: string
    target: {
      deviceId: string
      identifier: string
      value: string
    }
    value: string
  }>
  waterAnalysis: ''
  des: ''
}
const ruleForm = reactive<RuleForm>({
  preCondList: [{ deviceId: '', identifier: '', dataType: '', comparator: '', value: '', target: null, dateNumber: -1 }],
  condList: [{ deviceId: '', identifier: '', dataType: '', comparator: '', value: '', target: { deviceId: '', identifier: '', value: '' } }],
  waterAnalysis: '',
  des: '',
})
const selectedRowId = ref(null)
const rules = reactive<FormRules<RuleForm>>({
  //   preCondList: [
  //     {
  //       deviceId: [{ required: true, message: '设备不能为空', trigger: 'change' }],
  //       identifier: [{ required: true, message: '点位不能为空', trigger: 'change' }],
  //       comparator: [{ required: true, message: '比较不能为空', trigger: 'change' }],
  //       value: [{ required: true, message: '数值不能为空', trigger: 'blur', pattern: /^[^\u4e00-\u9fa5]+$/, message: '数值不能包含汉字'}],
  //     }
  //   ],
  //   condList: [
  //     {
  //       deviceId: [{ required: true, message: '设备不能为空', trigger: 'change' }],
  //       identifier: [{ required: true, message: '点位不能为空', trigger: 'change' }],
  //       comparator: [{ required: true, message: '比较不能为空', trigger: 'change' }],
  //       value: [{ required: true, message: '数值不能为空', trigger: 'blur', pattern: /^[^\u4e00-\u9fa5]+$/, message: '数值不能包含汉字' }],
  //       target: [{ required: true, message: '目标不能为空', trigger: 'change' }] // 假设 target 字段是必填
  //     }
  //   ],
  //   waterAnalysis: [{ required: true, message: '水质分析不能为空', trigger: 'blur' }],
  //   des: [{ required: true, message: '结论不能为空', trigger: 'blur' }]
})
const pushdialogVisible = ref(false)
const selectedStaff = ref<string[]>([]) // 用于保存选中的人员ID
const isAllSelected = ref(false) // 是否全选
// 关闭推送配置
const closePushDialog = () => {
  pushdialogVisible.value = false
}
// 推送配置
const pushConfiguration = () => {
  pushdialogVisible.value = true // 打开弹框
  const params = {
    deptId: '',
    phonenumber: '',
    status: '',
    userName: '',
  }

  // 获取所有用户信息
  getAllUser(params).then((res) => {
    if (res.code === 200) {
      staffList.value = res.data.rows // 保存所有人员信息
      // 获取已经选中的用户
      const p = {
        projectId: projectId,
      }
      getSelectedUser(p).then((res) => {
        if (res.code === 200) {
          // 获取到已经选择的用户ID
          const selectedUserIds = res.data

          // 使用返回的已选用户ID与staffList进行匹配
          const selectedStaffIds = staffList.value
            .filter((staff) => selectedUserIds.includes(staff.id.toString())) // 匹配ID
            .map((staff) => staff.id.toString()) // 提取已选中的ID

          // 调试输出，查看选中的人员ID
          // console.log('匹配到的选中用户ID:', selectedStaffIds)

          // 将选中的用户ID赋值给selectedStaff，用于显示在checkbox中
          selectedStaff.value = selectedStaffIds
          isAllSelected.value = selectedStaff.value.length === staffList.value.length
          // 确保视图更新
          nextTick(() => {})
        }
      })
    }
  })
}
const oneClickPush = () => {
  const params = {
    projectId: projectId,
  }
  pushPlan(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success('推送成功')
    }
  })
}
// 确认推送配置
// 确认推送
const confirmPush = () => {
  const params = {
    projectId: projectId,
    receiver: selectedStaff.value.join(','),
  }
  editPushUser(params).then(res=>{
      // selectedStaff.value = []
      if(res.code == 200){
        pushdialogVisible.value = false
          selectedStaff.value = []
      }
  })
  selectedStaff.value = []
}
// 新增前置条件
const addpreCondList = () => {
  preCondredisableddielete.value = false
  ruleForm.preCondList.push({ deviceId: '', identifier: '', dataType: '', comparator: '', value: '', target: null, dateNumber: -1 })
}
// 新增判断条件
const addCondList = () => {
  condrdisableddielete.value = false
  ruleForm.condList.push({
    deviceId: '',
    identifier: '',
    dataType: '',
    comparator: '',
    value: '',
    target: { deviceId: '', identifier: '', value: '' },
  })
}

const onSave = ({ type, data, cancel }: any) => {
  const { id: projectId } = cachedProjects
  const modifyData = {
    ...data,
    projectId,
  }
  if (type === 'add') {
    addModify(modifyData).then((res) => {
      if (res.code == 200) {
        ElMessage.success('添加成功')
        cancel()
        getData()
      }
    })
  } else {
    editModify(modifyData).then((res) => {
      if (res.code == 200) {
        ElMessage.success('修改成功')
        cancel()
        getData()
      }
    })
  }
}
const onSearch = (query: any) => {
  state.query = query
  getData()
}
const onRefresh = () => {
  state.page = {
    pageSize: 10,
    pageNum: 1,
  }
  state.query = {}
}
//   点击新增方案配置
const addReagent = (row) => {
  // proxy?.$download.oss(row.address)
  //   dialogVisible.value = false
  selectedRowId.value = row.id
  dialogVisible.value = true
  modifyListConfigList()
}
const modifyListConfigList = () => {
  const params = {
    medicalModify: selectedRowId.value,
    projectId: cachedProjects.id,
  }
  modifyListConfig(params).then((res) => {
    if (res.code == 200) {
      Reagentdata.value = res.data.rows
    }
  })
}
// 获取矫正方案列表

//   点击关闭新增方案配置
const handleClose = (done: () => void) => {
  dialogVisible.value = false
}
const preCondredisableddielete = ref(false)
const preCondremoveLogicDetail = (index: number) => {
  if (ruleForm.preCondList.length === 1) {
    preCondredisableddielete.value = true
    alert('至少保留一个逻辑条件')
    return
  }
  ruleForm.preCondList.splice(index, 1)
}
const condrdisableddielete = ref(false)
const condremoveLogicDetail = (index: number) => {
  if (ruleForm.condList.length === 1) {
    condrdisableddielete.value = true
    alert('至少保留一个逻辑条件')
    return
  }
  ruleForm.condList.splice(index, 1)
}
//   点击关闭新增药剂调整方案
const handleClosepharmaceutical = (done: () => void) => {
  configurationhandleClear()
  pharmaceuticaldialogVisible.value = false
  indisabled.value = false
  condrdisableddielete.value = false
  preCondredisableddielete.value = false
}
// 点击新增方案配置列表
const alarmBasic = ref({
  device: [],
  //   powerUnitallList:[],
  preCondPointList: [],
})
// 点击新增方案配置列表
const addReagentList = () => {
  const { id: projectId } = cachedProjects
  pharmaceuticaldialogVisible.value = false
  const params2 = {
    projectId: projectId,
  }
  getDeviceList(params2).then((res) => {
    if (res.code == 200) {
      pharmaceuticaldialogVisible.value = true
      if (res.data.rows.length > 0) {
        alarmBasic.value.device = res.data.rows
      }
    }
  })
}
// 选择了前置条件设备
const handleDependentDevices = (deviceId: string, index: number, isTarget = false) => {
  const selectedDevice = alarmBasic.value.device.find((item) => item.deviceId === deviceId)
  if (selectedDevice) {
    getDevicePoint(selectedDevice.productKey).then((res) => {
      if (res.code === 200 && res.data.model.properties.length > 0) {
        // 如果是前置条件的设备
        if (!isTarget) {
          alarmBasic.value.preCondPointList = res.data.model.properties
          ruleForm.preCondList[index].identifier = ruleForm.preCondList[index].identifier || res.data.model.properties[0].identifier
        } else {
          // 如果是目标设备
          alarmBasic.value.condPointList = res.data.model.properties
          ruleForm.condList[index].target.identifier = ruleForm.condList[index].target.identifier || res.data.model.properties[0].identifier
        }
      }
    })
  }
}
const updateDeviceProductKey = () => {
  const deviceIdToProductKey = alarmBasic.value.device.reduce((map, device) => {
    map[device.deviceId] = device.productKey
    return map
  }, {})

  ruleForm.preCondList.forEach((preCond) => {
    if (preCond.deviceId && deviceIdToProductKey[preCond.deviceId]) {
      preCond.productKey = deviceIdToProductKey[preCond.deviceId]
    }
  })

  ruleForm.condList.forEach((cond) => {
    if (cond.deviceId && deviceIdToProductKey[cond.deviceId]) {
      cond.productKey = deviceIdToProductKey[cond.deviceId]
    }

    if (cond.target && cond.target.deviceId && deviceIdToProductKey[cond.target.deviceId]) {
      cond.target.productKey = deviceIdToProductKey[cond.target.deviceId]
    }
  })
}

// 保存药剂配置方案
const configurationhandleSave = () => {
  updateDeviceProductKey()
  const params = {
    ...ruleForm,
    projectId: projectId,
    medicalModify: selectedRowId.value,
  }
// 如果ruleForm.id存在就是修改，不存在就是新增
  if (ruleForm.id) {
    editmodifyConfig(params).then((res) => {
      if (res.code == 200) {
        ElMessage.success('修改成功')
        configurationhandleClose()
        modifyListConfigList()
      }
    })
  } else {
  addCorrectivePlan(params).then((res) => {
    if (res.code == 200) {
      ElMessage.success('添加成功')
      configurationhandleClose()
      modifyListConfigList()
    }
  })
  }
}
// 清空药剂配置表单
const configurationhandleClear = () => {
  // 重置前置条件列表
  ruleForm.preCondList = [{ deviceId: '', identifier: '', dataType: '', comparator: '', value: '', target: null, dateNumber: -1 }]

  // 重置判断条件列表
  ruleForm.condList = [{ deviceId: '', identifier: '', dataType: '', comparator: '', value: '', target: { deviceId: '', identifier: '', value: '' } }]

  // 重置水质分析
  ruleForm.waterAnalysis = ''

  // 重置结论
  ruleForm.des = ''
  delete ruleForm.id?ruleForm.id:''
}

// 关闭药剂配置方案
const configurationhandleClose = () => {
  configurationhandleClear() // 清空表单
  pharmaceuticaldialogVisible.value = false
  indisabled.value = false
  condrdisableddielete.value = false
  preCondredisableddielete.value = false
}
// 删除当前方案配置列
const reagentDelete = (row) => {
  deleteModifyConfig([row.id]).then((res) => {
    if (res.code == 200) {
      ElMessage.success('删除成功')
      // getData()
      modifyListConfigList()
    }
  })
}
// 修改当前方案配置列
const reagentEdit = (row) => {
  selectedRowId.value = row.medicalModify
  const params = {
    projectId: row.projectId
  }
  // 获取设备列表
  getDeviceList(params).then((res) => {
    if (res.code === 200) {
      alarmBasic.value.device = res.data.rows

      // 填充 preCondList 和 condList
      ruleForm.preCondList = row.preCondList.map((item) => ({
        deviceId: item.deviceId || '',
        identifier: item.identifier || '',
        identifierName: item.identifierName || '',
        dataType: item.dataType || 'avg', // 默认值
        comparator: item.comparator || '=',
        value: item.value || '',
        dateNumber: item.dateNumber || -1,
        target: item.target || null,
      }))

      // 针对preCondList的设备，调用handleDependentDevices填充点位
      ruleForm.preCondList.forEach((preCond, index) => {
        handleDependentDevices(preCond.deviceId, index)
      })

      ruleForm.id = row.id

      // 填充 condList
      ruleForm.condList = row.condList.map((item) => ({
        deviceId: item.deviceId || '',
        identifier: item.identifier || '',
        identifierName: item.identifierName || '',
        dataType: item.dataType || 'instant', // 默认值
        comparator: item.comparator || '=',
        value: item.value || '',
        dateNumber: item.dateNumber || 0,
        target: item.target ? {
          deviceId: item.target.deviceId || '',
          identifier: item.target.identifier || '',
          value: item.target.value || '',
        } : { deviceId: '', identifier: '', value: '' },
      }))

      // 针对condList的target.deviceId，也需要调用handleDependentDevices
      ruleForm.condList.forEach((cond, index) => {
        if (cond.target && cond.target.deviceId) {
          handleDependentDevices(cond.target.deviceId, index) // 需要处理 target 的设备点位
          handleDependentDevices(cond.deviceId, index)
        }
      })

      // 填充其他字段
      ruleForm.waterAnalysis = row.waterAnalysis || ''
      ruleForm.des = row.des || ''

      // 打开编辑对话框
      pharmaceuticaldialogVisible.value = true
    }
  })
}



// 查看当前方案配置列
const reagentView = (row) => {
  indisabled.value = true
  condrdisableddielete.value = true
  preCondredisableddielete.value = true
  // 点击查看但是不能编辑
  selectedRowId.value = row.medicalModify
  // 获取设备列表
  const params = {
    projectId: row.projectId
  }
  // 调用接口获取设备列表
  getDeviceList(params).then((res) => {
    if (res.code === 200) {
      alarmBasic.value.device = res.data.rows
      // 填充 preCondList 和 condList
      ruleForm.preCondList = row.preCondList.map((item) => ({
        deviceId: item.deviceId || '',
        identifier: item.identifier || '',
        identifierName: item.identifierName || '',
        dataType: item.dataType || 'avg', // 默认值
        comparator: item.comparator || '=',
        value: item.value || '',
        dateNumber: item.dateNumber || -1,
        target: item.target || null,
      }))
      ruleForm.id=row.id
      ruleForm.condList = row.condList.map((item) => ({
        deviceId: item.deviceId || '',
        identifier: item.identifier || '',
        identifierName: item.identifierName || '',
        dataType: item.dataType || 'instant', // 默认值
        comparator: item.comparator || '=',
        value: item.value || '',
        dateNumber: item.dateNumber || 0,
        target: item.target ? {
          deviceId: item.target.deviceId || '',
          identifier: item.target.identifier || '',
          value: item.target.value || '',
        } : { deviceId: '', identifier: '', value: '' },
      }))

      // 获取点位列表
      ruleForm.preCondList.forEach((preCond, index) => {
        handleDependentDevices(preCond.deviceId, index)
      })

      ruleForm.condList.forEach((cond, index) => {
        if (cond.target && cond.target.deviceId) {
          handleDependentDevices(cond.target.deviceId, index, true) // 需要处理 target 的设备点位
        }
      })

      // 填充其他字段
      ruleForm.waterAnalysis = row.waterAnalysis || ''
      ruleForm.des = row.des || ''

      // 打开编辑对话框
      pharmaceuticaldialogVisible.value = true
    }
  })
}
const { id: projectId } = cachedProjects
const handleDelete = (row: any) => {
  deleteModify([row.id]).then((res) => {
    if (res.code == 200) {
      ElMessage.success('删除成功')
      getData()
    }
  })
}
const getData = () => {
  state.loading = true
  modifyList({
    ...state.page,
    ...state.query,
    projectId,
  }).then((res) => {
    state.loading = false
    state.total = res.data.total
    data.value = res.data.rows
  })
}
</script>

<style lang="scss">
// @import '@/assets/styles/ctable.scss';
/* 全局样式 */
@import '@/assets/styles/datapicker.scss';
/* 全局样式 */
</style>
<style scoped>
 :deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  /* box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5); */
  border: none;
}
:deep(.el-card__header) {
  border: none;
}
:deep(.el-table,
.el-table__expanded-cell ){
  background-color: transparent !important;
}
:deep(.el-table__body tr,
.el-table__body td) {
  padding: 0;
  height: 40px;
}
:deep(.el-table tr ){
  border: none;
  background-color: transparent;
}
:deep(.el-table th) {
  /* background-color: transparent; */
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}
:deep(.el-table) {
  --el-table-border-color: none;
}
:deep(.el-table__cell) {
  /* color: rgba(204, 204, 204, 1) !important; */
}
/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row) {
  /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
  /* 取消单元格背景色，确保整行背景色生效 */
}
:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;

  color: #ffffff;
}
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}
:deep(.el-select__wrapper){
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
.el-tree {
  background-color: transparent;
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #07355c;
}
.el-tree-node__expand-icon {
  color: #fff;
}
.el-tree-node__label {
  color: #fff;
}
.el-tree-node__content {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}
.el-select__tags .el-tag--info {
  background-color: #153059 !important;
}
.el-tag.el-tag--info {
  color: #fff !important;
}
</style>
