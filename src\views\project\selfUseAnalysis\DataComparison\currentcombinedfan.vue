<template>
  <!-- 循环水量 -->
  <div>
    <div class="ml-[50px] mb-[20px]">
      <el-button type="info" @click="goBack">返回</el-button>
      <el-button type="primary" @click="ExportTable" :disabled="!selectedDate">导出</el-button>
    </div>
    <!-- 日历选择区域 -->
    <div class="calendar-card ml-[20px] mb-[20px]">
      <div class="calendar-container">
        <h3>选择查询月份</h3>
        <el-date-picker
          v-model="selectedDate"
          type="month"
          placeholder="请选择月份"
          :disabled-date="disabledDate"
          :clearable="false"
          format="YYYY-MM"
          value-format="YYYY-MM"
          @change="handleDateChange"
          style="width: 200px"
        />
      </div>
    </div>

    <!-- 混合表头和数据的表格 -->
    <div class="table-container">
      <div v-for="(group, groupIndex) in rawData" :key="groupIndex" class="waterfall-item">
        <el-table :data="group.rows" border stripe style="width: 100%" :show-header="true">
          <el-table-column
            v-for="(header, colIndex) in group.headers"
            :key="colIndex"
            :prop="getColProp(header, colIndex)"
            :label="header"
            align="center"
          >
            <template #default="{ row }">
              {{ getCellValue(row, header, colIndex) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
  
  <script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import { getCalendarData, CalendarDataDTO, getdataFilterReportView, ReportDataDTO } from '../dataFilterConfig/index.api'

const router = useRouter()
const route = useRoute()

// 获取路由参数
const projectId = ref<number>()
const powerUnitId = ref<number>()
const filterType = ref<number>()
const unitName = ref<string>()

// 日历相关数据
const selectedDate = ref<string>('')
const availableMonths = ref<string[]>([])
const loading = ref(false)

// 初始化参数
const initParams = () => {
  projectId.value = Number(route.query.projectId)
  powerUnitId.value = Number(route.query.powerUnitId)
  filterType.value = Number(route.query.filterType)
  unitName.value = route.query.unitName as string

}

// 获取可选日期数据
const fetchAvailableMonths = async () => {
  if (!projectId.value || !powerUnitId.value || !filterType.value) {
    ElMessage.error('项目ID、机组ID和过滤类型不能为空')
    return
  }

  try {
    loading.value = true
    const response = await getCalendarData({
      projectId: projectId.value,
      powerUnitId: powerUnitId.value,
      filterType: filterType.value,
    })
    availableMonths.value = response.data || []

    // 如果有可选月份，默认选择最近的一个月份
    if (availableMonths.value.length > 0) {
      // 找到最近的（最大的）月份，使用字符串比较
      selectedDate.value = [...availableMonths.value].sort().pop() || availableMonths.value[0]
    }

  } catch (error) {
    ElMessage.error('获取可选月份失败')
  } finally {
    loading.value = false
  }
}

// 判断日期是否可选
const disabledDate = (time: Date) => {
  const month = time.getFullYear() + '-' + String(time.getMonth() + 1).padStart(2, '0')
  return !availableMonths.value.includes(month)
}

// 处理日期变化
const handleDateChange = (value: string) => {
  // 这里可以根据选择的月份重新获取表格数据
  fetchTableDataByMonth(value)
}

// 根据月份获取表格数据
const fetchTableDataByMonth = async (month: string) => {

  if (!projectId.value || !powerUnitId.value || !filterType.value) {
    ElMessage.error('参数不完整，无法获取数据')
    return
  }

  try {
    loading.value = true
    const response = await getdataFilterReportView({
      projectId: projectId.value,
      powerUnitId: powerUnitId.value,
      filterType: filterType.value,
      reportMonth: month,
    })

    if (response.data && Array.isArray(response.data)) {
      rawData.value = response.data
    } else {
      ElMessage.warning('获取到的数据格式不正确')
    }
  } catch (error) {
    ElMessage.error('获取表格数据失败')
  } finally {
    loading.value = false
  }
}

interface DataGroup {
  headers: string[] // 该组的表头
  rows: Array<{
    index: number
    filterCondition: string
    rangeTitle: string
    situationAnalysis: string
    proportion: string
    rangeInterval?: string
    percentRange?: string
    monthAvgVal?: string
    normalPumpCurrentRange1?: string
    normalPumpCurrentRange2?: string
    normalPumpCurrentAvg1?: number
    normalPumpCurrentAvg2?: number
  }>
}

// 列配置接口
interface ColumnConfig {
  width: number
  label: string
}

const rawData = ref<DataGroup[]>([])

// 动态列配置（根据数据中最多的列数计算）
const dynamicColumns = computed<ColumnConfig[]>(() => {
  if (rawData.value.length === 0) return []

  // 找到最大列数
  const maxColumns = Math.max(...rawData.value.map((group) => group.headers.length))

  // 生成列配置
  const columns: ColumnConfig[] = []
  for (let i = 0; i < maxColumns; i++) {
    columns.push({
      width: i === 0 ? 80 : i === 1 ? 150 : 140,
      label: `col${i}`,
    })
  }

  return columns
})
// 根据 header 和 colIndex 获取 prop 名
const getColProp = (header: string, colIndex: number) => {
  return header
}

// 根据 header 获取 row 里的值
const getCellValue = (row: any, header: string, colIndex: number) => {
  switch (header) {
    case '序号':
      return row.index
    case '筛选条件':
      return row.filterCondition
    case '1#风机区间范围(A)':
      return row.fanCurrentRange1
    case '2#风机区间范围(A)':
      return row.fanCurrentRange2
    case '1#风机80%区间范围(A)':
      return row.fanCheckRange1
    case '2#风机80%区间范围(A)':
      return row.fanCheckRange2
    case '1#风机月平均电流(A)':
      return row.fanCurrentAvg1
    case '2#风机月平均电流(A)':
      return row.fanCurrentAvg2
    default:
      return ''
  }
}

const goBack = () => {
  router.go(-1)
}

// 导出表格为Excel
const ExportTable = () => {
  try {
    const workbook = XLSX.utils.book_new()
    const excelData: any[][] = []

    // 遍历每个 group，依次写入表头和数据
    rawData.value.forEach((group, groupIndex) => {
      // 表头
      excelData.push(group.headers)
      // 数据
      group.rows.forEach(row => {
        const rowData = group.headers.map(header => {
          // 这里和 getCellValue 保持一致
          switch (header) {
            case '序号':
              return row.index
            case '筛选条件':
              return row.filterCondition
            case '1#风机区间范围(A)':
              return row.fanCurrentRange1
            case '2#风机区间范围(A)':
              return row.fanCurrentRange2
            case '1#风机80%区间范围(A)':
              return row.fanCheckRange1
            case '2#风机80%区间范围(A)':
              return row.fanCheckRange2
            case '1#风机月平均电流(A)':
              return row.fanCurrentAvg1
            case '2#风机月平均电流(A)':
              return row.fanCurrentAvg2
            default:
              return ''
          }
        })
        excelData.push(rowData)
      })
      // 每个 group 之间空一行
      if (groupIndex !== rawData.value.length - 1) {
        excelData.push([])
      }
    })

    const worksheet = XLSX.utils.aoa_to_sheet(excelData)
    XLSX.utils.book_append_sheet(workbook, worksheet, `循环水量数据_${selectedDate.value || 'default'}`)

    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
    })

    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })

    const monthSuffix = selectedDate.value ? `_${selectedDate.value}` : ''
    const fileName = `${unitName.value}_风机水泵风机电流数据_${monthSuffix}.xlsx`
    saveAs(blob, fileName)
    console.log('Excel文件导出成功')
  } catch (error) {
    console.error('导出Excel时发生错误:', error)
  }
}

// 组件挂载后的初始化
onMounted(async () => {
  try {
    // 初始化参数
    initParams()

    // 获取可选月份
    await fetchAvailableMonths()

    // 如果有可选月份，自动获取第一个月份的数据
    if (availableMonths.value.length > 0 && selectedDate.value) {
      await fetchTableDataByMonth(selectedDate.value)
    }

  } catch (error) {
    console.error('获取表格数据失败:', error)
  }
})
</script>
<style scoped>
.params-card {
  background-color: rgba(2, 28, 51, 0.5);
  border: 1px solid rgba(11, 56, 93, 1);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  max-width: 600px;
}

.params-card h3 {
  color: #ffffff;
  margin-bottom: 12px;
  font-size: 16px;
}

.params-display {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.params-display span {
  color: #ffffff;
  padding: 6px 12px;
  background-color: rgba(11, 56, 93, 0.3);
  border-radius: 4px;
  font-size: 14px;
}

.calendar-card {
  background-color: rgba(2, 28, 51, 0.5);
  border: 1px solid rgba(11, 56, 93, 1);
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
  max-width: 350px;
  display: inline-block;
}

.calendar-card h3 {
  color: #ffffff;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.calendar-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.available-months {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.available-months .label {
  color: #ffffff;
  font-size: 14px;
  white-space: nowrap;
}
.el-table {
  margin-bottom: 20px;
  background-color: rgba(2, 28, 51, 0.5) !important;
}

/* 表头单元格样式 */
.header-cell {
  font-weight: bold;
  color: #ffffff;
  display: block;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

/* 数据单元格样式 */
.data-cell {
  color: #ffffff;
  display: block;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

.table-container {
  column-count: 2; /* 2列瀑布流，可根据需要调整 */
  column-gap: 32px; /* 列间距 */
  /* 可选：让列数自适应宽度
  column-width: 420px;
  */
  width: 100%;
}

.waterfall-item {
  break-inside: avoid; /* 防止表格被拆分到两列 */
  margin-bottom: 32px;
  width: 100%;
  /* 可选：加阴影或边框美化 */
}

.waterfall-item :deep(.el-table__header),
.waterfall-item :deep(.el-table__header th) {
  background-color:rgb(11, 56, 93) !important;
  color: #fff !important;
}

.waterfall-item :deep(.el-table__body),
.waterfall-item :deep(.el-table__body td) {
  background-color: rgba(2, 28, 51, 0.5);
  color: #ffffff !important;
}

.waterfall-item :deep(.el-table__body tr:hover td) {
  background-color:rgba(2, 28, 51, 0.5) !important;
  color: #f8f8f8 !important;
}
</style>
