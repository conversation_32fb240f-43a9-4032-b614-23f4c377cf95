<!-- src/components/TypewriterText.vue -->
<template>
  <span>
    {{ displayedText }}<span v-if="showCursor" class="cursor">|</span>
    <span v-if="typingDone"><slot /></span>
  </span>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps, onBeforeUnmount } from 'vue'

// 定义接收的 props
const props = defineProps<{
  text: string
  typingSpeed?: number // 可选，默认为100ms
}>()

const emitDone = defineEmits(['done'])

const displayedText = ref('')
let currentIndex = 0
let timer: number | null = null
const typingDone = ref(false)
const showCursor = ref(true)

// 打字机效果函数
const typeText = () => {
  if (currentIndex < props.text.length) {
    displayedText.value += props.text.charAt(currentIndex)
    currentIndex++
  } else {
    // 完成打字
    if (timer !== null) {
      clearInterval(timer)
      timer = null
    }
    showCursor.value = false
    typingDone.value = true
    emitDone('done') // 发出完成事件
  }
}

onMounted(() => {
  displayedText.value = ''
  currentIndex = 0
  typingDone.value = false
  showCursor.value = true
  const speed = props.typingSpeed || 100
  timer = window.setInterval(typeText, speed)
})

// 监听 text 的变化
watch(
  () => props.text,
  (newText) => {
    if (timer !== null) {
      clearInterval(timer)
    }
    displayedText.value = ''
    currentIndex = 0
    typingDone.value = false
    showCursor.value = true
    const speed = props.typingSpeed || 100
    timer = window.setInterval(typeText, speed)
  }
)

onBeforeUnmount(() => {
  if (timer !== null) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.cursor {
  display: inline-block;
  width: 10px;
  animation: blink 1s step-start infinite;
  color: #fff; /* 根据需要调整光标颜色 */
}

@keyframes blink {
  50% {
    opacity: 0;
  }
}
</style>
