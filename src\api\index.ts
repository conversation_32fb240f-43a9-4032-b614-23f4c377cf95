import request from '@/utils/request'
import { AxiosPromise } from 'axios'

import {
  UnitParametersQuery,
  UnitParametersVO,
  DeviceStatusQuery,
  DeviceStatusVO,
  downSamplingQuery,
  DownSamplingVO,
  AlarmQuery,
  AlarmVO,
} from './types'
/**
 * 首页统计数据
 * @returns 统计数据
 */
export const stats = () => {
  return request({
    url: '/stats/main',
    method: 'post',
  })
}
// 获取机组数据
export function pointData(query: UnitParametersQuery): AxiosPromise<UnitParametersVO[]> {
  return request({
    url: '/device/home/<USER>/data',
    method: 'post',
    data: query,
  })
}
// 获取设备在线情况
export function DeviceStatus(query: DeviceStatusQuery): AxiosPromise<DeviceStatusVO[]> {
  return request({
    url: '/device/state/stats',
    method: 'post',
    data: query,
  })
}
// 获取汽耗率数据
export function downSampling(query: downSamplingQuery): AxiosPromise<DownSamplingVO[]> {
  return request({
    url: 'device/powerConsumption/downSampling/list',
    method: 'post',
    data: query,
  })
}

// 获取告警信息
export function Alarm(query: AlarmQuery): AxiosPromise<AlarmVO[]> {
  return request({
    url: '/alert/level/stats',
    method: 'post',
    data: query,
  })
}
