export interface subSystemMemberQuery {
  pageNum: number
  pageSize: number
  subSystemId: string
  userNickName: string
}
export interface subSystemMemberData {
  id: number
  userId: number
  userNickName?: string
  subSystemId?: number
}
export interface subSystemMemberVO {
  rows: subSystemMemberData[]
}

export interface subSystemMemberUserQuery {
  projectId: number
  nickName: string
}

export interface subSystemMemberUserVO {
  id: number
  nickName: string
}

export interface addUser {
  subSystemId: string
  members: string[]
}

export interface addUserId {
  data: string[]
}
