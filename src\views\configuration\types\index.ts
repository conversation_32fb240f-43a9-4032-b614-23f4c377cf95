// 组态相关类型定义

// 组态项目信息
export interface ConfigurationProject {
  id: string
  name: string
  description?: string
  thumbnail?: string
  width: number
  height: number
  backgroundColor: string
  components: ConfigurationComponent[]
  connections: ComponentConnection[]
  dataBindings: DataBinding[]
  createdAt: string
  updatedAt: string
  createdBy: string
  status: 'draft' | 'published' | 'archived'
}

// 组态组件
export interface ConfigurationComponent {
  id: string
  type: ComponentType
  name: string
  x: number
  y: number
  z?: number
  width: number
  height: number
  rotation: number
  opacity: number
  visible: boolean
  locked: boolean
  style: ComponentStyle
  data: ComponentData
  animation?: ComponentAnimation
  events?: ComponentEvent[]
}

// 组件类型
export type ComponentType =
  | 'text'           // 文本
  | 'image'          // 图片
  | 'shape'          // 形状
  | 'chart'          // 图表
  | 'gauge'          // 仪表盘
  | 'progress'       // 进度条
  | 'button'         // 按钮
  | 'switch'         // 开关
  | 'input'          // 输入框
  | 'video'          // 视频
  | 'iframe'         // 内嵌页面
  | 'model3d'        // 3D模型
  | 'container'      // 容器
  | 'hyperbolicCoolingTower'    // 双曲线冷却塔
  | 'mechanicalCoolingTower'    // 机械通风冷却塔
  | 'condenser'                 // 凝汽器
  | 'coolingTower25d'          // 2.5D冷却塔
  | 'condenser25d'             // 2.5D凝汽器
  | 'pipe'                     // 管道
  | 'equipment'                // 设备
  | 'control'                  // 控制
  | 'indicator'                // 指示器
  | 'straightPipe'             // 直管道
  | 'custom3dModel'            // 自定义3D模型
  | 'bendPipe'                 // 弯管道
  | 'steamTurbine'             // 汽轮机
  | 'vacuumPump'               // 真空泵
  | 'circulatingPump'          // 循环水泵
  | 'generator'                // 发电机
  | 'sideFilter'               // 旁滤
  | 'statusLight'              // 状态灯

// 组件样式
export interface ComponentStyle {
  backgroundColor?: string
  borderColor?: string
  borderWidth?: number
  borderRadius?: number
  fontSize?: number
  fontColor?: string
  fontWeight?: string
  textAlign?: 'left' | 'center' | 'right'
  boxShadow?: string
  gradient?: {
    type: 'linear' | 'radial'
    colors: string[]
    direction?: number
  }
}

// 组件数据（修复重复字段问题）
export interface ComponentData {
  static?: any
  dynamic?: {
    dataSource: string
    field: string
    format?: string
    unit?: string
    value?: any
  }
}

// 组件动画
export interface ComponentAnimation {
  type: 'fade' | 'slide' | 'rotate' | 'scale' | 'bounce'
  duration: number
  delay: number
  repeat: boolean
  trigger: 'load' | 'click' | 'hover' | 'data'
}

// 组件事件
export interface ComponentEvent {
  type: 'click' | 'hover' | 'change'
  action: 'navigate' | 'popup' | 'api' | 'script'
  params: any
}

// 数据绑定
export interface DataBinding {
  id: string
  name: string
  type: 'api' | 'websocket' | 'mqtt' | 'static'
  config: {
    url?: string
    method?: string
    headers?: Record<string, string>
    params?: Record<string, any>
    interval?: number
  }
  fields: DataField[]
}

// 数据字段
export interface DataField {
  key: string
  name: string
  type: 'number' | 'string' | 'boolean' | 'object'
  unit?: string
  format?: string
}

// 编辑器状态
export interface EditorState {
  selectedComponents: string[]
  clipboard: ConfigurationComponent[]
  history: {
    past: ConfigurationProject[]
    present: ConfigurationProject
    future: ConfigurationProject[]
  }
  zoom: number
  grid: {
    show: boolean
    size: number
    snap: boolean
  }
  rulers: boolean
  layers: boolean
}

// 组件库分类
export interface ComponentCategory {
  id: string
  name: string
  icon: string
  components: ComponentTemplate[]
}

// 组件模板
export interface ComponentTemplate {
  id: string
  name: string
  icon: string
  type: ComponentType
  defaultProps: Partial<ConfigurationComponent>
  preview: string
}

// 组件连接线
export interface ComponentConnection {
  id: string
  name: string
  sourceComponent: string
  targetComponent: string
  sourceAnchor: ConnectionAnchor
  targetAnchor: ConnectionAnchor
  style: ConnectionStyle
  animation?: ConnectionAnimation
  pathPoints?: { x: number; y: number }[] // 自定义路径点
  data?: any
}

// 连接锚点
export interface ConnectionAnchor {
  position: 'top' | 'right' | 'bottom' | 'left' | 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  offset?: { x: number; y: number }
}

// 连接线样式
export interface ConnectionStyle {
  strokeColor: string
  strokeWidth: number
  strokeDasharray?: string
  arrowSize: number
  arrowColor: string
  lineType: 'straight' | 'curved' | 'polyline'
}

// 连接线动画
export interface ConnectionAnimation {
  enabled: boolean
  type: 'flow' | 'pulse' | 'dash'
  speed: number
  direction: 'forward' | 'backward' | 'bidirectional'
}
