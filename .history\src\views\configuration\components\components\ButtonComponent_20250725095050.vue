<template>
  <div class="button-component">
    <el-button 
      :style="buttonStyle" 
      :disabled="!editing && disabled"
      @click="handleClick"
    >
      {{ buttonText }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
  editing?: boolean
}>()

// 按钮样式
const buttonStyle = computed(() => {
  const { style } = props.component
  
  return {
    width: '100%',
    height: '100%',
    backgroundColor: style.backgroundColor || '#409EFF',
    borderColor: style.borderColor || style.backgroundColor || '#409EFF',
    borderRadius: style.borderRadius ? `${style.borderRadius}px` : '4px',
    color: style.fontColor || '#FFFFFF',
    fontSize: style.fontSize ? `${style.fontSize}px` : '14px',
    fontWeight: style.fontWeight || 'normal',
    padding: '0',
    display: 'flex',
    alignItems: 'center',
    justifyContent: getJustifyContent(style.textAlign),
    boxShadow: style.boxShadow || 'none'
  }
})

// 获取对齐方式
const getJustifyContent = (textAlign?: string) => {
  switch (textAlign) {
    case 'left':
      return 'flex-start'
    case 'center':
      return 'center'
    case 'right':
      return 'flex-end'
    default:
      return 'center'
  }
}

// 按钮文本
const buttonText = computed(() => {
  const { data } = props.component
  
  // 如果有动态数据，优先使用动态数据
  if (data.dynamic && data.dynamic.value !== undefined) {
    return data.dynamic.value
  }
  
  // 否则使用静态数据
  return data.static || '按钮'
})

// 按钮是否禁用
const disabled = computed(() => {
  const { data } = props.component
  
  if (data.dynamic && data.dynamic.disabled !== undefined) {
    return data.dynamic.disabled
  }
  
  return false
})

// 处理按钮点击
const handleClick = () => {
  if (props.editing) {
    // 编辑模式下不执行实际操作
    return
  }
  
  const { events } = props.component
  
  if (!events || events.length === 0) {
    return
  }
  
  // 执行按钮事件
  events.forEach(event => {
    if (event.type === 'click') {
      executeAction(event.action, event.params)
    }
  })
}

// 执行动作
const executeAction = (action: string, params: any) => {
  switch (action) {
    case 'navigate':
      // 页面导航
      if (params && params.url) {
        window.location.href = params.url
      }
      break
    case 'popup':
      // 弹窗提示
      if (params && params.message) {
        ElMessage({
          message: params.message,
          type: params.type || 'info',
          duration: params.duration || 3000
        })
      }
      break
    case 'api':
      // 调用API
      if (params && params.url) {
        fetch(params.url, {
          method: params.method || 'GET',
          headers: params.headers || {},
          body: params.method !== 'GET' ? JSON.stringify(params.data) : undefined
        })
          .then(response => response.json())
          .then(data => {
            console.log('API调用成功', data)
            // 可以在这里处理API响应
          })
          .catch(error => {
            console.error('API调用失败', error)
          })
      }
      break
    case 'script':
      // 执行脚本
      if (params && params.code) {
        try {
          // 注意：在实际应用中，应该限制脚本的执行权限，避免安全风险
          // eslint-disable-next-line no-new-func
          const fn = new Function(params.code)
          fn()
        } catch (error) {
          console.error('脚本执行失败', error)
        }
      }
      break
  }
}
</script>

<style scoped>
.button-component {
  width: 100%;
  height: 100%;
}

.button-component .el-button {
  border-width: 1px;
  border-style: solid;
}
</style>