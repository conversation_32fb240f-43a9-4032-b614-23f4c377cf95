<template>
  <div class="performance-demo">
    <div class="demo-header">
      <h1>组态画布性能对比演示</h1>
      <div class="demo-controls">
        <el-button-group>
          <el-button 
            :type="currentMode === 'normal' ? 'primary' : ''"
            @click="switchMode('normal')"
          >
            普通画布
          </el-button>
          <el-button 
            :type="currentMode === 'performance' ? 'primary' : ''"
            @click="switchMode('performance')"
          >
            高性能画布
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button @click="generateTestComponents(100)">
            生成100个组件
          </el-button>
          <el-button @click="generateTestComponents(500)">
            生成500个组件
          </el-button>
          <el-button @click="generateTestComponents(1000)">
            生成1000个组件
          </el-button>
          <el-button @click="clearComponents" type="danger">
            清空组件
          </el-button>
        </el-button-group>

        <div class="performance-info">
          <span>组件数量: {{ testComponents.length }}</span>
          <span>连接数量: {{ testConnections.length }}</span>
          <span>当前模式: {{ currentMode === 'normal' ? '普通画布' : '高性能画布' }}</span>
        </div>
      </div>
    </div>

    <div class="demo-content">
      <!-- 普通画布渲染 -->
      <div 
        v-if="currentMode === 'normal'"
        class="normal-canvas"
        :style="canvasStyle"
        @dragover="handleDragOver"
        @drop="handleDrop"
      >
        <div class="performance-warning" v-if="testComponents.length > 200">
          ⚠️ 组件数量较多，普通模式可能出现卡顿
        </div>
        
        <!-- 普通方式：渲染所有组件 -->
        <div
          v-for="component in testComponents"
          :key="component.id"
          class="demo-component"
          :style="getComponentStyle(component)"
          @click="selectComponent(component.id)"
        >
          {{ component.name }}
        </div>
        
        <!-- 简单的连接线 -->
        <svg class="connections-svg" :style="svgStyle">
          <line
            v-for="connection in testConnections"
            :key="connection.id"
            :x1="connection.x1"
            :y1="connection.y1"
            :x2="connection.x2"
            :y2="connection.y2"
            stroke="#409eff"
            stroke-width="2"
          />
        </svg>
      </div>

      <!-- 高性能画布 -->
      <performance-optimized-canvas
        v-else
        :components="testComponents"
        :connections="testConnections"
        :canvas-width="1920"
        :canvas-height="1080"
        :background-color="'#f5f7fa'"
        :zoom="1"
        :show-grid="true"
        :grid-size="20"
        :selected-components="selectedComponents"
        :editing="true"
        @select-component="selectComponent"
        @update-component="updateComponent"
        @canvas-click="clearSelection"
      />
    </div>

    <!-- 性能对比说明 -->
    <div class="demo-explanation">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>性能对比说明</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <h3>🐌 普通画布</h3>
            <ul>
              <li>渲染所有组件（即使不可见）</li>
              <li>DOM元素数量 = 组件数量</li>
              <li>没有虚拟化优化</li>
              <li>大量组件时会卡顿</li>
              <li>内存使用持续增长</li>
            </ul>
          </el-col>
          <el-col :span="12">
            <h3>🚀 高性能画布</h3>
            <ul>
              <li>只渲染可见区域组件</li>
              <li>虚拟化减少DOM元素</li>
              <li>智能缓存和优化</li>
              <li>实时性能监控</li>
              <li>支持1000+组件流畅运行</li>
            </ul>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import PerformanceOptimizedCanvas from '../components/PerformanceOptimizedCanvas.vue'
import type { ConfigurationComponent, ComponentConnection } from '../types'

// 当前模式
const currentMode = ref<'normal' | 'performance'>('normal')

// 测试数据
const testComponents = ref<ConfigurationComponent[]>([])
const testConnections = ref<ComponentConnection[]>([])
const selectedComponents = ref<string[]>([])

// 切换模式
const switchMode = (mode: 'normal' | 'performance') => {
  currentMode.value = mode
  ElMessage.success(`已切换到${mode === 'normal' ? '普通' : '高性能'}画布模式`)
}

// 生成测试组件
const generateTestComponents = (count: number) => {
  const startTime = performance.now()
  
  const components: ConfigurationComponent[] = []
  const connections: ComponentConnection[] = []
  
  // 生成组件
  for (let i = 0; i < count; i++) {
    const component: ConfigurationComponent = {
      id: `test_component_${i}`,
      type: 'shape',
      name: `组件${i}`,
      x: Math.random() * 1500 + 100,
      y: Math.random() * 800 + 100,
      z: 0,
      width: 80 + Math.random() * 40,
      height: 60 + Math.random() * 30,
      rotation: 0,
      opacity: 1,
      visible: true,
      locked: false,
      style: {
        backgroundColor: `hsl(${Math.random() * 360}, 70%, 70%)`,
        borderRadius: 4,
        fontSize: 12,
        fontColor: '#333'
      },
      data: {
        static: `数据${i}`
      }
    }
    components.push(component)
    
    // 生成一些连接线
    if (i > 0 && Math.random() < 0.3) {
      const sourceComponent = components[Math.floor(Math.random() * i)]
      const connection: ComponentConnection = {
        id: `test_connection_${connections.length}`,
        name: `连接${connections.length}`,
        sourceComponent: sourceComponent.id,
        targetComponent: component.id,
        sourceAnchor: { position: 'right' },
        targetAnchor: { position: 'left' },
        style: {
          strokeColor: '#409eff',
          strokeWidth: 2,
          arrowSize: 8,
          arrowColor: '#409eff',
          lineType: 'straight'
        }
      }
      connections.push(connection)
    }
  }
  
  testComponents.value = components
  testConnections.value = connections
  
  const endTime = performance.now()
  ElMessage.success(`生成${count}个组件完成，耗时${(endTime - startTime).toFixed(2)}ms`)
}

// 清空组件
const clearComponents = () => {
  testComponents.value = []
  testConnections.value = []
  selectedComponents.value = []
  ElMessage.info('已清空所有组件')
}

// 选择组件
const selectComponent = (id: string) => {
  selectedComponents.value = [id]
}

// 清除选择
const clearSelection = () => {
  selectedComponents.value = []
}

// 更新组件
const updateComponent = (component: ConfigurationComponent) => {
  const index = testComponents.value.findIndex(c => c.id === component.id)
  if (index !== -1) {
    testComponents.value[index] = component
  }
}

// 画布样式
const canvasStyle = computed(() => ({
  width: '1920px',
  height: '1080px',
  backgroundColor: '#f5f7fa',
  position: 'relative',
  border: '1px solid #ddd',
  overflow: 'auto',
  transform: 'scale(0.5)',
  transformOrigin: 'top left'
}))

// SVG样式
const svgStyle = computed(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  pointerEvents: 'none'
}))

// 获取组件样式
const getComponentStyle = (component: ConfigurationComponent) => ({
  position: 'absolute',
  left: `${component.x}px`,
  top: `${component.y}px`,
  width: `${component.width}px`,
  height: `${component.height}px`,
  backgroundColor: component.style.backgroundColor,
  borderRadius: `${component.style.borderRadius || 0}px`,
  border: selectedComponents.value.includes(component.id) ? '2px solid #409eff' : '1px solid #ddd',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: '12px',
  cursor: 'pointer',
  userSelect: 'none'
})

// 拖拽处理
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  // 处理拖拽逻辑
}

// 初始生成一些测试组件
generateTestComponents(50)
</script>

<style scoped>
.performance-demo {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.demo-header {
  margin-bottom: 20px;
}

.demo-header h1 {
  margin: 0 0 15px 0;
  color: #333;
}

.demo-controls {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.performance-info {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #666;
}

.demo-content {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  background: #fff;
}

.normal-canvas {
  position: relative;
  margin: 0 auto;
  border: 1px solid #ddd;
}

.performance-warning {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 193, 7, 0.9);
  color: #856404;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.demo-component {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.demo-component:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.connections-svg {
  pointer-events: none;
}

.demo-explanation {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-explanation ul {
  margin: 10px 0;
  padding-left: 20px;
}

.demo-explanation li {
  margin: 5px 0;
  color: #666;
}

.demo-explanation h3 {
  color: #333;
  margin: 0 0 10px 0;
}
</style> 