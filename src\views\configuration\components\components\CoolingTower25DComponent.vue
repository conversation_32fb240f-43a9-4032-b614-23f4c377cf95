<template>
  <div 
    class="cooling-tower-25d"
    :style="componentStyle"
  >
    <!-- 2.5D冷却塔 -->
    <svg
      :width="component.width"
      :height="component.height"
      viewBox="0 0 180 200"
      class="tower-svg"
      preserveAspectRatio="xMidYMid meet"
    >
      <!-- 渐变和阴影定义 -->
      <defs>
        <!-- 塔体渐变 -->
        <linearGradient id="tower25dGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" :style="`stop-color:${lightTowerColor};stop-opacity:1`" />
          <stop offset="30%" :style="`stop-color:${towerColor};stop-opacity:1`" />
          <stop offset="70%" :style="`stop-color:${towerColor};stop-opacity:1`" />
          <stop offset="100%" :style="`stop-color:${darkTowerColor};stop-opacity:1`" />
        </linearGradient>
        
        <!-- 顶部渐变 -->
        <radialGradient id="topGradient" cx="50%" cy="30%" r="70%">
          <stop offset="0%" :style="`stop-color:${lightTowerColor};stop-opacity:1`" />
          <stop offset="100%" :style="`stop-color:${towerColor};stop-opacity:1`" />
        </radialGradient>
        
        <!-- 底部渐变 -->
        <linearGradient id="baseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#d0d0d0;stop-opacity:1" />
          <stop offset="50%" style="stop-color:#a0a0a0;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#808080;stop-opacity:1" />
        </linearGradient>
        
        <!-- 蒸汽渐变 - 纯白色 -->
        <radialGradient id="steam25dGradient" cx="50%" cy="50%" r="50%">
          <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
          <stop offset="70%" style="stop-color:#ffffff;stop-opacity:0.6" />
          <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
        </radialGradient>
        
        <!-- 阴影滤镜 -->
        <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="3" dy="6" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
        </filter>
        
        <!-- 内阴影滤镜 -->
        <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
          <feOffset dx="0" dy="0"/>
          <feGaussianBlur stdDeviation="2" result="offset-blur"/>
          <feFlood flood-color="#000000" flood-opacity="0.2"/>
          <feComposite in2="offset-blur" operator="in"/>
        </filter>
      </defs>
      
      <!-- 底部基础平台 -->
      <g class="base-platform">
        <!-- 基础阴影 -->
        <ellipse cx="90" cy="190" rx="70" ry="8" fill="#000000" opacity="0.2"/>
        
        <!-- 基础主体 -->
        <path d="M 25 175 L 155 175 L 160 185 L 20 185 Z" fill="url(#baseGradient)" stroke="#666" stroke-width="1"/>
        
        <!-- 基础顶面 -->
        <ellipse cx="90" cy="175" rx="67" ry="12" fill="#e0e0e0" stroke="#999" stroke-width="1"/>
      </g>
      
      <!-- 冷却塔主体 -->
      <g class="tower-body" filter="url(#dropShadow)">
        <!-- 塔体侧面 -->
        <path 
          :d="towerSidePath" 
          fill="url(#tower25dGradient)" 
          stroke="#555" 
          stroke-width="2"
        />
        
        <!-- 塔体正面 -->
        <path 
          :d="towerFrontPath" 
          :fill="towerColor" 
          stroke="#666" 
          stroke-width="1.5"
          opacity="0.9"
        />
        
        <!-- 塔体顶部椭圆 - 80px直径对应40px半径 -->
        <ellipse cx="90" cy="40" rx="40" ry="12" fill="url(#topGradient)" stroke="#666" stroke-width="1.5"/>
        
        <!-- 内部结构线条 -->
        <g class="internal-structure" opacity="0.3">
          <line v-for="i in 8" :key="i" 
            :x1="55 + i * 8" :y1="60" 
            :x2="55 + i * 8" :y2="160" 
            stroke="#333" stroke-width="0.5"/>
          <line v-for="i in 6" :key="i" 
            :x1="50" :y1="70 + i * 15" 
            :x2="130" :y2="70 + i * 15" 
            stroke="#333" stroke-width="0.5"/>
        </g>
      </g>
      
      <!-- 进出水管道 -->
      <g class="piping-system">
        <!-- 进水管道 -->
        <g class="inlet-pipe">
          <path d="M 10 120 Q 15 115 25 115 L 50 115 Q 55 115 55 120 L 55 130 Q 55 135 50 135 L 25 135 Q 15 135 10 130 Z" 
                :fill="pipeColor" stroke="#444" stroke-width="1"/>
          <ellipse cx="12" cy="125" rx="3" ry="8" :fill="darkPipeColor"/>
          <text x="5" y="110" font-size="10" fill="#666">进水</text>
          
          <!-- 水流动画 -->
          <g v-if="isRunning" class="water-flow">
            <circle v-for="i in 4" :key="i" 
              :cx="15 + i * 10" :cy="125" r="2" 
              :fill="waterColor" 
              class="flow-particle"
              :style="`animation-delay: ${i * 0.2}s`"/>
          </g>
        </g>
        
        <!-- 出水管道 -->
        <g class="outlet-pipe">
          <path d="M 125 150 Q 130 145 140 145 L 165 145 Q 170 145 170 150 L 170 160 Q 170 165 165 165 L 140 165 Q 130 165 125 160 Z" 
                :fill="pipeColor" stroke="#444" stroke-width="1"/>
          <ellipse cx="172" cy="155" rx="3" ry="8" :fill="darkPipeColor"/>
          <text x="145" y="140" font-size="10" fill="#666">出水</text>
          
          <!-- 水流动画 -->
          <g v-if="isRunning" class="water-flow">
            <circle v-for="i in 4" :key="i" 
              :cx="130 + i * 10" :cy="155" r="2" 
              :fill="waterColor" 
              class="flow-particle"
              :style="`animation-delay: ${i * 0.2}s`"/>
          </g>
        </g>
      </g>
      
      <!-- 蒸汽效果 -->
      <g v-if="isRunning" class="steam-system">
        <g v-for="(steam, index) in steamClouds25d" :key="index" class="steam-cloud-25d">
          <ellipse 
            :cx="steam.x" 
            :cy="steam.y" 
            :rx="steam.rx" 
            :ry="steam.ry" 
            fill="url(#steam25dGradient)"
            :opacity="steam.opacity"
            :style="`animation-delay: ${steam.delay}s`"
            class="steam-animation"
          />

        </g>
      </g>
      
      <!-- 控制面板 -->
      <g class="control-panel">
        <rect x="140" y="80" width="30" height="40" fill="#2c3e50" stroke="#34495e" stroke-width="1" rx="3"/>
        <rect x="143" y="83" width="24" height="34" fill="#34495e" stroke="#4a6741" stroke-width="0.5" rx="2"/>
        
        <!-- 显示屏 -->
        <rect x="145" y="85" width="20" height="12" fill="#000" stroke="#333" stroke-width="0.5" rx="1"/>
        <text x="155" y="93" text-anchor="middle" font-size="8" fill="#00ff00" class="digital-text">
          {{ displayTemperature }}°C
        </text>
        
        <!-- 指示灯 -->
        <circle cx="150" cy="105" r="3" :fill="statusColor" stroke="#fff" stroke-width="1" class="status-led"/>
        <circle cx="160" cy="105" r="3" :fill="powerColor" stroke="#fff" stroke-width="1" class="power-led"/>
        
        <!-- 按钮 -->
        <rect x="147" y="110" width="6" height="4" fill="#7f8c8d" stroke="#95a5a6" stroke-width="0.5" rx="1"/>
        <rect x="157" y="110" width="6" height="4" fill="#7f8c8d" stroke="#95a5a6" stroke-width="0.5" rx="1"/>
      </g>
      
      <!-- 传感器 -->
      <g class="sensors">
        <circle cx="70" cy="90" r="4" :fill="sensorColor" stroke="#fff" stroke-width="1"/>
        <text x="76" y="94" font-size="8" fill="#666">T1</text>
        
        <circle cx="110" cy="130" r="4" :fill="sensorColor" stroke="#fff" stroke-width="1"/>
        <text x="116" y="134" font-size="8" fill="#666">T2</text>
        
        <rect x="85" y="70" width="8" height="6" :fill="sensorColor" stroke="#fff" stroke-width="1" rx="1"/>
        <text x="90" y="68" font-size="8" fill="#666" text-anchor="middle">P</text>
      </g>
      
      <!-- 参数显示 -->
      <text x="90" y="105" text-anchor="middle" font-size="12" :fill="textColor" class="main-parameter">
        效率: {{ efficiency }}%
      </text>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
}>()

// 动画状态
const animationFrame = ref(0)
let animationId: number | null = null

// 组件样式
const componentStyle = computed(() => ({
  width: `${props.component.width}px`,
  height: `${props.component.height}px`,
  transform: `rotate(${props.component.rotation || 0}deg)`,
  opacity: props.component.opacity || 1
}))

// 获取数据值
const getValue = (key: string, defaultValue: any = 0) => {
  const data = props.component.data
  if (data?.dynamic?.[key] !== undefined) {
    return data.dynamic[key]
  }
  return data?.static?.[key] ?? defaultValue
}

// 运行状态和参数
const isRunning = computed(() => getValue('isRunning', true))
const temperature = computed(() => getValue('temperature', 28))
const efficiency = computed(() => getValue('efficiency', 92))
const flowRate = computed(() => getValue('flowRate', 1500))

// 显示参数
const displayTemperature = computed(() => Math.round(temperature.value * 10) / 10)

// 颜色配置
const towerColor = computed(() => getValue('towerColor', '#5dade2'))
const lightTowerColor = computed(() => {
  // 计算较亮的颜色
  const color = towerColor.value
  const hex = color.replace('#', '')
  const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + 40)
  const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + 40)
  const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + 40)
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
})
const darkTowerColor = computed(() => {
  // 计算较暗的颜色
  const color = towerColor.value
  const hex = color.replace('#', '')
  const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 40)
  const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 40)
  const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 40)
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
})

const pipeColor = computed(() => getValue('pipeColor', '#3498db'))
const darkPipeColor = computed(() => getValue('darkPipeColor', '#2980b9'))
const waterColor = computed(() => getValue('waterColor', '#85c1e9'))
const sensorColor = computed(() => getValue('sensorColor', '#e74c3c'))
const textColor = computed(() => getValue('textColor', '#2c3e50'))

// 状态颜色
const statusColor = computed(() => {
  if (!isRunning.value) return '#e74c3c'
  if (efficiency.value > 90) return '#27ae60'
  if (efficiency.value > 75) return '#f39c12'
  return '#e74c3c'
})

const powerColor = computed(() => isRunning.value ? '#27ae60' : '#95a5a6')

// 2.5D塔体路径 - 正确的双曲线形状
const towerSidePath = computed(() => {
  // 侧面视图：底部100px直径，中间50px直径，顶部80px直径
  // 中心线在x=90，所以：
  // 底部：40-140 (100px宽)
  // 中间：65-115 (50px宽)
  // 顶部：50-130 (80px宽)
  return `
    M 40 175
    Q 50 160 55 145
    Q 60 130 62 115
    Q 64 100 65 85
    Q 65 70 65 55
    Q 65 45 50 40
    L 130 40
    Q 115 45 115 55
    Q 115 70 115 85
    Q 116 100 118 115
    Q 120 130 125 145
    Q 130 160 140 175
    L 140 180
    L 40 180
    Z
  `
})

const towerFrontPath = computed(() => {
  // 正面视图：正确的双曲线形状
  // 底部：45-135 (90px宽，稍窄于侧面)
  // 中间：70-110 (40px宽，最细腰部)
  // 顶部：55-125 (70px宽)
  return `
    M 45 175
    Q 55 160 60 145
    Q 65 130 67 115
    Q 69 100 70 85
    Q 70 70 70 55
    Q 70 45 55 40
    L 125 40
    Q 110 45 110 55
    Q 110 70 110 85
    Q 111 100 113 115
    Q 115 130 120 145
    Q 125 160 135 175
    Z
  `
})

// 2.5D蒸汽云朵 - 从80px直径的塔顶开口冒出
const steamClouds25d = computed(() => {
  if (!isRunning.value) return []

  return [
    { x: 85, y: 35, rx: 12, ry: 6, opacity: 0.7, delay: 0 },
    { x: 95, y: 30, rx: 15, ry: 8, opacity: 0.5, delay: 0.4 },
    { x: 88, y: 22, rx: 18, ry: 9, opacity: 0.4, delay: 0.8 },
    { x: 98, y: 25, rx: 14, ry: 7, opacity: 0.6, delay: 1.2 },
    { x: 82, y: 18, rx: 16, ry: 8, opacity: 0.3, delay: 1.6 },
    { x: 90, y: 12, rx: 20, ry: 10, opacity: 0.2, delay: 2.0 }
  ]
})

// 动画循环
const animate = () => {
  animationFrame.value += 1
  animationId = requestAnimationFrame(animate)
}

onMounted(() => {
  if (isRunning.value) {
    animate()
  }
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped>
.cooling-tower-25d {
  position: relative;
  display: block;
  user-select: none;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.tower-svg {
  filter: drop-shadow(4px 8px 12px rgba(0,0,0,0.3));
  width: 100%;
  height: 100%;
  display: block;
  /* 确保SVG能够正确缩放 */
  max-width: 100%;
  max-height: 100%;
}

.steam-animation {
  animation: steam25dRise 4s ease-in-out infinite;
}



@keyframes steam25dRise {
  0% {
    transform: translateY(0) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.6;
  }
  100% {
    transform: translateY(-40px) scale(1.4);
    opacity: 0;
  }
}

.flow-particle {
  animation: waterFlow25d 2s linear infinite;
}

@keyframes waterFlow25d {
  0% { 
    opacity: 0; 
    transform: translateX(-10px) scale(0.5); 
  }
  50% { 
    opacity: 1; 
    transform: translateX(0) scale(1); 
  }
  100% { 
    opacity: 0; 
    transform: translateX(10px) scale(0.5); 
  }
}

.status-led, .power-led {
  animation: ledPulse 2s ease-in-out infinite;
}

@keyframes ledPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.digital-text {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  text-shadow: 0 0 3px currentColor;
}

.main-parameter {
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(255,255,255,0.8);
}

.internal-structure {
  pointer-events: none;
}

.control-panel {
  filter: drop-shadow(1px 2px 3px rgba(0,0,0,0.4));
}

.sensors circle, .sensors rect {
  filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.3));
}

/* 2.5D立体效果 */
.tower-body {
  transform-style: preserve-3d;
}

.base-platform {
  filter: drop-shadow(2px 4px 6px rgba(0,0,0,0.4));
}
</style>
