import { number } from 'vue-types'

/**
 * 注册
 */
export type RegisterForm = {
  tenantId: string
  username: string
  password: string
  confirmPassword?: string
  code?: string
  uuid?: string
  userType?: string
}

/**
 * 登录请求
 */
export interface LoginData {
  // tenantId: string
  username: string
  password: string
  rememberMe?: boolean
  code?: string
  uuid?: string
}

/**
 * 登录响应
 */
export interface LoginResult {
  token: string
}

/**
 * 验证码返回
 */
export interface VerifyCodeResult {
  captchaEnabled: boolean
  uuid?: string
  img?: string
}

/**
 * 租户
 */
export interface TenantVO {
  companyName: string
  domain: any
  tenantId: string
}

export interface TenantInfo {
  tenantEnabled: boolean
  voList: TenantVO[]
}

// 获取机组数据
export interface UnitParametersQuery {
  data: number
}
export interface UnitParametersDataVO {
  identifier?: string
  name?: string
  value?: number
  unit?: string
  deviceId?: string
}
export interface UnitParametersVO {
  unitId?: number
  unitName?: string
  datas?: UnitParametersDataVO[]
}

// 获取设备状态
export interface DeviceStatusQuery {
  data: number
}
export interface DeviceStatusVO {
  offline: number
  online: number
}

// 获取历史汽耗率
export interface downSamplingQuery {
  projectId: number
  startTime: string
  endTime: string
}
export interface DownSamplingHisVO {
  value: number
  occur: string
  time: string
}
export interface DownSamplingVO {
  his: DownSamplingHisVO[]
}

// 获取告警信息
export interface AlarmQuery {
  data: number
}

export interface AlarmVO {
  level: number
  cnt: number
}
