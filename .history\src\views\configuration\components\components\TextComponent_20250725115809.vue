<template>
  <div 
    class="text-component"
    :style="textStyle"
  >
    {{ displayText }}
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
  editing?: boolean
}>()

// 显示文本
const displayText = computed(() => {
  if (props.component.data?.dynamic?.dataSource) {
    // 动态数据
    return props.component.data.dynamic.field || '动态文本'
  } else {
    // 静态数据
    return props.component.data?.static || '文本内容'
  }
})

// 文本样式
// 文本样式
const textStyle = computed(() => {
  const style = props.component.style || {}
  return {
    color: style.fontColor || '#333',
    fontSize: `${style.fontSize || 14}px`,
    fontWeight: (style.fontWeight || 'normal') as any,
    textAlign: (style.textAlign || 'left') as any,
    backgroundColor: style.backgroundColor || 'transparent',
    border: style.borderWidth ? `${style.borderWidth}px solid ${style.borderColor || '#ddd'}` : 'none',
    borderRadius: style.borderRadius ? `${style.borderRadius}px` : '0',
    padding: '4px 8px',
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: style.textAlign === 'center' ? 'center' : style.textAlign === 'right' ? 'flex-end' : 'flex-start',
    boxShadow: style.boxShadow || 'none',
    wordBreak: 'break-word' as any,
    overflow: 'hidden'
  }
})
</script>

<style scoped>
.text-component {
  user-select: none;
}
</style>