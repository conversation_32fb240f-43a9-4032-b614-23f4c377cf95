<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form ref="queryFormRef" :inline="true" label-width="70">
          <el-form-item label="配置类型" prop="configType">
            <el-select v-model="queryParams.configType" placeholder="智能运维配置类型" clearable>
              <el-option v-for="dict in intelligent_operation_config_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="机组" prop="powerUnitIds">
            <el-select v-model="queryParams.powerUnitIds" placeholder="机组" clearable>
              <el-option v-for="dict in powerUnitList" :key="dict.id" :label="dict.name" :value="dict.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <!-- 操作 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
      </el-row>
      <!-- 表格 -->
      <el-table v-loading="state.loading" :data="IntelligentSolutionData">
        <!-- 编号 -->
        <el-table-column label="序号" type="index" align="center" width="80" />
        <el-table-column label="配置类型" align="center" prop="configType">
          <template #default="{ row }">
            {{ intelligent_operation_config_type.find(item => item.value === String(row.configType))?.label || "" }}
          </template>
        </el-table-column>
        <el-table-column label="机组" align="center" prop="powerUnitNames" />
        <el-table-column label="逻辑关系" align="center" prop="logic">
          <template #default="{ row }">
            {{ row.logic === '&&' ? '且' : (row.logic === '||' ? '或' : row.logic) }}
          </template>
        </el-table-column>
        <el-table-column label="方案类型" align="center" prop="chooseType">
          <template #default="{ row }">
            {{ Intelligent_choose_type.find(item => item.value === String(row.chooseType))?.label || "" }}
          </template>
        </el-table-column>

        <el-table-column label="条件类型" align="center" prop="condType">
          <template #default="{ row }">
            {{ Intelligent_cond_type_enum.find(item => item.value === String(row.condType))?.label || "" }}
          </template>
        </el-table-column>

        <el-table-column label="描述" align="center" prop="condRemark" />
        <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" />
            </el-tooltip>
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="state.total > 0"
        v-model:page="state.page.pageNum"
        v-model:limit="state.page.pageSize"
        :total="state.total"
        layout="total, prev, pager, next, jumper"
        @pagination="getpagination"
      />
    </el-card>

    <!-- 弹窗：新增/编辑/查看 -->
    <el-dialog
      v-model="dialogVisible"
      title="智能方案"
      width="1200"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form ref="fromRef" :size="formSize" :model="formModel" :rules="rules" label-width="auto">
        <el-row>
          <el-form-item label="配置类型" prop="configType">
            <el-select v-model="formModel.configType" placeholder="请选择配置类型" clearable :disabled="dialogType === 'view'">
              <el-option v-for="dict in intelligent_operation_config_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="方案类型" prop="chooseType">
            <el-select v-model="formModel.chooseType" placeholder="智能运维方案类型" clearable :disabled="dialogType === 'view'">
              <el-option v-for="dict in Intelligent_choose_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="条件类型" prop="condType">
            <el-select v-model="formModel.condType" placeholder="智能运维条件类型" clearable :disabled="dialogType === 'view'">
              <el-option v-for="dict in Intelligent_cond_type_enum" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="机组" prop="powerUnitIds">
            <el-select v-model="formModel.powerUnitIds" multiple placeholder="请选择机组" clearable :disabled="dialogType === 'view'">
              <el-option v-for="dict in powerUnitList" :key="dict.id" :label="dict.name" :value="dict.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="逻辑关系" prop="logic">
            <el-radio-group v-model="formModel.logic" :disabled="dialogType === 'view'">
              <el-radio label="&&">且</el-radio>
              <el-radio label="||">或</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="因子" prop="factor">
            <el-input v-model="formModel.factor" placeholder="请输入因子" clearable style="width: 210px;" :disabled="dialogType === 'view'" />
          </el-form-item>

          <el-form-item label="条件" prop="cond">
            <el-row style="margin-bottom: 10px" gutter="20">
              <el-col :span="24">
                <el-button type="primary" size="small" @click="addCond" :disabled="dialogType === 'view'"> 新增条件 </el-button>
              </el-col>
              <el-col :span="24">
                <div v-for="(item, index) in formModel.cond.parameters" :key="index">
                  <el-row style="margin-bottom: 10px">
                    <el-col :span="5">
                      <el-form-item label="设备" prop="deviceId">
                        <el-select
                          v-model="item.deviceId"
                          placeholder="请选择设备"
                          @change="val => handleDependentDevices(val, item)"
                          clearable
                          :disabled="dialogType === 'view'"
                        >
                          <el-option v-for="dev in deviceList" :key="dev.deviceId" :label="dev.productName" :value="dev.deviceId" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="点位" prop="identifier">
                        <el-select v-model="item.identifier" placeholder="请选择点位" clearable filterable :disabled="dialogType === 'view'">
                          <el-option
                            v-for="option in deviceIdentifierOptions[item.deviceId] || []"
                            :key="option.identifier"
                            :label="option.name"
                            :value="option.identifier"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="比较" prop="comparator">
                        <el-select
                          v-model="item.comparator"
                          placeholder="请选择比较"
                          filterable
                          clearable
                          :rules="[{ required: true, message: '比较不能为空', trigger: 'change' }]"
                          :disabled="dialogType === 'view'"
                        >
                          <el-option label=">" value=">" />
                          <el-option label="<" value="<" />
                          <el-option label="=" value="=" />
                          <el-option label="≥" value="≥" />
                          <el-option label="≤" value="≤" />
                          <el-option label="≠" value="≠" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="值" prop="value">
                        <el-input v-model="item.value" placeholder="请填写值" :disabled="dialogType === 'view'" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="4">
                      <el-button type="danger" size="small" @click="deleteCond(index)" :disabled="dialogType === 'view'"> 删除 </el-button>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item label="条件描述" prop="condRemark">
            <el-input
              type="textarea"
              v-model="formModel.condRemark"
              placeholder="请填写条件描述"
              :rows="6"
              style="width: 300px"
              :disabled="dialogType === 'view'"
            />
          </el-form-item>

          <el-form-item label="原因说明" prop="explain">
            <el-input
              type="textarea"
              v-model="formModel.explain"
              placeholder="请填写原因说明"
              :rows="6"
              style="width: 300px"
              :disabled="dialogType === 'view'"
            />
          </el-form-item>
        </el-row>
      </el-form>

      <!-- 弹窗底部按钮，查看时只显示“关闭”，编辑/新增时显示 确定 / 重置 -->
      <template #footer>
        <span class="dialog-footer" v-if="dialogType === 'view'">
          <el-button @click="handleClose">关 闭</el-button>
        </span>
        <span class="dialog-footer" v-else>
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitForm(fromRef)">确 定</el-button>
          <el-button @click="resetForm(fromRef)">重置</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  powerUnitAllList,
  getDevicePoint,
  getDeviceList,
  getPlanChooseConfigList,
  deletePlanChooseConfig,
  editPlanChooseConfig,
  addPlanChooseConfig,
} from './index.api'
import { ComponentSize, FormInstance, FormRules } from 'element-plus'

import emitter from '@/utils/eventBus.js'
import { useLocalCache, CACHE_KEY } from '@/hooks/web/useCache'

const fromRef = ref<FormInstance>()
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const { id: projectId } = cachedProjects

emitter.on('projectListChanged', () => {
  location.reload()
})
emitter.on('projectListChanged', () => {
  location.reload()
})
import { ComponentInternalInstance } from 'vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { intelligent_operation_config_type, Intelligent_choose_type, Intelligent_cond_type_enum } = toRefs<any>(
  proxy?.useDict('intelligent_operation_config_type', 'Intelligent_choose_type', 'Intelligent_cond_type_enum')
)

// 弹窗可见性
const dialogVisible = ref(false)
// 区分对话框模式：新增 / 编辑 / 查看
const dialogType = ref<'add' | 'edit' | 'view'>('add')

// 查询参数
const queryParams = reactive({
  // chooseType: '',
  // condType: '',
  configType: '',
  powerUnitIds: '',
})
const formSize = ref<ComponentSize>('default')

// 表单结构
const formModel = reactive({
  code: '',
  id: '',
  powerUnitIds: '',
  configType: '',
  logic: '&&',
  chooseType: '',
  condType: '',
  factor: '',
  cond: {
    parameters: [{ deviceId: '', identifier: '', comparator: '', value: '', change: '', relation: '' }],
  },
  condRemark: '',
  explain: '',
  // 用于显示机组名称
  powerUnitNames: '',
})

interface RuleForm {
  code: string
  id: string
  powerUnitIds: string
  configType: string
  logic: string
  chooseType: string
  condType: string
  factor: number
  cond: {
    parameters: {
      deviceId: string
      identifier: string
      comparator: string
      value: string
      change: string
      relation: string
    }[]
  }
  condRemark: string
}
const rules = reactive<FormRules<RuleForm>>({})
const showSearch = ref(true)

// 机组数组
const powerUnitList = ref([])
// 设备列表
const deviceList = ref([])
// 全部点位分类（设备id -> 点位数组）
const deviceIdentifierOptions = reactive<Record<string, any[]>>({})

// 表格数据
const IntelligentSolutionData = ref([])

// 分页 & 加载
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})

const queryFormRef = ref<FormInstance>()

/** 根据行数据填充表单，用于编辑和查看 */
const fillFormWithRowData = async (row: any) => {
  formModel.id = row.id
  formModel.code = row.code
  formModel.powerUnitIds = row.powerUnitIdList
    ? row.powerUnitIdList.map((item: any) => Number(item))
    : row.powerUnitIds
    ? row.powerUnitIds.split(',').map((item: string) => Number(item))
    : []
  formModel.configType = String(row.configType)
  formModel.chooseType = String(row.chooseType)
  formModel.condType = String(row.condType)
  formModel.logic = row.logic
  formModel.factor = row.factor

  formModel.cond = row.cond
    ? JSON.parse(JSON.stringify(row.cond))
    : { parameters: [{ deviceId: '', identifier: '', comparator: '', value: '', change: '', relation: '' }] }

  // 预加载点位信息
  if (formModel.cond.parameters && formModel.cond.parameters.length > 0) {
    for (const param of formModel.cond.parameters) {
      if (param.productKey && param.deviceId) {
        const res = await getDevicePoint(param.productKey)
        if (res.data && res.data.model && res.data.model.properties) {
          deviceIdentifierOptions[param.deviceId] = res.data.model.properties
          // 如果当前的 identifier 不在最新的 options 中，则清空
          const options = deviceIdentifierOptions[param.deviceId] || []
          if (!options.find((opt: any) => opt.identifier === param.identifier)) {
            param.identifier = ''
          }
        } else {
          deviceIdentifierOptions[param.deviceId] = []
          param.identifier = ''
        }
      }
    }
  }

  formModel.condRemark = row.condRemark
  formModel.explain = row.explain
}

/** 查看 */
const handleView = async (row: any) => {
  dialogType.value = 'view'
  await fillFormWithRowData(row)
  dialogVisible.value = true
}

/** 新增 */
const handleAdd = () => {
  dialogType.value = 'add'
  resetForm(fromRef.value)
  dialogVisible.value = true
}

/** 修改 */
const handleUpdate = async (row: any) => {
  dialogType.value = 'edit'
  await fillFormWithRowData(row)
  dialogVisible.value = true
}

/** 删除 */
const handleDelete = (row: any) => {
  deletePlanChooseConfig([row.id]).then((res) => {
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList()
    }
  })
}

/** 查询 */
const handleQuery = () => {
  state.query = { ...queryParams }
  state.page.pageNum = 1
  getList()
}

/** 重置查询 */
const resetQuery = () => {
  // queryParams.chooseType = ''
  // queryParams.condType = ''
  queryParams.configType = ''
  queryParams.powerUnitIds = ''
  queryFormRef.value?.resetFields()
  state.query = { ...queryParams }
  state.page.pageNum = 1
  getList()
}

/** 获取表格数据 */
const getList = () => {
  state.loading = true
  const params = {
    ...state.page,
    ...state.query,
    projectId,
  }
  getPlanChooseConfigList(params)
    .then((res) => {
      if (res.code === 200) {
        IntelligentSolutionData.value = res.data.rows
        state.total = res.data.total
      }
    })
    .finally(() => {
      state.loading = false
    })
}

/** 获取机组信息 */
const getPowerUnit = () => {
  const params = { projectId }
  powerUnitAllList(params).then((res) => {
    if (res.code === 200) {
      powerUnitList.value = res.data
    }
  })
}

/** 获取设备列表 */
const getDeviceData = () => {
  const params = { projectId }
  getDeviceList(params).then((res) => {
    if (res.code === 200) {
      deviceList.value = res.data.rows
    }
  })
}

/** 设备选择联动，获取点位列表 */
const handleDependentDevices = async (val: any, conditionItem: any) => {
  const selectedDevice = deviceList.value.find((device) => device.deviceId === val)
  if (!selectedDevice) {
    deviceIdentifierOptions[val] = []
    conditionItem.identifier = ''
    return
  }
  const res = await getDevicePoint(selectedDevice.productKey)
  if (res.data && res.data.model && res.data.model.properties) {
    deviceIdentifierOptions[val] = res.data.model.properties
    // 检查原 identifier 是否还有效
    const options = deviceIdentifierOptions[val] || []
    if (!options.find((opt: any) => opt.identifier === conditionItem.identifier)) {
      conditionItem.identifier = ''
    }
  } else {
    deviceIdentifierOptions[val] = []
    conditionItem.identifier = ''
  }
}

/** 新增条件 */
const addCond = () => {
  formModel.cond.parameters.push({
    deviceId: '',
    identifier: '',
    comparator: '',
    value: '',
    change: '',
    relation: '',
  })
}

/** 删除条件 */
const deleteCond = (index: number) => {
  if (formModel.cond.parameters.length === 1) {
    ElMessage.warning('至少需要保留一个条件')
    return
  }
  formModel.cond.parameters.splice(index, 1)
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm(fromRef.value)
}

/** 提交(新增/编辑) */
const submitForm = (formEl: FormInstance | undefined) => {
  // 将 powerUnitIds 数组转成逗号分隔
  if (Array.isArray(formModel.powerUnitIds)) {
    const idsArray = formModel.powerUnitIds
    formModel.powerUnitIds = idsArray.join(',')
    // 拼接机组名称
    const matchingUnits = powerUnitList.value.filter((unit) => idsArray.includes(unit.id))
    formModel.powerUnitNames = matchingUnits.map((unit) => unit.name).join(',')
  }

  // cond 带上一些属性
  formModel.cond = {
    ...formModel.cond,
    logic: formModel.logic,
    chooseType: formModel.chooseType,
    condType: formModel.condType,
    factor: formModel.factor,
  }

  const params = {
    projectId,
    ...formModel,
  }
  if (!formEl) return
  formEl.validate((valid, fields) => {
    if (valid) {
      if (!formModel.id) {
        // 新增
        addPlanChooseConfig(params).then((response) => {
          if (response.code === 200) {
            dialogVisible.value = false
            getList()
          }
        })
      } else {
        // 编辑
        editPlanChooseConfig(params).then((response) => {
          if (response.code === 200) {
            dialogVisible.value = false
            getList()
          }
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

/** 重置表单 */
const resetForm = (formEl: FormInstance | undefined) => {
  formModel.id = ''
  formModel.powerUnitIds = ''
  formModel.configType = ''
  formModel.logic = '&&'
  formModel.chooseType = ''
  formModel.condType = ''
  formModel.factor = ''
  formModel.cond = {
    parameters: [
      {
        deviceId: '',
        identifier: '',
        comparator: '',
        value: '',
        change: '',
        relation: '',
      },
    ],
  }
  formModel.condRemark = ''
  formModel.explain = ''
  formModel.powerUnitNames = ''
  formEl?.resetFields()
}

/** 分页 */
const getpagination = (pagination: { page: number; limit: number }) => {
  state.page.pageNum = pagination.page
  state.page.pageSize = pagination.limit
  getList()
}

onMounted(() => {
  getList()
  getPowerUnit()
  getDeviceData()
})
</script>

<style lang="scss" scoped>
:deep(.el-tag.el-tag--info){
  color: #000 !important;
}
:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  border: none;
}

:deep(.el-card__body) {
  border: none;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

:deep(.el-table__cell) {
  // color: rgba(204, 204, 204, 1) !important;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}

:deep(.el-tree) {
  background-color: transparent;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #07355c;
}

:deep(.el-tree-node__expand-icon) {
  color: #fff;
}
:deep(.el-select__wrapper){
  width: 180px;
  color: #fff!important;
  background: rgb(3, 43, 82) !important;
  box-shadow:0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
  color: #fff;
}
:deep(.el-tree-node__label) {
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}

:deep(.el-select__tags .el-tag--info) {
  background-color: #153059 !important;
}
</style>
