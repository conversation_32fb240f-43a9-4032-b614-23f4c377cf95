import { ref } from 'vue'

// 3D模型信息接口
export interface Model3DInfo {
  id: string
  name: string
  description?: string
  fileName: string
  fileSize: number
  fileType: string
  filePath: string
  thumbnail?: string
  tags?: string[]
  category?: string
  createdAt: string
  updatedAt: string
  createdBy: string
  downloadCount: number
  isPublic: boolean
}

// 模型上传进度接口
export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

// 模型管理服务
class ModelService {
  private models = ref<Model3DInfo[]>([])
  private uploadProgress = ref<UploadProgress | null>(null)

  constructor() {
    this.loadModelsFromStorage()
  }

  // 获取所有模型
  getModels() {
    return this.models.value
  }

  // 获取模型列表的响应式引用
  getModelsRef() {
    return this.models
  }

  // 获取上传进度
  getUploadProgress() {
    return this.uploadProgress
  }

  // 从本地存储加载模型列表
  private loadModelsFromStorage() {
    try {
      const stored = localStorage.getItem('configuration_3d_models')
      if (stored) {
        this.models.value = JSON.parse(stored)
      }
    } catch (error) {
      console.error('加载模型列表失败:', error)
      this.models.value = []
    }
  }

  // 保存模型列表到本地存储
  private saveModelsToStorage() {
    try {
      localStorage.setItem('configuration_3d_models', JSON.stringify(this.models.value))
    } catch (error) {
      console.error('保存模型列表失败:', error)
    }
  }

  // 生成唯一ID
  private generateId(): string {
    return 'model_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  // 上传3D模型文件
  async uploadModel(file: File, metadata: {
    name: string
    description?: string
    tags?: string[]
    category?: string
    isPublic?: boolean
  }): Promise<Model3DInfo> {
    return new Promise((resolve, reject) => {
      // 验证文件类型
      const allowedTypes = [
        'model/gltf-binary',
        'model/gltf+json',
        'application/octet-stream', // .glb files
        'model/obj',
        'model/fbx',
        'model/3ds',
        'model/dae',
        'model/stl'
      ]
      
      const fileExtension = file.name.split('.').pop()?.toLowerCase()
      const allowedExtensions = ['glb', 'gltf', 'obj', 'fbx', '3ds', 'dae', 'stl']
      
      if (!allowedExtensions.includes(fileExtension || '')) {
        reject(new Error('不支持的文件格式。支持的格式：GLB, GLTF, OBJ, FBX, 3DS, DAE, STL'))
        return
      }

      // 检查文件大小 (限制为50MB)
      const maxSize = 50 * 1024 * 1024
      if (file.size > maxSize) {
        reject(new Error('文件大小不能超过50MB'))
        return
      }

      // 创建FileReader来读取文件
      const reader = new FileReader()
      
      reader.onloadstart = () => {
        this.uploadProgress.value = { loaded: 0, total: file.size, percentage: 0 }
      }

      reader.onprogress = (event) => {
        if (event.lengthComputable) {
          const percentage = Math.round((event.loaded / event.total) * 100)
          this.uploadProgress.value = {
            loaded: event.loaded,
            total: event.total,
            percentage
          }
        }
      }

      reader.onload = async (event) => {
        try {
          const arrayBuffer = event.target?.result as ArrayBuffer
          
          // 生成模型信息
          const modelInfo: Model3DInfo = {
            id: this.generateId(),
            name: metadata.name,
            description: metadata.description || '',
            fileName: file.name,
            fileSize: file.size,
            fileType: fileExtension || '',
            filePath: '', // 将在保存文件后设置
            thumbnail: '',
            tags: metadata.tags || [],
            category: metadata.category || 'general',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'current_user', // 实际应用中应该从用户信息获取
            downloadCount: 0,
            isPublic: metadata.isPublic || false
          }

          // 保存文件到本地存储 (实际应用中应该上传到服务器)
          const base64Data = this.arrayBufferToBase64(arrayBuffer)
          const filePath = `models/${modelInfo.id}.${fileExtension}`
          
          // 保存到localStorage (实际应用中应该保存到服务器)
          localStorage.setItem(`model_file_${modelInfo.id}`, base64Data)
          modelInfo.filePath = filePath

          // 生成缩略图 (如果是GLTF/GLB格式)
          if (['glb', 'gltf'].includes(fileExtension || '')) {
            try {
              const thumbnail = await this.generateThumbnail(arrayBuffer, fileExtension || '')
              modelInfo.thumbnail = thumbnail
            } catch (error) {
              console.warn('生成缩略图失败:', error)
            }
          }

          // 添加到模型列表
          this.models.value.push(modelInfo)
          this.saveModelsToStorage()

          this.uploadProgress.value = null
          resolve(modelInfo)
        } catch (error) {
          this.uploadProgress.value = null
          reject(error)
        }
      }

      reader.onerror = () => {
        this.uploadProgress.value = null
        reject(new Error('文件读取失败'))
      }

      reader.readAsArrayBuffer(file)
    })
  }

  // 删除模型
  async deleteModel(modelId: string): Promise<void> {
    const index = this.models.value.findIndex(model => model.id === modelId)
    if (index === -1) {
      throw new Error('模型不存在')
    }

    // 删除文件数据
    localStorage.removeItem(`model_file_${modelId}`)
    
    // 从列表中移除
    this.models.value.splice(index, 1)
    this.saveModelsToStorage()
  }

  // 获取模型文件数据
  async getModelFile(modelId: string): Promise<ArrayBuffer> {
    const base64Data = localStorage.getItem(`model_file_${modelId}`)
    if (!base64Data) {
      throw new Error('模型文件不存在')
    }

    return this.base64ToArrayBuffer(base64Data)
  }

  // 更新模型信息
  async updateModel(modelId: string, updates: Partial<Model3DInfo>): Promise<Model3DInfo> {
    const index = this.models.value.findIndex(model => model.id === modelId)
    if (index === -1) {
      throw new Error('模型不存在')
    }

    const model = this.models.value[index]
    const updatedModel = {
      ...model,
      ...updates,
      updatedAt: new Date().toISOString()
    }

    this.models.value[index] = updatedModel
    this.saveModelsToStorage()

    return updatedModel
  }

  // 搜索模型
  searchModels(query: string, category?: string): Model3DInfo[] {
    let results = this.models.value

    if (category && category !== 'all') {
      results = results.filter(model => model.category === category)
    }

    if (query.trim()) {
      const searchTerm = query.toLowerCase()
      results = results.filter(model => 
        model.name.toLowerCase().includes(searchTerm) ||
        model.description?.toLowerCase().includes(searchTerm) ||
        model.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      )
    }

    return results
  }

  // ArrayBuffer转Base64
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  // Base64转ArrayBuffer
  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = atob(base64)
    const bytes = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }
    return bytes.buffer
  }

  // 生成缩略图 (简化版本)
  private async generateThumbnail(arrayBuffer: ArrayBuffer, fileType: string): Promise<string> {
    // 这里应该使用Three.js加载模型并生成缩略图
    // 为了简化，这里返回一个默认的缩略图
    return '/src/assets/icons/3d-model-placeholder.png'
  }

  // 获取模型统计信息
  getModelStats() {
    const models = this.models.value
    return {
      total: models.length,
      byCategory: models.reduce((acc, model) => {
        acc[model.category || 'general'] = (acc[model.category || 'general'] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      totalSize: models.reduce((acc, model) => acc + model.fileSize, 0),
      recentUploads: models
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5)
    }
  }
}

// 导出单例实例
export const modelService = new ModelService()
