// 通道配置Api
import request from '@/utils/request'
import { AxiosPromise, AxiosResponse } from 'axios'

const Api = {}
export interface TagList {
  id?: number
  content: string
  messageType: string
  status: boolean
  createAt: number
  updateAt: number
}
export interface Information {
  id?: number
  projectName: string
  projectType: string
  address: string
  latitude: number | undefined
  longitude: number | undefined
  altitude: number | undefined
  remark: string
  // createAt:  createAt: number | undefined
  createAt: number | undefined
}

export interface InformQuery extends PageQuery {
  projectName: string
  projectType: string
  latitude: number | undefined
  longitude: number | undefined
  altitude: number | undefined
  remark: string
  createAt: number | undefined
}

export interface subsystemsdata {}
// 水质报告tab配置
export interface waterConfigData {
  id?: number
  name: string
  project: string
  createTime: number | undefined
}
// 水质点位配置
export interface waterPointData {
  id?: number
  name: string
  project: string
  createTime: number | undefined
}
