/**
 * 分公司查询参数
 */
export interface DeptQuery extends PageQuery {
  deptName?: string
  status?: number
}

/**
 * 分公司类型
 */
export interface DeptVO extends BaseEntity {
  id: number | string
  parentName: string
  parentId: number | string
  children: DeptVO[]
  deptId: number | string
  deptName: string
  orderNum: number
  leader: string
  phone: string
  email: string
  status: string
  delFlag: string
  ancestors: string
  menuId: string | number
}

/**
 * 分公司表单类型
 */
export interface DeptForm {
  parentName?: string
  parentId?: number | string
  children?: DeptForm[]
  id?: number | string
  deptName?: string
  orderNum?: number
  leader?: string
  phone?: string
  email?: string
  status?: string
  delFlag?: string
  ancestors?: string
}
