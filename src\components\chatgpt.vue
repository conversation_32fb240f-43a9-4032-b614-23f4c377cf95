<template>
  <!-- <div>
        <div class="chatgpt" @click="centerDialogVisible = true">
            <img class="hoverimg" src="../assets/images/gpt.png" alt="">
        </div>
        <div class="popupgo" v-if="centerDialogVisible">
            <img class="imgsize" @click="centerDialogVisible = false" src="../assets/images/close.png" alt="">
            <iframe width="100%" height="100%" src="https://ai.menghn.com" frameborder="0"></iframe>
        </div>
    </div> -->
</template>
<script setup>
// import { ref } from 'vue'
// const centerDialogVisible = ref(false)
</script>
<style scope>
/* .imgsize{
    width: 20px;
    height: 20px;
    position: absolute;
    top: -8px;
    right: -8px;
}
    .chatgpt {
        position: fixed;
        text-align: center;
        width: 50px;
        height: 50px;
        bottom: 80px;
        right: 30px;
        z-index: 100;
        background: #fff;
        border-radius: 50%;
        padding: 5px;
        box-sizing: border-box;
        box-shadow:0px 12px 32px 4px rgba(0, 0, 0, .04), 0px 8px 20px rgba(0, 0, 0, .08);
    }
    .hoverimg{
        width: 35px;
        height: 35px;
    }
    .popupgo{
        position: fixed;
        bottom: 100px;
        right: 30px;
        z-index: 100;
        width: 500px;
        height: 500px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow:0px 12px 32px 4px rgba(0, 0, 0, .04), 0px 8px 20px rgba(0, 0, 0, .08);
    } */
</style>
