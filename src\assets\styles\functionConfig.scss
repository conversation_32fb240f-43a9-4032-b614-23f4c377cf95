.el-tabs--border-card{
    background: transparent !important;
  }
.el-tabs__item:hover {
    color: #fff; /* 悬停时的文字颜色 -------------*/
  }
.el-tabs--border-card > .el-tabs__header {
    background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important;
    border-bottom: 1px solid rgba(33, 148, 255, 1);
  }
.el-tabs--border-card {
    border: none;
  }
.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
    border-radius: 2px;
    background: linear-gradient(180deg, rgb(6, 101, 243) 0%, rgba(119, 122, 126, 0.1) 100%);
    opacity: 0.8;
    color: rgba(255, 255, 255, 1);
    border: 1px solid rgba(0, 0, 0, 1);
  }