<template>
  <yt-crud
    ref="crudRef"
    :data="data"
    :column="column"
    :table-props="{
        selection: false,//多选
        dialogBtn:false,
        menuSlot: true,//自定义操作按钮
      }"
    :form-props="{
        width: 550,
        labelWidth:150
      }"
    @save-fun="onSave"
    @del-fun="handleDelete"
    @onLoad="getData"
    :loading="state.loading"
    :total="state.total"
    v-model:page="state.page"
    v-model:query="state.query"
  >
    <template #menuSlot="scope">
      <el-tooltip class="box-item" effect="dark" content="智能运维告警配置" placement="top">
        <el-button link type="primary" icon="Operation" @click="openalarmMaintenance(scope.row)" />
      </el-tooltip>
      <el-tooltip class="box-item" effect="dark" content="复制" placement="top">
        <el-button link type="primary" icon="DocumentCopy" @click="aramCopy(scope.row)" />
      </el-tooltip>
    </template>
    <template #enable="{ row }">
      <el-switch v-model="row.enable" active-color="#0070ff" inactive-color="#dcdfe6" @change="handleSwitchChange(row)" size="small" />
    </template>
  </yt-crud>
  <el-dialog
    v-model="copyVisible"
    title="复制"
    width="500"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
  >
    <el-form label-position="top" style="margin-bottom: 20px;">
      <el-form-item label="请选择要被复制的配置">
        <el-select
          ref="copySelectRef"
          v-model="selectedCopyId"
          filterable
          clearable
          remote
          :remote-method="handleRemoteSearch"
          :loading="copystate.loading"
          placeholder="搜索并选择配置"
          style="width: 100%;"
        >
          <el-option v-for="item in filteredOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <div style="text-align: right">
      <el-button type="primary" @click="copyHandleSave">保存</el-button>
      <el-button @click="copyHandleClear">清空</el-button>
      <el-button @click="copyhandleClose">关闭</el-button>
    </div>
  </el-dialog>
  <el-dialog
    v-model="dialogalarmMaintenanceLIist"
    title="告警运维配置列表"
    width=""
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    @close="handleDialogClose"
  >
    <div class="alarmMaintenanceLIist">
      <div style="margin-bottom: 10px;">
        <el-button @click="addalarmMaintenanceLIist" type="primary">新增</el-button>
        <el-button @click="batchDelete" type="danger" :disabled="selectedRows.length === 0" style="margin-left: 10px;">
          批量删除 ({{ selectedRows.length }})
        </el-button>
      </div>
      <el-table :data="alarmMaintenanceLIist" border style="width: 100%" @selection-change="handleSelectionChange" ref="alarmTableRef">
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="80" type="index" />
        <el-table-column prop="powerUnitName" label="所属机组" />
        <el-table-column prop="identifierName" label="点位名称" width="180" />
        <el-table-column prop="remark" label="计算公式" />
        <el-table-column fixed="right" label="操作" min-width="120">
          <template #default="scope">
            <el-button link type="primary" icon="Delete" size="small" @click="alarmDelete(scope.row)"></el-button>
            <el-button link type="primary" icon="Edit" size="small" @click="alarmEdit(scope.row)"></el-button>
            <el-button link type="primary" icon="DocumentCopy" size="small" @click="alarmdetailscopy(scope.row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div style="text-align: right">
      <el-button @click="currentLineClose">关闭</el-button>
    </div>
  </el-dialog>
  <el-dialog
    v-model="dialogalarmMaintenanceVisible"
    title="新增/编辑"
    :close-on-press-escape="false"
    :before-close="handleClose"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
  >
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="auto" class="demo-ruleForm" :size="formSize" status-icon>
      <!-- 设备下拉框 -->
      <el-form-item label="设备" prop="deviceId">
        <el-select v-model="ruleForm.deviceId" placeholder="请选择设备" filterable clearable @change="handleDeviceChange">
          <el-option v-for="item in alarmBasic.device" :key="item.value" :label="item.productName" :value="item.deviceId" />
        </el-select>
      </el-form-item>

      <!-- 点位下拉框 -->
      <el-form-item label="点位" prop="identifier">
        <el-select v-model="ruleForm.identifier" placeholder="请选择点位" filterable clearable>
          <el-option v-for="item in alarmBasic.pointList" :key="item.identifier" :label="item.name" :value="item.identifier" />
          <!-- <el-option label="点位2" value="location2" />
          <el-option label="点位3" value="location3" /> -->
        </el-select>
      </el-form-item>

      <!-- 机组下拉框 -->
      <el-form-item label="机组" prop="powerUnitId">
        <el-select v-model="ruleForm.powerUnitId" placeholder="请选择机组" filterable clearable multiple collapse-tags>
          <el-option v-for="item in alarmBasic.powerUnitallList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <!-- 逻辑关系单选 -->
      <el-form-item label="逻辑关系" prop="logicRelation">
        <el-radio-group v-model="ruleForm.logicRelation">
          <el-radio label="&">且</el-radio>
          <el-radio label="||">或</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 小表单 - 点位，比较，数值，依赖设备 -->
      <el-form-item label="依赖点位" prop="condition">
        <el-button type="primary" icon="el-icon-plus" @click="addLogicDetail"> 新增 </el-button>
        <div v-for="(item, index) in ruleForm.condition" :key="index" class="logic-detail-row" style="margin-bottom: 20px;">
          <!-- 第一行：依赖设备、点位、比较 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="依赖设备">
                <el-select v-model="item.deviceId" placeholder="请选择依赖设备" filterable clearable @change="handleDependentDevices">
                  <el-option v-for="option in alarmBasic.device" :key="option.value" :label="option.productName" :value="option.deviceId" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="点位">
                <el-select v-model="item.identifier" placeholder="请选择点位" filterable clearable>
                  <el-option v-for="option in alarmBasic.dependPointList" :key="option.identifier" :label="option.name" :value="option.identifier" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="比较">
                <el-select v-model="item.comparator" placeholder="请选择比较" filterable clearable>
                  <el-option label=">" value=">" />
                  <el-option label="<" value="<" />
                  <el-option label="==" value="==" />
                  <el-option label="≥" value="≥" />
                  <el-option label="≤" value="≤" />
                  <el-option label="≠" value="!=" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行：数值、持续时间、是否关联 -->
          <el-row :gutter="20" style="margin-top: 10px;">
            <el-col :span="8">
              <el-form-item label="数值">
                <el-input v-model="item.value" placeholder="请输入数值" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="持续时间">
                <el-input v-model="item.change" placeholder="请输入持续时间" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否关联">
                <el-switch v-model="item.relation" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行：告警级别，仅在关联开启时显示 -->
          <el-row :gutter="20" style="margin-top: 10px;" v-if="item.relation">
            <el-col :span="8">
              <el-form-item label="告警级别">
                <el-select v-model="item.alarmLevel" placeholder="请选择告警级别" filterable clearable>
                  <el-option label="低限预警" value="1" />
                  <el-option label="低限报警" value="2" />
                  <el-option label="低限严重警告" value="3" />
                  <el-option label="高限预警" value="4" />
                  <el-option label="高限报警" value="5" />
                  <el-option label="高限严重警告" value="6" />
                  <el-option label="正常值" value="-1" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 操作按钮 -->
          <el-button type="danger" icon="el-icon-delete" @click="removeLogicDetail(index)" :disabled="disableddielete" size="mini"> 删除 </el-button>
        </div>
      </el-form-item>

      <el-form-item label="计算内容" prop="remark">
        <el-input type="textarea" v-model="ruleForm.remark" placeholder="请输入计算内容" :rows="2" style="width: 300px" />
      </el-form-item>
      <el-form-item label="结论" prop="conclusion">
        <el-input type="textarea" v-model="ruleForm.conclusion" placeholder="请输入结论" :rows="4" style="width: 300px" />
      </el-form-item>
      <el-form-item label="建议" prop="recommend">
        <el-input type="textarea" v-model="ruleForm.recommend" placeholder="请输入建议" :rows="6" style="width: 300px" />
      </el-form-item>
    </el-form>
    <div style="text-align: right">
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button @click="handleClear">清空</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { IColumn } from '@/components/common/types/tableCommon'
import {
  getConfigList,
  saveConfig,
  deleteConfig,
  getConfigDetail,
  addConfigDetail,
  getDeviceList,
  getpowerUnitall,
  getDevicePoint,
  deleteConfigDetail,
  editConfigDetail,
  aramcopyConfig,
  aramcopydetail,
} from './api/alarm.api'
import { subSystemList } from '@/api/project/api'
import { getRuleList } from '../ruleEngine/api/rule.api'
import YtCrud from '@/components/common/yt-crud.vue'
import { getTemplatesList } from '../channel/api/templates.api'

import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

import type { ComponentSize, FormInstance, FormRules } from 'element-plus'

interface RuleForm {
  deviceId: string
  identifier: string
  powerUnitId: []
  logicRelation: string
  condition: Array<{
    identifier: string
    comparator: string
    value: string
    deviceId: string
    relation: boolean
    alarmLevel: string
    change: string
  }>
  remark: string
  conclusion: string
  recommend: string
}

const formSize = ref<ComponentSize>('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  deviceId: '',
  identifier: '',
  powerUnitId: [],
  logicRelation: '&',
  condition: [{ identifier: '', comparator: '', value: '', deviceId: '', relation: false, alarmLevel: '', change: '' }],
  remark: '',
  conclusion: '',
  recommend: '',
})

const rules = reactive<FormRules<RuleForm>>({
  deviceId: [{ required: true, message: '请选择设备', trigger: 'change' }],
  identifier: [{ required: true, message: '请选择点位', trigger: 'change' }],
  powerUnitId: [{ required: true, message: '请选择机组', trigger: 'change' }],
  logicRelation: [{ required: true, message: '请选择逻辑关系', trigger: 'change' }],
  condition: [
    {
      required: true,
      message: '请填写所有逻辑条件',
      trigger: 'change',
    },
  ],
  conclusion: [{ required: true, message: '请填写结论', trigger: 'change' }],
})

const addLogicDetail = () => {
  ruleForm.condition.push({
    identifier: '',
    comparator: '',
    value: '',
    deviceId: '',
    relation: false,
    alarmLevel: '',
    change: '',
  })
}
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {},
})
const column = ref<IColumn[]>([
  {
    label: '配置名称',
    key: 'name',
    search: true,
    rules: [{ required: true, message: '配置名称不能为空' }],
    align: 'center',
  },
  {
    label: '连续报警间隔(分钟)',
    key: 'intervalTime',
    rules: [
      { required: true, message: '连续报警间隔不能为空' },
      {
        validator: (rule, value, callback) => {
          const reg = /^\d+$/ // 正则表达式校验非负整数（包括0）
          if (!reg.test(value)) {
            callback(new Error('连续报警间隔必须为整数'))
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
    tableWidth: 170,
    align: 'center',
  },
  {
    label: '告警等级',
    key: 'level',
    tableWidth: 120,
    search: true,
    type: 'select',
    rules: [{ required: true, message: '请选择告警等级' }],
    componentProps: {
      options: [
        {
          label: '低限预警',
          value: 1,
        },
        {
          label: '低限报警',
          value: 2,
        },
        {
          label: '低限严重警告',
          value: 3,
        },
        {
          label: '高限预警',
          value: 4,
        },
        {
          label: '高限报警',
          value: 5,
        },
        {
          label: '高限严重警告',
          value: 6,
        },
        {
          label: '正常值',
          value: -1,
        },
      ],
    },
  },
  {
    label: '所属子系统',
    key: 'subSystemIdList',
    tableWidth: 120,
    // search: true,
    hide: true,
    type: 'select',
    rules: [{ required: true, message: '请选择子系统' }],
    componentProps: {
      multiple: true,
      options: [],
    },
  },
  {
    label: '告警分类',
    key: 'alertType',
    type: 'select',
    componentProps: {
      options: [
        {
          label: '凝汽器换热效率',
          value: 1,
        },
        {
          label: '循环水水质',
          value: 2,
        },
        {
          label: '循环水量',
          value: 3,
        },
        {
          label: '循环水水质',
          value: 4,
        },
        {
          label: '冷却塔',
          value: 5,
        },
        {
          label: '真空优化',
          value: 6,
        },
        {
          label: '表计',
          value: 7,
        },
      ],
    },
  },
  {
    label: '消息模板',
    key: 'messageTemplateId',
    type: 'select',
    // tableWidth: 120,
    componentProps: {
      filterable: true,
      labelAlias: 'title',
      valueAlias: 'id',
      options: [],
    },
  },
  {
    label: '规则',
    key: 'ruleInfoId',
    // tableWidth: 120,
    type: 'select',
    componentProps: {
      filterable: true,
      labelAlias: 'name',
      valueAlias: 'id',
      options: [],
    },
  },
  {
    label: '是否启用',
    key: 'enable',
    tableWidth: 120,
    type: 'switch',
    slot: true,
  },
  {
    label: '建议',
    key: 'description',
    hide: true,
    componentProps: {
      type: 'textarea',
      rows: 4,
    },
  },
])

emitter.on('projectListChanged', (e) => {
  location.reload()
})
const getDict = async () => {
  let options: any[] = []
  const res1 = await getRuleList({
    pageSize: 999999,
    pageNum: 1,
    type: 'scene',
    projectId,
  })
  const res2 = await getRuleList({
    pageSize: 999999,
    pageNum: 1,
    type: 'flow',
    projectId,
  })
  options = [...res1.data.rows, ...res2.data.rows]
  column.value.forEach((item) => {
    if (item.key === 'ruleInfoId') {
      item.componentProps.options = options
      // 输入框搜索
      item.componentProps.filterable = (value, option) => {
        return option.name.toLowerCase().indexOf(value.toLowerCase()) > -1
      }
    }
  })
  const getTemplatesListstate = reactive({
    total: 0,
    page: {
      pageSize: 200,
      pageNum: 1,
    },
  })
  getTemplatesList({ ...getTemplatesListstate.page }).then((res) => {
    column.value.forEach((item) => {
      if (item.key === 'messageTemplateId') {
        item.componentProps.options = res.data.rows
        item.componentProps.filterable = (value, option) => {
          return option.title.toLowerCase().indexOf(value.toLowerCase()) > -1
        }
      }
    })
  })
}
const copyVisible = ref(false)
const copystate = reactive({ pageSize: 300, pageNum: 1, total: 0, loading: false, query: {} })
const copyselsetvalue = ref<any[]>([])
const filteredOptions = ref<any[]>([])
const selectedCopyId = ref<number | null>(null)
const copyTargetID = ref<number | null>(null)
const copySelectRef = ref<any>(null)

const handleRemoteSearch = (query: string) => {

  const q = query.trim().toLowerCase()
  if (!q) {
    return
  }
  // 如果 query 为空，可恢复全部列表
  filteredOptions.value = q ? copyselsetvalue.value.filter((item) => item.name.toLowerCase().includes(q)) : copyselsetvalue.value
}
// 明细复制
const alarmdetailscopy = (row: any) => {
  aramcopydetail(row.id).then((res) => {
    if (res.code === 200) {
      ElMessage.success('复制成功')
      const params = {
        pageNum: 1,
        pageSize: 50,
        configId: currentLine.value.id,
      }
      getConfigDetail(params).then((res) => {
        if (res.data.rows.length > 0) {
          alarmMaintenanceLIist.value = res.data.rows
        } else {
          alarmMaintenanceLIist.value = []
        }
      })
    }
  })
}
// 整体复制
const aramCopy = (row: any) => {
  copyTargetID.value = row.id
  copyVisible.value = true
  copystate.loading = true

  getConfigList({ pageSize: copystate.pageSize, pageNum: copystate.pageNum, projectId: cachedProjects.id, ...copystate.query })
    .then((res) => {
      copyselsetvalue.value = res.data.rows
      // 先展示全列表
      filteredOptions.value = copyselsetvalue.value

      // 取前两位前缀
      const prefix = row.name.slice(0, 2)

      nextTick(() => {
        if (copySelectRef.value) {
          copySelectRef.value.filterableInput = prefix
        }
        handleRemoteSearch(prefix)
        // 3. 打开下拉
        copySelectRef.value?.toggleMenu()
      })
    })
    .finally(() => {
      copystate.loading = false
    })
}

const { id: projectId } = cachedProjects

getDict()
const getData = () => {
  state.loading = true
  const project = { ...state.query, projectId }
  getConfigList({
    ...state.page,
    ...project,
  })
    .then((res) => {
      data.value = res.data.rows.map((item) => ({
        ...item,
      }))
      state.total = res.data.total
    })
    .finally(() => {
      state.loading = false
    })
}
// 保存数据
const onSave = ({ type, data, cancel }: any) => {
  state.loading = true
  const { id: projectId } = cachedProjects

  // 检查 type 是否为 'update'，并过滤掉 data.intervalTime 中的 '分钟'
  const modifiedData = {
    ...data,
    projectId,
  }
  saveConfig(toRaw(modifiedData))
    .then((res) => {
      if (res.code === 200) {
        cancel()
        getData()
      }
    })
    .finally(() => {
      state.loading = false
    })
}
// 删除
const handleDelete = (row: any) => {
  state.loading = true
  deleteConfig(row.id)
    .then((res) => {
      if (res.code === 200) {
        ElMessage.success('删除成功!')
        getData()
      }
    })
    .finally(() => {
      state.loading = false
    })
}
const getSubSystemList = () => {
  const data = cachedProjects.id
  subSystemList(data).then((res) => {
    const options = res.data.map((item: any) => ({
      label: item.powerUnitNames + item.name,
      value: item.id,
    }))

    const subSystemColumn = column.value.find((item) => item.key === 'subSystemIdList')
    if (subSystemColumn) {
      subSystemColumn.componentProps.options = options
    }
  })
}
const configId = ref()
const dialogalarmMaintenanceLIist = ref(false)
const dialogalarmMaintenanceVisible = ref(false)
const alarmMaintenanceLIist = ref([])
const disableddielete = ref(false)
const currentLine = ref()
const selectedRows = ref<any[]>([])
const alarmTableRef = ref<any>()

const openalarmMaintenance = (row: any) => {
  configId.value = row.id
  currentLine.value = row
  dialogalarmMaintenanceLIist.value = true
  const params = {
    pageNum: 1,
    pageSize: 50,
    configId: row.id,
  }
  getConfigDetail(params).then((res) => {
    if (res.data.rows.length > 0) {
      alarmMaintenanceLIist.value = res.data.rows
    } else {
      alarmMaintenanceLIist.value = []
    }
  })
  const params2 = {
    projectId: projectId,
  }
  getDeviceList(params2).then((res) => {
    if (res.data.rows.length > 0) {
      alarmBasic.value.device = res.data.rows
    }
  })
  const params1 = {
    configType: 4,
    projectId: projectId,
  }
  getpowerUnitall(params1).then((res) => {
    if (res.data.length > 0) {
      alarmBasic.value.powerUnitallList = res.data
    }
  })
}
const alarmBasic = ref({
  device: [],
  powerUnitallList: [],
  pointList: [],
  dependPointList: [],
})
// 新增告警智能运维配置
const addalarmMaintenanceLIist = () => {
  dialogalarmMaintenanceVisible.value = true
}
// 删除告警智能运维配置
const alarmDelete = (row: any) => {
  deleteConfigDetail([row.id]).then((res) => {
    if (res.code === 200) {
      ElMessage.success('删除成功!')
      openalarmMaintenance({ id: configId.value })
    }
  })
}

// 处理表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 批量删除
const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 条数据吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const ids = selectedRows.value.map((row: any) => row.id)
    deleteConfigDetail(ids).then((res) => {
      if (res.code === 200) {
        ElMessage.success('批量删除成功!')
        selectedRows.value = []
        // 清空选择
        if (alarmTableRef.value) {
          alarmTableRef.value.clearSelection()
        }
        openalarmMaintenance({ id: configId.value })
      }
    })
  }).catch(() => {
    // 用户取消删除
  })
}
// console.log(row);
// deleteConfigDetail
// 修改告警智能运维配置
const alarmEdit = (row: any) => {
  ruleForm.deviceId = row.deviceId
  ruleForm.identifier = row.identifier
  getDevicePoint(row.productKey).then((res) => {
    if (res.data.model.properties.length > 0) {
      alarmBasic.value.pointList = res.data.model.properties
    }
  })
  ruleForm.powerUnitId = row.powerUnitId ? row.powerUnitId.split(',').map(Number) : []
  ruleForm.logicRelation = row.logicRelation
  ruleForm.condition = row.condition
    ? row.condition.parameters
    : [{ identifier: '', comparator: '', value: '', deviceId: '', relation: false, alarmLevel: '', change: '' }]
  // row.condition是个数组，需要遍历拿到里面的productKey去调用接口拿到对应的点位
  ruleForm.condition.forEach((item: any) => {
    if (item.productKey != null) {
      getDevicePoint(item.productKey).then((res) => {
        if (res.data.model.properties.length > 0) {
          alarmBasic.value.dependPointList = res.data.model.properties
        }
      })
    }
  })
  ruleForm.remark = row.remark
  ruleForm.conclusion = row.conclusion
  ruleForm.recommend = row.recommend
  ruleForm.id = row.id
  dialogalarmMaintenanceVisible.value = true
}
const removeLogicDetail = (index: number) => {
  if (ruleForm.condition.length === 1) {
    disableddielete.value = true
    alert('至少保留一个逻辑条件')
    return
  }
  ruleForm.condition.splice(index, 1)
}
watch(
  () => ruleForm.condition.length,
  (val) => {
    if (val === 1) {
      disableddielete.value = true
    } else {
      disableddielete.value = false
    }
  }
)
// 关闭
const copyhandleClose = () => {
  copyHandleClear()
}
const currentLineClose = () => {
  dialogalarmMaintenanceLIist.value = false
  currentLine.value = null
  // 清空选择状态
  selectedRows.value = []
  if (alarmTableRef.value) {
    alarmTableRef.value.clearSelection()
  }
}

// 处理弹窗关闭事件（无论通过什么方式关闭都会触发）
const handleDialogClose = () => {
  // 清空选择状态
  selectedRows.value = []
  currentLine.value = null
}
//清空
const copyHandleClear = () => {
  selectedCopyId.value = null
  copyTargetID.value = null
  copyVisible.value = false
}
// 保存
const copyHandleSave = () => {
  if (!selectedCopyId.value) {
    return ElMessage.warning('请先选择要复制的配置')
  }
  const chosen = copyselsetvalue.value.find((item) => item.id === selectedCopyId.value)
  const params = {
    srcConfigId: copyTargetID.value,
    destConfigId: chosen.id,
  }
  aramcopyConfig(params).then((res) => {
    if (res.code === 200) {
      ElMessage.success('复制成功')
      copyHandleClear()
      getData()
    }
  })
}
const handleSave = () => {
  ruleFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      const point = alarmBasic.value.pointList.find((item) => item.identifier === ruleForm.identifier)
      const powerUnitNames = ruleForm.powerUnitId.map((id) => {
        const powerUnit = alarmBasic.value.powerUnitallList.find((item) => item.id === id)
        return powerUnit ? powerUnit.name : 'null'
      })
      const powerUnitNamesStr = powerUnitNames.join(', ')
      ruleForm.powerUnitId = ruleForm.powerUnitId.join(', ')
      const conditionData = ruleForm.condition
      delete ruleForm.condition
      const { unit, name } = point
      const params = {
        configId: configId.value,
        ...ruleForm,
        projectId: projectId,
        identifierName: name,
        unitName: unit,
        powerUnitName: powerUnitNamesStr,
        condition: { parameters: conditionData },
      }
      if (ruleForm.id) {
        // 编辑
        editConfigDetail(params).then((res) => {
          if (res.code === 200) {
            handleClear()
            dialogalarmMaintenanceVisible.value = false
            openalarmMaintenance({ id: configId.value })
          } else {
            ruleFormRef.value?.resetFields()
            ruleForm.condition = [{ identifier: '', comparator: '', value: '', deviceId: '', relation: false, alarmLevel: '', change: '' }]
          }
        })
      } else {
        // 新增
        addConfigDetail(params).then((res) => {
          if (res.code === 200) {
            handleClear()
            dialogalarmMaintenanceVisible.value = false
            openalarmMaintenance({ id: configId.value })
          } else {
            ruleFormRef.value?.resetFields()
            ruleForm.condition = [{ identifier: '', comparator: '', value: '', deviceId: '', relation: false, alarmLevel: '', change: '' }]
          }
        })
      }
    } else {
      ruleFormRef.value?.resetFields()
      ruleForm.condition = [{ identifier: '', comparator: '', value: '', deviceId: '', relation: false, alarmLevel: '', change: '' }]
      // console.log('表单验证失败')
    }
  })
}

const handleClear = () => {
  ruleForm.deviceId = ''
  ruleForm.identifier = ''
  ruleForm.powerUnitId = []
  ruleForm.logicRelation = '&'

  ruleForm.remark = ''
  ruleForm.conclusion = ''
  ruleForm.recommend = ''
  delete ruleForm.id
  ruleForm.condition = [{ identifier: '', comparator: '', value: '', deviceId: '', relation: false, alarmLevel: '', change: '' }]
}
// 关闭填写
const handleClose = () => {
  // ruleFormRef.value?.resetFields()
  ruleForm.deviceId = ''
  ruleForm.identifier = ''
  ruleForm.powerUnitId = []
  ruleForm.logicRelation = '&'

  ruleForm.remark = ''
  ruleForm.conclusion = ''
  ruleForm.recommend = ''
  delete ruleForm.id
  ruleForm.condition = [{ identifier: '', comparator: '', value: '', deviceId: '', relation: false, alarmLevel: '', change: '' }]
  dialogalarmMaintenanceVisible.value = false
}
// 根据所选择的设备拿到设备对应的点位
const handleDeviceChange = (val: any) => {
  const selectedDevice = alarmBasic.value.device.find((item) => item.deviceId === val)
  getDevicePoint(selectedDevice.productKey).then((res) => {
    // console.log(res, '点位')
    if (res.data.model.properties.length > 0) {
      alarmBasic.value.pointList = res.data.model.properties
    }
  })
}
// 根据依赖设备拿到点位
const handleDependentDevices = (val: any) => {
  const selectedDevice = alarmBasic.value.device.find((item) => item.deviceId === val)
  // console.log('当前选中的设备:', selectedDevice)
  getDevicePoint(selectedDevice.productKey).then((res) => {
    // console.log(res, '点位')
    if (res.data.model.properties.length > 0) {
      alarmBasic.value.dependPointList = res.data.model.properties
    }
  })
}
onMounted(() => {
  getSubSystemList()
})
const handleSwitchChange = (row: any) => {
  state.loading = true
  const { id: projectId } = cachedProjects

  // 检查 type 是否为 'update'，并过滤掉 data.intervalTime 中的 '分钟'
  const modifiedData = {
    ...row,
    projectId,
  }
  saveConfig(toRaw(modifiedData))
    .then((res) => {
      if (res.code === 200) {
        // cancel()
        getData()
      }
    })
    .finally(() => {
      state.loading = false
    })
}
const data = ref([])
</script>
<style scoped>
:deep(.el-select__wrapper) {
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: none !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  /* box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5); */
  border: none;
}
:deep(.el-card__header) {
  border: none;
}
:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}
:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}
:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}
:deep(.el-table th) {
  /* background-color: transparent; */
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}
:deep(.el-table) {
  --el-table-border-color: none;
}
:deep(.el-table__cell) {
  /* color: rgba(204, 204, 204, 1) !important; */
}
/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row) {
  /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
  /* 取消单元格背景色，确保整行背景色生效 */
}
:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;

  color: #ffffff;
}
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}
.el-tree {
  background-color: transparent;
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #07355c;
}
.el-tree-node__expand-icon {
  color: #fff;
}
.el-tree-node__label {
  color: #fff;
}
.el-tree-node__content {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}
.el-select__tags .el-tag--info {
  background-color: #153059 !important;
}
.el-tag.el-tag--info {
  color: #fff !important;
}
:deep(.el-select__wrapper) {
  background-color: #063057 !important;
  box-shadow: none !important;
  /* border: none !important; */
  /* box-shadow: #2090f7 0 0 0 1px inset !important; */
}
</style>
