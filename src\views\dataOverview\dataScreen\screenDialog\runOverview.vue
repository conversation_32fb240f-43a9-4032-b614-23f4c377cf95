<template>
  <div>
    <!-- 机组切换 -->
    <div class="powerUnitsegmented" v-if="dialogData.length > 1">
      <el-segmented v-model="selectedUnit" :options="segOptions" @change="handleUnitChange" class="custom-style" />
    </div>

    <!-- 图表容器 -->
    <div ref="chartRef" style="width: 100%; height: 400px;"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch, computed } from 'vue'
import * as echarts from 'echarts'

// 定义 props，若父组件未传递数据则默认为空数组
const props = defineProps<{
  dialogData: Array<{
    powerUnitId: number | null
    powerUnitName: string
    timeDataList: Array<{
      time: string
      datas: Array<{
        identifier: string
        label: string
        value: number | null
      }>
    }>
  }>
}>()
const dialogData = props.dialogData || []

// 默认选中的机组索引
const selectedUnit = ref(0)
const segOptions = computed(() =>
  dialogData.map((item, index) => ({
    label: item.powerUnitName,
    value: index // 这里用索引作为值
  }))
)

// 图表 DOM 容器引用
const chartRef = ref<HTMLElement | null>(null)
// ECharts 实例
let chartInstance: echarts.ECharts | null = null

// 辅助函数：将十六进制颜色转换为 rgba 格式
const hexToRgba = (hex: string, alpha: number): string => {
  hex = hex.replace(/^#/, '')
  if (hex.length === 3) {
    hex = hex.split('').map(c => c + c).join('')
  }
  const bigint = parseInt(hex, 16)
  const r = (bigint >> 16) & 255
  const g = (bigint >> 8) & 255
  const b = bigint & 255
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

// 默认颜色数组，可根据需要调整
const defaultColors = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc'
]

// 初始化 ECharts 图表
const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    setChartOption()
  }
}

// 根据当前选中机组数据构造图表配置
const setChartOption = () => {
  if (!dialogData || dialogData.length === 0) {
    console.warn('没有传递 dialogData 数据')
    chartInstance && chartInstance.setOption({ series: [] })
    return
  }

  const unitData = dialogData[selectedUnit.value]
  if (!unitData || !unitData.timeDataList) {
    console.warn('当前机组数据为空')
    chartInstance && chartInstance.setOption({ series: [] })
    return
  }

  const currentData = unitData.timeDataList || []
  // 构造 X 轴数据，仅保留 time 中的时:分
  const times = currentData.map(item => item.time ? item.time.slice(11, 16) : '')

  // 收集所有指标数据（以 identifier 为 key）
  const seriesMap = new Map<string, { name: string; data: Array<number | null> }>()
  currentData.forEach(item => {
    if (item.datas && item.datas.length) {
      item.datas.forEach(dataItem => {
        if (!seriesMap.has(dataItem.identifier)) {
          seriesMap.set(dataItem.identifier, {
            name: dataItem.label,
            data: []
          })
        }
      })
    }
  })

  // 填充每个指标在各时间点的数据，若无数据填充 null
  currentData.forEach(item => {
    seriesMap.forEach((value, key) => {
      const found = item.datas ? item.datas.find(d => d.identifier === key) : undefined
      value.data.push(found ? found.value : null)
    })
  })

  // 构造 series 数组，并对每个系列应用渐变效果
  const series = []
  let index = 0
  seriesMap.forEach(val => {
    const baseColor = defaultColors[index % defaultColors.length]
    series.push({
      name: val.name,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 5,
      lineStyle: { color: baseColor },
      areaStyle: {
        // 使用竖向渐变：上方色值（透明度 0.6） -> 下方完全透明
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: hexToRgba(baseColor, 0.6) },
          { offset: 1, color: hexToRgba(baseColor, 0) }
        ])
      },
      data: val.data
    })
    index++
  })

  const legend = {
    data: series.map(s => s.name),
    icon: 'circle',
    textStyle: {
      color: '#fff'
    }
  }

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '10%',
      bottom: '10%'
    },
    legend,
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: times,
      axisLabel: { color: '#fff' },
      axisLine: { lineStyle: { color: '#fff' } }
    },
    yAxis: {
      type: 'value',
      axisLabel: { color: '#fff' },
      axisLine: { lineStyle: { color: '#fff' } },
      splitLine: { lineStyle: {  type: 'dashed',color: '#3f4e61' } }
    },
    series: series
  }

  chartInstance && chartInstance.setOption(option)
}

const handleUnitChange = () => {
  // 机组切换时更新图表
  setChartOption()
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 监听 selectedUnit 改变时更新图表
watch(selectedUnit, () => {
  setChartOption()
})
</script>

<style scoped>
.powerUnitsegmented {
  text-align: center;
  margin-bottom: 20px;
}
/* 自定义 el-segmented 样式 */
.custom-style .el-segmented {
  --el-segmented-item-selected-color: var(--el-text-color-primary);
  --el-segmented-item-selected-bg-color: #ffd100;
  --el-border-radius-base: 16px;
}
</style>
