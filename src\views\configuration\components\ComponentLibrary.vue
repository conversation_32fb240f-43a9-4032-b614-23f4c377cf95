<template>
  <div class="component-library">
    <div class="library-header">
      <h3>组件库</h3>
      <el-input
        v-model="searchText"
        placeholder="搜索组件"
        prefix-icon="Search"
        clearable
      />
    </div>
    
    <el-collapse v-model="activeCategories">
      <el-collapse-item 
        v-for="category in filteredCategories" 
        :key="category.id"
        :title="category.name"
        :name="category.id"
      >
        <!-- 模型组件库特殊处理 -->
        <div v-if="category.id === 'models'" class="models-section">
          <div class="models-header">
            <el-button
              type="primary"
              size="small"
              @click="showModelManager = true"
            >
              <el-icon><Setting /></el-icon>
              管理模型
            </el-button>
          </div>

          <!-- 动态加载的3D模型列表 -->
          <div class="component-grid">
            <div
              v-for="model in availableModels"
              :key="model.id"
              class="component-item model-item"
              @click="addModelComponent(model)"
            >
              <div class="component-preview">
                <img v-if="model.thumbnail" :src="model.thumbnail" :alt="model.name" />
                <div v-else class="component-icon">
                  <el-icon><Box /></el-icon>
                </div>
              </div>
              <div class="component-name">{{ model.name }}</div>
              <div class="model-type">{{ model.fileType.toUpperCase() }}</div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="availableModels.length === 0" class="empty-models">
            <el-icon><Box /></el-icon>
            <p>暂无3D模型</p>
            <el-button
              type="text"
              size="small"
              @click="showModelManager = true"
            >
              点击上传模型
            </el-button>
          </div>
        </div>

        <!-- 普通组件 -->
        <div v-else class="component-grid">
          <div
            v-for="component in category.components"
            :key="component.id"
            class="component-item"
            draggable="true"
            @dragstart="onDragStart($event, component)"
          >
            <div class="component-preview">
              <img
                v-if="component.preview"
                :src="component.preview"
                :alt="component.name"
                class="component-preview-image"
                @error="handleImageError"
              />
              <div v-if="!component.preview" class="component-icon">
                <el-icon>
                  <component :is="component.icon.replace('icon-', '')" />
                </el-icon>
              </div>
            </div>
            <div class="component-name">{{ component.name }}</div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>

    <!-- 模型管理对话框 -->
    <el-dialog
      v-model="showModelManager"
      title="模型组件库管理"
      width="80%"
      :close-on-click-modal="false"
    >
      <ModelLibraryManager
        @model-selected="handleModelSelected"
        @add-to-canvas="handleAddModelToCanvas"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, Box } from '@element-plus/icons-vue'
import { useConfigurationStore } from '../stores/configurationStore'
import { modelService, type Model3DInfo } from '../services/modelService'
import ModelLibraryManager from './ModelLibraryManager.vue'
import type { ComponentTemplate } from '../types'

const configStore = useConfigurationStore()

// 搜索文本
const searchText = ref('')

// 展开的分类
const activeCategories = ref(configStore.componentLibrary.map(category => category.id))

// 模型管理相关
const showModelManager = ref(false)
const availableModels = modelService.getModelsRef()

// 过滤后的组件分类
const filteredCategories = computed(() => {
  if (!searchText.value) {
    return configStore.componentLibrary
  }
  
  const search = searchText.value.toLowerCase()
  
  return configStore.componentLibrary
    .map(category => {
      const filteredComponents = category.components.filter(component => 
        component.name.toLowerCase().includes(search)
      )
      
      return {
        ...category,
        components: filteredComponents
      }
    })
    .filter(category => category.components.length > 0)
})

// 拖拽开始
const onDragStart = (event: DragEvent, component: ComponentTemplate) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('component', JSON.stringify(component))
    event.dataTransfer.effectAllowed = 'copy'
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  // 显示备用图标
  const preview = img.parentElement
  if (preview) {
    const iconDiv = document.createElement('div')
    iconDiv.className = 'component-icon'
    iconDiv.innerHTML = '<i class="el-icon-picture"></i>'
    preview.appendChild(iconDiv)
  }
}

// 添加模型组件到画布
const addModelComponent = (model: Model3DInfo) => {
  const component = {
    id: `custom3dModel_${Date.now()}`,
    name: `3D模型 - ${model.name}`,
    type: 'custom3dModel' as const,
    icon: 'icon-3d-model',
    preview: model.thumbnail || '/src/assets/icons/3d-model-placeholder.png',
    defaultProps: {
      width: 200,
      height: 200,
      data: {
        static: {
          modelId: model.id,
          modelName: model.name,
          modelType: model.fileType
        }
      },
      style: {
        backgroundColor: 'transparent'
      }
    }
  }

  // 触发拖拽事件，模拟拖拽添加
  const event = new DragEvent('dragstart')
  onDragStart(event, component)

  ElMessage.success(`已选择模型: ${model.name}`)
}

// 处理模型选择
const handleModelSelected = (model: Model3DInfo) => {
  console.log('选择了模型:', model)
}

// 处理添加模型到画布
const handleAddModelToCanvas = (model: Model3DInfo) => {
  addModelComponent(model)
  showModelManager.value = false
}

// 组件挂载时的初始化
onMounted(() => {
  // 确保模型分类在展开列表中
  if (!activeCategories.value.includes('models')) {
    activeCategories.value.push('models')
  }
})
</script>

<style scoped>
.component-library {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--el-border-color-light);
}

.library-header {
  padding: 10px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.library-header h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 10px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  cursor: move;
  transition: all 0.3s;
}

.component-item:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.component-preview {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

.component-preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 2px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
}

.component-preview-image:not([src]),
.component-preview-image[src=""] {
  display: none;
}

.component-icon {
  font-size: 24px;
}

.component-name {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
}

/* 模型组件库样式 */
.models-section {
  padding: 10px;
}

.models-header {
  margin-bottom: 15px;
  text-align: center;
}

.model-item {
  position: relative;
}

.model-type {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 10px;
  background: rgba(64, 158, 255, 0.8);
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
}

.empty-models {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  text-align: center;
}

.empty-models .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-models p {
  margin: 0 0 12px 0;
  font-size: 14px;
}
</style>