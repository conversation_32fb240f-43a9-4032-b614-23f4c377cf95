import request from '@/utils/request'
import axios from 'axios'
import { getToken } from '@/utils/auth'

enum Api {
  monitorStop = '/device/data/monitor/stop',
  monitor = '/device/data/monitor',
  monitorStatus = '/device/data/monitor/status',
  opneMonitorStart = '/device/setting/sync',
  uploadhisData = '/device/upload/historyData',
  modbusConfigAdd = '/modbus/config/add',
  modbusConfigList = '/modbus/config/list',
  modbusConfigUpdate = '/modbus/config/edit',
  modbusConfigDelete = '/modbus/config/delete',
}
// 上传历史数据
export const uploadhisData = (data) => {
  return axios({
    url: import.meta.env.VITE_APP_BASE_API + Api.uploadhisData,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
      token: getToken(),
    },
  })
}
// 数据停止模拟
export const monitorStop = (data) => {
  return request({
    url: Api.monitorStop,
    method: 'post',
    data,
  })
}
// 数据开始模拟
export const monitor = (data) => {
  return request({
    url: Api.monitor,
    method: 'post',
    data,
  })
}
// 查询数据模拟状态
export const monitorStatus = (data) => {
  return request({
    url: Api.monitorStatus,
    method: 'post',
    data,
  })
}
// 数据回传
export const opneMonitorStart = (data) => {
  return request({
    url: Api.opneMonitorStart,
    method: 'post',
    data,
  })
}
// 添加modbus配置
export const modbusConfigAdd = (data) => {
  return request({
    url: Api.modbusConfigAdd,
    method: 'post',
    data,
  })
}
// 查询modbus配置
export const modbusConfigList = (data) => {
  return request({
    url: Api.modbusConfigList,
    method: 'post',
    data,
  })
}
// 更新modbus配置
export const modbusConfigUpdate = (data) => {
  return request({
    url: Api.modbusConfigUpdate,
    method: 'post',
    data,
  })
}
// 删除modbus配置
export const modbusConfigDelete = (data) => {
  return request({
    url: Api.modbusConfigDelete,
    method: 'post',
    data,
  })
}
