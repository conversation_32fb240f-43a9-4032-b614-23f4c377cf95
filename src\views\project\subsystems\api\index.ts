import request from '@/utils/request'
import { AxiosPromise } from 'axios'
import { subSystemMemberQuery, subSystemMemberVO, subSystemMemberUserQuery, subSystemMemberUserVO, addUser, addUserId } from './types'
export function subSystemMemberList(query: subSystemMemberQuery): AxiosPromise<subSystemMemberVO[]> {
  return request({
    url: '/system/subSystemMember/list',
    method: 'post',
    data: query,
  })
}

// 查询
export function subSystemMemberListUser(query: subSystemMemberUserQuery): AxiosPromise<subSystemMemberUserVO[]> {
  return request({
    url: '/system/subSystemMember/project/users',
    method: 'post',
    data: query,
  })
}
//新增system/subSystemMember/add
export function addUser(data: addUser) {
  return request({
    url: '/system/subSystemMember/add',
    method: 'post',
    data,
  })
}
// 删除
export function subSystemMemberDelete(data: addUserId) {
  return request({
    url: '/system/subSystemMember/delete',
    method: 'post',
    data,
  })
}
