{
  "compilerOptions": {
    "target": "es2022",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "allowJs": true,
    "paths": {
      "@/*": ["src/*"]
    },
    "types": ["vite/client"],
    "skipLibCheck": true,
    // 允许默认导入
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": false,
    // 以允许 TypeScript 即使在存在类型错误的情况下也能生成输出文件
    "noEmitOnError": false,
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/types/**/*.d.ts"],
  "exclude": ["node_modules", "dist", "**/*.js"]
}
