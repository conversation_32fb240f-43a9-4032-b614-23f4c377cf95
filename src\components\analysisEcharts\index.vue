<template>
  <div :id="chartId" style="width: 100%; height: 400px;"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, defineProps, watch, ref } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({}),
  },
  showLegend: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
})

const chartId = `bar-chart-${props.id}`
const chartInstance = ref<echarts.ECharts | null>(null)

// 封装日期格式化函数
const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp)
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${hours}:${minutes}`
}

// 初始化图表
const initializeChart = () => {
  const chartElement = document.getElementById(chartId)
  if (!chartElement) {
    console.error(`Chart element with id ${chartId} not found.`)
    return
  }
  chartInstance.value = echarts.init(chartElement)
  setChartOptions()
  window.addEventListener('resize', resizeChart)
}

// 设置图表配置项
const setChartOptions = () => {
  if (!chartInstance.value || !props.chartData) return

  // 检查必要的数据
  if (
    !Array.isArray(props.chartData.xaxisData) ||
    !Array.isArray(props.chartData.yaxisData)
  ) {
    console.error('Invalid chartData format. xaxisData and yaxisData should be arrays.')
    return
  }

  const formattedXaxisData = props.chartData.xaxisData.map(formatTimestamp)

  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: formattedXaxisData,
      nameTextStyle: {
        color: '#FFFFFF',
      },
      axisLabel: {
        color: '#FFFFFF',
      },
      axisLine: {
        lineStyle: {
          color: '#FFFFFF',
        },
      },
      axisTick: {
        lineStyle: {
          color: '#FFFFFF',
        },
      },
      splitLine: {
        show: false,
      },
      nameLocation: 'end',
      nameGap: 10,
    },
    yAxis: {
      type: 'value',
      // name: props.chartData.yaxisUnit || '',
      name:`单位：${props.chartData.yaxisUnit || ''}`,
      nameTextStyle: {
        color: '#FFFFFF',
      },
      axisLabel: {
        color: '#FFFFFF',
      },
      axisLine: {
        lineStyle: {
          color: '#FFFFFF',
        },
      },
      axisTick: {
        lineStyle: {
          color: '#FFFFFF',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(62, 65, 77, 1)',
        },
      },
      nameLocation: 'end',
      nameGap: 30,
    },
    series: [
      {
        data: props.chartData.yaxisData,
        type: 'bar',
        name: '暂无数据',
        itemStyle: {
          color: 'rgba(33, 148, 255, 1)',
        },
        label: {
          show: true,
          position: 'top',
          formatter: (params: any) => {
            return params.value !== null && params.value !== undefined ? params.value : ''
          },
          color: '#FFFFFF',
          fontSize: 12,
        },
      },
    ],
    legend: props.showLegend
      ? {
          show: true,
          data: [],
          textStyle: {
            color: '#FFFFFF',
          },
        }
      : {},
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
  }

  chartInstance.value.setOption(option)
}

// 监听窗口大小变化，调整图表大小
const resizeChart = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 监听 chartData 的变化，更新图表
watch(
  () => props.chartData,
  () => {
    setChartOptions()
  },
  { deep: true }
)

// 组件挂载时初始化图表
onMounted(() => {
  initializeChart()
})

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
    chartInstance.value = null
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="scss">
/* 可以根据需要添加具体的样式 */
</style>
