<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar ref="navbarRef" @setLayout="setLayout" v-if="!sidebar.topHide" />
        <tags-view v-if="needTagsView" />
      </div>
      <div class="layout-box" :style="backgroundStyle" :class="{ 'no-layout-box': appStore.sidebar.topHide }">
        <side-bar v-if="!sidebar.fullHide" class="sidebar-container" :class="{ collapsed: sidebar.hide }" />
        <app-main />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import SideBar from './components/Sidebar/index.vue'
import { AppMain, Navbar, Settings, TagsView } from './components'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import { useLocalCache, CACHE_KEY } from '@/hooks/web/useCache'
import { useBackgroundStore } from '@/store/modules/background'
import defaultBg from '@/assets/images/bigbg1.png'
const { wsCache } = useLocalCache()
const cachedBackground = wsCache.get(CACHE_KEY.BACKGROUND)

const backgroundUrl = (
  cachedBackground === null ||
  (Array.isArray(cachedBackground) && cachedBackground.length === 0)
) ? defaultBg : cachedBackground
// 构造动态背景样式
const backgroundStyle = computed(() => ({
  background: `url(${backgroundUrl})`,
}))
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const theme = computed(() => settingsStore.theme)
const sidebar = computed(() => useAppStore().sidebar)
const device = computed(() => useAppStore().device)
const needTagsView = computed(() => settingsStore.tagsView)
const fixedHeader = computed(() => settingsStore.fixedHeader)

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}))

const { width } = useWindowSize()
const WIDTH = 992 // refer to Bootstrap's responsive design

watchEffect(() => {
  if (device.value === 'mobile' && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false })
  }
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice('mobile')
    useAppStore().closeSideBar({ withoutAnimation: true })
  } else {
    useAppStore().toggleDevice('desktop')
  }
})

const navbarRef = ref(Navbar)
const settingRef = ref(Settings)

onMounted(() => {
  nextTick(() => {
    navbarRef.value.initTenantList()
  })
})

const handleClickOutside = () => {
  useAppStore().closeSideBar({ withoutAnimation: false })
}

const setLayout = () => {
  settingRef.value.openSetting()
}
</script>

<style lang="scss" scoped>
  @import "@/assets/styles/mixin.scss";
  @import "@/assets/styles/variables.module.scss";
.layout-box {
  position: relative;
  display: flex;
  padding-top: 50px;
  background: rgba(14, 25, 36, 1);
  height: 100%;
  background: url('@/assets/images/mask.png');
  background-size: 1920px 1080px;
  background-position: center; /* 将图片中心位置对齐 */
  background-repeat: no-repeat; /* 防止重复平铺 */
}
.no-layout-box{
  position: relative;
  display: flex;
  padding-top: 0px !important;
  background: rgba(14, 25, 36, 1);
  height: 100%;
  // background: url('@/assets/images/dbg.jpg');
  background-size: cover;
  background-position: center; /* 将图片中心位置对齐 */
  background-repeat: no-repeat; /* 防止重复平铺 */
}
.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
</style>
