<template>
  <yt-crud
    ref="crudRef"
    :data="data"
    :column="column"
    v-model:page="state.page"
    v-model:query="state.query"
    :total="state.total"
    :loading="state.loading"
    @handleUpdate="handleUpdate"
    @onLoad="getData"
  >
    <template #status="{ row }">
      <el-switch v-model="row.status" disabled />
    </template>
  </yt-crud>
</template>

<script lang="ts" setup>
  import { IColumn } from '@/components/common/types/tableCommon'

  import YtCrud from '@/components/common/yt-crud.vue'
  import {TagList } from '../label/api/configs.api'

//   const data = ref<TagList[]>([])
  const data = ref( [{id: 1, messageType: '测试', content: '测试', status: true, createAt: '2022-08-01 12:00:00'}])


  const column: IColumn[] = [{
    label: '标签名称',
    key: 'messageType',
  }, {
    label: '描述',
    key: 'content',
    type: 'string',
    componentProps: {
      type: 'textarea',
      rows: 4,
    }
  }, {
    label: '状态',
    key: 'status',
    type: 'switch',
    slot: true,
  }, {
    label: '发送日期',
    key: 'createAt',
    type: 'date',
    formHide: true,
  }]
  const state = reactive({
    total: 0,
    page: {
      pageSize: 10,
      pageNum: 1,
    },
    query: {},
    loading: false
  })
  const handleUpdate = () => {}
  const handleDelete = () => {}
  const getData = () => {
    // state.loading = true
    // getMsgs({
    //   ...state.page,
    //   ...state.query,
    // }).then(res => {
    //   data.value = res.data.rows || []
    //   state.total = res.data.total
    // }).finally(() => {
    //   state.loading = false
    // })
  }
</script>

<!-- <style lang="scss" scoped>
  
  </style> -->
