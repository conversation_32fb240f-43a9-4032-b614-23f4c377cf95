<template>
  <div class="connection-manager">
    <!-- 连接线渲染层 -->
    <div class="connections-layer">
      <connection-line
        v-for="connection in connections"
        :key="connection.id"
        :connection="connection"
        :source-component="getComponentById(connection.sourceId)!"
        :target-component="getComponentById(connection.targetId)!"
      />
    </div>
    
    <!-- 临时连接线（拖拽时显示） -->
    <svg
      v-if="tempConnection"
      class="temp-connection"
      :style="{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 2
      }"
    >
      <line
        :x1="tempConnection.start.x"
        :y1="tempConnection.start.y"
        :x2="tempConnection.end.x"
        :y2="tempConnection.end.y"
        stroke="#409eff"
        stroke-width="2"
        stroke-dasharray="5,5"
      />
    </svg>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ConnectionLine from './ConnectionLine.vue'
import type { ComponentConnection, ConfigurationComponent } from '../types'

const props = defineProps<{
  connections: ComponentConnection[]
  components: ConfigurationComponent[]
  connectionMode: boolean
}>()

const emit = defineEmits<{
  addConnection: [connection: ComponentConnection]
  updateConnection: [connection: ComponentConnection]
  deleteConnection: [connectionId: string]
}>()

// 临时连接线（拖拽时显示）
const tempConnection = ref<{
  start: { x: number; y: number }
  end: { x: number; y: number }
} | null>(null)

// 连接模式状态
const isConnecting = ref(false)
const sourceComponentId = ref<string | null>(null)

// 根据ID获取组件
const getComponentById = (id: string) => {
  return props.components.find(comp => comp.id === id)
}

// 开始连接
const startConnection = (componentId: string, point: { x: number; y: number }) => {
  if (!props.connectionMode) return
  
  isConnecting.value = true
  sourceComponentId.value = componentId
  tempConnection.value = {
    start: point,
    end: point
  }
}

// 更新临时连接线
const updateTempConnection = (point: { x: number; y: number }) => {
  if (!tempConnection.value) return
  
  tempConnection.value.end = point
}

// 完成连接
const finishConnection = (targetComponentId: string, point: { x: number; y: number }) => {
  if (!isConnecting.value || !sourceComponentId.value || sourceComponentId.value === targetComponentId) {
    cancelConnection()
    return
  }
  
  // 创建新连接
  const connection: ComponentConnection = {
    id: 'connection_' + Date.now(),
    name: '数据流',
    sourceId: sourceComponentId.value,
    targetId: targetComponentId,
    sourceAnchor: { position: 'right' },
    targetAnchor: { position: 'left' },
    style: {
      strokeColor: '#409eff',
      strokeWidth: 2,
      arrowSize: 8,
      arrowColor: '#409eff',
      lineType: 'curved'
    },
    animation: {
      enabled: true,
      type: 'flow',
      speed: 3,
      direction: 'forward'
    }
  }
  
  emit('addConnection', connection)
  cancelConnection()
}

// 取消连接
const cancelConnection = () => {
  isConnecting.value = false
  sourceComponentId.value = null
  tempConnection.value = null
}

// 暴露方法给父组件
defineExpose({
  startConnection,
  updateTempConnection,
  finishConnection,
  cancelConnection
})
</script>

<style scoped>
.connection-manager {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connections-layer {
  position: relative;
  width: 100%;
  height: 100%;
}

.temp-connection {
  pointer-events: none;
}
</style>