<template>
  <!-- 循环水量 -->
  <div>
    <div class="ml-[50px] mb-[20px]">
      <el-button type="info" @click="goBack">返回</el-button>
      <el-button type="primary" @click="ExportTable" :disabled="!selectedDate">导出</el-button>
    </div>
    <!-- 日历选择区域 -->
    <div class="calendar-card ml-[20px] mb-[20px]">
      <div class="calendar-container">
        <h3>选择查询月份</h3>
        <el-date-picker
          v-model="selectedDate"
          type="month"
          placeholder="请选择月份"
          :disabled-date="disabledDate"
          :clearable="false"
          format="YYYY-MM"
          value-format="YYYY-MM"
          @change="handleDateChange"
          style="width: 200px"
        />
      </div>
    </div>

    <!-- 混合表头和数据的表格 -->
    <div class="table-container">
      <el-table :data="displayData" border stripe style="width: 100%" :span-method="spanMethod" :show-header="false">
        <el-table-column v-for="(column, columnIndex) in dynamicColumns" :key="columnIndex" :prop="`col${columnIndex}`" align="center">
          <template #default="{ row }">
            <span v-if="row.isHeader" class="header-cell">{{ row[`col${columnIndex}`] }}</span>
            <span v-else class="data-cell">{{ row[`col${columnIndex}`] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import { getCalendarData, CalendarDataDTO, getdataFilterReportView, ReportDataDTO } from '../dataFilterConfig/index.api'

const router = useRouter()
const route = useRoute()

// 获取路由参数
const projectId = ref<number>()
const powerUnitId = ref<number>()
const filterType = ref<number>()
const unitName = ref<string>()

// 日历相关数据
const selectedDate = ref<string>('')
const availableMonths = ref<string[]>([])
const loading = ref(false)

// 初始化参数
const initParams = () => {
  projectId.value = Number(route.query.projectId)
  powerUnitId.value = Number(route.query.powerUnitId)
  filterType.value = Number(route.query.filterType)
  unitName.value = route.query.unitName as string
}

// 获取可选日期数据
const fetchAvailableMonths = async () => {
  if (!projectId.value || !powerUnitId.value || !filterType.value) {
    ElMessage.error('项目ID、机组ID和过滤类型不能为空')
    return
  }

  try {
    loading.value = true
    const response = await getCalendarData({
      projectId: projectId.value,
      powerUnitId: powerUnitId.value,
      filterType: filterType.value
    })
    availableMonths.value = response.data || []

    // 如果有可选月份，默认选择最近的一个月份
    if (availableMonths.value.length > 0) {
      // 找到最近的（最大的）月份，使用字符串比较
      selectedDate.value = [...availableMonths.value].sort().pop() || availableMonths.value[0]
    }
  } catch (error) {
    console.error('获取可选月份失败:', error)
    ElMessage.error('获取可选月份失败')
  } finally {
    loading.value = false
  }
}

// 判断日期是否可选
const disabledDate = (time: Date) => {
  const month = time.getFullYear() + '-' + String(time.getMonth() + 1).padStart(2, '0')
  return !availableMonths.value.includes(month)
}

// 处理日期变化
const handleDateChange = (value: string) => {
  // 这里可以根据选择的月份重新获取表格数据
  fetchTableDataByMonth(value)
}

// 根据月份获取表格数据
const fetchTableDataByMonth = async (month: string) => {
  if (!projectId.value || !powerUnitId.value || !filterType.value) {
    ElMessage.error('参数不完整，无法获取数据')
    return
  }

  try {
    loading.value = true
    const response = await getdataFilterReportView({
      projectId: projectId.value,
      powerUnitId: powerUnitId.value,
      filterType: filterType.value,
      reportMonth: month
    })

    if (response.data && Array.isArray(response.data)) {
      rawData.value = response.data
    } else {
      console.warn('API返回数据格式不正确:', response)
      ElMessage.warning('获取到的数据格式不正确')
    }
  } catch (error) {
    console.error('获取表格数据失败:', error)
    ElMessage.error('获取表格数据失败')
  } finally {
    loading.value = false
  }
}

interface DataGroup {
  headers: string[] // 该组的表头
  rows: Array<{
    index: number
    filterCondition: string
    rangeTitle: string
    situationAnalysis: string
    proportion: string
    rangeInterval?: string
    percentRange?: string
    monthAvgVal?: string
    normalPumpCurrentRange1?: string
    normalPumpCurrentRange2?: string
    normalPumpCurrentAvg1?: number
    normalPumpCurrentAvg2?: number
  }>
}

interface DisplayRow {
  isHeader?: boolean
  filterCondition?: string
  [key: string]: any
}

// 列配置接口
interface ColumnConfig {
  width: number
  label: string
}

const rawData = ref<DataGroup[]>([])

// 动态列配置（根据数据中最多的列数计算）
const dynamicColumns = computed<ColumnConfig[]>(() => {
  if (rawData.value.length === 0) return []

  // 找到最大列数
  const maxColumns = Math.max(...rawData.value.map((group) => group.headers.length))

  // 生成列配置
  const columns: ColumnConfig[] = []
  for (let i = 0; i < maxColumns; i++) {
    columns.push({
      width: i === 0 ? 80 : i === 1 ? 150 : 140, // 序号列80px，筛选条件150px，其他140px
      label: `col${i}`,
    })
  }

  return columns
})

// 将原始数据转换为显示数据
const displayData = computed<DisplayRow[]>(() => {
  const result: DisplayRow[] = []

  rawData.value.forEach((group) => {
    // 添加表头行
    const headerRow: DisplayRow = {
      isHeader: true,
    }

    // 动态添加列数据
    group.headers.forEach((header, index) => {
      headerRow[`col${index}`] = header
    })

    result.push(headerRow)

    // 添加数据行
    group.rows.forEach((row) => {
      const dataRow: DisplayRow = {
        isHeader: false,
        filterCondition: row.filterCondition,
      }

      // 根据当前数据组的headers动态生成数据
      group.headers.forEach((header, index) => {
        let value = ''

        // 根据header的内容来决定对应的数据
        switch (header) {
          case '序号':
            value = String(row.index)
            break
          case '筛选条件':
            value = row.filterCondition || ''
            break
          case '计算流量(m3/h)':
          case '计算清洁系数':
            value = row.rangeTitle || ''
            break
          case '情况分析':
            value = row.situationAnalysis || ''
            break
          case '占比(%)':
          case '占比':
            value = row.proportion || ''
            break
          case '区间范围(m3/h)':
          case '区间范围':
            value = row.rangeInterval || ''
            break
          case '80%区间范围(m3/h)':
          case '90%区间范围':
            value = row.percentRange || ''
            break
          case '月平均流量(m3/h)':
          case '月平均清洁系数':
            value = row.monthAvgVal || ''
            break
          case '2#循环水泵电流区间范围(A)':
            value = row.normalPumpCurrentRange1 || ''
            break
          case '4#循环水泵电流区间范围(A)':
            value = row.normalPumpCurrentRange2 || ''
            break
          case '2#循环水泵月平均电流(A)':
            value = String(row.normalPumpCurrentAvg1 || '')
            break
          case '4#循环水泵月平均电流(A)':
            value = String(row.normalPumpCurrentAvg2 || '')
            break
          default:
            value = ''
        }

        dataRow[`col${index}`] = value
      })

      result.push(dataRow)
    })
  })

  return result
})

// 行合并方法
const spanMethod = ({ row, rowIndex, columnIndex }: any) => {
  // 如果是表头行，跳过合并逻辑
  if (row.isHeader) {
    return
  }

  // 筛选条件列合并（第二列，索引为1）
  if (columnIndex === 1) {
    if (row.filterCondition) {
      // 计算该筛选条件对应的行数
      let count = 1
      let nextIndex = rowIndex + 1

      while (nextIndex < displayData.value.length && !displayData.value[nextIndex].isHeader && !displayData.value[nextIndex].filterCondition) {
        count++
        nextIndex++
      }

      return {
        rowspan: count,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
}

// 判断指定列索引是否为数值类型列
const isNumericColumnByIndex = (columnIndex: number, rowIndex: number): boolean => {
  // 获取当前行的表头信息
  let currentGroupIndex = 0
  let currentRowInGroup = 0

  // 计算当前行属于哪个数据组
  for (let i = 0; i < rawData.value.length; i++) {
    const group = rawData.value[i]
    // 表头行 + 数据行数
    const totalRowsInGroup = 1 + group.rows.length

    if (rowIndex < totalRowsInGroup) {
      currentGroupIndex = i
      currentRowInGroup = rowIndex - 1 // 减去表头行
      break
    }

    rowIndex -= totalRowsInGroup
  }

  if (currentGroupIndex >= rawData.value.length) return false

  const group = rawData.value[currentGroupIndex]
  if (columnIndex >= group.headers.length) return false

  const header = group.headers[columnIndex]

  // 判断哪些列是数值类型
  const numericHeaders = [
    '序号',
    '占比(%)',
    '占比',
    '月平均流量(m3/h)',
    '月平均清洁系数',
    '2#循环水泵月平均电流(A)',
    '4#循环水泵月平均电流(A)'
  ]

  return numericHeaders.includes(header)
}

const goBack = () => {
  router.go(-1)
}

// 导出表格为Excel
const ExportTable = () => {
  try {
    // 创建工作簿
    const workbook = XLSX.utils.book_new()

    // 准备Excel数据，保持数值类型
    const excelData: any[][] = []

    // 处理显示数据，转换为Excel格式，保持数值类型
    displayData.value.forEach((row, rowIndex) => {
      const rowData: any[] = []

      // 找到当前行的最大列数
      const maxCols = Math.max(...rawData.value.map(group => group.headers.length))

      // 填充行数据
      for (let i = 0; i < maxCols; i++) {
        const cellValue = row[`col${i}`]

        // 如果是表头行，直接使用字符串
        if (row.isHeader) {
          rowData.push(cellValue !== undefined ? cellValue : '')
        } else {
          // 对于数据行，尝试转换为数值类型
          if (cellValue !== undefined && cellValue !== '') {
            // 检查是否是数值类型的数据
            const isNumericColumn = isNumericColumnByIndex(i, rowIndex)
            if (isNumericColumn) {
              // 尝试转换为数值
              const numericValue = parseFloat(cellValue.toString().replace(/[^\d.-]/g, ''))
              if (!isNaN(numericValue)) {
                rowData.push(numericValue)
              } else {
                rowData.push(cellValue)
              }
            } else {
              rowData.push(cellValue)
            }
          } else {
            rowData.push('')
          }
        }
      }

      excelData.push(rowData)
    })

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(excelData)

    // 处理合并单元格
    const merges: XLSX.Range[] = []

    // 处理筛选条件列的合并
    let currentRowIndex = 0
    displayData.value.forEach((row, index) => {
      if (row.isHeader) {
        currentRowIndex++
        return
      }

      // 如果有筛选条件且不为空，计算需要合并的行数
      if (row.filterCondition) {
        let mergeCount = 1
        let nextIndex = index + 1

        while (nextIndex < displayData.value.length &&
               !displayData.value[nextIndex].isHeader &&
               !displayData.value[nextIndex].filterCondition) {
          mergeCount++
          nextIndex++
        }

        // 如果需要合并多行，添加合并信息
        if (mergeCount > 1) {
          merges.push({
            s: { r: currentRowIndex, c: 1 }, // 开始行，列（筛选条件列）
            e: { r: currentRowIndex + mergeCount - 1, c: 1 } // 结束行，列
          })
        }
      }

      currentRowIndex++
    })

    // 应用合并单元格
    if (merges.length > 0) {
      worksheet['!merges'] = merges
    }

    // 设置列宽
    const colWidths = dynamicColumns.value.map(col => ({
      wch: col.width / 10 // 转换为字符宽度
    }))
    worksheet['!cols'] = colWidths

    // 设置行高
    const rowHeights: any[] = []
    for (let i = 0; i < displayData.value.length; i++) {
      if (displayData.value[i].isHeader) {
        rowHeights.push({ hpx: 24 }) // 表头行高
      } else {
        rowHeights.push({ hpx: 20 }) // 数据行高
      }
    }
    worksheet['!rows'] = rowHeights

    // 设置行高和样式
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1')
    for (let R = range.s.r; R <= range.e.r; ++R) {
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellRef = XLSX.utils.encode_cell({ r: R, c: C })
        if (!worksheet[cellRef]) continue

                const displayRow = displayData.value[R]

        // 设置表头样式
        if (displayRow?.isHeader) {
          worksheet[cellRef].s = {
            font: { bold: true, color: { rgb: 'FFFFFF' }, size: 12 },
            fill: { fgColor: { rgb: '0B385D' } },
            alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
            border: {
              top: { style: 'thin', color: { rgb: '000000' } },
              bottom: { style: 'thin', color: { rgb: '000000' } },
              left: { style: 'thin', color: { rgb: '000000' } },
              right: { style: 'thin', color: { rgb: '000000' } }
            }
          }
        } else {
          // 设置数据行样式
          worksheet[cellRef].s = {
            font: { size: 11 },
            alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
            border: {
              top: { style: 'thin', color: { rgb: '000000' } },
              bottom: { style: 'thin', color: { rgb: '000000' } },
              left: { style: 'thin', color: { rgb: '000000' } },
              right: { style: 'thin', color: { rgb: '000000' } }
            }
          }
        }
      }
    }

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, `循环水量数据_${selectedDate.value || 'default'}`)

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    })

    // 创建Blob并下载
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 生成文件名（带月份）
    const monthSuffix = selectedDate.value ? `_${selectedDate.value}` : ''
    const fileName = `${unitName.value}_循环水量数据_${monthSuffix}.xlsx`

    // 下载文件
    saveAs(blob, fileName)


  } catch (error) {
    console.error('导出Excel时发生错误:', error)
  }
}

// 组件挂载后的初始化
onMounted(async () => {
  try {
    // 初始化参数
    initParams()

    // 获取可选月份
    await fetchAvailableMonths()

    // 如果有可选月份，自动获取第一个月份的数据
    if (availableMonths.value.length > 0 && selectedDate.value) {
      await fetchTableDataByMonth(selectedDate.value)
    }

  } catch (error) {
    console.error('获取表格数据失败:', error)
  }
})
</script>

<style scoped>
.params-card {
  background-color: rgba(2, 28, 51, 0.5);
  border: 1px solid rgba(11, 56, 93, 1);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  max-width: 600px;
}

.params-card h3 {
  color: #ffffff;
  margin-bottom: 12px;
  font-size: 16px;
}

.params-display {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.params-display span {
  color: #ffffff;
  padding: 6px 12px;
  background-color: rgba(11, 56, 93, 0.3);
  border-radius: 4px;
  font-size: 14px;
}

.calendar-card {
  background-color: rgba(2, 28, 51, 0.5);
  border: 1px solid rgba(11, 56, 93, 1);
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
  max-width: 350px;
  display: inline-block;
}

.calendar-card h3 {
  color: #ffffff;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.calendar-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.available-months {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.available-months .label {
  color: #ffffff;
  font-size: 14px;
  white-space: nowrap;
}
.el-table {
  margin-bottom: 20px;
  background-color: rgba(2, 28, 51, 0.5) !important;
}

/* 表头单元格样式 */
.header-cell {
  font-weight: bold;
  color: #ffffff;
  display: block;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

/* 数据单元格样式 */
.data-cell {
  color: #ffffff;
  display: block;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

/* 整个表格背景色 */
:deep(.el-table) {
  background-color: rgba(2, 28, 51, 0.3) !important;
}

:deep(.el-table__body) {
  background-color: rgba(2, 28, 51, 0.3) !important;
}

:deep(.el-table td) {
  background-color: rgba(2, 28, 51, 0.3) !important;
  padding: 12px 8px;
  color: #ffffff;
  vertical-align: middle;
  min-height: 48px;
}

:deep(.el-table th) {
  background-color: rgba(2, 28, 51, 0.3) !important;
  color: #ffffff;
}

/* 表头行样式 */
:deep(.el-table__body tr:has(.header-cell)) {
  background-color: rgb(11, 56, 93) !important;
}

:deep(.el-table__body tr:has(.header-cell) td) {
  background-color: rgb(11, 56, 93) !important;
  color: #ffffff;
  padding: 12px 8px !important;
  vertical-align: middle;
  min-height: 48px;
}

/* 悬浮效果 */
:deep(.el-table__body tr:hover td) {
  background-color: rgb(41, 70, 93) !important;
  /* border: 2px solid rgb(22, 91, 156) !important; */
  color: #ffffff;
}

/* 表头行悬浮效果 */
:deep(.el-table__body tr:has(.header-cell):hover td) {
  background-color: rgb(11, 56, 93) !important;
  /* border: 1px solid #ebeef5 !important; */
  color: #ffffff;
}

/* 条纹效果 */
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: rgba(2, 28, 51, 0.5) !important;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover td) {
  background-color: rgb(41, 70, 93) !important;
  /* border: 2px solid rgb(22, 91, 156) !important; */
}

/* 日期选择器样式 */
:deep(.el-date-editor) {
  background-color: rgba(2, 28, 51, 0.3) !important;
  border-color: rgba(11, 56, 93, 1) !important;
}

:deep(.el-date-editor .el-input__inner) {
  color: #ffffff !important;
  background-color: transparent !important;
}

:deep(.el-date-editor .el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.6) !important;
}
</style>
