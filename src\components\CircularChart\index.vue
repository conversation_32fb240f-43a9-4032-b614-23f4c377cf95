<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import bgyuan from '@/assets/images/yuan.png'
// 接收父组件传入的数据
const props = defineProps<{
  alarmData: {
    alertLevelCnts: { level: number; cnt: number }[]
    dealRate?: number | null
    notDealCount?: number
    recentCount?: number
    total?: number
  }
}>()

const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null

// 初始化 ECharts 配置，legend 初始留空，后续通过 watch 动态更新
const option = ref({
  tooltip: {
    trigger: 'item',
    position: function (point, params, dom, rect, size) {
    // 无论鼠标在哪，始终返回固定位置，比如 {x: 10, y: 10}
    return [10, 10]
  }

   },
  // legend 分为两个组件：左上（top）和左下（bottom）
  legend: [] as any[],
  series: [
    {
      name: '报警统计',
      type: 'pie',
      // silent: true,
      radius: ['55%', '70%'],
      center: ['65%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 0
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: { show: false, fontSize: 10, fontWeight: 'bold' }
      },
      labelLine: { show: false },
      data: [] as { value: number; name: string }[]
    }
  ],
  // graphic 用于在图表中央显示统计总数以及背景效果，可根据需要自定义
  graphic: [
    {
      type: 'group',
      right: '10%',
      top: 'center',
      children: [
        {
          type: 'image',
          left: 'center',
          top: 'center',
          style: {
            image: bgyuan,
            width: 110,
            height: 110
          },
          z: 0
        },
        {
          type: 'text',
          left: 'center',
          top: -15,
          style: {
            text: '0',
            fill: '#fff',
            font: 'bold 23px sans-serif',
            textAlign: 'center'
          }
        },
        {
          type: 'rect',
          left: 'center',
          top: 8,
          shape: { x: -30, y: 0, width: 80, height: 2, r: 4 },
          style: {
            fill: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(41, 165, 227, 0)' },
              { offset: 0.22, color: 'rgba(42, 167, 230, 1)' },
              { offset: 0.5, color: 'rgba(139, 133, 255, 1)' },
              { offset: 0.8008, color: 'rgba(41, 167, 230, 1)' },
              { offset: 1, color: 'rgba(42, 166, 228, 0)' }
            ])
          }
        },
        {
          type: 'text',
          left: 'center',
          top: 15,
          style: {
            text: '告警总数',
            fill: '#e6e6e6',
            font: '15px Georgia, serif',
            textAlign: 'center'
          }
        }
      ]
    }
  ]
})

// 利用 watch 监听传入的告警数据动态更新图表配置
watch(
  () => props.alarmData,
  (newData) => {
    if (newData && newData.alertLevelCnts) {
      // 根据接口返回的 alertLevelCnts 获取各级别的告警数
      const lowAlarm = newData.alertLevelCnts.find(item => item.level === 2)?.cnt || 0
      const lowSerious = newData.alertLevelCnts.find(item => item.level === 3)?.cnt || 0
      const highAlarm = newData.alertLevelCnts.find(item => item.level === 5)?.cnt || 0
      const highSerious = newData.alertLevelCnts.find(item => item.level === 6)?.cnt || 0

      // 构造 series 数组，顺序与图例保持一致
      const seriesData = [
      { value: lowSerious, name: '低限严重警告' },
        { value: lowAlarm, name: '低限报警' },
        { value: highAlarm, name: '高限报警' },
        { value: highSerious, name: '高限严重警告' }
      ]
      option.value.series[0].data = seriesData

      // 定义两个图例：
      // 左上 legend：显示低限告警相关（两个数据）
      const legendTopData = ['低限严重警告','低限报警']
      // 左下 legend：显示高限告警相关（两个数据）
      const legendBottomData = ['高限报警', '高限严重警告']
      option.value.legend = [
        {
          top: '10%',
          left: 'left',
          orient: 'vertical',
          data: legendTopData,
          textStyle: { color: '#fff' },
          selectedMode: false
        },
        {
          bottom: '5%',
          left: 'left',
          orient: 'vertical',
          data: legendBottomData,
          textStyle: { color: '#fff' },
          selectedMode: false
        }
      ]

      // 更新图表中央显示的总告警数
    //   const totalCount = seriesData.reduce((sum, item) => sum + item.value, 0)
    //   console.log(totalCount)
      option.value.graphic[0].children[1].style.text = newData.total

      // 更新图表
      chartInstance?.setOption(option.value)
    }
  },
  { immediate: true }
)

const resizeChart = () => {
  chartInstance?.resize()
}

onMounted(() => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    chartInstance.setOption(option.value)
    window.addEventListener('resize', resizeChart)
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart)
  chartInstance?.dispose()
})
</script>

<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
