import request from '@/utils/request'

enum Api {
  addPharmaceutical = '/medical/manage/add',
  getPharmaceuticalList = '/medical/manage/list',
  getAllPharmaceuticalList = '/medical/manage/listAll',
  editPharmaceutical = '/medical/manage/edit',
  deletePharmaceutical = '/medical/manage/delete',
}

// 新增药剂
export const addpharmaceutical = (data) => {
  return request({
    url: Api.addPharmaceutical,
    method: 'post',
    data,
  })
}
// 获取药剂列表
export const getPharmaceuticalList = (data) => {
  return request({
    url: Api.getPharmaceuticalList,
    method: 'post',
    data,
  })
}
// 查询所有药剂列表
export const getAllPharmaceuticalList = (data) => {
  return request({
    url: Api.getAllPharmaceuticalList,
    method: 'post',
    data,
  })
}

// 编辑����药
export const editPharmaceutical = (data) => {
  return request({
    url: Api.editPharmaceutical,
    method: 'post',
    data,
  })
}

// 删除药剂
export const deletePharmaceutical = (data) => {
  return request({
    url: Api.deletePharmaceutical,
    method: 'post',
    data,
  })
}
