<template>
  <div class="connection-manager">
    <!-- SVG 连接线渲染层 -->
    <svg 
      class="connections-svg"
      :style="{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 1
      }"
    >
      <!-- 现有的连接线 -->
      <connection-line
        v-for="(connection, index) in connections"
        :key="connection.id"
        :connection="connection"
        :source-component="getComponentById(connection.sourceComponent)"
        :target-component="getComponentById(connection.targetComponent)"
        :all-components="components"
        :all-connections="connections"
        :connection-index="index"
        :is-selected="selectedConnectionId === connection.id"
        @select="onSelectConnection"
        @delete="onDeleteConnection"
        @update="onUpdateConnection"
      />
      
      <!-- 临时连接线（拖拽时显示） -->
      <connection-line
        v-if="tempConnection"
        :connection="tempConnection"
        :source-component="tempSourceComponent"
        :target-component="null"
        :temp-end-point="tempEndPoint"
        :all-components="components"
        :all-connections="connections"
        :connection-index="connections.length"
      />
    </svg>
  </div>
</template>

<script setup lang="ts">
import { ref, readonly } from 'vue'
import ConnectionLine from './ConnectionLine.vue'
import type { ComponentConnection, ConfigurationComponent } from '../types'

const props = defineProps<{
  connections: ComponentConnection[]
  components: ConfigurationComponent[]
  selectedConnectionId?: string
}>()

const emit = defineEmits(['addConnection', 'updateConnection', 'deleteConnection', 'selectConnection'])

// 连接状态
// 连接状态
const isConnecting = ref(false)
const sourceComponentId = ref<string | null>(null)
const sourcePoint = ref<{ x: number; y: number } | null>(null)
const sourceAnchor = ref<string>('center')
const tempEndPoint = ref<{ x: number; y: number } | null>(null)

// 临时连接线和源组件
const tempConnection = ref<ComponentConnection | null>(null)
const tempSourceComponent = ref<ConfigurationComponent | null>(null)

// 根据ID获取组件
const getComponentById = (id: string) => {
  return props.components.find(comp => comp.id === id) || null
}

// 开始连接
const startConnection = (componentId: string, data: { point: { x: number; y: number }, anchor: string }) => {
  console.log('ConnectionManager: 开始连接', componentId, data)

  // 如果已经在连接中，则忽略开始连接事件
  if (isConnecting.value) {
    console.log('ConnectionManager: 已经在连接中，忽略开始连接事件')
    return
  }

  isConnecting.value = true
  sourceComponentId.value = componentId
  sourcePoint.value = data.point
  sourceAnchor.value = data.anchor
  tempEndPoint.value = data.point

  // 获取源组件
  const sourceComponent = getComponentById(componentId)
  if (sourceComponent) {
    tempSourceComponent.value = sourceComponent
    
    // 创建临时连接线
    tempConnection.value = {
      id: 'temp_connection',
      name: '临时连接',
      sourceComponent: componentId,
      targetComponent: '',
      sourceAnchor: {
        position: data.anchor as any,
        offset: { x: 0, y: 0 }
      },
      targetAnchor: {
        position: 'center',
        offset: { x: 0, y: 0 }
      },
      style: {
        lineType: 'straight',
        strokeWidth: 3,
        strokeColor: '#409eff',
        strokeDasharray: '8,4',
        arrowSize: 6,
        arrowColor: '#409eff'
      },
      animation: {
        enabled: false,
        type: 'flow',
        speed: 2,
        direction: 'forward'
      }
    }
  }
}

// 完成连接
// 完成连接
const finishConnection = (componentId: string, data: { point: { x: number; y: number }, anchor: string }) => {
  console.log('ConnectionManager: 完成连接', componentId, data)

  // 如果还没有开始连接，则忽略完成连接事件
  if (!isConnecting.value || !sourceComponentId.value || !sourcePoint.value) {
    console.log('ConnectionManager: 连接状态无效，忽略完成连接事件')
    return
  }

  // 不能连接到自己
  if (sourceComponentId.value === componentId) {
    console.log('ConnectionManager: 不能连接到自己')
    cancelConnection()
    return
  }

  console.log('ConnectionManager: 开始创建连接')
  
  // 创建新连接，使用正确的锚点位置
  const connection: ComponentConnection = {
    id: 'connection_' + Date.now(),
    name: `连接_${Date.now()}`,
    sourceComponent: sourceComponentId.value,
    targetComponent: componentId,
    sourceAnchor: {
      position: sourceAnchor.value as any,
      offset: { x: 0, y: 0 }
    },
    targetAnchor: {
      position: data.anchor as any,
      offset: { x: 0, y: 0 }
    },
    style: {
      lineType: 'straight',
      strokeWidth: 4,
      strokeColor: '#409eff',
      strokeDasharray: '',
      arrowSize: 8,
      arrowColor: '#409eff'
    },
    animation: {
      enabled: false,
      type: 'flow',
      speed: 2,
      direction: 'forward'
    }
  }
  
  console.log('ConnectionManager: 发送添加连接事件', connection)

  // 发送添加连接事件
  emit('addConnection', connection)

  // 重置连接状态
  cancelConnection()
}

// 取消连接
const cancelConnection = () => {
  console.log('ConnectionManager: 取消连接')
  
  isConnecting.value = false
  sourceComponentId.value = null
  sourcePoint.value = null
  tempEndPoint.value = null
  tempConnection.value = null
  tempSourceComponent.value = null
}

// 更新临时连接线的终点
const updateTempConnection = (point: { x: number; y: number }) => {
  if (isConnecting.value) {
    tempEndPoint.value = point
  }
}

// 暴露连接状态给父组件
defineExpose({
  startConnection,
  finishConnection,
  cancelConnection,
  updateTempConnection,
  isConnecting: readonly(isConnecting)
})

// 选择连接线
const onSelectConnection = (connection: ComponentConnection) => {
  emit('selectConnection', connection)
}

// 删除连接线
const onDeleteConnection = (connectionId: string) => {
  emit('deleteConnection', connectionId)
}

// 更新连接
const onUpdateConnection = (connection: ComponentConnection) => {
  emit('updateConnection', connection)
}


</script>

<style scoped>
.connection-manager {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connections-svg {
  overflow: visible;
}
</style>