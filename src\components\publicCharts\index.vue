<template>
  <div ref="chartRef" :style="containerStyle"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

// 定义从父组件传递过来的属性（props）
const props = defineProps<{
  chartOptions: echarts.EChartsOption; // 图表选项
  data: any[]; // 数据
  customStyle: Record<string, any>; // 自定义样式
}>()

const chartRef = ref<HTMLDivElement | null>(null)
let myChart: echarts.ECharts | null = null

// 计算容器样式
const containerStyle = ref({ ...props.customStyle })

// 初始化图表函数
const initChart = () => {
  if (chartRef.value) {
    myChart = echarts.init(chartRef.value)
    // 设置图表选项
    myChart.setOption(props.chartOptions)
  }
}

// 销毁图表函数
const disposeChart = () => {
  if (myChart) {
    myChart.dispose()
    myChart = null
  }
}

// 监控属性的变化
watch(
  () => props.data,
  async (newData) => {
    if (chartRef.value) {
      // 销毁旧的图表实例
      disposeChart()
      // 等待 DOM 更新完成
      await nextTick()
      // 重新初始化图表实例
      initChart()
    }
  },
  { immediate: true, deep: true }
)

// 在组件挂载时初始化图表
onMounted(() => {
  initChart()
  // 窗口大小变化时，调整图表大小
  window.addEventListener('resize', resizeChart)
})

// 在组件卸载时销毁图表实例
onUnmounted(() => {
  disposeChart()
  window.removeEventListener('resize', resizeChart)
})

// 调整图表大小
function resizeChart() {
  if (myChart) {
    myChart.resize()
  }
}
</script>

<style scoped>
/* 默认样式，如果需要，可以在父组件中覆盖 */
div {
  width: 100%;
  height: 400px;
}
</style>
