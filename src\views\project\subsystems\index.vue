<template>
  <div class="p-2 ctableData">
    <!-- <el-card shadow="never"> -->
    <el-table :data="tableData" v-loading="loading" style="width: 100%">
      <el-table-column label="序号" align="center" prop="index" width="100" />
      <el-table-column label="子系统名称" prop="name" align="center" />
      <el-table-column label="所属机组" prop="powerUnitNames" align="center" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="设计参数" placement="top">
            <el-button link type="primary" icon="Files" @click="handleUpdate(scope.row)" />
          </el-tooltip>
          <el-tooltip content="运维人员" placement="top">
            <el-button link type="primary" icon="Avatar" @click="handleStaffList(scope.row)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- </el-card> -->
    <!-- <steamTurbine :dialog="dialog"></steamTurbine> -->

    <el-dialog v-model="dialogVisible" :title="title" width="1500">
      <steamTurbine :row-data="selectedRow"></steamTurbine>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisibleStaff" title="运维人员列表" width="850">
      <el-card shadow="hover">
        <template #header>
          <el-row :gutter="10">
            <el-col :span="1.5">
              <el-button type="primary" plain @click="handleAdd()" v-has-permi="['system:user:add']" icon="Plus">新增</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns" :search="true" />
          </el-row>
        </template>
        <el-table v-loading="loading" :data="tableStaffData" style="width: 100%">
          <el-table-column prop="userNickName" label="昵称" />
          <el-table-column prop="phoneNumber" label="手机号码" />
          <el-table-column label="操作" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-tooltip content="删除" placement="top" v-if="scope.row.id !== 1">
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:user:remove']" />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisibleStaff = false">关闭</el-button>
          </div>
        </template>
      </el-card>
    </el-dialog>

    <el-dialog v-model="dialogVisibleAdd" title="新增运维人员" width="500" align-center>
      <el-select v-model="Staffvalue" filterable multiple placeholder="请选择运维人员" style="width: 240px">
        <el-option v-for="item in staffOptions" :key="item.value" :label="item.label" :disabled="item.disabled" :value="item.value" />
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addUserList()">确 定</el-button>
          <el-button @click="dialogVisibleAdd = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {subSystemList} from '@/api/project/api'
import {listUser,} from '@/api/system/user/index'
import {subSystemMemberList,subSystemMemberListUser,addUser,subSystemMemberDelete} from './api/index'
import steamTurbine from '@/components/project/subsystems/steamTurbine.vue'
import { useCache, CACHE_KEY,useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
emitter.on('projectListChanged', (e) => {
  location.reload()
})
const selectedRow = ref()//存放传递给子组件的参数
const dialogVisible = ref(false)//弹窗
const dialogVisibleStaff = ref(false)//弹窗
const dialogVisibleAdd = ref(false)//弹窗
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const loading = ref(false)
const tableData = ref()
const title = ref('')
const Staffvalue=ref([])
const staffOptions=ref([])
const tableStaffData=ref()
const newsubSystemId=ref()
const handleUpdate=(row)=>{
  selectedRow.value = row
  title.value =selectedRow.value.name+'基本参数'
  nextTick(() => {
    dialogVisible.value = true // 在 DOM 更新后再显示对话框
  })
}
const handleClose = (done: () => void) => {
  // ElMessageBox.confirm('Are you sure to close this dialog?')
  //   .then(() => {
  //     done()
  //   })
  //   .catch(() => {
  //     // catch error
  //   })
}
// 新增
const handleAdd = () => {
  dialogVisibleAdd.value = true
  const parms = {
    projectId: cachedProjects.id,
  }

  subSystemMemberListUser(parms).then((res) => {
    if (res.data.length > 0) {
      // 获取当前选中的子系统的运维人员列表
      const existingUserIds = tableStaffData.value.map(user => user.userId)
      // 处理并设置staffOptions
      staffOptions.value = res.data.map((item) => {
        return {
          value: item.id,
          label: item.nickName,
          disabled: existingUserIds.includes(item.id) // 如果在tableStaffData里找到相同的userId，则禁用该选项
        }
      })
    } else {
      staffOptions.value = []
    }
  })
}
// 确认新增
const addUserList = ()=>{
  const parms={
    subSystemId:newsubSystemId.value,
    members:Staffvalue.value
  }
  addUser(parms).then((res)=>{
    // console.log(res,'确认新增-------')
    if (res.code===200) {
      ElMessage.success('新增成功!')
      dialogVisibleAdd.value = false
      userList(newsubSystemId.value)
    }
  })
}
// 查询运维人员
const handleStaffList=(row)=>{
  newsubSystemId.value=row.id
  dialogVisibleStaff.value = true
  userList(row.id)
}
// 获取运维人员列表
const userList =(row)=>{
  // console.log(row,'-----')
  const parms={
    pageNum:1,
    pageSize:100,
    subSystemId:row
  }
  subSystemMemberList(parms).then((res)=>{
    // console.log(res,'0-------')
    if (res.data.rows.length > 0) {
      tableStaffData.value = res.data.rows
    }else{
      tableStaffData.value=[]
    }
  })
}
const handleDelete=(row)=>{
  // console.log(row,'删除运维人员-------')
  const data=[row.id]
  subSystemMemberDelete(data).then((res)=>{
    // console.log(res,'------')
    if (res.code===200) {
      userList(newsubSystemId.value)
      ElMessage.success('删除成功!')
    }else{
      ElMessage.error('删除失败!')

    }
  })
}
// 获取数据子系统列表
const getList = () => {
  const projectId = cachedProjects.id
  loading.value = true
  subSystemList(projectId).then((res) => {
    if (res.data.length > 0) {
      tableData.value = res.data.map((item, index) => ({
        ...item,
        index: index + 1 // 设置序号，从1开始
      }))
    } else {
      tableData.value = []
    }
  }).finally(() => {
    loading.value = false
  })
}
onMounted(() => {
  getList()
})
</script>
<style scoped lang="scss">
:deep(.el-card){
    background: rgba(2, 28, 51, 0.5);
    box-shadow:inset 0px 2px 28px  rgba(33, 148, 255, 0.5);
    border:none;
}
:deep(.el-card__body){
    border: none;
}
:deep(.el-table, .el-table__expanded-cell ){
    background-color: transparent !important;

  }
:deep(.el-table__body tr, .el-table__body td) {
    padding: 0;
    height: 40px;
  }
:deep(.el-table tr) {
    border: none;
    background-color: transparent;
  }
:deep(.el-table th) {
    /* background-color: transparent; */
    background-color: rgba(7, 53, 92, 1);
    color: rgba(204, 204, 204, 1) !important;
    font-size: 14px;
    font-weight: 400;
  }
:deep(.el-table){
    --el-table-border-color: none;
  }
:deep(.el-table__cell) {
    // color: rgba(204, 204, 204, 1) !important;
  }
  /*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
:deep(.el-table__body-wrapper .el-table__row){
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
:deep(.el-table__body-wrapper .el-table__row:hover td ){
    background: none !important;
    /* 取消单元格背景色，确保整行背景色生效 */
  }
:deep(.el-table__header thead tr th) {
    background: rgba(7, 53, 92, 1) !important;

    color: #ffffff;
  }
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
    color: #fff;
  }
:deep(.el-tree){
    background-color: transparent;
  }
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content){
    background-color:   #07355c;
  }
  :deep(.el-tree-node__expand-icon){
    color: #fff;
  }
  :deep(.el-tree-node__label){
    color: #fff;
  }
  :deep(.el-tree-node__content) {
    &:hover {
      background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    }
  }
:deep(.el-select__tags .el-tag--info){
    background-color:#153059 !important;
}
:deep(.el-tag.el-tag--info){
  color: #fff !important;
}
.el-card{
    background: rgba(2, 28, 51, 0.5);
    box-shadow:inset 0px 2px 28px  rgba(33, 148, 255, 0.5);
    border:none;
}
.el-card__header{
    border: none;
}
.el-table, .el-table__expanded-cell {
    background-color: transparent !important;
  }
.el-table__body tr, .el-table__body td {
    padding: 0;

    height: 40px;
  }
.el-table tr {
    border: none;
    background-color: transparent;
  }
.el-table th {
    background-color: rgba(7, 53, 92, 1);
    color: rgba(204, 204, 204, 1) !important;
    font-size: 14px;
    font-weight: 400;
  }
.el-table{
    --el-table-border-color: none;
  }
.el-table__cell {
    // color: rgba(204, 204, 204, 1) !important;
  }
.el-table__body-wrapper .el-table__row:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    outline: 2px solid rgba(19, 89, 158, 1);
    color: #fff;
  }
.el-table__body-wrapper .el-table__row{
    color: #fff;
  }
.el-table__body-wrapper .el-table__row:hover td {
    background: none !important;
  }
.el-table__header thead tr th {
    background: rgba(7, 53, 92, 1) !important;
    color: #ffffff;
  }
.el-table_1_column_1 .is-leaf .el-table__cell {
    color: #fff;
  }
  .el-tree{
    background-color: transparent;
  }
  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
    background-color:   #07355c;
  }
  .el-tree-node__expand-icon{
    color: #fff;
  }
  .el-tree-node__label{
    color: #fff;
  }
  .el-tree-node__content {
    &:hover {
      background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    }
  }
.el-select__tags .el-tag--info{
    background-color:#153059 !important;
  }
</style>
