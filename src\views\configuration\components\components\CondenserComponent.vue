<template>
  <div 
    class="condenser"
    :style="componentStyle"
  >
    <!-- 凝汽器主体 -->
    <svg
      :width="component.width"
      :height="component.height"
      viewBox="0 0 200 120"
      class="condenser-svg"
      preserveAspectRatio="xMidYMid meet"
    >
      <!-- 渐变定义 -->
      <defs>
        <linearGradient id="condenserGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" :style="`stop-color:${shellColor};stop-opacity:0.8`" />
          <stop offset="50%" :style="`stop-color:${shellColor};stop-opacity:1`" />
          <stop offset="100%" :style="`stop-color:${shellColor};stop-opacity:0.8`" />
        </linearGradient>
        
        <!-- 管束渐变 -->
        <linearGradient id="tubeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#e0e0e0;stop-opacity:1" />
          <stop offset="50%" style="stop-color:#f0f0f0;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#d0d0d0;stop-opacity:1" />
        </linearGradient>
        
        <!-- 蒸汽流动效果 -->
        <linearGradient id="steamFlowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.1" />
          <stop offset="50%" style="stop-color:#e6f3ff;stop-opacity:0.6" />
          <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
        </linearGradient>
      </defs>
      
      <!-- 凝汽器外壳 -->
      <rect x="20" y="30" width="160" height="60" fill="url(#condenserGradient)" stroke="#666" stroke-width="2" rx="8"/>
      
      <!-- 管束结构 -->
      <g class="tube-bundle">
        <!-- 管板 -->
        <rect x="25" y="35" width="8" height="50" fill="#c0c0c0" stroke="#888" stroke-width="1"/>
        <rect x="167" y="35" width="8" height="50" fill="#c0c0c0" stroke="#888" stroke-width="1"/>
        
        <!-- 冷却管束 -->
        <g class="cooling-tubes">
          <line v-for="i in 12" :key="i" 
            :x1="33" :y1="38 + i * 4" 
            :x2="167" :y2="38 + i * 4" 
            stroke="url(#tubeGradient)" stroke-width="2"/>
        </g>
      </g>
      
      <!-- 蒸汽入口 -->
      <g class="steam-inlet">
        <rect x="80" y="15" width="40" height="15" :fill="pipeColor" stroke="#666" stroke-width="1" rx="2"/>
        <text x="100" y="12" text-anchor="middle" font-size="10" fill="#666">蒸汽入口</text>
        
        <!-- 蒸汽流动效果 -->
        <rect v-if="isRunning" x="85" y="18" width="30" height="9" fill="url(#steamFlowGradient)" class="steam-flow"/>
      </g>
      
      <!-- 冷凝水出口 -->
      <g class="condensate-outlet">
        <rect x="80" y="90" width="40" height="15" :fill="pipeColor" stroke="#666" stroke-width="1" rx="2"/>
        <text x="100" y="118" text-anchor="middle" font-size="10" fill="#666">冷凝水出口</text>
      </g>
      
      <!-- 冷却水入口 -->
      <g class="cooling-water-inlet">
        <rect x="5" y="50" width="15" height="20" :fill="waterPipeColor" stroke="#666" stroke-width="1" rx="2"/>
        <text x="2" y="48" font-size="9" fill="#666">冷却水入口</text>
        
        <!-- 水流指示 -->
        <g v-if="isRunning" class="water-flow-in">
          <circle v-for="i in 3" :key="i" 
            :cx="12" :cy="55 + i * 5" r="1" 
            :fill="waterColor" 
            class="water-particle"
            :style="`animation-delay: ${i * 0.3}s`"/>
        </g>
      </g>
      
      <!-- 冷却水出口 -->
      <g class="cooling-water-outlet">
        <rect x="180" y="50" width="15" height="20" :fill="waterPipeColor" stroke="#666" stroke-width="1" rx="2"/>
        <text x="177" y="48" font-size="9" fill="#666">冷却水出口</text>
        
        <!-- 水流指示 -->
        <g v-if="isRunning" class="water-flow-out">
          <circle v-for="i in 3" :key="i" 
            :cx="188" :cy="55 + i * 5" r="1" 
            :fill="waterColor" 
            class="water-particle"
            :style="`animation-delay: ${i * 0.3}s`"/>
        </g>
      </g>
      
      <!-- 真空泵连接 -->
      <g class="vacuum-pump">
        <rect x="140" y="10" width="20" height="10" :fill="pumpColor" stroke="#666" stroke-width="1" rx="2"/>
        <text x="150" y="8" text-anchor="middle" font-size="8" fill="#666">真空泵</text>
      </g>
      
      <!-- 温度传感器 -->
      <circle cx="50" cy="45" r="3" :fill="sensorColor" stroke="#666" stroke-width="1"/>
      <text x="55" y="48" font-size="8" fill="#666">T1</text>
      
      <circle cx="150" cy="75" r="3" :fill="sensorColor" stroke="#666" stroke-width="1"/>
      <text x="155" y="78" font-size="8" fill="#666">T2</text>
      
      <!-- 压力传感器 -->
      <rect x="90" y="40" width="6" height="6" :fill="sensorColor" stroke="#666" stroke-width="1"/>
      <text x="98" y="45" font-size="8" fill="#666">P</text>
      
      <!-- 状态指示器 -->
      <circle 
        cx="185" 
        cy="25" 
        r="5" 
        :fill="statusColor" 
        stroke="#fff" 
        stroke-width="2"
        class="status-indicator"
      />
      
      <!-- 参数显示 -->
      <text x="100" y="55" text-anchor="middle" font-size="11" :fill="textColor" class="parameter-text">
        {{ displayPressure }} kPa
      </text>
      <text x="100" y="68" text-anchor="middle" font-size="10" :fill="textColor" class="parameter-text">
        {{ displayTemperature }}°C
      </text>
      <text x="100" y="80" text-anchor="middle" font-size="9" :fill="textColor" class="parameter-text">
        效率: {{ efficiency }}%
      </text>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
}>()

// 动画状态
const animationFrame = ref(0)
let animationId: number | null = null

// 组件样式
const componentStyle = computed(() => ({
  width: `${props.component.width}px`,
  height: `${props.component.height}px`,
  transform: `rotate(${props.component.rotation || 0}deg)`,
  opacity: props.component.opacity || 1
}))

// 获取数据值
const getValue = (key: string, defaultValue: any = 0) => {
  const data = props.component.data
  if (data?.dynamic?.[key] !== undefined) {
    return data.dynamic[key]
  }
  return data?.static?.[key] ?? defaultValue
}

// 运行状态和参数
const isRunning = computed(() => getValue('isRunning', true))
const pressure = computed(() => getValue('pressure', 8.5))
const temperature = computed(() => getValue('temperature', 45))
const efficiency = computed(() => getValue('efficiency', 92))
const vacuumLevel = computed(() => getValue('vacuumLevel', 95))

// 显示参数
const displayPressure = computed(() => Math.round(pressure.value * 10) / 10)
const displayTemperature = computed(() => Math.round(temperature.value * 10) / 10)

// 颜色配置
const shellColor = computed(() => getValue('shellColor', '#b8d4f0'))
const pipeColor = computed(() => getValue('pipeColor', '#4a90e2'))
const waterPipeColor = computed(() => getValue('waterPipeColor', '#2e7bd6'))
const waterColor = computed(() => getValue('waterColor', '#4da6ff'))
const pumpColor = computed(() => getValue('pumpColor', '#666'))
const sensorColor = computed(() => getValue('sensorColor', '#ff6b35'))
const textColor = computed(() => getValue('textColor', '#333'))

// 状态颜色
const statusColor = computed(() => {
  if (!isRunning.value) return '#ff4444'
  if (efficiency.value > 90 && vacuumLevel.value > 90) return '#44ff44'
  if (efficiency.value > 80 && vacuumLevel.value > 80) return '#ffaa44'
  return '#ff4444'
})

// 动画循环
const animate = () => {
  animationFrame.value += 1
  animationId = requestAnimationFrame(animate)
}

onMounted(() => {
  if (isRunning.value) {
    animate()
  }
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped>
.condenser {
  position: relative;
  display: block;
  user-select: none;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.condenser-svg {
  filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.2));
  width: 100%;
  height: 100%;
  display: block;
  /* 确保SVG能够正确缩放 */
  max-width: 100%;
  max-height: 100%;
}

.steam-flow {
  animation: steamFlow 2s ease-in-out infinite;
}

@keyframes steamFlow {
  0%, 100% { opacity: 0.3; transform: translateX(0); }
  50% { opacity: 0.8; transform: translateX(5px); }
}

.water-particle {
  animation: waterFlow 1.5s linear infinite;
}

@keyframes waterFlow {
  0% { opacity: 0; transform: translateX(-5px); }
  50% { opacity: 1; }
  100% { opacity: 0; transform: translateX(5px); }
}

.status-indicator {
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.parameter-text {
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
}

.cooling-tubes line {
  opacity: 0.8;
}

.water-flow-in .water-particle,
.water-flow-out .water-particle {
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
</style>
