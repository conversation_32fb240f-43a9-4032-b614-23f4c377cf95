<template>
  <div class="shape-component" :style="shapeStyle"></div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
  editing?: boolean
}>()

// 形状样式
const shapeStyle = computed(() => {
  const { style } = props.component
  
  return {
    width: '100%',
    height: '100%',
    backgroundColor: style.backgroundColor || '#409EFF',
    borderRadius: style.borderRadius ? `${style.borderRadius}px` : '0',
    border: style.borderWidth ? `${style.borderWidth}px solid ${style.borderColor || '#ddd'}` : 'none',
    boxShadow: style.boxShadow || 'none',
    opacity: props.component.opacity,
    background: style.gradient 
      ? getGradientBackground(style.gradient) 
      : (style.backgroundColor || '#409EFF')
  }
})

// 获取渐变背景
const getGradientBackground = (gradient: any) => {
  if (!gradient || !gradient.colors || gradient.colors.length < 2) {
    return ''
  }
  
  if (gradient.type === 'linear') {
    const direction = gradient.direction || 0
    return `linear-gradient(${direction}deg, ${gradient.colors.join(', ')})`
  } else if (gradient.type === 'radial') {
    return `radial-gradient(circle, ${gradient.colors.join(', ')})`
  }
  
  return ''
}
</script>

<style scoped>
.shape-component {
  box-sizing: border-box;
}
</style>