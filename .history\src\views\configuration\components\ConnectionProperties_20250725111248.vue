<template>
  <div class="connection-properties">
    <el-form label-position="top" size="small">
      <el-form-item label="连接名称">
        <el-input 
          v-model="localConnection.name" 
          @change="updateConnection"
          class="dark-text"
        />
      </el-form-item>
      
      <el-form-item label="线条类型">
        <el-select v-model="localConnection.style.lineType" @change="updateConnection" class="dark-text">
          <el-option label="直线" value="straight" />
          <el-option label="曲线" value="curved" />
          <el-option label="折线" value="polyline" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="线条宽度">
        <el-input-number 
          v-model="localConnection.style.strokeWidth" 
          :min="1" 
          :max="10"
          controls-position="right"
          @change="updateConnection"
          class="dark-text"
        />
      </el-form-item>
      
      <el-form-item label="线条颜色">
        <el-color-picker 
          v-model="localConnection.style.strokeColor" 
          @change="updateConnection"
        />
      </el-form-item>
      
      <el-form-item label="箭头大小">
        <el-input-number 
          v-model="localConnection.style.arrowSize" 
          :min="4" 
          :max="20"
          controls-position="right"
          @change="updateConnection"
          class="dark-text"
        />
      </el-form-item>
      
      <el-form-item label="箭头颜色">
        <el-color-picker 
          v-model="localConnection.style.arrowColor" 
          @change="updateConnection"
        />
      </el-form-item>
      
      <el-form-item label="虚线样式">
        <el-input 
          v-model="localConnection.style.strokeDasharray" 
          placeholder="例如: 5,5"
          @change="updateConnection"
          class="dark-text"
        />
      </el-form-item>
      
      <el-divider>动画设置</el-divider>
      
      <el-form-item label="启用动画">
        <el-switch 
          v-model="animationEnabled" 
          @change="toggleAnimation"
        />
      </el-form-item>
      
      <template v-if="animationEnabled">
        <el-form-item label="动画类型">
          <el-select v-model="localConnection.animation!.type" @change="updateConnection" class="dark-text">
            <el-option label="流动" value="flow" />
            <el-option label="脉冲" value="pulse" />
            <el-option label="虚线" value="dash" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="动画速度(秒)">
          <el-input-number 
            v-model="localConnection.animation!.speed" 
            :min="0.5" 
            :max="10"
            :step="0.5"
            controls-position="right"
            @change="updateConnection"
            class="dark-text"
          />
        </el-form-item>
        
        <el-form-item label="动画方向">
          <el-select v-model="localConnection.animation!.direction" @change="updateConnection" class="dark-text">
            <el-option label="正向" value="forward" />
            <el-option label="反向" value="backward" />
            <el-option label="双向" value="bidirectional" />
          </el-select>
        </el-form-item>
      </template>
      
      <el-divider>锚点设置</el-divider>
      
      <el-form-item label="源锚点位置">
        <el-select v-model="localConnection.sourceAnchor.position" @change="updateConnection" class="dark-text">
          <el-option label="顶部" value="top" />
          <el-option label="右侧" value="right" />
          <el-option label="底部" value="bottom" />
          <el-option label="左侧" value="left" />
          <el-option label="中心" value="center" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="目标锚点位置">
        <el-select v-model="localConnection.targetAnchor.position" @change="updateConnection" class="dark-text">
          <el-option label="顶部" value="top" />
          <el-option label="右侧" value="right" />
          <el-option label="底部" value="bottom" />
          <el-option label="左侧" value="left" />
          <el-option label="中心" value="center" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ComponentConnection } from '../types'

const props = defineProps<{
  connection: ComponentConnection
}>()

const emit = defineEmits<{
  update: [connection: ComponentConnection]
}>()

// 本地连接副本
const localConnection = ref<ComponentConnection>({ ...props.connection })

// 动画是否启用
const animationEnabled = computed({
  get: () => localConnection.value.animation?.enabled || false,
  set: (value: boolean) => {
    if (!localConnection.value.animation) {
      localConnection.value.animation = {
        enabled: value,
        type: 'flow',
        speed: 2,
        direction: 'forward'
      }
    } else {
      localConnection.value.animation.enabled = value
    }
    updateConnection()
  }
})

// 监听属性变化，更新本地副本
watch(() => props.connection, (newConnection) => {
  localConnection.value = { ...newConnection }
}, { deep: true })

// 切换动画
const toggleAnimation = () => {
  if (!localConnection.value.animation) {
    localConnection.value.animation = {
      enabled: true,
      type: 'flow',
      speed: 2,
      direction: 'forward'
    }
  }
  updateConnection()
}

// 更新连接
const updateConnection = () => {
  emit('update', { ...localConnection.value })
}
</script>

<style scoped>
.connection-properties {
  padding: 10px;
  color: #000;
}

/* 确保所有文字都是黑色的 */
:deep(.el-form-item__label) {
  color: #000 !important;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner),
:deep(.el-select-dropdown__item),
:deep(.el-radio-button__inner),
:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  color: #000 !important;
}

:deep(input),
:deep(textarea) {
  color: #000 !important;
}

:deep(.el-switch__label) {
  color: #000 !important;
}

.dark-text :deep(input) {
  color: #000 !important;
}
</style>