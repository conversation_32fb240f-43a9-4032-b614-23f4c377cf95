<template>
  <div class="connection-manager">
    <!-- SVG 连接线渲染层 -->
    <svg 
      class="connections-svg"
      :style="{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 1
      }"
    >
      <!-- 现有的连接线 -->
      <connection-line
        v-for="connection in connections"
        :key="connection.id"
        :connection="connection"
        :source-component="getComponentById(connection.sourceComponent)"
        :target-component="getComponentById(connection.targetComponent)"
        @select="onSelectConnection"
        @delete="onDeleteConnection"
      />
      
      <!-- 临时连接线（拖拽时显示） -->
      <connection-line
        v-if="tempConnection"
        :connection="tempConnection"
        :source-component="tempSourceComponent"
        :target-component="null"
        :temp-end-point="tempEndPoint"
      />
    </svg>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ConnectionLine from './ConnectionLine.vue'
import type { ComponentConnection, ConfigurationComponent } from '../types'

const props = defineProps<{
  connections: ComponentConnection[]
  components: ConfigurationComponent[]
  connectionMode: boolean
}>()

const emit = defineEmits<{
  addConnection: [connection: ComponentConnection]
  updateConnection: [connection: ComponentConnection]
  deleteConnection: [connectionId: string]
  selectConnection: [connection: ComponentConnection]
}>()

// 连接状态
const isConnecting = ref(false)
const sourceComponentId = ref<string | null>(null)
const sourcePoint = ref<{ x: number; y: number } | null>(null)
const tempEndPoint = ref<{ x: number; y: number } | null>(null)

// 临时连接线和源组件
const tempConnection = ref<ComponentConnection | null>(null)
const tempSourceComponent = ref<ConfigurationComponent | null>(null)

// 根据ID获取组件
const getComponentById = (id: string) => {
  return props.components.find(comp => comp.id === id) || null
}

// 开始连接
const startConnection = (componentId: string, point: { x: number; y: number }) => {
  console.log('ConnectionManager: 开始连接', componentId, point)
  
  if (!props.connectionMode) return
  
  isConnecting.value = true
  sourceComponentId.value = componentId
  sourcePoint.value = point
  tempEndPoint.value = point
  
  // 获取源组件
  const sourceComponent = getComponentById(componentId)
  if (sourceComponent) {
    tempSourceComponent.value = sourceComponent
    
    // 创建临时连接线
    tempConnection.value = {
      id: 'temp_connection',
      name: '临时连接',
      sourceComponent: componentId,
      targetComponent: '',
      sourceAnchor: {
        position: 'center',
        offset: { x: 0, y: 0 }
      },
      targetAnchor: {
        position: 'center',
        offset: { x: 0, y: 0 }
      },
      style: {
        lineType: 'straight',
        strokeWidth: 2,
        strokeColor: '#409eff',
        strokeDasharray: '5,5',
        arrowSize: 8,
        arrowColor: '#409eff'
      },
      animation: {
        enabled: false,
        type: 'flow',
        speed: 2,
        direction: 'forward'
      }
    }
  }
}

// 完成连接
const finishConnection = (componentId: string, point: { x: number; y: number }) => {
  console.log('ConnectionManager: 完成连接', componentId, point)
  
  if (!isConnecting.value || !sourceComponentId.value || !sourcePoint.value) {
    console.log('ConnectionManager: 连接状态无效')
    return
  }
  
  // 不能连接到自己
  if (sourceComponentId.value === componentId) {
    console.log('ConnectionManager: 不能连接到自己')
    cancelConnection()
    return
  }
  
  // 创建新连接
  const connection: ComponentConnection = {
    id: 'connection_' + Date.now(),
    name: `连接_${Date.now()}`,
    sourceComponent: sourceComponentId.value,
    targetComponent: componentId,
    sourceAnchor: {
      position: 'center',
      offset: { x: 0, y: 0 }
    },
    targetAnchor: {
      position: 'center',
      offset: { x: 0, y: 0 }
    },
    style: {
      lineType: 'straight',
      strokeWidth: 2,
      strokeColor: '#409eff',
      strokeDasharray: '',
      arrowSize: 8,
      arrowColor: '#409eff'
    },
    animation: {
      enabled: false,
      type: 'flow',
      speed: 2,
      direction: 'forward'
    }
  }
  
  console.log('ConnectionManager: 发送添加连接事件', connection)
  
  // 发送添加连接事件
  // 发送添加连接事件
  emit('addConnection', connection)
  
  // 重置连接状态
  cancelConnection()
}

// 取消连接
const cancelConnection = () => {
  console.log('ConnectionManager: 取消连接')
  
  isConnecting.value = false
  sourceComponentId.value = null
  sourcePoint.value = null
  tempEndPoint.value = null
  tempConnection.value = null
  tempSourceComponent.value = null
}

// 更新临时连接线的终点
const updateTempConnection = (point: { x: number; y: number }) => {
  if (isConnecting.value) {
    tempEndPoint.value = point
  }
}

// 选择连接线
// 选择连接线
const onSelectConnection = (connection: ComponentConnection) => {
  emit('selectConnection', connection)
}

// 删除连接线
const onDeleteConnection = (connectionId: string) => {
  emit('deleteConnection', connectionId)
}

// 暴露方法给父组件
defineExpose({
  startConnection,
  finishConnection,
  cancelConnection,
  updateTempConnection
})
</script>

<style scoped>
.connection-manager {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connections-svg {
  overflow: visible;
}
</style>