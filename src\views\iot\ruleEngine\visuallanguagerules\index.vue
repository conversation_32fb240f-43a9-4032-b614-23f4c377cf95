<template>
  <div style="margin-top: 30px;">
    <iframe ref="myframe" width="100%" height="750px" :src="'http://***************:11880/?token='+ token"></iframe>
  </div>
</template>

<script setup>
import { ref } from 'vue'
 const getCookie = (name)=>{
  const cookieValue = document.cookie
      .split('; ')
      .find(row => row.startsWith(name + '='))
      ?.split('=')[1]
      return cookieValue ? decodeURIComponent(cookieValue) : null
  }

const token = ref(getCookie('token'))
</script>

<style scoped>
iframe {
  border: none;
}
</style>
