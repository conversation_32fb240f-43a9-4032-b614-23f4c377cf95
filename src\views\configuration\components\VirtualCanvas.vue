<template>
  <div 
    class="virtual-canvas"
    ref="canvasRef"
    :style="{
      width: `${canvasWidth}px`,
      height: `${canvasHeight}px`,
      backgroundColor: backgroundColor,
      transform: `scale(${zoom})`,
      backgroundImage: showGrid ? 'linear-gradient(#ddd 1px, transparent 1px), linear-gradient(90deg, #ddd 1px, transparent 1px)' : 'none',
      backgroundSize: `${gridSize}px ${gridSize}px`
    }"
    @click="onCanvasClick"
    @dragover="onDragOver"
    @drop="onDrop"
    @mousemove="onCanvasMouseMove"
    @scroll="updateViewport"
  >
    <!-- 虚拟化渲染的组件 -->
    <component-renderer
      v-for="component in visibleComponents"
      :key="component.id"
      :component="component"
      :selected="isSelected(component.id)"
      :editing="editing"
      @select="$emit('selectComponent', component.id)"
      @update="$emit('updateComponent', $event)"
      @start-connection="$emit('startConnection', component.id, $event)"
      @finish-connection="$emit('finishConnection', component.id, $event)"
    />
    
    <!-- 连接线管理器 -->
    <connection-manager
      v-if="connections"
      ref="connectionManagerRef"
      :connections="visibleConnections"
      :components="visibleComponents"
      :selected-connection-id="selectedConnectionId"
      @add-connection="$emit('addConnection', $event)"
      @update-connection="$emit('updateConnection', $event)"
      @delete-connection="$emit('deleteConnection', $event)"
      @select-connection="$emit('selectConnection', $event)"
    />
    
    <!-- 性能监控信息（开发模式） -->
    <div v-if="showPerformanceInfo" class="performance-info">
      <div>总组件: {{ totalComponents }}</div>
      <div>可见组件: {{ visibleComponents.length }}</div>
      <div>总连接: {{ totalConnections }}</div>
      <div>可见连接: {{ visibleConnections.length }}</div>
      <div>渲染时间: {{ renderTime }}ms</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { throttle, debounce } from 'lodash-es'
import ComponentRenderer from './ComponentRenderer.vue'
import ConnectionManager from './ConnectionManager.vue'
import type { ConfigurationComponent, ComponentConnection } from '../types'

const props = defineProps<{
  components: ConfigurationComponent[]
  connections: ComponentConnection[]
  canvasWidth: number
  canvasHeight: number
  backgroundColor: string
  zoom: number
  showGrid: boolean
  gridSize: number
  selectedComponents: string[]
  selectedConnectionId?: string
  editing: boolean
}>()

const emit = defineEmits([
  'canvasClick',
  'selectComponent', 
  'updateComponent',
  'startConnection',
  'finishConnection',
  'addConnection',
  'updateConnection', 
  'deleteConnection',
  'selectConnection',
  'canvasMouseMove',
  'dragOver',
  'drop'
])

// 画布引用
const canvasRef = ref<HTMLElement>()
const connectionManagerRef = ref()

// 性能相关
const showPerformanceInfo = ref(process.env.NODE_ENV === 'development')
const renderTime = ref(0)

// 视窗信息
const viewport = ref({
  x: 0,
  y: 0,
  width: 0,
  height: 0
})

// 缓冲区大小（像素），用于提前渲染即将进入视窗的组件
const BUFFER_SIZE = 100

// 总数统计
const totalComponents = computed(() => props.components.length)
const totalConnections = computed(() => props.connections.length)

// 更新视窗信息（节流处理）
const updateViewport = throttle(() => {
  if (!canvasRef.value) return
  
  const container = canvasRef.value.parentElement
  if (!container) return
  
  const rect = container.getBoundingClientRect()
  const scrollLeft = container.scrollLeft || 0
  const scrollTop = container.scrollTop || 0
  
  viewport.value = {
    x: scrollLeft / props.zoom - BUFFER_SIZE,
    y: scrollTop / props.zoom - BUFFER_SIZE,
    width: rect.width / props.zoom + BUFFER_SIZE * 2,
    height: rect.height / props.zoom + BUFFER_SIZE * 2
  }
}, 16) // 约60fps

// 判断组件是否在视窗内
const isComponentVisible = (component: ConfigurationComponent) => {
  const { x, y, width, height } = component
  const { x: vx, y: vy, width: vw, height: vh } = viewport.value
  
  return !(x + width < vx || x > vx + vw || y + height < vy || y > vy + vh)
}

// 判断连接线是否在视窗内
const isConnectionVisible = (connection: ComponentConnection) => {
  const sourceComp = props.components.find(c => c.id === connection.sourceComponent)
  const targetComp = props.components.find(c => c.id === connection.targetComponent)
  
  if (!sourceComp || !targetComp) return false
  
  // 连接线的包围盒
  const minX = Math.min(sourceComp.x, targetComp.x)
  const maxX = Math.max(sourceComp.x + sourceComp.width, targetComp.x + targetComp.width)
  const minY = Math.min(sourceComp.y, targetComp.y)
  const maxY = Math.max(sourceComp.y + sourceComp.height, targetComp.y + targetComp.height)
  
  const { x: vx, y: vy, width: vw, height: vh } = viewport.value
  
  return !(maxX < vx || minX > vx + vw || maxY < vy || minY > vy + vh)
}

// 可见的组件（性能优化核心）
const visibleComponents = computed(() => {
  const startTime = performance.now()
  
  // 如果组件数量较少，直接返回所有组件
  if (props.components.length <= 50) {
    renderTime.value = performance.now() - startTime
    return props.components
  }
  
  // 使用空间索引进行快速筛选
  const visible = props.components.filter(isComponentVisible)
  
  renderTime.value = performance.now() - startTime
  return visible
})

// 可见的连接线
const visibleConnections = computed(() => {
  if (props.connections.length <= 20) {
    return props.connections
  }
  
  return props.connections.filter(isConnectionVisible)
})

// 判断组件是否被选中
const isSelected = (componentId: string) => {
  return props.selectedComponents.includes(componentId)
}

// 事件处理（防抖优化）
const onCanvasClick = debounce((event: MouseEvent) => {
  emit('canvasClick', event)
}, 50)

const onCanvasMouseMove = throttle((event: MouseEvent) => {
  emit('canvasMouseMove', event)
}, 16)

const onDragOver = (event: DragEvent) => {
  emit('dragOver', event)
}

const onDrop = (event: DragEvent) => {
  emit('drop', event)
}

// 监听缩放变化，更新视窗
watch(() => props.zoom, () => {
  nextTick(() => {
    updateViewport()
  })
})

// 监听组件变化，清理缓存
watch(() => props.components.length, () => {
  // 组件数量变化时，重新计算视窗
  nextTick(() => {
    updateViewport()
  })
})

// 组件挂载时初始化视窗
onMounted(() => {
  nextTick(() => {
    updateViewport()
    
    // 监听容器滚动事件
    const container = canvasRef.value?.parentElement
    if (container) {
      container.addEventListener('scroll', updateViewport)
      window.addEventListener('resize', updateViewport)
    }
  })
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  const container = canvasRef.value?.parentElement
  if (container) {
    container.removeEventListener('scroll', updateViewport)
    window.removeEventListener('resize', updateViewport)
  }
})
</script>

<style scoped>
.virtual-canvas {
  position: relative;
  transform-origin: top left;
  min-width: 100%;
  min-height: 100%;
}

.performance-info {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  font-family: monospace;
}

.performance-info div {
  margin-bottom: 2px;
}
</style> 