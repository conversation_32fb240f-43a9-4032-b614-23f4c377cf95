import request from '@/utils/request'

enum Api {
  titleList = '/project/powerPointConfig/list',
  downSamplingApi = '/device/dashboard/downSampling/list',
  waterPumpApi = '/device/dashboard/last/data',
  watertable = '/device/dashboard/water/report',
  intelligence = '/device/dashboard/intelligent/operation',
}

// title查询
export const overviewTitleList = (data) => {
  return request({
    url: Api.titleList,
    method: 'post',
    data,
  })
}

// 查询title所属的图表
export const downSamplingList = (data) => {
  return request({
    url: Api.downSamplingApi,
    method: 'post',
    data,
  })
}
// 查询非曲线数据
export const waterPumpList = (data) => {
  return request({
    url: Api.waterPumpApi,
    method: 'post',
    data,
  })
}
// 查询水质报告表格
export const watertableList = (data) => {
  return request({
    url: Api.watertable,
    method: 'post',
    data,
  })
}
// 查询智能运维
export const intelligenceList = (data) => {
  return request({
    url: Api.intelligence,
    method: 'post',
    data,
  })
}
