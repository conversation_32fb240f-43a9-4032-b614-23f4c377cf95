<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form :model="queryParams" ref="queryFormRef" :inline="true">
          <el-form-item label="用户名称" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="手机号码" prop="phonenumber">
            <el-input v-model="queryParams.phonenumber" placeholder="请输入手机号码" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="openSelectUser" v-hasPermi="['system:role:add']">添加用户</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="CircleClose" :disabled="multiple" @click="cancelAuthUserAll" v-hasPermi="['system:role:remove']">
              批量取消授权
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Close" @click="handleClose">关闭</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :search="true" />
        </el-row>
      </template>
      <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="用户名称" prop="userName" :show-overflow-tooltip="true" />
        <el-table-column label="用户昵称" prop="nickName" :show-overflow-tooltip="true" />
        <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
        <el-table-column label="手机" prop="phonenumber" :show-overflow-tooltip="true" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="取消授权" placement="top">
              <el-button link type="primary" icon="CircleClose" @click="cancelAuthUser(scope.row)" v-hasPermi="['system:role:remove']"> </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      <select-user ref="selectRef" :roleId="queryParams.roleId" @ok="handleQuery" />
    </el-card>
  </div>
</template>

<script setup name="AuthUser" lang="ts">
import { allocatedUserList, authUserCancel, authUserCancelAll } from '@/api/system/role'
import { UserQuery } from '@/api/system/user/types'
import { ComponentInternalInstance } from 'vue'
import { UserVO } from '@/api/system/user/types'
import SelectUser from './selectUser.vue'
import { FormInstance } from 'element-plus'

const route = useRoute()
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'))

const userList = ref<UserVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const multiple = ref(true)
const total = ref(0)
const userIds = ref<Array<string | number>>([])

const queryFormRef = ref<FormInstance>()
const selectRef = ref(SelectUser)

const queryParams = reactive<UserQuery>({
  pageNum: 1,
  pageSize: 10,
  roleId: route.params.roleId as string,
  userName: undefined,
  phonenumber: undefined,
})

/** 查询授权用户列表 */
const getList = async () => {
  loading.value = true
  const res: any = await allocatedUserList(queryParams)
  userList.value = res.data.rows
  total.value = res.data.total
  loading.value = false
}
// 返回按钮
const handleClose = () => {
  const obj = { path: '/system/role' }
  proxy?.$tab.closeOpenPage(obj)
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
// 多选框选中数据
const handleSelectionChange = (selection: UserVO[]) => {
  userIds.value = selection.map((item) => item.id)
  multiple.value = !selection.length
}
/** 打开授权用户表弹窗 */
const openSelectUser = () => {
  selectRef.value.show()
}
/** 取消授权按钮操作 */
const cancelAuthUser = async (row: UserVO) => {
  await proxy?.$modal.confirm('确认要取消该用户"' + row.userName + '"角色吗？')
  await authUserCancel({ userId: row.id, roleId: queryParams.roleId })
  getList()
  proxy?.$modal.msgSuccess('取消授权成功')
}
/** 批量取消授权按钮操作 */
const cancelAuthUserAll = async () => {
  const roleId = queryParams.roleId
  const uIds = userIds.value
  await proxy?.$modal.confirm('是否取消选中用户授权数据项?')
  await authUserCancelAll({ roleId: roleId, userIds: uIds })
  getList()
  proxy?.$modal.msgSuccess('取消授权成功')
}

onMounted(() => {
  getList()
})
</script>
<style scoped>
:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  /* box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5); */
  border: none;
}
:deep(.el-card__body) {
  border: none;
}
:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}
:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}
:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}
:deep(.el-table th) {
  /* background-color: transparent; */
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}
:deep(.el-table) {
  --el-table-border-color: none;
}
/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row) {
  /* 设置鼠标悬停时整行的背景色 */
  color: #fff;
}
:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
  /* 取消单元格背景色，确保整行背景色生效 */
}
:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;

  color: #ffffff;
}
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}
:deep(.el-tree) {
  background-color: transparent;
}
:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #07355c;
}
:deep(.el-tree-node__expand-icon) {
  color: #fff;
}
:deep(.el-tree-node__label) {
  color: #fff;
}
:deep(.el-tree-node__content) {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}
:deep(.el-select__tags .el-tag--info) {
  background-color: #153059 !important;
}
:deep(.el-tag.el-tag--info) {
  color: #fff !important;
}
:deep(.el-select__wrapper) {
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
</style>
