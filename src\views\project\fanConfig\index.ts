import request from '@/utils/request'

enum Api {
  list = '/project/fanConfig/list',
  add = '/project/fanConfig/add',
  saveFun = '/project/fanConfig/edit',
  delete = '/project/fanConfig/delete',
  ziList = '/project/subSystem/sublist',
  // 设备控制
  checkConfigList = '/control/checkConfig/list',
  checkConfigedit = '/control/checkConfig/edit',
  historyAdd = '/control/history/add',
  getControlHistory = '/control/history/list',
  // 新增：设备列表和物模型接口
  getDeviceList = '/product/list',
  getThingModelList = '/product/getThingModelByProductKey',
  // 新增：风机控制点位配置接口
  addFanCtrl = '/system/fanPumpControl/add',
  getFanCtrlList = '/system/fanPumpControl/queryOneByCondition',
  editFanCtrl = '/system/fanPumpControl/edit',
  // 查询设备控制状态
  getDeviceStatus = '/control/history/device/status',
  // 删除设备控制
  deleteDevice = '/control/checkConfig/delete',
}
// 删除设备控制
export const deleteDevice = (data) => {
  return request({
    url: Api.deleteDevice,
    method: 'post',
    data,
  })
}
// 查询控制历史
export const getControlHistory = (data) => {
  return request({
    url: Api.getControlHistory,
    method: 'post',
    data,
  })
}
// 查询设备控制状态
export const getDeviceStatus = (data) => {
  return request({
    url: Api.getDeviceStatus,
    method: 'post',
    data,
  })
}
// 查询设备控制
export const addhistory = (data) => {
  return request({
    url: Api.historyAdd,
    method: 'post',
    data,
  })
}
export const getCheckConfigedit = (data) => {
  return request({
    url: Api.checkConfigedit,
    method: 'post',
    data,
  })
}

// 设备控制查询
export const getCheckConfigList = (data) => {
  return request({
    url: Api.checkConfigList,
    method: 'post',
    data,
  })
}
// 获取列表
export const listFanConfig = (data) => {
  return request({
    url: Api.list,
    method: 'post',
    data,
  })
}
// 获取子系统列表
export const getSubsystemList = (data) => {
  return request({
    url: Api.ziList,
    method: 'post',
    data,
  })
}
// 告警配置保存
export const saveFunList = (data) => {
  return request({
    url: data.id ? Api.saveFun : Api.add,
    method: 'post',
    data,
  })
}
// 删除
export const deleteFun = (data) => {
  return request({
    url: Api.delete,
    method: 'post',
    data,
  })
}

// 新增：风机控制点位配置相关接口
export const addFanCtrl = (data) => {
  return request({
    url: Api.addFanCtrl,
    method: 'post',
    data,
  })
}

export const getFanCtrlList = (data) => {
  return request({
    url: Api.getFanCtrlList,
    method: 'post',
    data,
  })
}

export const editFanCtrl = (data) => {
  return request({
    url: Api.editFanCtrl,
    method: 'post',
    data,
  })
}

export const getDeviceList = (data) => {
  return request({
    url: Api.getDeviceList,
    method: 'post',
    data,
  })
}

export const getThingModelList = (data) => {
  return request({
    url: Api.getThingModelList,
    method: 'post',
    data,
  })
}
