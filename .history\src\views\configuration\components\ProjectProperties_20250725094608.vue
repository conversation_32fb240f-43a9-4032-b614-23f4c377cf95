<template>
  <div class="project-properties">
    <el-form label-position="top" size="small">
      <el-form-item label="项目名称">
        <el-input v-model="localProject.name" @change="updateProject" />
      </el-form-item>
      
      <el-form-item label="项目描述">
        <el-input 
          v-model="localProject.description" 
          type="textarea" 
          rows="3"
          @change="updateProject" 
        />
      </el-form-item>
      
      <el-form-item label="画布尺寸">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-input-number 
              v-model="localProject.width" 
              :min="800" 
              controls-position="right"
              placeholder="宽度"
              @change="updateProject"
            />
          </el-col>
          <el-col :span="12">
            <el-input-number 
              v-model="localProject.height" 
              :min="600" 
              controls-position="right"
              placeholder="高度"
              @change="updateProject"
            />
          </el-col>
        </el-row>
      </el-form-item>
      
      <el-form-item label="背景颜色">
        <el-color-picker 
          v-model="localProject.backgroundColor" 
          show-alpha
          @change="updateProject"
        />
      </el-form-item>
      
      <el-form-item label="状态">
        <el-select v-model="localProject.status" @change="updateProject">
          <el-option label="草稿" value="draft" />
          <el-option label="已发布" value="published" />
          <el-option label="已归档" value="archived" />
        </el-select>
      </el-form-item>
      
      <el-divider>数据绑定</el-divider>
      
      <div class="data-bindings">
        <div v-if="localProject.dataBindings.length === 0" class="no-data">
          <el-empty description="暂无数据绑定" />
        </div>
        
        <el-collapse v-else>
          <el-collapse-item 
            v-for="binding in localProject.dataBindings" 
            :key="binding.id"
            :title="binding.name"
          >
            <el-form label-position="top" size="small">
              <el-form-item label="数据源类型">
                <el-select v-model="binding.type" @change="updateProject">
                  <el-option label="API" value="api" />
                  <el-option label="WebSocket" value="websocket" />
                  <el-option label="MQTT" value="mqtt" />
                  <el-option label="静态数据" value="static" />
                </el-select>
              </el-form-item>
              
              <template v-if="binding.type === 'api'">
                <el-form-item label="API地址">
                  <el-input v-model="binding.config.url" @change="updateProject" />
                </el-form-item>
                
                <el-form-item label="请求方法">
                  <el-select v-model="binding.config.method" @change="updateProject">
                    <el-option label="GET" value="GET" />
                    <el-option label="POST" value="POST" />
                    <el-option label="PUT" value="PUT" />
                    <el-option label="DELETE" value="DELETE" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="刷新间隔(ms)">
                  <el-input-number 
                    v-model="binding.config.interval" 
                    :min="1000" 
                    :step="1000"
                    controls-position="right"
                    @change="updateProject"
                  />
                </el-form-item>
              </template>
              
              <template v-if="binding.type === 'websocket' || binding.type === 'mqtt'">
                <el-form-item label="连接地址">
                  <el-input v-model="binding.config.url" @change="updateProject" />
                </el-form-item>
              </template>
              
              <el-form-item label="数据字段">
                <div v-for="(field, index) in binding.fields" :key="index" class="field-item">
                  <el-row :gutter="10">
                    <el-col :span="8">
                      <el-input 
                        v-model="field.key" 
                        placeholder="字段键名"
                        @change="updateProject"
                      />
                    </el-col>
                    <el-col :span="8">
                      <el-input 
                        v-model="field.name" 
                        placeholder="显示名称"
                        @change="updateProject"
                      />
                    </el-col>
                    <el-col :span="6">
                      <el-select 
                        v-model="field.type" 
                        placeholder="类型"
                        @change="updateProject"
                      >
                        <el-option label="数字" value="number" />
                        <el-option label="文本" value="string" />
                        <el-option label="布尔" value="boolean" />
                        <el-option label="对象" value="object" />
                      </el-select>
                    </el-col>
                    <el-col :span="2">
                      <el-button 
                        type="danger" 
                        icon="Delete" 
                        circle
                        @click="removeField(binding, index)"
                      />
                    </el-col>
                  </el-row>
                </div>
                
                <el-button 
                  type="primary" 
                  plain 
                  icon="Plus"
                  @click="addField(binding)"
                >
                  添加字段
                </el-button>
              </el-form-item>
              
              <el-form-item>
                <el-button 
                  type="danger" 
                  @click="removeBinding(binding.id)"
                >
                  删除数据源
                </el-button>
              </el-form-item>
            </el-form>
          </el-collapse-item>
        </el-collapse>
        
        <div class="add-binding">
          <el-button 
            type="primary" 
            icon="Plus"
            @click="addBinding"
          >
            添加数据源
          </el-button>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { ConfigurationProject, DataBinding, DataField } from '../types'

const props = defineProps<{
  project: ConfigurationProject
}>()

const emit = defineEmits<{
  update: [project: ConfigurationProject]
}>()

// 本地项目副本，用于编辑
const localProject = ref<ConfigurationProject>({...props.project})

// 监听项目变化，更新本地副本
watch(() => props.project, (newProject) => {
  localProject.value = {...newProject}
}, { deep: true })

// 更新项目
const updateProject = () => {
  emit('update', {...localProject.value})
}

// 添加数据绑定
const addBinding = () => {
  const newBinding: DataBinding = {
    id: 'binding_' + Date.now(),
    name: '新数据源',
    type: 'api',
    config: {
      url: '',
      method: 'GET',
      headers: {},
      params: {},
      interval: 5000
    },
    fields: []
  }
  
  localProject.value.dataBindings.push(newBinding)
  updateProject()
}

// 删除数据绑定
const removeBinding = (id: string) => {
  const index = localProject.value.dataBindings.findIndex(binding => binding.id === id)
  if (index !== -1) {
    localProject.value.dataBindings.splice(index, 1)
    updateProject()
  }
}

// 添加字段
const addField = (binding: DataBinding) => {
  const newField: DataField = {
    key: '',
    name: '',
    type: 'string'
  }
  
  binding.fields.push(newField)
  updateProject()
}

// 删除字段
const removeField = (binding: DataBinding, index: number) => {
  binding.fields.splice(index, 1)
  updateProject()
}
</script>

<style scoped>
.project-properties {
  padding: 10px;
}

.data-bindings {
  margin-top: 10px;
}

.field-item {
  margin-bottom: 10px;
}

.add-binding {
  margin-top: 20px;
  text-align: center;
}

.no-data {
  padding: 20px 0;
}
</style>