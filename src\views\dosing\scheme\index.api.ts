import request from '@/utils/request'

enum Api {
  getPharmaceuticallistAll = 'medical/manage/listAll',
  addScheme = '/medical/plan/add',
  editScheme = '/medical/plan/edit',
  deleteScheme = '/medical/plan/delete',
  getSchemeList = '/medical/plan/list',
  pushSolution = '/medical/plan/send',
  getAllUser = '/system/user/list',
  getSelectedUser = '/medical/plan/listReceiver',
  saveSelectedUser = '/medical/plan/editReceiver',
  listCalendar = '/medical/addHistory/listCalendar',
  AddHistory = '/medical/addHistory/addBatch',
}
// 保存加药记录
export const saveAddHistory = (data) => {
  return request({
    url: Api.AddHistory,
    method: 'post',
    data,
  })
}
// 获取加药日历
export const listCalendar = (data) => {
  return request({
    url: Api.listCalendar,
    method: 'post',
    data,
  })
}
// 获取药剂列表
export const getPharmaceuticalAll = (data) => {
  return request({
    url: Api.getPharmaceuticallistAll,
    method: 'post',
    data,
  })
}

// 新增方案
export const addScheme = (data) => {
  return request({
    url: Api.addScheme,
    method: 'post',
    data,
  })
}

// 编辑方案
export const editScheme = (data) => {
  return request({
    url: Api.editScheme,
    method: 'post',
    data,
  })
}

// 删除方案
export const deleteScheme = (data) => {
  return request({
    url: Api.deleteScheme,
    method: 'post',
    data,
  })
}

// 获取方案列表
export const getSchemeList = (data) => {
  return request({
    url: Api.getSchemeList,
    method: 'post',
    data,
  })
}
// 一键推送
export const oneClickPushScheme = (data) => {
  return request({
    url: Api.pushSolution,
    method: 'post',
    data,
  })
}
// 查询所有人员
export const getAllUser = (data) => {
  return request({
    url: Api.getAllUser,
    method: 'post',
    data,
  })
}
// 查询已经选择的人员
export const getSelectedUser = (data) => {
  return request({
    url: Api.getSelectedUser,
    method: 'post',
    data,
  })
}
// 保存需要推送的人员
export const saveSelectedUser = (data) => {
  return request({
    url: Api.saveSelectedUser,
    method: 'post',
    data,
  })
}
