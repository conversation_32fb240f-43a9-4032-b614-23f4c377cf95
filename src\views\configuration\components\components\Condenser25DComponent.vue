<template>
  <div 
    class="condenser-25d"
    :style="componentStyle"
  >
    <!-- 2.5D凝汽器 -->
    <svg
      :width="component.width"
      :height="component.height"
      viewBox="0 0 240 140"
      class="condenser-svg"
      preserveAspectRatio="xMidYMid meet"
    >
      <!-- 渐变和阴影定义 -->
      <defs>
        <!-- 外壳渐变 -->
        <linearGradient id="shell25dGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" :style="`stop-color:${lightShellColor};stop-opacity:1`" />
          <stop offset="30%" :style="`stop-color:${shellColor};stop-opacity:1`" />
          <stop offset="70%" :style="`stop-color:${shellColor};stop-opacity:1`" />
          <stop offset="100%" :style="`stop-color:${darkShellColor};stop-opacity:1`" />
        </linearGradient>
        
        <!-- 顶部渐变 -->
        <linearGradient id="topShellGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" :style="`stop-color:${lightShellColor};stop-opacity:1`" />
          <stop offset="100%" :style="`stop-color:${shellColor};stop-opacity:1`" />
        </linearGradient>
        
        <!-- 管束渐变 -->
        <linearGradient id="tube25dGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#f0f0f0;stop-opacity:1" />
          <stop offset="50%" style="stop-color:#e0e0e0;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#d0d0d0;stop-opacity:1" />
        </linearGradient>
        
        <!-- 蒸汽流动渐变 -->
        <linearGradient id="steamFlow25dGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.1" />
          <stop offset="30%" style="stop-color:#e6f3ff;stop-opacity:0.8" />
          <stop offset="70%" style="stop-color:#cce7ff;stop-opacity:0.6" />
          <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
        </linearGradient>
        
        <!-- 阴影滤镜 -->
        <filter id="dropShadow25d" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="4" dy="6" stdDeviation="4" flood-color="#000000" flood-opacity="0.3"/>
        </filter>
        
        <!-- 内发光滤镜 -->
        <filter id="innerGlow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge> 
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
      
      <!-- 底部阴影 -->
      <ellipse cx="120" cy="125" rx="100" ry="8" fill="#000000" opacity="0.2"/>
      
      <!-- 凝汽器主体 -->
      <g class="condenser-body" filter="url(#dropShadow25d)">
        <!-- 外壳侧面 -->
        <path d="M 30 45 L 210 45 L 215 50 L 215 95 L 210 100 L 30 100 L 25 95 L 25 50 Z" 
              fill="url(#shell25dGradient)" stroke="#555" stroke-width="2"/>
        
        <!-- 外壳顶面 -->
        <path d="M 30 45 L 210 45 L 205 40 L 35 40 Z" 
              fill="url(#topShellGradient)" stroke="#666" stroke-width="1"/>
        
        <!-- 外壳正面 -->
        <rect x="30" y="40" width="180" height="60" :fill="shellColor" stroke="#666" stroke-width="1.5" opacity="0.9"/>
        
        <!-- 管板 -->
        <rect x="35" y="45" width="8" height="50" fill="#c0c0c0" stroke="#888" stroke-width="1"/>
        <rect x="197" y="45" width="8" height="50" fill="#c0c0c0" stroke="#888" stroke-width="1"/>
        
        <!-- 管板侧面 -->
        <path d="M 35 45 L 40 40 L 40 90 L 35 95 Z" fill="#a0a0a0" stroke="#777" stroke-width="1"/>
        <path d="M 197 45 L 202 40 L 202 90 L 197 95 Z" fill="#a0a0a0" stroke="#777" stroke-width="1"/>
      </g>
      
      <!-- 冷却管束 -->
      <g class="tube-bundle">
        <g v-for="i in 12" :key="i" class="tube-row">
          <!-- 管束主线 -->
          <line :x1="43" :y1="48 + i * 4" :x2="197" :y2="48 + i * 4" 
                stroke="url(#tube25dGradient)" stroke-width="2"/>
          <!-- 管束侧面 -->
          <line :x1="43" :y1="48 + i * 4" :x2="45" :y2="46 + i * 4" 
                stroke="#b0b0b0" stroke-width="1" opacity="0.7"/>
        </g>
      </g>
      
      <!-- 蒸汽入口 -->
      <g class="steam-inlet">
        <!-- 入口管道主体 -->
        <path d="M 100 25 L 140 25 L 145 30 L 145 40 L 140 45 L 100 45 L 95 40 L 95 30 Z" 
              :fill="pipeColor" stroke="#444" stroke-width="1.5"/>
        <!-- 入口管道顶面 -->
        <path d="M 100 25 L 140 25 L 135 20 L 105 20 Z" 
              :fill="lightPipeColor" stroke="#555" stroke-width="1"/>
        
        <text x="120" y="18" text-anchor="middle" font-size="10" fill="#666">蒸汽入口</text>
        
        <!-- 蒸汽流动效果 -->
        <g v-if="isRunning" class="steam-flow-25d">
          <rect x="105" y="28" width="30" height="14" fill="url(#steamFlow25dGradient)" 
                class="steam-flow-animation" opacity="0.8"/>
          <rect x="107" y="26" width="26" height="10" fill="url(#steamFlow25dGradient)" 
                class="steam-flow-animation" opacity="0.6"/>
        </g>
      </g>
      
      <!-- 冷凝水出口 -->
      <g class="condensate-outlet">
        <!-- 出口管道主体 -->
        <path d="M 100 100 L 140 100 L 145 105 L 145 115 L 140 120 L 100 120 L 95 115 L 95 105 Z" 
              :fill="pipeColor" stroke="#444" stroke-width="1.5"/>
        <!-- 出口管道底面 -->
        <path d="M 100 120 L 140 120 L 135 125 L 105 125 Z" 
              :fill="darkPipeColor" stroke="#333" stroke-width="1"/>
        
        <text x="120" y="135" text-anchor="middle" font-size="10" fill="#666">冷凝水出口</text>
        
        <!-- 冷凝水流动 -->
        <g v-if="isRunning" class="condensate-flow">
          <circle v-for="i in 5" :key="i" 
            :cx="105 + i * 6" :cy="110" r="1.5" 
            :fill="waterColor" 
            class="condensate-drop"
            :style="`animation-delay: ${i * 0.3}s`"/>
        </g>
      </g>
      
      <!-- 冷却水系统 -->
      <g class="cooling-water-system">
        <!-- 冷却水入口 -->
        <g class="water-inlet">
          <path d="M 5 65 L 25 65 L 30 70 L 30 80 L 25 85 L 5 85 L 0 80 L 0 70 Z" 
                :fill="waterPipeColor" stroke="#444" stroke-width="1.5"/>
          <path d="M 5 65 L 25 65 L 22 62 L 8 62 Z" 
                :fill="lightWaterPipeColor" stroke="#555" stroke-width="1"/>
          
          <text x="2" y="60" font-size="9" fill="#666">冷却水入口</text>
          
          <!-- 入口水流 -->
          <g v-if="isRunning" class="water-flow-in">
            <circle v-for="i in 4" :key="i" 
              :cx="8 + i * 5" :cy="75" r="1.5" 
              :fill="waterColor" 
              class="water-particle-in"
              :style="`animation-delay: ${i * 0.25}s`"/>
          </g>
        </g>
        
        <!-- 冷却水出口 -->
        <g class="water-outlet">
          <path d="M 210 65 L 230 65 L 235 70 L 235 80 L 230 85 L 210 85 L 205 80 L 205 70 Z" 
                :fill="waterPipeColor" stroke="#444" stroke-width="1.5"/>
          <path d="M 210 65 L 230 65 L 227 62 L 213 62 Z" 
                :fill="lightWaterPipeColor" stroke="#555" stroke-width="1"/>
          
          <text x="207" y="60" font-size="9" fill="#666">冷却水出口</text>
          
          <!-- 出口水流 -->
          <g v-if="isRunning" class="water-flow-out">
            <circle v-for="i in 4" :key="i" 
              :cx="213 + i * 5" :cy="75" r="1.5" 
              :fill="waterColor" 
              class="water-particle-out"
              :style="`animation-delay: ${i * 0.25}s`"/>
          </g>
        </g>
      </g>
      
      <!-- 真空系统 -->
      <g class="vacuum-system">
        <path d="M 160 15 L 185 15 L 190 20 L 190 30 L 185 35 L 160 35 L 155 30 L 155 20 Z" 
              :fill="pumpColor" stroke="#444" stroke-width="1.5"/>
        <path d="M 160 15 L 185 15 L 182 12 L 163 12 Z" 
              :fill="lightPumpColor" stroke="#555" stroke-width="1"/>
        
        <text x="172" y="10" text-anchor="middle" font-size="9" fill="#666">真空泵</text>
        
        <!-- 真空指示 -->
        <circle cx="172" cy="25" r="6" fill="#2c3e50" stroke="#34495e" stroke-width="1"/>
        <text x="172" y="28" text-anchor="middle" font-size="7" fill="#00ff00">{{ vacuumLevel }}%</text>
      </g>
      
      <!-- 传感器系统 -->
      <g class="sensor-system">
        <!-- 温度传感器 -->
        <circle cx="60" cy="55" r="4" :fill="sensorColor" stroke="#fff" stroke-width="1.5" filter="url(#innerGlow)"/>
        <text x="66" y="58" font-size="8" fill="#666">T1</text>
        
        <circle cx="180" cy="85" r="4" :fill="sensorColor" stroke="#fff" stroke-width="1.5" filter="url(#innerGlow)"/>
        <text x="186" y="88" font-size="8" fill="#666">T2</text>
        
        <!-- 压力传感器 -->
        <rect x="115" y="50" width="8" height="6" :fill="sensorColor" stroke="#fff" stroke-width="1" rx="1" filter="url(#innerGlow)"/>
        <text x="119" y="48" text-anchor="middle" font-size="8" fill="#666">P</text>
        
        <!-- 流量传感器 -->
        <circle cx="50" cy="75" r="3" :fill="flowSensorColor" stroke="#fff" stroke-width="1" filter="url(#innerGlow)"/>
        <text x="55" y="78" font-size="7" fill="#666">F</text>
      </g>
      
      <!-- 控制面板 -->
      <g class="control-panel-25d">
        <path d="M 45 105 L 85 105 L 90 110 L 90 125 L 85 130 L 45 130 L 40 125 L 40 110 Z" 
              fill="#2c3e50" stroke="#34495e" stroke-width="1.5"/>
        <path d="M 45 105 L 85 105 L 82 102 L 48 102 Z" 
              fill="#34495e" stroke="#4a6741" stroke-width="1"/>
        
        <!-- 显示屏 -->
        <rect x="48" y="108" width="32" height="12" fill="#000" stroke="#333" stroke-width="1" rx="2"/>
        <text x="64" y="116" text-anchor="middle" font-size="9" fill="#00ff00" class="digital-display">
          {{ displayPressure }} kPa
        </text>
        
        <!-- 参数显示 -->
        <text x="64" y="127" text-anchor="middle" font-size="8" fill="#ecf0f1">
          效率: {{ efficiency }}%
        </text>
      </g>
      
      <!-- 状态指示灯 -->
      <g class="status-indicators">
        <circle cx="220" cy="30" r="5" :fill="statusColor" stroke="#fff" stroke-width="2" class="main-status"/>
        <circle cx="220" cy="45" r="4" :fill="powerColor" stroke="#fff" stroke-width="1.5" class="power-status"/>
        <circle cx="220" cy="58" r="3" :fill="alarmColor" stroke="#fff" stroke-width="1" class="alarm-status"/>
      </g>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import type { ConfigurationComponent } from '../../types'

const props = defineProps<{
  component: ConfigurationComponent
}>()

// 动画状态
const animationFrame = ref(0)
let animationId: number | null = null

// 组件样式
const componentStyle = computed(() => ({
  width: `${props.component.width}px`,
  height: `${props.component.height}px`,
  transform: `rotate(${props.component.rotation || 0}deg)`,
  opacity: props.component.opacity || 1
}))

// 获取数据值
const getValue = (key: string, defaultValue: any = 0) => {
  const data = props.component.data
  if (data?.dynamic?.[key] !== undefined) {
    return data.dynamic[key]
  }
  return data?.static?.[key] ?? defaultValue
}

// 运行状态和参数
const isRunning = computed(() => getValue('isRunning', true))
const pressure = computed(() => getValue('pressure', 7.2))
const temperature = computed(() => getValue('temperature', 42))
const efficiency = computed(() => getValue('efficiency', 94))
const vacuumLevel = computed(() => getValue('vacuumLevel', 96))
const flowRate = computed(() => getValue('flowRate', 2800))

// 显示参数
const displayPressure = computed(() => Math.round(pressure.value * 10) / 10)

// 颜色配置
const shellColor = computed(() => getValue('shellColor', '#5dade2'))
const lightShellColor = computed(() => {
  const color = shellColor.value
  const hex = color.replace('#', '')
  const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + 50)
  const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + 50)
  const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + 50)
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
})
const darkShellColor = computed(() => {
  const color = shellColor.value
  const hex = color.replace('#', '')
  const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 50)
  const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 50)
  const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 50)
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
})

const pipeColor = computed(() => getValue('pipeColor', '#3498db'))
const lightPipeColor = computed(() => getValue('lightPipeColor', '#85c1e9'))
const darkPipeColor = computed(() => getValue('darkPipeColor', '#2980b9'))
const waterPipeColor = computed(() => getValue('waterPipeColor', '#16a085'))
const lightWaterPipeColor = computed(() => getValue('lightWaterPipeColor', '#48c9b0'))
const waterColor = computed(() => getValue('waterColor', '#7fb3d3'))
const pumpColor = computed(() => getValue('pumpColor', '#34495e'))
const lightPumpColor = computed(() => getValue('lightPumpColor', '#5d6d7e'))
const sensorColor = computed(() => getValue('sensorColor', '#e74c3c'))
const flowSensorColor = computed(() => getValue('flowSensorColor', '#9b59b6'))
const textColor = computed(() => getValue('textColor', '#2c3e50'))

// 状态颜色
const statusColor = computed(() => {
  if (!isRunning.value) return '#e74c3c'
  if (efficiency.value > 92 && vacuumLevel.value > 95) return '#27ae60'
  if (efficiency.value > 85 && vacuumLevel.value > 90) return '#f39c12'
  return '#e74c3c'
})

const powerColor = computed(() => isRunning.value ? '#27ae60' : '#95a5a6')
const alarmColor = computed(() => {
  if (pressure.value > 10 || temperature.value > 50) return '#e74c3c'
  return '#95a5a6'
})

// 动画循环
const animate = () => {
  animationFrame.value += 1
  animationId = requestAnimationFrame(animate)
}

onMounted(() => {
  if (isRunning.value) {
    animate()
  }
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped>
.condenser-25d {
  position: relative;
  display: block;
  user-select: none;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.condenser-svg {
  filter: drop-shadow(4px 8px 12px rgba(0,0,0,0.3));
  width: 100%;
  height: 100%;
  display: block;
  /* 确保SVG能够正确缩放 */
  max-width: 100%;
  max-height: 100%;
}

.steam-flow-animation {
  animation: steamFlow25d 3s ease-in-out infinite;
}

@keyframes steamFlow25d {
  0%, 100% { 
    opacity: 0.3; 
    transform: translateX(0) scaleX(0.8); 
  }
  50% { 
    opacity: 0.8; 
    transform: translateX(8px) scaleX(1.2); 
  }
}

.condensate-drop {
  animation: condensateDrop 2s linear infinite;
}

@keyframes condensateDrop {
  0% { 
    opacity: 0; 
    transform: translateY(-5px) scale(0.5); 
  }
  50% { 
    opacity: 1; 
    transform: translateY(0) scale(1); 
  }
  100% { 
    opacity: 0; 
    transform: translateY(5px) scale(0.5); 
  }
}

.water-particle-in, .water-particle-out {
  animation: waterFlow25d 2.5s linear infinite;
}

@keyframes waterFlow25d {
  0% { 
    opacity: 0; 
    transform: translateX(-8px) scale(0.6); 
  }
  50% { 
    opacity: 1; 
    transform: translateX(0) scale(1); 
  }
  100% { 
    opacity: 0; 
    transform: translateX(8px) scale(0.6); 
  }
}

.main-status, .power-status, .alarm-status {
  animation: statusPulse25d 2.5s ease-in-out infinite;
}

@keyframes statusPulse25d {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1); 
  }
}

.digital-display {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  text-shadow: 0 0 4px currentColor;
}

.component-label {
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
  font-weight: 600;
  text-shadow: 1px 1px 3px rgba(255,255,255,0.8);
}

/* 2.5D立体效果 */
.condenser-body {
  transform-style: preserve-3d;
}

.tube-bundle {
  opacity: 0.9;
}

.control-panel-25d {
  filter: drop-shadow(2px 3px 5px rgba(0,0,0,0.4));
}

.sensor-system circle, .sensor-system rect {
  filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.3));
}
</style>
