import request from '@/utils/request'

enum Api {
  list = '/benefit/config/list',
  addbenifit = '/benefit/config/add',
  editbenifit = '/benefit/config/edit',
  deletebenifit = '/benefit/config/delete',
}

// 查询效益配置列表
export const benefitConfigList = (data) => {
  return request({
    url: Api.list,
    method: 'post',
    data,
  })
}
// 新增效益配置
export const addConfigbenifit = (data) => {
  return request({
    url: Api.addbenifit,
    method: 'post',
    data,
  })
}
// 删除效益配置

export const deleteConfigbenifit = (data) => {
  return request({
    url: Api.deletebenifit,
    method: 'post',
    data,
  })
}
// 编辑效益配置
export const editConfigbenifit = (data) => {
  return request({
    url: Api.editbenifit,
    method: 'post',
    data,
  })
}
