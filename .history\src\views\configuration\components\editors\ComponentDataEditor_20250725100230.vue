<template>
  <div class="data-editor">
    <el-form label-position="top" size="small">
      <el-form-item label="数据类型">
        <el-radio-group v-model="dataType" @change="switchDataType" class="dark-text">
          <el-radio-button label="static">静态数据</el-radio-button>
          <el-radio-button label="dynamic">动态数据</el-radio-button>
        </el-radio-group>
      </el-form-item>
      
      <!-- 静态数据编辑 -->
      <template v-if="dataType === 'static'">
        <!-- 文本组件 -->
        <template v-if="component.type === 'text'">
          <el-form-item label="文本内容">
            <el-input 
              v-model="localData.static" 
              type="textarea" 
              rows="3"
              @change="updateData"
              class="dark-text"
            />
          </el-form-item>
        </template>
        
        <!-- 按钮组件 -->
        <template v-else-if="component.type === 'button'">
          <el-form-item label="按钮文本">
            <el-input 
              v-model="localData.static" 
              @change="updateData"
              class="dark-text"
            />
          </el-form-item>
        </template>
        
        <!-- 图片组件 -->
        <template v-else-if="component.type === 'image'">
          <el-form-item label="图片URL">
            <el-input 
              v-model="localData.static" 
              placeholder="输入图片URL"
              @change="updateData"
              class="dark-text"
            />
          </el-form-item>
          
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :auto-upload="false"
            :on-change="handleImageChange"
          >
            <img v-if="localData.static" :src="localData.static" class="preview-image" />
            <el-icon v-else class="upload-icon"><Plus /></el-icon>
          </el-upload>
        </template>
        
        <!-- 图表组件 -->
        <template v-else-if="component.type === 'chart'">
          <el-form-item label="图表配置">
            <el-tabs>
              <el-tab-pane label="可视化编辑">
                <el-form-item label="图表类型">
                  <el-select v-model="chartType" @change="updateChartType" class="dark-text">
                    <el-option label="折线图" value="line" />
                    <el-option label="柱状图" value="bar" />
                    <el-option label="饼图" value="pie" />
                    <el-option label="散点图" value="scatter" />
                  </el-select>
                </el-form-item>
                
                <template v-if="['line', 'bar', 'scatter'].includes(chartType)">
                  <el-form-item label="X轴数据">
                    <el-input 
                      v-model="xAxisData" 
                      placeholder="用逗号分隔的值，如: 一月,二月,三月"
                      @change="updateChartData"
                      class="dark-text"
                    />
                  </el-form-item>
                  
                  <el-form-item label="Y轴数据">
                    <el-input 
                      v-model="yAxisData" 
                      placeholder="用逗号分隔的值，如: 10,20,30"
                      @change="updateChartData"
                      class="dark-text"
                    />
                  </el-form-item>
                </template>
                
                <template v-if="chartType === 'pie'">
                  <el-form-item label="饼图数据">
                    <div v-for="(item, index) in pieData" :key="index" class="pie-data-item">
                      <el-row :gutter="10">
                        <el-col :span="10">
                          <el-input 
                            v-model="item.name" 
                            placeholder="名称"
                            @change="updateChartData"
                            class="dark-text"
                          />
                        </el-col>
                        <el-col :span="10">
                          <el-input-number 
                            v-model="item.value" 
                            :min="0" 
                            controls-position="right"
                            placeholder="值"
                            @change="updateChartData"
                            class="dark-text"
                          />
                        </el-col>
                        <el-col :span="4">
                          <el-button 
                            type="danger" 
                            icon="Delete" 
                            circle
                            @click="removePieDataItem(index)"
                          />
                        </el-col>
                      </el-row>
                    </div>
                    
                    <el-button 
                      type="primary" 
                      plain 
                      icon="Plus"
                      @click="addPieDataItem"
                    >
                      添加数据项
                    </el-button>
                  </el-form-item>
                </template>
              </el-tab-pane>
              
              <el-tab-pane label="JSON编辑">
                <el-form-item>
                  <el-input 
                    v-model="chartJson" 
                    type="textarea"