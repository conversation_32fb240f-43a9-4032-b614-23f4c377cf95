<template>
  <div ref="chartContainer" style="width: 100%; height: 100%;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, defineComponent, PropType } from 'vue'
import * as echarts from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { TooltipComponent, GridComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

echarts.use([
  LineChart,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  CanvasRenderer
])

const props = defineProps({
  seriesData: {
    type: Array as PropType<Array<{ name: string; type: string; smooth?: boolean; stack?: string; areaStyle?: object; data: number[] }>>,
    required: true
  },
  xAxisData: {
    type: Array as PropType<string[]>,
    required: true
  }
})

const chartContainer = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null
let resizeObserver: ResizeObserver | null = null

const updateChart = () => {
  if (!chartInstance) return
  chartInstance.setOption({
    tooltip: {
      trigger: 'axis'
    },
  //   areaStyle: {
  //   opacity: 0.5 // 调整面积的透明度
  // },
    legend: {
      data: props.seriesData?.map((series: { name: string }) => series.name) || [],
      textStyle: {
        color: '#FFFFFF'
      }
    },
    xAxis: {
      type: 'category',
      data: props.xAxisData,
      axisLabel: {
        color: '#FFFFFF'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#FFFFFF'
      }
    },
    series: props.seriesData,
    // 可选：设置图表背景色或其他全局样式
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    // 可选：设置颜色主题
    color: ['#5470C6', '#91CC75', '#EE6666']
  })
}

onMounted(() => {
  if (chartContainer.value) {
    chartInstance = echarts.init(chartContainer.value)
    updateChart()

    // 使用 ResizeObserver 监听容器尺寸变化
    resizeObserver = new ResizeObserver(() => {
      if (chartInstance) {
        chartInstance.resize()
      }
    })
    resizeObserver.observe(chartContainer.value)
  }
})

// 监听数据变化并更新图表
watch(() => props.seriesData, updateChart, { deep: true })
watch(() => props.xAxisData, updateChart, { deep: true })

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  if (resizeObserver && chartContainer.value) {
    resizeObserver.unobserve(chartContainer.value)
    resizeObserver = null
  }
})

defineComponent({
  name: 'PublicCharts'
})
</script>
<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
