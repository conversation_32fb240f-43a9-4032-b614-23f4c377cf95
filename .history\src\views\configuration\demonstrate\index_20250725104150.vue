<template>
  <div class="demonstrate-container">
    <div class="demonstrate-header" v-if="!isFullscreen">
      <h2>{{ currentProject?.name || '组态演示' }}</h2>
      <div class="header-actions">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>
          返回
        </el-button>
        <el-button @click="toggleFullscreen">
          <el-icon><FullScreen /></el-icon>
          全屏
        </el-button>
      </div>
    </div>
    
    <div 
      class="demonstrate-canvas"
      :class="{ 'fullscreen': isFullscreen }"
      :style="{
        width: `${currentProject?.width || 1920}px`,
        height: `${currentProject?.height || 1080}px`,
        backgroundColor: currentProject?.backgroundColor || '#f0f0f0',
        transform: `scale(${scale})`,
      }"
    >
      <template v-if="currentProject">
        <component-renderer
          v-for="component in currentProject.components"
          :key="component.id"
          :component="component"
          :editing="false"
        />
      </template>
      <div v-else class="empty-canvas">
        <el-empty description="未找到组态项目" />
      </div>
      
      <div v-if="isFullscreen" class="fullscreen-controls">
        <el-button circle @click="toggleFullscreen">
          <el-icon><CloseBold /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useConfigurationStore } from '../stores/configurationStore'
import ComponentRenderer from '../components/ComponentRenderer.vue'
import type { ConfigurationProject, DataBinding } from '../types'

const route = useRoute()
const router = useRouter()
const configStore = useConfigurationStore()

// 当前项目
const currentProject = ref<ConfigurationProject | null>(null)

// 是否全屏
const isFullscreen = ref(false)

// 缩放比例
const scale = ref(1)

// 数据源连接
const dataConnections = ref<any[]>([])

// 计算容器尺寸和缩放比例
const calculateScale = () => {
  if (!currentProject.value) return
  
  const containerWidth = window.innerWidth - (isFullscreen.value ? 0 : 40)
  const containerHeight = window.innerHeight - (isFullscreen.value ? 0 : 100)
  
  const scaleX = containerWidth / currentProject.value.width
  const scaleY = containerHeight / currentProject.value.height
  
  // 使用较小的缩放比例，确保整个画布都能显示
  scale.value = Math.min(scaleX, scaleY)
}

// 监听窗口大小变化
const handleResize = () => {
  calculateScale()
}

// 加载项目
onMounted(async () => {
  const projectId = route.params.id as string
  if (projectId) {
    await loadProject(projectId)
    calculateScale()
    window.addEventListener('resize', handleResize)
  } else {
    ElMessage.error('未指定项目ID')
    router.back()
  }
})

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  // 关闭所有数据连接
  closeAllDataConnections()
})

// 监听全屏状态变化
watch(isFullscreen, () => {
  calculateScale()
})

// 加载项目
const loadProject = async (id: string) => {
  try {
    // 这里应该从API加载项目数据
    // 临时使用示例数据
    const project: ConfigurationProject = {
      id,
      name: '示例组态项目',
      description: '这是一个示例组态项目',
      width: 1920,
      height: 1080,
      backgroundColor: '#f0f0f0',
      components: [],
      dataBindings: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'admin',
      status: 'published'
    }
    
    currentProject.value = project
    
    // 初始化数据连接
    initDataConnections()
  } catch (error) {
    ElMessage.error('加载项目失败')
    router.back()
  }
}

// 初始化数据连接
const initDataConnections = () => {
  if (!currentProject.value) return
  
  // 关闭现有连接
  closeAllDataConnections()
  
  // 为每个数据绑定创建连接
  currentProject.value.dataBindings.forEach(binding => {
    createDataConnection(binding)
  })
}

// 创建数据连接
const createDataConnection = (binding: DataBinding) => {
  switch (binding.type) {
    case 'api':
      createApiConnection(binding)
      break
    case 'websocket':
      createWebSocketConnection(binding)
      break
    case 'mqtt':
      createMqttConnection(binding)
      break
  }
}

// 创建API连接
const createApiConnection = (binding: DataBinding) => {
  if (!binding.config.url || !binding.config.interval) return
  
  // 创建定时器，定期请求API
  const timer = setInterval(async () => {
    try {
      const response = await fetch(binding.config.url as string, {
        method: binding.config.method || 'GET',
        headers: binding.config.headers || {},
        body: binding.config.method !== 'GET' ? JSON.stringify(binding.config.params) : undefined
      })
      
      const data = await response.json()
      updateComponentsData(binding.id, data)
    } catch (error) {
      console.error('API请求失败', error)
    }
  }, binding.config.interval)
  
  // 保存连接信息
  dataConnections.value.push({
    id: binding.id,
    type: 'api',
    timer
  })
}

// 创建WebSocket连接
const createWebSocketConnection = (binding: DataBinding) => {
  if (!binding.config.url) return
  
  try {
    const ws = new WebSocket(binding.config.url as string)
    
    ws.onopen = () => {
      console.log('WebSocket连接已建立')
    }
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        updateComponentsData(binding.id, data)
      } catch (error) {
        console.error('WebSocket数据解析失败', error)
      }
    }
    
    ws.onerror = (error) => {
      console.error('WebSocket错误', error)
    }
    
    ws.onclose = () => {
      console.log('WebSocket连接已关闭')
    }
    
    // 保存连接信息
    dataConnections.value.push({
      id: binding.id,
      type: 'websocket',
      connection: ws
    })
  } catch (error) {
    console.error('WebSocket连接失败', error)
  }
}

// 创建MQTT连接
const createMqttConnection = (binding: DataBinding) => {
  // MQTT连接需要引入MQTT客户端库
  // 这里仅作为示例，实际实现需要根据具体的MQTT库
  console.log('MQTT连接暂未实现')
}

// 更新组件数据
const updateComponentsData = (bindingId: string, data: any) => {
  if (!currentProject.value) return
  
  // 查找使用该数据源的组件，并更新其数据
  currentProject.value.components.forEach(component => {
    if (component.data.dynamic && component.data.dynamic.dataSource === bindingId) {
      // 根据字段路径获取数据
      const field = component.data.dynamic.field
      const value = getValueByPath(data, field)
      
      // 更新组件数据
      if (value !== undefined) {
        component.data.dynamic.value = value
      }
    }
  })
}

// 根据路径获取对象中的值
const getValueByPath = (obj: any, path: string) => {
  return path.split('.').reduce((prev, curr) => {
    return prev && prev[curr]
  }, obj)
}

// 关闭所有数据连接
const closeAllDataConnections = () => {
  dataConnections.value.forEach(connection => {
    if (connection.type === 'api' && connection.timer) {
      clearInterval(connection.timer)
    } else if (connection.type === 'websocket' && connection.connection) {
      connection.connection.close()
    }
    // 其他类型的连接关闭逻辑
  })
  
  dataConnections.value = []
}

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  if (isFullscreen.value) {
    // 进入全屏模式
    const elem = document.documentElement
    if (elem.requestFullscreen) {
      elem.requestFullscreen()
    }
  } else {
    // 退出全屏模式
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
.demonstrate-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  overflow: hidden;
}

.demonstrate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #fff;
  border-bottom: 1px solid var(--el-border-color-light);
}

.demonstrate-header h2 {
  margin: 0;
  font-size: 18px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.demonstrate-canvas {
  position: relative;
  transform-origin: top left;
  margin: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.demonstrate-canvas.fullscreen {
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.empty-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fullscreen-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
}
</style>