import request from '@/utils/request'
import { AxiosPromise } from 'axios'
import { hisDataQuery, hisDataVO, echatsQuery, echatsVO } from './type'
// 获取机组、tabs切换
export function hisCalCurve(query: hisDataQuery): AxiosPromise<hisDataVO[]> {
  return request({
    url: '/project/powerPointConfig/all',
    method: 'post',
    data: query,
  })
}
// 获取图表数据
export function echatsData(query: echatsQuery): AxiosPromise<echatsVO[]> {
  return request({
    url: '/device/deviceProperty/downSampling/list',
    method: 'post',
    data: query,
  })
}
