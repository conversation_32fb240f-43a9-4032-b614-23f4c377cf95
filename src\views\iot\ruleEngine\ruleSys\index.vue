<template>
  <div>
    <yt-crud
      v-bind="options"
      ref="crudRef"
      :data="data"
      :column="column"
      :loading="state.loading"
      :total="state.total"
      v-model:page="state.page"
      v-model:query="state.query"
      @on-load="getData"
      @saveFun="onSave"
      @del-fun="handleDelete"
    >
      <template #state="scope">
        <el-switch
          v-model="scope.row.state"
          active-value="running"
          inactive-value="stopped"
          disabled
          style="--el-switch-on-color: #029D40; --el-switch-off-color: #DFDFDF"
        />
        <!-- <div v-if="scope.row.state === 'stopped'" style="color: red;">已停止</div>
        <div v-if="scope.row.state === 'running'" style="color: green;">运行中</div> -->
      </template>
      <template #log="scope">
        <el-button size="small" type="primary" @click="handleViewLog(scope.row.id)">查看</el-button>
      </template>
      <template #menuSlot="scope">
        <el-tooltip class="box-item" effect="dark" content="停止" placement="top">
          <el-button v-if="scope.row.state === 'running'" link type="danger" icon="SwitchButton" @click="handlePause(scope.row)" />
        </el-tooltip>
        <el-tooltip class="box-item" effect="dark" content="开启" placement="top">
          <el-button v-if="scope.row.state === 'stopped'" link type="success" icon="Open" @click="handleOpen(scope.row)" />
        </el-tooltip>
      </template>
      <template #customFormItem="{row}">
        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="监听器" :name="1">
            <listener v-if="activeName === 1" v-model:listeners="row.listeners" @update:modelData="handleModelData" />
          </el-tab-pane>
          <el-tab-pane label="过滤器" :name="2">
            <filtera v-if="activeName === 2" v-model:filters="row.filters" />
          </el-tab-pane>
          <el-tab-pane label="输出" :name="3">
            <Output v-if="activeName === 3" v-model:list="row.actions" type="rule" actions="device,http,mqtt,kafka,tcp,alert" />
          </el-tab-pane>
        </el-tabs>
      </template>
    </yt-crud>
    <log-dialog ref="logDialogRef" title="场景执行日志" />
  </div>
</template>
<script lang="ts" setup>
import { IColumn } from '@/components/common/types/tableCommon'
import { getRuleList, saveRule, deleteRule, pauseRule, resumeRule } from '../api/rule.api'
import { getObjectModel } from '@/views/iot/equipment/api/products.api'
import Listener from './modules/listener.vue'
import Filtera from './modules/filtera.vue'
import Output from './modules/output.vue'
import LogDialog from '../modules/logDialog.vue'
import YtCrud from '@/components/common/yt-crud.vue'
import emitter from '@/utils/eventBus.js'
import { CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
// 查看日志
const logDialogRef = ref()
const handleViewLog = (id: string) => {
  logDialogRef.value.openDialog(id)
}
const activeName = ref(1)
const column: IColumn[] = [
  {
    label: '规则名称',
    key: 'name',
    search: true,
    rules: [{ required: true, message: '规则名称不能为空' }],
  },
  {
    label: '状态',
    key: 'state',
    slot: true,
    formHide: true,
  },
  {
    label: '规则类型',
    key: 'type',
    type: 'select',
    search: true,
    componentProps: {
      defaultValue: 'flow',
      clearable: false,
      options: [
        {
          label: '场景联动',
          value: 'scene',
        },
        {
          label: '数据流转',
          value: 'flow',
        },
      ],
    },
  },
  {
    label: '执行日志',
    key: 'log',
    slot: true,
    formHide: true,
  },
  {
    label: '场景描述',
    key: 'desc',
    hide: true,
    componentProps: {
      type: 'textarea',
      row: 3,
    },
  },
  {
    label: '自定义表单项',
    key: 'custom',
    hide: true,
    formItemSlot: true,
  },
  {
    label: '创建时间',
    key: 'createAt',
    type: 'date',
    formHide: true,
  },
]
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {
    type: 'flow',
  },
})
const data = ref([])
const comparators = ref([
  {
    name: '大于',
    value: '>',
  },
  {
    name: '等于',
    value: '==',
  },
  {
    name: '大于等于',
    value: '>=',
  },
  {
    name: '小于',
    value: '<',
  },
  {
    name: '小于等于',
    value: '<=',
  },
  {
    name: '不等于',
    value: '!=',
  },
  {
    name: '包含',
    value: 'in',
  },
  {
    name: '不包含',
    value: 'notin',
  },
  {
    name: '相似',
    value: 'like',
  },
  {
    name: '任意',
    value: '*',
  },
  {
    name: '季节',
    value: 'season'
  }
])
const seasons = ref([
  { label: '春季', value: 0 },
  { label: '夏季', value: 1 },
  { label: '秋季', value: 2 },
  { label: '冬季', value: 3 }
])
// 保存数据
const onSave = async ({ type, data, cancel }: any) => {
  state.loading = true
  const obj = toRaw(data)

  // 遍历 obj.listeners 对象，依次根据 pk 调用 getObjectModel 接口
  obj.listeners = await Promise.all(
    (obj.listeners || []).map(async (listener) => {
      // 调用 getObjectModel 接口获取设备信息
      const modelData = await getObjectModel(listener.pk)

      // 检查 modelData 和 modelData.model 是否存在，避免出现 undefined 错误
      if (modelData && modelData.data && modelData.data.model && modelData.data.model.properties) {
        // 更新 listener 中的 conditions parameters
        listener.conditions = listener.conditions.map((condition) => {
          condition.parameters = condition.parameters.map((param) => {
            // 在模型数据中查找匹配的 identifier
            const matchedProperty = modelData.data.model.properties.find((property) => property.identifier === param.identifier)
            const comparatorMatch = comparators.value.find(
              (comparator) => comparator.value === param.comparator
            )
            // 如果是季节则不需要单位 并且他们的参数值是seasons枚举出来的
            if (param.comparator === 'season') {
              const season = seasons.value.find(season => season.value === param.value)
              if (season&&comparatorMatch) {
                param.condDesc = `${comparatorMatch.name || ''} ${season.label}`
              }
            } else if(comparatorMatch){
              param.condDesc = `${matchedProperty.name}${comparatorMatch.name || ''} ${param.value}${matchedProperty.unit || ''}`
            }

            return param
          })
          return condition
        })
      } else {
        console.error(`pk ${listener.pk}的模型数据缺失或无效`)
      }

      return {
        ...listener,
        config: JSON.stringify(listener),
      }
    })
  )

  // 处理 obj.filters 和 obj.actions 的逻辑
  obj.filters = (obj.filters || []).map((m) => {
    if (m?.conditions == undefined && m?.config) {
      m = JSON.parse(m.config)
    }
    const mObj = {
      type: 'device',
      pk: m.pk,
      dn: m.dn,
      conditions: m?.conditions.map((m2) => ({
        ...m2,
        device: m.device,
      })),
      deviceRadio: m.deviceRadio,
    }
    return {
      ...mObj,
      config: JSON.stringify(mObj),
    }
  })

  obj.actions = (obj.actions || []).map((m) => {
    m.saved = true
    if (m.config) {
      return m
    }
    return {
      ...m,
      config: JSON.stringify(m),
    }
  })

  obj.projectId = cachedProjects.id

  // 调用 saveRule 接口
  saveRule(obj)
    .then((res) => {
      ElMessage.success(type === 'add' ? '添加成功' : '编辑成功')
      cancel()
      getData()
    })
    .finally(() => {
      state.loading = false
    })
}

// const onSave = ({ type, data, cancel }: any) => {
//   state.loading = true
//   const obj = toRaw(data)
//   obj.listeners = (obj.listeners || [])?.map( (m) => {
//     const mObj = {
//       type: m.type,
//       pk: m.pk,
//       dn: m?.dn,
//       conditions: m?.conditions.map((m2) => ({
//         ...m2,
//         device: m?.device,
//       })),
//     }
//     console.log('mObj:', mObj)
//     return {
//       ...mObj,
//       config: JSON.stringify(mObj),
//     }
//   })
//   obj.filters = (obj.filters || [])?.map((m) => {
//     if (m?.conditions == undefined && m?.config) {
//       m = JSON.parse(m.config)
//     }
//     const mObj = {
//       type: 'device',
//       pk: m.pk,
//       dn: m.dn,
//       conditions: m?.conditions.map((m2) => ({
//         ...m2,
//         device: m.device,
//       })),
//       deviceRadio: m.deviceRadio,
//     }
//     return {
//       ...mObj,
//       config: JSON.stringify(mObj),
//     }
//   })
//   obj.actions = (obj.actions || [])?.map((m) => {
//     m.saved = true
//     if (m.config) {
//       return m
//     }
//     return {
//       ...m,
//       config: JSON.stringify(m),
//     }
//   })
//   obj.projectId = cachedProjects.id
//   console.log('obj:', obj)
//   saveRule(obj)
//     .then((res) => {
//       ElMessage.success(type === 'add' ? '添加成功' : '编辑成功')
//       cancel()
//       getData()
//     })
//     .finally(() => {
//       state.loading = false
//     })
// }
emitter.on('projectListChanged', (e) => {
  // console.log(e,'----------')
  location.reload()
})

const getData = () => {
  state.loading = true
  const { id: projectId } = cachedProjects
  const project = { ...state.query, projectId }
  getRuleList({
    ...state.page,
    ...project,
  }).then((res) => {
    data.value = res.data.rows
    state.total = res.data.total
  })
  state.loading = false
}
const handleDelete = (row) => {
  state.loading = true
  deleteRule(row.id)
    .then((res) => {
      ElMessage.success('删除成功!')
      getData()
    })
    .finally(() => {
      state.loading = false
    })
}
const handleOpen = (row) => {
  state.loading = true
  resumeRule(row.id)
    .then((res) => {
      ElMessage.success('开启成功!')
      getData()
    })
    .finally(() => {
      state.loading = false
    })
}
const handlePause = (row) => {
  state.loading = true
  pauseRule(row.id)
    .then((res) => {
      ElMessage.success('停止成功!')
      getData()
    })
    .finally(() => {
      state.loading = false
    })
}
const options = reactive({
  formProps: {
    width: 1000,
  },
  tableProps: {
    selection: false,
    viewBtn: false,
    menuSlot: true,
    menuWidth: 300,
  },
  searchProps: {},
})
</script>
<style scoped>
:deep(.el-select__wrapper) {
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
</style>
<style lang="scss">
@import '@/assets/styles/functionConfig.scss';
</style>
