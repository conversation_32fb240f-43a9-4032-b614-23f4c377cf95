<template>
  <div 
    class="component-renderer"
    :class="{ 
      'selected': selected,
      'editing': editing,
      'locked': component.locked,
      'connecting': isConnecting
    }"
    :style="{
      position: 'absolute',
      left: `${component.x}px`,
      top: `${component.y}px`,
      width: `${component.width}px`,
      height: `${component.height}px`,
      zIndex: component.z,
      transform: `rotate(${component.rotation}deg)`,
      opacity: component.opacity,
      visibility: component.visible ? 'visible' : 'hidden'
    }"
    @click="handleClick"
    @mousedown="handleMouseDown"
  >
    <!-- 组件内容 -->
    <div class="component-content">
      <component 
        :is="componentType" 
        :component="component"
        :editing="editing"
        @update="handleUpdate"
      />
    </div>
    
    <!-- 连接点 - 只在编辑模式且启用连线模式时显示 -->
    <template v-if="editing && showConnectionPoints">
      <!-- 顶部连接点 -->
      <div 
        class="connection-point top"
        :class="{ 'active': activePoint === 'top' }"
        @mousedown.stop="startConnection('top')"
        @mouseenter="highlightPoint('top')"
        @mouseleave="unhighlightPoint"
      ></div>
      
      <!-- 右侧连接点 -->
      <div 
        class="connection-point right"
        :class="{ 'active': activePoint === 'right' }"
        @mousedown.stop="startConnection('right')"
        @mouseenter="highlightPoint('right')"
        @mouseleave="unhighlightPoint"
      ></div>
      
      <!-- 底部连接点 -->
      <div 
        class="connection-point bottom"
        :class="{ 'active': activePoint === 'bottom' }"
        @mousedown.stop="startConnection('bottom')"
        @mouseenter="highlightPoint('bottom')"
        @mouseleave="unhighlightPoint"
      ></div>
      
      <!-- 左侧连接点 -->
      <div 
        class="connection-point left"
        :class="{ 'active': activePoint === 'left' }"
        @mousedown.stop="startConnection('left')"
        @mouseenter="highlightPoint('left')"
        @mouseleave="unhighlightPoint"
      ></div>
      
      <!-- 中心连接点 -->
      <div 
        class="connection-point center"
        :class="{ 'active': activePoint === 'center' }"
        @mousedown.stop="startConnection('center')"
        @mouseenter="highlightPoint('center')"
        @mouseleave="unhighlightPoint"
      ></div>
      
      <!-- 顶部左角连接点 -->
      <div 
        class="connection-point top-left"
        :class="{ 'active': activePoint === 'top-left' }"
        @mousedown.stop="startConnection('top-left')"
        @mouseenter="highlightPoint('top-left')"
        @mouseleave="unhighlightPoint"
      ></div>
      
      <!-- 顶部右角连接点 -->
      <div 
        class="connection-point top-right"
        :class="{ 'active': activePoint === 'top-right' }"
        @mousedown.stop="startConnection('top-right')"
        @mouseenter="highlightPoint('top-right')"
        @mouseleave="unhighlightPoint"
      ></div>
      
      <!-- 底部左角连接点 -->
      <div 
        class="connection-point bottom-left"
        :class="{ 'active': activePoint === 'bottom-left' }"
        @mousedown.stop="startConnection('bottom-left')"
        @mouseenter="highlightPoint('bottom-left')"
        @mouseleave="unhighlightPoint"
      ></div>
      
      <!-- 底部右角连接点 -->
      <div 
        class="connection-point bottom-right"
        :class="{ 'active': activePoint === 'bottom-right' }"
        @mousedown.stop="startConnection('bottom-right')"
        @mouseenter="highlightPoint('bottom-right')"
        @mouseleave="unhighlightPoint"
      ></div>
    </template>
    
    <!-- 选中时的调整手柄 -->
    <template v-if="selected && editing && !isConnecting">
      <div class="resize-handle nw" @mousedown.stop="startResize('nw')"></div>
      <div class="resize-handle ne" @mousedown.stop="startResize('ne')"></div>
      <div class="resize-handle sw" @mousedown.stop="startResize('sw')"></div>
      <div class="resize-handle se" @mousedown.stop="startResize('se')"></div>
      <div class="resize-handle n" @mousedown.stop="startResize('n')"></div>
      <div class="resize-handle s" @mousedown.stop="startResize('s')"></div>
      <div class="resize-handle w" @mousedown.stop="startResize('w')"></div>
      <div class="resize-handle e" @mousedown.stop="startResize('e')"></div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, inject } from 'vue'
import type { ConfigurationComponent } from '../types'

// 组件映射
const componentMap = {
  text: () => import('./components/TextComponent.vue'),
  button: () => import('./components/ButtonComponent.vue'),
  image: () => import('./components/ImageComponent.vue'),
  shape: () => import('./components/ShapeComponent.vue'),
  gauge: () => import('./components/GaugeComponent.vue'),
  chart: () => import('./components/ChartComponent.vue'),
  model3d: () => import('./components/Model3dComponent.vue')
}

const props = defineProps<{
  component: ConfigurationComponent
  selected?: boolean
  editing?: boolean
}>()

const emit = defineEmits<{
  select: [id: string, multiple?: boolean]
  update: [component: ConfigurationComponent]
  startConnection: [componentId: string, anchor: string]
  endConnection: [componentId: string, anchor: string]
}>()

// 注入编辑器状态
const editorState = inject('editorState', ref({ connectionMode: false }))

// 当前高亮的连接点
const activePoint = ref<string | null>(null)

// 是否正在连接
const isConnecting = computed(() => editorState.value.connectionMode)

// 是否显示连接点
const showConnectionPoints = computed(() => {
  return editorState.value.connectionMode || props.selected
})

// 动态组件类型
const componentType = computed(() => {
  const type = props.component.type
  return componentMap[type as keyof typeof componentMap] || 'div'
})

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (!props.editing) return
  
  event.stopPropagation()
  const multiple = event.ctrlKey || event.metaKey
  emit('select', props.component.id, multiple)
}

// 处理鼠标按下事件
const handleMouseDown = (event: MouseEvent) => {
  if (!props.editing || props.component.locked) return
  
  event.preventDefault()
  
  // 开始拖拽
  const startX = event.clientX
  const startY = event.clientY
  const startComponentX = props.component.x
  const startComponentY = props.component.y
  
  const handleMouseMove = (e: MouseEvent) => {
    const deltaX = e.clientX - startX
    const deltaY = e.clientY - startY
    
    const updatedComponent = {
      ...props.component,
      x: startComponentX + deltaX,
      y: startComponentY + deltaY
    }
    
    emit('update', updatedComponent)
  }
  
  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 开始连接
const startConnection = (anchor: string) => {
  if (!isConnecting.value) return
  
  emit('startConnection', props.component.id, anchor)
}

// 高亮连接点
const highlightPoint = (point: string) => {
  if (isConnecting.value) {
    activePoint.value = point
  }
}

// 取消高亮连接点
const unhighlightPoint = () => {
  activePoint.value = null
}

// 开始调整大小
const startResize = (direction: string) => {
  // 调整大小逻辑
  console.log('开始调整大小:', direction)
}

// 处理组件更新
const handleUpdate = (updatedComponent: ConfigurationComponent) => {
  emit('update', updatedComponent)
}
</script>

<style scoped>
.component-renderer {
  border: 1px solid transparent;
  cursor: move;
  user-select: none;
}

.component-renderer.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 1px #409eff;
}

.component-renderer.locked {
  cursor: not-allowed;
}

.component-renderer.connecting {
  cursor: crosshair;
}

.component-content {
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.component-renderer.editing .component-content {
  pointer-events: auto;
}

/* 连接点样式 */
.connection-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #409eff;
  border: 2px solid #fff;
  border-radius: 50%;
  cursor: crosshair;
  opacity: 0;
  transition: all 0.2s;
  z-index: 1000;
}

.component-renderer:hover .connection-point,
.component-renderer.connecting .connection-point,
.component-renderer.selected .connection-point {
  opacity: 1;
}

.connection-point.active {
  background-color: #67c23a;
  transform: scale(1.2);
}

/* 连接点位置 */
.connection-point.top {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.connection-point.right {
  top: 50%;
  right: -6px;
  transform: translateY(-50%);
}

.connection-point.bottom {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.connection-point.left {
  top: 50%;
  left: -6px;
  transform: translateY(-50%);
}

.connection-point.center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.connection-point.top-left {
  top: -6px;
  left: -6px;
}

.connection-point.top-right {
  top: -6px;
  right: -6px;
}

.connection-point.bottom-left {
  bottom: -6px;
  left: -6px;
}

.connection-point.bottom-right {
  bottom: -6px;
  right: -6px;
}

/* 调整手柄样式 */
.resize-handle {
  position: absolute;
  background-color: #409eff;
  border: 1px solid #fff;
  width: 6px;
  height: 6px;
  z-index: 1001;
}

.resize-handle.nw {
  top: -3px;
  left: -3px;
  cursor: nw-resize;
}

.resize-handle.ne {
  top: -3px;
  right: -3px;
  cursor: ne-resize;
}

.resize-handle.sw {
  bottom: -3px;
  left: -3px;
  cursor: sw-resize;
}

.resize-handle.se {
  bottom: -3px;
  right: -3px;
  cursor: se-resize;
}

.resize-handle.n {
  top: -3px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

.resize-handle.s {
  bottom: -3px;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
}

.resize-handle.w {
  top: 50%;
  left: -3px;
  transform: translateY(-50%);
  cursor: w-resize;
}

.resize-handle.e {
  top: 50%;
  right: -3px;
  transform: translateY(-50%);
  cursor: e-resize;
}
</style>