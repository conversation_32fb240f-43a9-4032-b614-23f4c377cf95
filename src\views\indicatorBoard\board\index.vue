<template>
  <div class="app-container home">
    <!-- 导出按钮 -->

    <div v-for="(item, index) in titles" :key="index" class="a-container">
      <!-- 标题部分 -->
      <div class="export">
        <h2 class="title1">
          <span class="icon-title-container">
            <img src="@/assets/images/zx.png" alt="icon" class="title-icon" />
            {{ item.title }}
          </span>
        </h2>
        <el-button class="export-button" @click="handleExport(item)" type="primary">导出</el-button>
      </div>
      <el-row :gutter="20" class="b-container">
        <el-col v-for="(contentRow, idx) in item.contents.datas" :key="idx" :span="4" class="c-container">
          <div v-for="(section, sectionIndex) in contentRow" :key="sectionIndex" class="section with-spacing" @click="goEcharts(contentRow)">
            <div class="upper">
              <p class="title">{{ section.name }}</p>
              <div class="lower">
                <!-- <div class="left">{{ section.value === null ? '--' : section.value }}&nbsp;{{ section.unit ? section.unit : '' }}</div> -->
                <div class="left">
                  <span style="color: rgba(0, 160, 233, 1);font-size: 22px;font-weight: 700;">{{ section.value === null
                    ? '--' : section.value }}</span
                  >&nbsp;<span style="font-size: 14px;">{{ section.unit ? section.unit : '' }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <EmptyDataPage :imageSrc="emptyImagePath" v-if="titles.length === 0" />
    <el-dialog v-model="exportDialogVisible" title="数据导出" width="430" align-center :before-close="handleClose">
      <span class="spanTitle">请选择点位:</span>
      <div>
        <el-select
          v-model="selectedTreeOptionsvalue"
          multiple
          clearable
          collapse-tags
          placeholder="请选择需要导出的点位"
          popper-class="custom-header"
          :max-collapse-tags="1"
          style="width: 240px"
        >
          <template #header>
            <el-checkbox v-model="checkAll" :indeterminate="indeterminate" @change="handleCheckAll"> 全选 </el-checkbox>
          </template>
          <el-option v-for="item in selectedTreeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="block">
          <span class="spanTitle">请选择时间:</span>
          <el-date-picker
            v-model="datePicker"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            date-format="YYYY/MM/DD ddd"
            time-format="A hh:mm:ss"
            :disabled-date="disabledDate"
            :picker-options="pickerOptions"
            @change="handleDateChange"
          />
        </div>
        <div class="leixing">
          <span class="spanTitle">请选择时间粒度:</span>
          <el-row :gutter="20">
            <!-- 输入框部分 -->
            <el-col :span="12">
              <el-input v-model="inputValue" placeholder="请输入数字" />
            </el-col>

            <!-- el-select 下拉选择部分 -->
            <el-col :span="12">
              <el-select v-model="selectedUnit" placeholder="选择时间粒度" @change="handleSelectChange" style="width: 100%;">
                <el-option label="分" value="m" />
                <el-option label="时" value="h" />
                <el-option label="天" value="d" />
              </el-select>
            </el-col>
          </el-row>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseExport">取消</el-button>
          <el-button type="primary" @click="confirmExport"> 下载 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import type { CheckboxValueType } from 'element-plus'
import emptyImagePath from '@/assets/images/noData.png'
import EmptyDataPage from '@/components/emptyDataPage/index.vue'
import { powerPointConfiglable, pointData } from './index'
import { exportPoint } from '@/api/exportPoint/exportPoint'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import WebSocketService from '@/utils/socket_service'
import emitter from '@/utils/eventBus.js'
import { formatDate } from '@/utils/formatTime'
import { ElMessageBox, ElMessage } from 'element-plus'
// import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import { ref } from 'vue'

const router = useRouter()
const exportDialogVisible = ref(false)
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
emitter.on('projectListChanged', (e) => {
  location.reload()
})
const PointConfiglableData = ref([])
const titles = ref([])

const currentItem = ref(null) // 当前处理的 item


const getpowerPointConfiglable = async () => {
  const parms = {
    projectId: cachedProjects.id,
    configType: 4,
  }

  try {
    const res = await powerPointConfiglable(parms)
    if (res.data.length > 0) {
      PointConfiglableData.value = res.data.map((i) => ({
        id: i.id,
        name: i.name,
      }))

      // 使用 Promise.all 调用所有的 pointData 请求
      const pointDataPromises = PointConfiglableData.value.map((item) => {
        return pointData(item.id)
      })

      const pointDataResults = await Promise.all(pointDataPromises)
      titles.value = pointDataResults.map((result, index) => ({
        // 存入 titles 中
        id: PointConfiglableData.value[index].id,
        title: PointConfiglableData.value[index].name,
        contents: result.data,
      }))
    } else {
      titles.value = []
    }
  } catch (error) {
    console.error('Error fetching data:', error)
    titles.value = []
  }
}
const selectedTreeOptions = ref([])
const checkAll = ref(false)
const indeterminate = ref(false)
const selectedTreeOptionsvalue = ref<CheckboxValueType[]>([])
// 当前时间（
const currentDate = new Date()

// 计算当前时间之前三个月的日期
const threeMonthsAgo = new Date(currentDate)
threeMonthsAgo.setMonth(currentDate.getMonth() - 3)

// 初始化日期选择器的值
const datePicker = ref([threeMonthsAgo, currentDate])

// 限制日期选择：禁用大于当前时间的日期
const disabledDate = (date: Date) => {
  return date > currentDate // 禁用未来日期
}

// 设置最大时间范围选择（快捷选项） - 最近三个月
const pickerOptions = {
  shortcuts: [
    {
      text: '最近三个月',
      value: () => {
        return [threeMonthsAgo, currentDate]
      },
    },
  ],
}

watch(selectedTreeOptionsvalue, (val) => {
  if (val.length === 0) {
    checkAll.value = false
    indeterminate.value = false
  } else if (val.length === selectedTreeOptions.value.length) {
    checkAll.value = true
    indeterminate.value = false
  } else {
    indeterminate.value = true
  }
})
// 监听时间范围选择变化
const handleDateChange = (val: any) => {
  const [startDate, endDate] = val
  // 判断选择的日期区间是否大于3个月
  const diffInMonths = (endDate.getFullYear() - startDate.getFullYear()) * 12 + endDate.getMonth() - startDate.getMonth()
  if (diffInMonths > 3) {
    // 如果选择的区间超过3个月，弹出提示
    ElMessageBox.confirm('选择的时间范围不能超过3个月')
    // 可以将日期选择器的值重置为一个合理的时间范围（比如三个月前至当前时间）
    datePicker.value = [threeMonthsAgo, currentDate]
  }
}

const inputValue = ref('1') // 输入框的值
const selectedUnit = ref('h') // 默认选择 '时'

const handleSelectChange = (value: string) => {
  // console.log(`Selected time unit: ${value}`)
}

const handleExport = (item) => {
  const currentDate = new Date()
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(currentDate.getDate() - 7) // 设置为一周前

  datePicker.value = [oneWeekAgo, currentDate]
  // 打开导出对话框
  exportDialogVisible.value = true
  let valueCounter = 1 // 用于自增长的 value

  // 2. 遍历 item.datas，将每一项转化为目标格式并自增长 value
  const transformedData = item.contents.datas.flatMap((group) =>
    group.map((data) => ({
      value: valueCounter++, // 自增长的 value
      identifier: data.identifier,
      label: data.name,
      deviceId: data.deviceId,
    }))
  )

  selectedTreeOptions.value = transformedData
}


const handleClose = (done: () => void) => {
  ElMessageBox.confirm('您确定要关闭对话框吗？')
    .then(() => {
      done()
      exportDialogVisible.value = false
      inputValue.value = ''
      // Add these lines to clear selected points
      selectedTreeOptionsvalue.value = []
      checkAll.value = false
      indeterminate.value = false
    })
    .catch(() => {
      // catch error
    })
}
const handleCloseExport = () => {
  exportDialogVisible.value = false
  inputValue.value = ''
  // Add these lines to clear selected points
  selectedTreeOptionsvalue.value = []
  checkAll.value = false
  indeterminate.value = false
}

const confirmExport = () => {
  // 校验表单数据
  if (!datePicker.value || !selectedTreeOptionsvalue.value.length || !inputValue.value || !selectedUnit.value) {
    ElMessage.error('请确保所有字段都已填写')
    return
  }

  // 获取选中的数据点
  const selectedData = selectedTreeOptionsvalue.value.map((val) => {
    return selectedTreeOptions.value.find((option) => option.value === val)
  })

  // 构建请求参数
  const parms = {
    points: selectedData,
    startTime: formatDate(datePicker.value[0]),
    endTime: formatDate(datePicker.value[1]),
    interval: inputValue.value + selectedUnit.value,
  }

  // 调用 API 导出数据
  exportPoint(parms)
    .then((res) => {
      const link = document.createElement('a')
      let blob = new Blob([res], { type: 'application/vnd.ms-excel' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = '指标看板.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    })
    .catch((error) => {
      ElMessage.error('导出过程中发生错误')
      console.error(error)
    })

  // 关闭对话框和清空表单
  exportDialogVisible.value = false
  inputValue.value = ''
  selectedTreeOptionsvalue.value = [] // 清空选择的数据点
  checkAll.value = false // 重置全选框
  indeterminate.value = false // 重置半选状态
}
// 全选
const handleCheckAll = (val: boolean) => {
  indeterminate.value = false
  if (val) {
    // 全选时，将所有选项的 value 添加到 selectedTreeOptionsvalue
    selectedTreeOptionsvalue.value = selectedTreeOptions.value.map(item => item.value)
  } else {
    // 取消全选时，清空 selectedTreeOptionsvalue
    selectedTreeOptionsvalue.value = []
  }
}
// 获取机组的数据
const getUnitDataByItem = (unitItem) => {
  const data = []
  unitItem.contents.datas.forEach((dataRow) => {
    dataRow.forEach((section) => {
      data.push({
        name: `${unitItem.title}${section.name}`,
        value: section.value === null ? '--' : section.value,
        unit: section.unit || '',
      })
    })
  })
  return data
}

// 获取指标的数据
const getIndicatorDataBySection = (parentId, section) => {
  const titleItem = currentItem.value
  if (titleItem && titleItem.id === parentId) {
    return {
      name: `${titleItem.title}${section.name}`,
      value: section.value === null ? '--' : section.value,
      unit: section.unit || '',
    }
  }
  return null
}

// 导出数据到 Excel
// const exportToExcel = (data) => {
//   // 使用 XLSX 和 file-saver 库
//   const exportData = []
//   exportData.push(['名称', '值', '单位'])

//   data.forEach((item) => {
//     exportData.push([item.name, item.value, item.unit])
//   })

//   const worksheet = XLSX.utils.aoa_to_sheet(exportData)
//   const workbook = XLSX.utils.book_new()
//   XLSX.utils.book_append_sheet(workbook, worksheet, '导出数据')

//   const workbookBinary = XLSX.write(workbook, {
//     bookType: 'xlsx',
//     type: 'array',
//   })

//   saveAs(new Blob([workbookBinary], { type: 'application/octet-stream' }), '导出数据.xlsx')
// }

const goEcharts = (section) => {
  router.push({
    path: '/indicatorBoard/boardEcharts',
    state: {
      data: toRaw(section),
    },
  })
}

// websocket 接收数据并且进行处理
const handleMessage = (event: MessageEvent) => {
  let dataString = event.data
  let dataObject
  // console.log(dataString, '接收到的数据');

  // 检查数据类型
  if (typeof dataString === 'string') {
    // 修复可能存在的问题，使其成为有效的 JSON 字符串
    const fixedDataString = fixJsonString(dataString)
    try {
      dataObject = JSON.parse(fixedDataString)
    } catch (e) {
      console.error('无法将消息数据解析为JSON', e)
      return
    }
  } else if (typeof dataString === 'object') {
    dataObject = dataString
  } else {
    console.error('不支持的数据类型', typeof dataString)
    return
  }

  const isPropertyMessage = dataObject.MESSAGETYPE === 'property'
  const hasMessageContent = !!dataObject.MESSAGECONTENT
  const isIndicatorBoardType = dataObject.MESSAGECONTENT?.type === 'indicatorBoard'
  // console.log(titles.value, 'titles')

  const matchingTitle = titles.value.find((title) => Number(title.id) === Number(dataObject.MESSAGECONTENT?.deviceId))
  // console.log(matchingTitle, 'matchingTitle')
  if (isPropertyMessage && hasMessageContent && isIndicatorBoardType && matchingTitle) {
    const matchingId = matchingTitle.id // 获取 matchingTitle 的 id
    const newValues = dataObject.MESSAGECONTENT.boardDataList // 新值列表

    // 遍历 itlets.value，找到并更新匹配的机组数据
    titles.value.forEach((title) => {
      if (title.id === matchingId) {
        // console.log(title, 'title')
        // console.log(newValues, 'newValues')
        // 匹配机组 ID 后，更新对应的数据
        title.contents.datas.forEach((dataRow) => {
          dataRow.forEach((section) => {
            // 根据 identifier 匹配新值
            const newValueItem = newValues.flat().find((newItem) => newItem.identifier === section.identifier)

            if (newValueItem.value !== null && newValueItem.value !== section.value) {
              section.value = newValueItem.value
            }
          })
        })
      }
    })
  }
}

// 辅助函数：修复 JSON 字符串
function fixJsonString(str: string): string {
  return str
    .replace(/([{,]\s*)([A-Za-z0-9_]+)\s*:/g, '$1"$2":') // 给键添加双引号
    .replace(/'/g, '"') // 将单引号替换为双引号
}

onMounted(() => {
  const webSocketInstance = WebSocketService.getInstance()
  webSocketInstance.connect()
  webSocketInstance.ws?.addEventListener('message', handleMessage)
  getpowerPointConfiglable()
})

onBeforeUnmount(() => {
  // 获取 WebSocket 实例，并断开连接
  const webSocketInstance = WebSocketService.getInstance()
  webSocketInstance.ws?.removeEventListener('message', handleMessage)
  // webSocketInstance.disconnect()
})
</script>

<style scoped lang="scss">
.custom-header {
  .el-checkbox {
    display: flex;
    height: unset;
  }
}
.spanTitle {
  color: #ccc;
}

.leixing {
  margin-top: 10px;
}

.block {
  margin-top: 10px;
}

.home {
  height: 100%;
}

.a-container {
  width: 100%;
  margin-bottom: 40px;
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  padding: 10px 48px;
}

.export {
  width: 100%;
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title1 {
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  color: rgba(255, 255, 255, 1);
  margin-bottom: 20px;
}

.icon-title-container {
  display: flex;
  align-items: center;
}

.icon-title-container .el-icon,
.title-icon {
  margin-right: 8px;
  font-size: 12px;
}

.b-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.c-container {
  margin-bottom: 20px;
  background: rgb(18 30 55 / 50%);
  // rgba(12, 30, 66, 0.5);
  // rgba(12, 30, 66, 0.5)
  border: 1px solid #ccc;
  border: 1px solid rgba(30, 138, 237, 1);
  flex: 1 1 calc(16.6667% - 20px);
  max-width: calc(16.6667% - 20px);
}

.section {
  padding: 10px;
}

.upper .title {
  font-weight: 400;
  margin-bottom: 0px;
  text-align: left;
  color: rgba(204, 204, 204, 1);
  font-size: 14px;
}

.lower {
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  line-height: 23.87px;
  color: rgba(255, 255, 255, 1);
}

.lower {
  display: flex;
  justify-content: space-between;
}

.left,
.right {
  width: 100%;
}
:deep(.el-select__wrapper){
  background: rgb(3, 43, 82) !important;
  box-shadow:none !important;
  border: 1px solid #034374 !important;
}
</style>
<style lang="scss">

// @import '@/assets/styles/ctable.scss';
/* 全局样式 */
@import '@/assets/styles/datapicker.scss';
/* 全局样式 */
</style>
