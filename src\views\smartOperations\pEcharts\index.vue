<template>
  <div class="home">
    <!-- 切换 -->
    <div class="content">
      <div class="tbas">
        <el-tabs v-model="activeName" type="border-card" class="demo-tabs" @tab-click="handleClick">
          <!-- 动态生成的标签页 -->
          <el-tab-pane v-for="tab in tabs" :key="tab.deviceId" :label="tab.productName" :name="tab.deviceId">
            <div class="export">
              <el-button class="export-button" @click="handleExport()" type="primary">导出</el-button>
            </div>
            <!-- 内容区域 -->
            <div class="content-data">
              <!-- 一行最多展示8个盒子 -->
              <el-row v-if="contentData.length > 0" :gutter="20">
                <el-col v-for="(item, index) in contentData" :key="index" :xs="24" :sm="12" :md="8" :lg="4" @click="getEcharts(item)">
                  <div class="content-box">
                    <p class="name">{{ item.name ?? "--" }}</p>
                    <div class="value-unit">
                      <p class="value">{{ item.value ?? "--" }}</p>
                      <p class="unit">{{ item.unit ?? "--" }}</p>
                      <!-- <p class="unit">{{ item.value != null ? item.unit : '' }}</p> -->
                    </div>
                  </div>
                </el-col>
              </el-row>
              <!-- 显示没有数据的提示 -->
              <div class="box-img" v-if="contentData.length===0">
                <img src="@/assets/images/noData.png" />
                <p class="box-text">暂无数据</p>
                <!-- <p class="box-text1">暂无数据</p> -->
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <!-- 图表区域 -->
    <div class="content-echarts">
      <div class="tubiao" v-if="steamChartNoData">
        <img src="@/assets/images/noData.png" />
        <p class="box-text">暂无数据</p>
      </div>
      <div v-else>
        <div class="block">
          <el-date-picker
            v-model="pickerVal"
            type="datetimerange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            date-format="YYYY/MM/DD ddd"
            time-format="A hh:mm:ss"
            :disabled-date="disabledDate"
            @change="selectpicker"
            class="customdatapicker"
            size="large"
          />
        </div>
        <div style="height: 100%; width: 100%;">
          <PublicCharts :chartOptions="steamChartOptions" :data="steamData" />
        </div>
      </div>
    </div>

    <!-- 导出 -->
    <el-dialog v-model="exportDialogVisible" title="数据导出" width="430" align-center :before-close="handleClose">
      <span class="spanTitle">请选择点位:</span>
      <div>
        <el-select
          v-model="selectedTreeOptionsvalue"
          multiple
          clearable
          collapse-tags
          placeholder="请选择需要导出的点位"
          popper-class="custom-header"
          :max-collapse-tags="1"
          style="width: 240px"
        >
          <template #header>
            <el-checkbox v-model="checkAll" :indeterminate="indeterminate" @change="handleCheckAll"> 全选 </el-checkbox>
          </template>
          <el-option v-for="item in selectedTreeOptions" :key="item.identifier" :label="item.name" :value="item.identifier" />
        </el-select>
        <div class="block1">
          <span class="spanTitle">请选择时间:</span>
          <el-date-picker
            v-model="datePicker"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            date-format="YYYY/MM/DD ddd"
            time-format="A hh:mm:ss"
            :disabled-date="disabledDates"
            :picker-options="pickerOptions"
            @change="handleDateChange"
          />
        </div>
        <div class="leixing">
          <span class="spanTitle">请选择时间粒度:</span>
          <el-row :gutter="20">
            <!-- 输入框部分 -->
            <el-col :span="12">
              <el-input v-model="inputValue" placeholder="请输入数字" />
            </el-col>

            <!-- el-select 下拉选择部分 -->
            <el-col :span="12">
              <el-select v-model="selectedUnit" placeholder="选择时间粒度" @change="handleSelectChange" style="width: 100%;">
                <el-option label="分" value="m" />
                <el-option label="时" value="h" />
                <el-option label="天" value="d" />
              </el-select>
            </el-col>
          </el-row>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseExport">取消</el-button>
          <el-button type="primary" @click="confirmExport"> 下载 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts/core'
import { TooltipComponent, LegendComponent, TitleComponent, ToolboxComponent, GridComponent } from 'echarts/components'
import { PieChart, LineChart, GaugeChart } from 'echarts/charts'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import PublicCharts from '@/components/publicCharts/index.vue'
import { getTabsList, getDataList, getechartsData } from './index.api'
import type { TabsPaneContext } from 'element-plus'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import { ref, onMounted } from 'vue'
import { formatDate } from '@/utils/formatTime'
import emitter from '@/utils/eventBus.js'
import WebSocketService from '@/utils/socket_service'
import { exportPoint } from '@/api/exportPoint/exportPoint'
import type { CheckboxValueType } from 'element-plus'
echarts.use([
  TooltipComponent,
  LegendComponent,
  PieChart,
  CanvasRenderer,
  LabelLayout,
  TitleComponent,
  ToolboxComponent,
  GridComponent,
  LineChart,
  UniversalTransition,
  GaugeChart,
])
emitter.on('projectListChanged', (e) => {
  location.reload()
})
const pickerVal = ref<[Date, Date]>([new Date(new Date().setDate(new Date().getDate() - 7)), new Date()])
const disabledDate = (time: Date) => {
  const oneYearAgo = new Date()
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)
  return time.getTime() > Date.now() || time.getTime() < oneYearAgo.getTime()
}
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const activeName = ref()
const tabs = ref([])
const contentData = ref([])
const steamData = ref([]) // 存储steam图表的数据

// 导出
const selectedTreeOptions = ref([])
const checkAll = ref(false)
const indeterminate = ref(false)
const selectedTreeOptionsvalue = ref<CheckboxValueType[]>([])
const inputValue = ref('1') // 输入框的值
const selectedUnit = ref('h') // 默认选择 '时'
// 当前时间（
const currentDate = new Date()

// 计算当前时间之前三个月的日期
const threeMonthsAgo = new Date(currentDate)
threeMonthsAgo.setMonth(currentDate.getMonth() - 3)

// 初始化日期选择器的值
const datePicker = ref([threeMonthsAgo, currentDate])
const exportDialogVisible = ref(false)
const handleExport = () => {
  exportDialogVisible.value = true
  const currentDate = new Date()
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(currentDate.getDate() - 7) // 设置为一周前

  datePicker.value = [oneWeekAgo, currentDate]

  selectedTreeOptions.value = contentData.value
}
const handleClose = (done: () => void) => {
  ElMessageBox.confirm('您确定要关闭对话框吗？')
    .then(() => {
      done()
      exportDialogVisible.value = false
      inputValue.value = ''
      // Add these lines to clear selected points
      selectedTreeOptionsvalue.value = []
      checkAll.value = false
      indeterminate.value = false
    })
    .catch(() => {
      // catch error
    })
}
// 限制日期选择：禁用大于当前时间的日期
const disabledDates = (date: Date) => {
  return date > currentDate // 禁用未来日期
}

// 设置最大时间范围选择（快捷选项） - 最近三个月
const pickerOptions = {
  shortcuts: [
    {
      text: '最近三个月',
      value: () => {
        return [threeMonthsAgo, currentDate]
      },
    },
  ],
}
watch(selectedTreeOptionsvalue, (val) => {
  if (val.length === 0) {
    checkAll.value = false
    indeterminate.value = false
  } else if (val.length === selectedTreeOptions.value.length) {
    checkAll.value = true
    indeterminate.value = false
  } else {
    indeterminate.value = true
  }
})
// 监听时间范围选择变化
const handleDateChange = (val: any) => {
  const [startDate, endDate] = val
  // 判断选择的日期区间是否大于3个月
  const diffInMonths = (endDate.getFullYear() - startDate.getFullYear()) * 12 + endDate.getMonth() - startDate.getMonth()
  if (diffInMonths > 3) {
    // 如果选择的区间超过3个月，弹出提示
    ElMessageBox.confirm('选择的时间范围不能超过3个月')
    // 可以将日期选择器的值重置为一个合理的时间范围（比如三个月前至当前时间）
    datePicker.value = [threeMonthsAgo, currentDate]
  }
}
const handleSelectChange = (value: string) => {
}
const handleCloseExport = () => {
  exportDialogVisible.value = false
  inputValue.value = ''
  // Add these lines to clear selected points
  selectedTreeOptionsvalue.value = []
  checkAll.value = false
  indeterminate.value = false
}
const confirmExport = () => {
  // 校验表单数据
  if (!datePicker.value || !selectedTreeOptionsvalue.value.length || !inputValue.value || !selectedUnit.value) {
    ElMessage.error('请确保所有字段都已填写')
    return
  }

  // 获取选中的数据点
  const selectedData = selectedTreeOptionsvalue.value.map((val) => {
    return selectedTreeOptions.value.find((option) => option.identifier === val)
  })

  // 构建请求参数
  const parms = {
    points: selectedData,
    startTime: formatDate(datePicker.value[0]),
    endTime: formatDate(datePicker.value[1]),
    interval: inputValue.value + selectedUnit.value,
  }
  const findProductName = (tabs, activeName) => {
    const item = tabs.value.find((tab) => tab.deviceId === activeName)
    return item ? item.productName : '数据监控数据' // 如果匹配到则返回productName，否则返回null
  }
  const productName = findProductName(tabs, activeName.value)
  // const ExcelName=tabs.value
  // 调用 API 导出数据
  exportPoint(parms)
    .then((res) => {
      const link = document.createElement('a')
      let blob = new Blob([res], { type: 'application/vnd.ms-excel' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      // `${activeName.value}.xlsx`
      link.download = `${productName}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    })
    .catch((error) => {
      ElMessage.error('导出过程中发生错误')
      console.error(error)
    })

  // 关闭对话框和清空表单
  exportDialogVisible.value = false
  inputValue.value = ''
  selectedTreeOptionsvalue.value = [] // 清空选择的数据点
  checkAll.value = false // 重置全选框
  indeterminate.value = false // 重置半选状态
}
// 全选
const handleCheckAll = (val: boolean) => {
  indeterminate.value = false
  if (val) {
    // 全选时，将所有选项的 identifier 添加到 selectedTreeOptionsvalue
    selectedTreeOptionsvalue.value = selectedTreeOptions.value.map(item => item.identifier)
  } else {
    // 取消全选时，清空 selectedTreeOptionsvalue
    selectedTreeOptionsvalue.value = []
  }
}

const handleClick = (tab: TabsPaneContext, event: Event) => {
  const tabName = tab.props?.name
  activeName.value = tab.props?.name
  contentData.value = [] // 切换标签页时清空数据
  if (tabName) {
    getDataForTab(tabName)
  }
}
const getCurrentTab = () => {
  getDataForTab(activeName.value)
}

// 时间选择
const selectpicker = (value: string) => {
  const start = formatDate(value[0])
  const end = formatDate(value[1])
  pickerVal.value = [start, end]
  // getEchartsData() // 当选择日期时更新图表数据
  if (activeName.value) {
    // 获取图表数据
    const item = contentData.value[0] // 获取当前激活设备的数据
    if (item) {
      getEcharts(item) // 更新图表数据
    }
  }
}

const tabsList = () => {
  getTabsList(cachedProjects.id).then((res) => {
    // 过滤掉 productName 为 "opc" 的数据
    const filteredData = res.data.filter((item) => item.productName !== 'opc' && item.productName !== 'dcs（所有）')
    tabs.value = filteredData
    if (tabs.value.length > 0) {
      // 默认选择第一个标签页
      activeName.value = tabs.value[0].deviceId
      // 获取默认选择的第一个标签页的数据
      getDataForTab(activeName.value)
    }
  })
}
const steamChartNoData = ref(false)
// 获取指定标签页的数据
const getDataForTab = (deviceId: string) => {
  const parms = {
    deviceId: deviceId,
    pointType: 0,
  }
  getDataList(parms)
    .then((res) => {
      if (res.data.datas.length > 0) {
        contentData.value = res.data.datas
        // getEcharts(contentData.value[0])
      } else {
        contentData.value = []
        steamChartNoData.value = true
      }
    })
    .catch(() => {
      contentData.value = []
      steamChartNoData.value = true
    })
}

const getEcharts = (item) => {
  const [startDate, endDate] = pickerVal.value
  if (!startDate || !endDate) {
    return
  }

  const start = formatDate(startDate)
  const end = formatDate(endDate)
  // console.log(item)
  const parms = {
    displayGrowth: true,
    startTime: start,
    endTime: end,
    deviceId: item.deviceId,
    displayStats: true,
    identifier: item.identifier,
  }
  getechartsData(parms)
    .then((res) => {
      if (res.data.his && res.data.his.length > 0) {
        updateChartOptions(res.data) // 更新图表选项
      } else {
        steamChartOptions.value = {} // 清空图表选项
        steamChartNoData.value = true
      }
    })
    .catch(() => {
      steamChartOptions.value = {}
      steamChartNoData.value = true
    })
}

// 更新图表配置
const updateChartOptions = (data) => {
  // console.log(data.name, 'titole')
  if (!data || !Array.isArray(data.his)) {
    return
  }
  const times = data.his.map((item) => item.time)
  const values = data.his.map((item) => (item.value === null ? '' : item.value))
  const tbValues = data.his.map((item) => (item.tbValue === null ? '' : item.tbValue))
  const hbValues = data.his.map((item) => (item.hbValue === null ? '' : item.hbValue))
  steamChartOptions.value = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        let tooltipText = params[0].name + '<br/>'
        params.forEach((param) => {
          tooltipText += `${param.seriesName} : ${param.value} ${data.unit ? data.unit : ''}<br/>`
        })
        return tooltipText
      },
      position: function (pt: any) {
        return [pt[0], '10%']
      },
      backgroundColor: '#142433',
      textStyle: {
        color: '#fff', // 修改字体颜色
        fontSize: 14, // 可以同时修改字体大小等其他属性
      },
    },
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: 'none',
        },
        saveAsImage: {},
      },
    },
    legend: {
      data: [data.name, '同比数据', '环比数据'], // 添加图例的名称
      top: 'top', // 设置图例的位置，可以根据需要调整位置
      textStyle: {
        fontSize: 14,
        color: 'rgba(204, 204, 204, 1)',
        fontWeight: 400,
      },
    },
    xAxis: {
      type: 'category',
      data: times,
      axisLabel: {
        formatter: (value) => {
          const date = new Date(value)
          const month = (date.getMonth() + 1).toString().padStart(2, '0')
          const day = date.getDate().toString().padStart(2, '0')
          const hours = date.getHours().toString().padStart(2, '0')
          const minutes = date.getMinutes().toString().padStart(2, '0')
          const seconds = date.getSeconds().toString().padStart(2, '0')
          return `${month}-${day} ${hours}:${minutes}:${seconds}`
        },
        lineStyle: {
          color: 'rgba(62, 65, 77, 1)', // 修改 x 轴刻度线的颜色
        },
      },
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: 'rgba(204, 204, 204, 1)', // 修改 x 轴轴线的颜色
          width: 1,
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: `{value} ${data.unit ? data.unit : ''}`,
        color: 'rgba(204, 204, 204, 1)',
        fontSize: 14,
      },
      boundaryGap: [0, '100%'],
      splitLine: {
          show: true,
          lineStyle: { type: 'dashed', color: '#c6c7c8' },
        },
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
      },
      {
        start: 0,
        end: 100,
      },
    ],
    series: [
      {
        name: data.name,
        type: 'line',
        data: values,
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(33, 148, 255, 0.4)' }, // 对应渐变的起点颜色
            { offset: 1, color: 'rgba(33, 148, 255, 0.04)' }, // 对应渐变的终点颜色
          ]),
        },
      },
      {
        name: '同比数据',
        type: 'line',
        data: tbValues,
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(143, 198, 114, 0.4)' }, // 对应渐变的起点颜色
            { offset: 1, color: 'rgba(143, 198, 114, 0.04)' }, // 对应渐变的终点颜色
          ]),
        },
      },
      {
        name: '环比数据',
        type: 'line',
        data: hbValues,
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(244, 194, 85, 0.4)' }, // 对应渐变的起点颜色
            { offset: 1, color: 'rgba(244, 194, 85, 0.04)' }, // 对应渐变的终点颜色
          ]),
        },
      },
    ],
  }
  steamData.value = values // 更新steamData数据
}

const steamChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    formatter: '{b0}: {c0}',
    position: function (pt: any) {
      return [pt[0], '10%']
    },
    backgroundColor: '#142433',
    textStyle: {
      color: '#fff', // 修改字体颜色
      fontSize: 14, // 可以同时修改字体大小等其他属性
    },
  },
  toolbox: {
    feature: {
      dataZoom: {
        yAxisIndex: 'none',
      },
      saveAsImage: {},
    },
  },
  legend: {
    data: [],
    top: 'top', // 设置图例的位置，可以根据需要调整位置
    textStyle: {
      fontSize: 14,
      color: 'rgba(204, 204, 204, 1)',
      fontWeight: 400,
    },
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [],
    axisLine: {
      lineStyle: {
        color: 'rgba(204, 204, 204, 1)', // 修改 x 轴轴线的颜色
        width: 1,
      },
    },
    axisLabel: {
      lineStyle: {
        color: 'rgba(62, 65, 77, 1)', // 修改 x 轴刻度线的颜色
      },
    },
  },
  yAxis: {
    type: 'value',
    boundaryGap: [0, '100%'],
    splitLine: {
      lineStyle: {
        color: 'rgba(62, 65, 77, 1)', // 修改与 x 轴平行的网格线颜色
      },
    },
    axisLabel: {
      color: 'rgba(204, 204, 204, 1)',
      fontSize: 14,
    },
  },
  dataZoom: [
    {
      type: 'inside',
      start: 0,
      end: 100,
    },
    {
      start: 0,
      end: 100,
    },
  ],
  series: [
    {
      name: '暂无数据',
      type: 'line',
      symbol: 'circle',
      sampling: 'lttb',
      symbolSize: 12,
      itemStyle: {
        color: 'rgb(255, 70, 131)',
      },
      data: [],
    },
  ],
})
// 暂时使用定时器代替
const timer = ref(null)

const createTimer = () => {
  // 先清除已有的定时器
  if (timer.value) {
    clearInterval(timer.value)
  }
  // 重新生成新的定时器
  timer.value = setInterval(() => {
    getCurrentTab()
    // 时间到了清除定时器并重新生成一个新的定时器
    clearInterval(timer.value)
    createTimer() // 重新创建定时器
  }, 1000) // 1分钟
}

const handleMessage = (event: MessageEvent) => {
  let dataString = event.data
  // console.log("页面接收到消息:", dataString)
  // console.log("当前所在标签页:", activeName.value)
  let dataObject

  // 检查数据类型
  if (typeof dataString === 'string') {
    // 修复可能存在的问题，使其成为有效的 JSON 字符串
    const fixedDataString = fixJsonString(dataString)
    try {
      dataObject = JSON.parse(fixedDataString)
    } catch (e) {
      console.error('无法将消息数据解析为JSON', e)
      return
    }
  } else if (typeof dataString === 'object') {
    dataObject = dataString
  } else {
    console.error('不支持的数据类型', typeof dataString)
    return
  }

  // 检查消息类型并处理 "waterPumb" 类型的消息
  if (
    dataObject.MESSAGETYPE === 'property' &&
    dataObject.MESSAGECONTENT &&
    dataObject.MESSAGECONTENT.type === 'monitor' &&
    dataObject.MESSAGECONTENT.deviceId === activeName.value
  ) {
    contentData.value = dataObject.MESSAGECONTENT.monitorDataList
  }
}
function fixJsonString(str: string): string {
  return str
    .replace(/([{,]\s*)([A-Za-z0-9_]+)\s*:/g, '$1"$2":') // 给键添加双引号
    .replace(/'/g, '"') // 将单引号替换为双引号
}
onMounted(() => {
  tabsList() // 组件挂载时调用
  const webSocketInstance = WebSocketService.getInstance()
  webSocketInstance.connect()
  webSocketInstance.ws?.addEventListener('message', handleMessage)

  // createTimer() // 创建第一个定时器
})
onBeforeUnmount(() => {
  // 获取 WebSocket 实例，并断开连接
  const webSocketInstance = WebSocketService.getInstance()
  webSocketInstance.ws?.removeEventListener('message', handleMessage)
})
onUnmounted(() => {
  // 清除定时器，避免内存泄漏
  if (timer.value !== null) {
    clearInterval(timer.value)
  }
})
</script>
<style lang="scss">
// @import '@/assets/styles/ctable.scss';
/* 全局样式 */
@import '@/assets/styles/datapicker.scss';
/* 全局样式 */
</style>
<style scoped lang="scss">
.export {
  width: 100%;
  margin-bottom: 5px;
  display: flex;
  flex-direction: row-reverse;
}
.export-button {
}
.box-text {
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0px;
  line-height: 18.56px;
  color: rgba(255, 255, 255, 1);
}
.tubiao {
  text-align: center;
  height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
// &:not(:last-child)::after {
//         content: '';
//         position: absolute;
//         right: -5px;
//         top: 50%;
//         transform: translateY(-50%);
//         width: 2px;
//         height: 80px;
//         background: rgba(32, 73, 92, 1);
//       }
.home {
  background: rgba(2, 28, 51, 0.5);
  .box-img {
    text-align: center;
  }
}

.tbas {
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  // padding: 20px;
}
// tabs
:deep(.el-tabs__item.is-active) {
  /* background-color: #d50c0c; */
}
:deep(.el-tabs__item:hover) {
  color: #fff; /* 悬停时的文字颜色 -------------*/
}
:deep(.el-tabs__item) {
  /* opacity: 0; */
  /* background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important; */
}
:deep(.el-tabs--border-card) {
  background: rgba(2, 28, 51, 0.5);
  /* padding-bottom: 140px; */
}
:deep(.el-tabs--border-card > .el-tabs__header) {
  background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important;
  border-bottom: 1px solid rgba(33, 148, 255, 1);
}
:deep(.el-tabs--border-card) {
  border: none;
}
:deep(.el-select__wrapper){
  background: rgb(3, 43, 82) !important;
  box-shadow:none !important;
  border: 1px solid #034374 !important;
}
:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {
  border-radius: 2px;
  background: linear-gradient(180deg, rgb(6, 101, 243) 0%, rgba(119, 122, 126, 0.1) 100%);
  opacity: 0.8;
  color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(0, 0, 0, 1);
}
// ---------------
.content-data {
  width: 100%;
  flex-wrap: wrap;
  max-height: 350px;
  overflow-y: auto;
  gap: 20px;
  scrollbar-width: none; /* 对Firefox隐藏滚动条 */
  -ms-overflow-style: none; /* 对IE和Edge隐藏滚动条 */
  .el-col {
    position: relative; /* 添加 relative 定位 */
    justify-content: center;
    margin-bottom: 20px;
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: -5px;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 80px;
      z-index: 1;
      background: rgba(32, 73, 92, 1);
    }
  }
}

.content-data::-webkit-scrollbar {
  display: none; /* 对Chrome, Safari和Edge隐藏滚动条 */
}

.content-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  // border: 1px solid red;
  text-align: center;
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
  min-height: 90px;
  height: 100%; /* 保持盒子高度一致 */
}

.name {
  word-wrap: break-word;
  width: 100%;
  text-align: center;
  margin-bottom: 5px;
  color: rgba(204, 204, 204, 1);
  font-weight: 400;
  font-size: 16px;
}

.value-unit {
  display: flex;
  align-items: center; /* 确保整个容器内的内容垂直居中 */
  gap: 5px;
}

.value {
  font-size: 26px;
  font-weight: 500;
  line-height: 47.74px; /* 确保value的高度一致 */
  color: rgba(0, 160, 233, 1);
}

.unit {
  font-size: 16px;
  font-weight: 400;
  line-height: 1; /* 确保unit内容高度与其字体大小一致 */
  color: rgba(255, 255, 255, 1);
  align-self: center; /* 垂直居中对齐 */
}
.content-echarts {
  width: 100%;
  // height: 100%;
  margin-top: 10px;
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
}
p {
  margin: 0;
}

.no-data {
  text-align: center;
  width: 100%;
  padding: 20px;
  font-size: 16px;
  color: #888;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .content-box {
    min-height: 80px; /* 较小屏幕下的最小高度 */
  }
}

@media (max-width: 992px) {
  .content-box {
    min-height: 70px; /* 更小屏幕下的最小高度 */
  }
}

@media (max-width: 768px) {
  .content-box {
    min-height: 60px; /* 更小屏幕下的最小高度 */
  }
}

@media (max-width: 576px) {
  .content-box {
    min-height: 50px; /* 手机等小屏幕下的最小高度 */
  }
}

.content-echarts {
  margin-top: 10px;
  // border: 1px solid red;
}
.block {
  padding: 20px 0 0 20px;
}
.block1 {
  margin-top: 10px;
}
.custom-header {
  .el-checkbox {
    display: flex;
    height: unset;
  }
}
.spanTitle {
  color: #ccc;
}

.leixing {
  margin-top: 10px;
}
</style>
