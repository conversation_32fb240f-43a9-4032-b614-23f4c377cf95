<!--水质分析 -->
<template>
  <div class="p-2">
    <!-- 机组切换 Segmented -->
    <div class="unit-segmented" v-if="segOptions.length > 1">
      <el-segmented v-model="selectedUnit" :options="segOptions" />
    </div>

    <el-card shadow="never">
      <!-- 使用 tableData 作为表格数据 -->
      <el-table v-loading="state.loading" :data="tableData">
        <!-- 使用 header 对象的值作为 el-table-column 的 label -->
        <el-table-column prop="item" :label="header.item"></el-table-column>
        <el-table-column prop="unit" :label="header.unit"></el-table-column>
        <el-table-column prop="label1" :label="header.label1"></el-table-column>
        <el-table-column prop="label2" :label="header.label2"></el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, toRefs } from 'vue'

// 定义类型接口
interface WaterAnalysisItem {
  item: string
  unit: string | null
  label1: string | null
  label2: string | null
}

interface PowerUnit {
  powerUnitId: string
  powerUnitName: string
  items: WaterAnalysisItem[]
}

// 定义 props 并接收外部传入的 dialogData 数据
const props = defineProps<{
  dialogData: PowerUnit[]
}>()

const { dialogData } = toRefs(props)

// Segmented 当前选中的机组下标
const selectedUnit = ref(0)

// 生成 Segmented 选项： label 为机组名称、value 为索引
const segOptions = computed(() =>
  dialogData.value.map((item: PowerUnit, index: number) => ({
    label: item.powerUnitName,
    value: index
  }))
)

// 当前选中机组的数据
const selectedData = computed(() => {
  return dialogData.value?.[selectedUnit.value] || null
})

// computed 提取表头数据，即当前选中机组 items 数组的第一项
const header = computed(() => {
  return selectedData.value?.items?.[0] || {}
})

// computed 提取主体数据，排除掉当前选中机组 items 数组中的第一项（表头数据）
const tableData = computed(() => {
  return selectedData.value?.items?.length > 1 ? selectedData.value.items.slice(1) : []
})

// 定义状态，如加载状态等
const state = reactive({
  loading: false
})
</script>

<style lang="scss" scoped>
/* 机组切换样式 */
.unit-segmented {
  text-align: center;
  margin-bottom: 16px;
}

:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  border: none;
}

:deep(.el-card__body) {
  border: none;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border-bottom: 1px solid #00AAFF;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
  border-bottom: 1px solid #00AAFF;
  border-right: 1px solid #00AAFF;
}

:deep(.el-table td) {
  border-right: 1px solid #00AAFF;
  border-bottom: 1px solid #00AAFF;
}

:deep(.el-table) {
  --el-table-border-color: #00AAFF;
  border: 1px solid #00AAFF;
}

:deep(.el-table__cell) {
  // color: rgba(204, 204, 204, 1) !important;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}

:deep(.el-tree) {
  background-color: transparent;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #07355c;
}

:deep(.el-tree-node__expand-icon) {
  color: #fff;
}

:deep(.el-tree-node__label) {
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}

:deep(.el-select__tags .el-tag--info) {
  background-color: #153059 !important;
}

:deep(.el-tag.el-tag--info) {
  color: #fff !important;
}
:deep(.el-table__header thead tr th){
  background: #1F5499 !important;
}
:deep(.el-card){
  border: 2px solid #00AAFF !important;
}
</style>
