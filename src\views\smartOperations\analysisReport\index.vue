<template>
  <div class="report-container">
    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" v-if="tableData.length > 0">
      <el-tab-pane v-for="row in tableData" :key="row.id" :label="row.powerUnitNames || `分析报告${row.id}`" :name="String(row.id)">
        <div class="report">
          <div class="table-wrapper">
            <el-table style="width: 100%" :data="generateTableData(row.report)" stripe :span-method="arraySpanMethod" border>
              <!-- 第一层表头：指标 -->
              <el-table-column prop="target" label="指标" align="center" width="180" fixed="left" />
              <el-table-column align="center">
                <template #header>
                  <div class="table-header-with-refresh">
                    <span class="header-text">{{ `${row.title || '--'} ${row.updateTime || '--'}` }}</span>
                    <el-button size="small" circle class="refresh-btn" @click="handleRefreshSingleReport(row.powerUnitIds)" title="刷新数据">
                      <img src="@/assets/icons/svg/refresh.svg" alt="refresh" class="refresh-icon" />
                    </el-button>
                  </div>
                </template>
                <el-table-column prop="symbol" label="符号" align="center" />
                <el-table-column prop="unit" label="单位" align="center" />
                <template v-for="(item, itemIndex) in row.report.items" :key="`item-${itemIndex}`">
                  <el-table-column :label="item.title" align="center">
                    <template #header>
                      <span v-if="item.title">
                        <img
                          src="@/assets/icons/svg/best.svg"
                          alt="best"
                          style="width: 20px; height:20px; margin-right: 4px; vertical-align: middle;"
                        />
                        {{ item.title }}
                      </span>
                      <span v-else>--</span>
                    </template>
                    <template v-for="(member, memberIndex) in item.members" :key="`member-${memberIndex}`">
                      <el-table-column :label="member.current.powerUnitName" align="center">
                        <el-table-column :prop="`currentData_item${itemIndex}_member${memberIndex}`" label="当前值" align="center" />
                        <el-table-column :prop="`willData_item${itemIndex}_member${memberIndex}`" label="模拟值" align="center" />
                        <el-table-column :prop="`changeData_item${itemIndex}_member${memberIndex}`" label="变化值" align="center" />
                      </el-table-column>
                    </template>
                  </el-table-column>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 动态建议 -->
          <div class="conclusion">
            <div></div>
            <div class="line">
              <p class="conclusion-title">最优推演结果</p>
              <div class="conclusion-content1">
                <span>&nbsp;&nbsp;方案：{{ row.report.bestPlanTitle !== null ? `${row.report.bestPlanTitle} ` : '--' }}</span>
                <span
                  >&nbsp;&nbsp;理论提升发电量：{{ row.report.improvePower !== null ? `${row.report.improvePower} kW·h` : '--&nbsp;&nbsp;kW·h' }}</span
                >
                <span>
                  &nbsp;&nbsp;理论综合提升发电量：{{ row.report.realImprovePower !== null ? `${row.report.realImprovePower}
                  kW·h` : '--&nbsp;&nbsp;kW·h' }}
                </span>
                <span> &nbsp;&nbsp;最优换算系数：{{ row.report.bestValue !== null ? `${row.report.bestValue}` : '--' }} </span>
                <span> &nbsp;&nbsp;当前换算系数：{{ row.report.currentBestValue !== null ? `${row.report.currentBestValue}` : '--' }} </span>
              </div>
            </div>
            <div class="line">
              <p class="conclusion-title">预检查</p>
              <div class="conclusion-content1">
                <ul v-infinite-scroll="load" class="infinite-list" style="overflow: auto">
                  <li v-for="(i,index2) in row.preCheckList" :key="i" class="infinite-list-item">{{index2+1}}·{{ i }}</li>
                </ul>
              </div>
            </div>
            <div class="line">
              <p class="conclusion-title">推演过程</p>
              <div class="conclusion-content1">
                <ul v-infinite-scroll="load" class="infinite-list" style="overflow: auto">
                  <li v-for="(i, index1) in row.processList" :key="i.id" class="infinite-list-item">
                    {{ index1 + 1 }}·{{ i.value }}
                    <!-- Popover 触发图标 -->
                    <el-popover trigger="click" placement="top" width="300">
                      <!-- Popover 内容 -->
                      <div v-html="formatExplain(i.explain)" />
                      <!-- Popover 触发元素 -->
                      <template #reference>
                        <el-icon :size="16" color="#ebe5b5" class="popover-icon">
                          <QuestionFilled />
                        </el-icon>
                      </template>
                    </el-popover>
                  </li>
                </ul>
              </div>
            </div>
            <div class="line">
              <p class="conclusion-title">建议</p>
              <div class="conclusion-content1">
                <div class="recommendation-row" v-for="(recommendation, index) in formatRecommendations(row.conclusion)" :key="index">
                  <el-tooltip class="item" placement="top" :disabled="true">
                    <div class="recommendation-item">&nbsp;&nbsp;{{ index + 1 }}·{{ recommendation }}</div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>

          <!-- 备选方案 -->
          <div class="alternative-solutions">
            <candidate :candidate-data="candidateData" :total="candidateTotal" :report-id="activeTab" @get-data="handleCandidateGetData"></candidate>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 空数据状态 -->
    <EmptyDataPage :imageSrc="emptyImagePath" v-if="!tableData.length" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
// import { Refresh } from '@element-plus/icons-vue'
import { getSmartList, getSmartCandidateList, manuallyTriggereddata, downloadReportLog } from './index.api'
import { useLocalCache, CACHE_KEY } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
import emptyImagePath from '@/assets/images/noData.png'
import candidate from './candidate/index.vue'

// 定义需要合并的指标
const mergeTargets = ['增加常用电量', '理论综合提升发电量', '最优换算系数']

const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const drawerVisible = ref(false) //抽屉

// 添加activeTab状态
const activeTab = ref<string>('')
// 添加一个临时存储当前选中tab的变量
const tempActiveTab = ref<string>('')

// 定义 TableRow 接口
interface TableRow {
  id: number
  title: string | null
  updateTime: string | null
  report: {
    bestPlanTitle: string | null
    improvePower: number | null
    increaseCommonPower: number | null
    realImprovePower: number | null
    bestValue: number | null
    currentBestValue: number | null
    electricityChangeRate: any
    items: Array<{
      title: string | null
      improvePower: number
      increaseCommonPower: number
      realImprovePower: number
      bestValue: number
      electricityChangeRate: any
      members: Array<{
        current: any
        will: any
        improveUnitPressure: number | null
        improveEfficiency: number | null
        improvePower: number | null
      }>
    }>
  }
  preCheckList: string[]
  processList: Array<{ id: number; value: string; explain?: string }>
  conclusion: string
}

// 更新 tableData 的类型
const tableData = ref<TableRow[]>([])

// 静态内容
const staticData = [
  { target: '真空', symbol: '/', unit: 'kPa' },
  { target: '机组排汽温度', symbol: 'ts', unit: '℃' },
  { target: '对应背压值', symbol: 'BP', unit: 'kPa' },
  { target: '冷却水进口温度', symbol: 't1', unit: '℃' },
  { target: '冷却水出口温度', symbol: 't2', unit: '℃' },
  { target: '逼近度', symbol: 'ap', unit: '℃' },
  { target: '冷却水温升', symbol: 'Δt', unit: '℃' },
  { target: '端差', symbol: 'δt', unit: '℃' },
  { target: '提升发电效率', symbol: 'ΔP', unit: '%' },
  { target: '理论提升发电量', symbol: 'ΔPW', unit: 'kW·h' },
  { target: '增加常用电量', symbol: 'ΔPW', unit: 'kW·h' },
  { target: '理论综合提升发电量', symbol: 'ΔPW', unit: 'kW·h' },
  { target: '最优换算系数', symbol: '/', unit: '/' },
]

// 格式化 explain 字段
const formatExplain = (explain?: string) => {
  if (!explain) return ''
  return explain.replace(/\n/g, '<br/>')
}

const load = () => {}

// 监听项目列表变化
emitter.on('projectListChanged', () => {
  location.reload()
})

// 监听activeTab变化，用于调试
watch(activeTab, (newVal, oldVal) => {}, { immediate: true })

// 监听tableData变化，保持tab状态
watch(
  tableData,
  (newData, oldData) => {
    if (newData.length > 0) {
      // 如果有临时保存的activeTab且存在于新数据中，则使用它
      if (tempActiveTab.value) {
        const tempTab = tempActiveTab.value // 先保存到局部变量
        const exists = newData.some((row) => String(row.id) === tempTab)
        if (exists) {
          // 使用nextTick确保DOM更新完成后再设置
          nextTick(() => {
            activeTab.value = tempTab
            tempActiveTab.value = '' // 在这里清空临时存储
          })
        } else {
          nextTick(() => {
            activeTab.value = String(newData[0].id)
            tempActiveTab.value = '' // 在这里清空临时存储
          })
        }
      } else if (!activeTab.value) {
        // 首次加载时设置为第一个
        nextTick(() => {
          activeTab.value = String(newData[0].id)
        })
      }
    }
  },
  { immediate: true }
)

// Tab点击处理
const handleTabClick = (tab: any) => {
  // 尝试多种方式获取tab name
  const tabName = tab.name || tab.props?.name || tab.paneName
  if (tabName) {
    activeTab.value = tabName
  } else {
    console.error('无法从tab事件参数中获取name')
  }
}

// 格式化建议
const formatRecommendations = (conclusion: string) => {
  try {
    return JSON.parse(conclusion)
  } catch (e) {
    return []
  }
}

// 生成表格数据
const generateTableData = (report: TableRow['report']) => {
  if (!report || !Array.isArray(report.items)) {
    return [] // 如果 report 或 report.items 为 null，则返回空数组
  }

  const tableRows = staticData
    .filter((data) => !['总的提升发电量', '总增加常用电量'].includes(data.target))
    .map((data) => {
      const row: Record<string, any> = {
        target: data.target,
        symbol: data.symbol,
        unit: data.unit,
        mergeInfo: [], // 存储需要合并的信息
      }

      report.items.forEach((item, itemIndex) => {
        if (Array.isArray(item.members) && item.members.length > 0) {
          const colspan = item.members.length * 3 // 每个 member 对应三个子列
          row.mergeInfo.push({ itemIndex, colspan })

          item.members.forEach((member, memberIndex) => {
            const baseKey = `item${itemIndex}_member${memberIndex}`

            if (mergeTargets.includes(data.target)) {
              // 对于需要合并的指标，从 item 中提取数据
              row[`currentData_item${itemIndex}_member${memberIndex}`] = getCurrentData(data.target, member.current, item)
              row[`willData_item${itemIndex}_member${memberIndex}`] = getWillData(data.target, member.will, item)
              row[`changeData_item${itemIndex}_member${memberIndex}`] = getChangeData(data.target, member, item)
            } else {
              // 对于其他指标，从 member 中提取数据
              row[`currentData_item${itemIndex}_member${memberIndex}`] = getCurrentData(data.target, member.current, null)
              row[`willData_item${itemIndex}_member${memberIndex}`] = getWillData(data.target, member.will, null)
              row[`changeData_item${itemIndex}_member${memberIndex}`] = getChangeData(data.target, member, null)
            }
          })
        }
      })

      // 仅对需要合并的指标计算 colspan
      if (mergeTargets.includes(data.target)) {
        const totalMembers = report.items.reduce((sum, item) => sum + (Array.isArray(item.members) ? item.members.length : 0), 0)
        row.colspan = totalMembers * 3 // 每个 member 对应三个子列
      }

      return row
    })

  // 过滤掉为 null 的行
  const filteredRows = tableRows.filter((row) => row !== null)

  // 标记需要隐藏的 'target' 单元格
  const mergedRowCount: Record<string, number> = {}
  mergeTargets.forEach((target) => {
    mergedRowCount[target] = 0
  })

  return filteredRows.map((row) => {
    if (mergeTargets.includes(row.target)) {
      if (mergedRowCount[row.target] === 0) {
        mergedRowCount[row.target]++
        return row
      } else {
        return { ...row, target: '' }
      }
    }
    return row
  })
}

// 获取当前值
const getCurrentData = (target: string, current: any, item: any): string => {
  if (mergeTargets.includes(target)) {
    if (!item) return '/'
    switch (target) {
      case '增加常用电量':
        return typeof item.increaseCommonPower === 'number' ? item.increaseCommonPower.toFixed(2) : '/'
      case '理论综合提升发电量':
        return typeof item.realImprovePower === 'number' ? item.realImprovePower.toFixed(2) : '/'
      case '最优换算系数':
        return typeof item.bestValue === 'number' ? item.bestValue.toFixed(2) : '/'
      default:
        return '/'
    }
  } else {
    if (!current) return '/'
    switch (target) {
      case '真空':
        return current.vacuumValue !== undefined ? current.vacuumValue.toFixed(2) : '/'
      case '机组排汽温度':
        return current.exhaustTemperature !== undefined ? current.exhaustTemperature.toFixed(2) : '/'
      case '对应背压值':
        return current.unitPressure !== undefined ? current.unitPressure.toFixed(2) : '/'
      case '冷却水进口温度':
        return current.inTemp !== undefined ? current.inTemp.toFixed(2) : '/'
      case '冷却水出口温度':
        return current.outTemp !== undefined ? current.outTemp.toFixed(2) : '/'
      case '逼近度':
        return current.coolApproach !== undefined ? current.coolApproach.toFixed(2) : '/'
      case '冷却水温升':
        return current.temperatureRise !== undefined ? current.temperatureRise.toFixed(2) : '/'
      case '端差':
        return current.ttd !== undefined ? current.ttd.toFixed(2) : '/'
      case '提升发电效率':
        return typeof current.improveEfficiency === 'number' ? current.improveEfficiency.toFixed(2) : '/'
      case '理论提升发电量':
        return typeof current.improvePower === 'number' ? current.improvePower.toFixed(2) : '/'
      default:
        return '/'
    }
  }
}

// 获取模拟值
const getWillData = (target: string, will: any, item: any): string => {
  if (mergeTargets.includes(target)) {
    if (!item) return '/'
    switch (target) {
      case '增加常用电量':
        return typeof item.realImprovePower === 'number' ? item.realImprovePower.toFixed(2) : '/'
      case '理论综合提升发电量':
        return '/'
      case '最优换算系数':
        return '/'
      default:
        return '/'
    }
  } else {
    if (!will) return '/'
    switch (target) {
      case '真空':
        return will.vacuumValue !== undefined ? will.vacuumValue.toFixed(2) : '/'
      case '机组排汽温度':
        return will.exhaustTemperature !== undefined ? will.exhaustTemperature.toFixed(2) : '/'
      case '对应背压值':
        return will.unitPressure !== undefined ? will.unitPressure.toFixed(2) : '/'
      case '冷却水进口温度':
        return will.inTemp !== undefined ? will.inTemp.toFixed(2) : '/'
      case '冷却水出口温度':
        return will.outTemp !== undefined ? will.outTemp.toFixed(2) : '/'
      case '逼近度':
        return will.coolApproach !== undefined ? will.coolApproach.toFixed(2) : '/'
      case '冷却水温升':
        return will.temperatureRise !== undefined ? will.temperatureRise.toFixed(2) : '/'
      case '端差':
        return will.ttd !== undefined ? will.ttd.toFixed(2) : '/'
      case '提升发电效率':
        return typeof will.improveEfficiency === 'number' ? will.improveEfficiency.toFixed(2) : '/'
      case '理论提升发电量':
        return typeof will.improvePower === 'number' ? will.improvePower.toFixed(2) : '/'
      default:
        return '/'
    }
  }
}

// 获取变化值
const getChangeData = (target: string, member: any, item: any): string => {
  if (mergeTargets.includes(target)) {
    if (!item) return '/'
    switch (target) {
      case '增加常用电量':
        return typeof item.bestValue === 'number' ? item.bestValue.toFixed(2) : '/'
      case '理论综合提升发电量':
        return '/'
      case '最优换算系数':
        return '/'
      default:
        return '/'
    }
  } else {
    if (!member || !member.current || !member.will) return '/'
    switch (target) {
      case '真空':
        return typeof member.will.vacuumValue === 'number' && typeof member.current.vacuumValue === 'number'
          ? (member.will.vacuumValue - member.current.vacuumValue).toFixed(2)
          : '/'
      case '机组排汽温度':
        return typeof member.will.exhaustTemperature === 'number' && typeof member.current.exhaustTemperature === 'number'
          ? (member.will.exhaustTemperature - member.current.exhaustTemperature).toFixed(2)
          : '/'
      case '对应背压值':
        return typeof member.will.unitPressure === 'number' && typeof member.current.unitPressure === 'number'
          ? (member.will.unitPressure - member.current.unitPressure).toFixed(2)
          : '/'
      case '冷却水进口温度':
        return typeof member.will.inTemp === 'number' && typeof member.current.inTemp === 'number'
          ? (member.will.inTemp - member.current.inTemp).toFixed(2)
          : '/'
      case '冷却水出口温度':
        return typeof member.will.outTemp === 'number' && typeof member.current.outTemp === 'number'
          ? (member.will.outTemp - member.current.outTemp).toFixed(2)
          : '/'
      case '逼近度':
        return typeof member.will.coolApproach === 'number' && typeof member.current.coolApproach === 'number'
          ? (member.will.coolApproach - member.current.coolApproach).toFixed(2)
          : '/'
      case '冷却水温升':
        return typeof member.will.temperatureRise === 'number' && typeof member.current.temperatureRise === 'number'
          ? (member.will.temperatureRise - member.current.temperatureRise).toFixed(2)
          : '/'
      case '端差':
        return typeof member.will.ttd === 'number' && typeof member.current.ttd === 'number'
          ? (member.will.ttd - member.current.ttd).toFixed(2)
          : '/'
      case '提升发电效率':
        return typeof member.improveEfficiency === 'number' ? member.improveEfficiency.toFixed(2) : '/'
      case '理论提升发电量':
        return typeof member.improvePower === 'number' ? member.improvePower.toFixed(2) : '/'
      default:
        return '/'
    }
  }
}
// 获取表格数据
const getTabData = () => {
  const params = {
    pageNum: 1,
    pageSize: 9999,
    projectId: cachedProjects.id,
    analysisType: 0,
  }

  // 在请求前保存当前选中的tab
  if (activeTab.value) {
    tempActiveTab.value = activeTab.value
  }

  getSmartList(params)
    .then((res) => {
      // 检查 res.data.rows 是否为数组，如果不是，则赋值为空数组
      const rows = Array.isArray(res.data.rows) ? res.data.rows : []
      rows.forEach((row: any) => {
        try {
          row.report = row.report ? JSON.parse(row.report) : null // 解析 report，如果 report 为 null，则保持为 null
        } catch (e) {
          console.warn(`解析 report 失败: ${e}`)
          row.report = null
        }
      })
      // 直接更新数据，let watch处理tab状态
      tableData.value = rows as TableRow[]
    })
    .catch((error) => {
      console.error('获取数据失败:', error)
      tableData.value = [] // 请求失败时，设置为空数组
    })
}

// 添加候选方案数据状态
const candidateData = ref<any[]>([])
const candidateTotal = ref(0)

// 保存当前候选方案的查询状态
const currentCandidateParams = ref<any>({})

// 获取候选方案数据
const getCandidateData = (reportId: string, pageNum = 1, pageSize = 10, planName?: string) => {
  const params = {
    reportId: reportId,
    pageNum: pageNum,
    pageSize: pageSize,
    ...(planName && { planName }),
  }
  getSmartCandidateList(params)
    .then((res) => {
      candidateData.value = Array.isArray(res.data.rows) ? res.data.rows : []
      candidateTotal.value = res.data.total || 0
    })
    .catch((error) => {
      console.error('获取候选方案数据失败:', error)
      candidateData.value = []
      candidateTotal.value = 0
    })
}

// 处理候选方案数据获取
const handleCandidateGetData = (params: { reportId: string; pageNum: number; pageSize: number; planName?: string }) => {
  // 保存当前查询参数
  currentCandidateParams.value = { ...params }
  getCandidateData(params.reportId, params.pageNum, params.pageSize, params.planName)
}

// 定时器逻辑
const timer = ref<ReturnType<typeof setInterval> | null>(null)

// 统一的数据刷新函数
const refreshAllData = () => {
  // 更新主数据
  getTabData()
  // 如果有选中的tab，使用当前的查询参数更新候选方案数据
  if (activeTab.value) {
    const params = currentCandidateParams.value
    getCandidateData(activeTab.value, params.pageNum || 1, params.pageSize || 10, params.planName)
  }
}

// 刷新单个报告数据
const handleRefreshSingleReport = (powerUnitIds) => {
  const params={
    projectId: cachedProjects.id,
    powerUnitIds:powerUnitIds
  }
  manuallyTriggereddata(params).then((res) => {
    console.log(res)
  }).catch((err) => {
    ElMessage.error('刷新数据失败，请联系管理员')
  })
}

// 下载报告日志txt文件
const handleDownloadReportLog = () => {
  if (!activeTab.value) {
    ElMessage.error('请先选择一个报告')
    return
  }

  const params = {
    reportId: activeTab.value
  }
  downloadReportLog(params)
    .then((res) => {
      const link = document.createElement('a')
      let blob = new Blob([res.data], { type: 'text/plain' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = `报告日志_${activeTab.value}.txt`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    })
    .catch((error) => {
      ElMessage.error('导出日志过程中发生错误')
      console.error(error)
    })
}

const createTimer = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
  timer.value = setInterval(() => {
    refreshAllData()

    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
    createTimer()
  }, 60000) // 1分钟
}

onMounted(() => {
  refreshAllData() // 初始化时也使用统一函数
  createTimer() // 创建第一个定时器
})

onUnmounted(() => {
  if (timer.value !== null) {
    clearInterval(timer.value)
    timer.value = null
  }
})

// 实现 span-method
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  let rowspan = 1
  let colspan = 1

  // 定义静态列的数量
  const staticColumnCount = 3 // target, symbol, unit

  if (mergeTargets.includes(row.target)) {
    // 遍历 mergeInfo 以找到当前列是否需要合并
    let currentCol = staticColumnCount
    for (const merge of row.mergeInfo) {
      const { itemIndex, colspan: itemColspan } = merge
      if (columnIndex === currentCol) {
        // 当前列是需要合并的起始列
        colspan = itemColspan
        break
      } else if (columnIndex > currentCol && columnIndex < currentCol + itemColspan) {
        // 当前列在合并范围内，隐藏该单元格
        rowspan = 0
        colspan = 0
        break
      }
      currentCol += itemColspan
    }
  }

  return { rowspan, colspan }
}
</script>

<style scoped lang="scss">
.table-header-with-refresh {
  display: inline-flex;
  align-items: flex-start;
  justify-content: center;
  position: relative;
  width: 100%;

  .header-text {
    display: inline-block;
    text-align: center;
  }

  .refresh-btn {
    position: relative;
    margin-left: 8px;
    width: 20px;
    height: 20px;
    min-height: 20px;
    padding: 0;
    font-size: 10px;
    background-color: transparent;
    border: 1px solid #2493fa;
    color: #2493fa;

    &:hover {
      transform: scale(1.1);
      background-color: rgba(36, 147, 250, 0.1);
      border-color: #2493fa;
      color: #2493fa;
    }

    .refresh-icon {
      width: 12px;
      height: 12px;
    }
  }
}

.alternative-solutions{
  margin-top: 5px;
  border: 1px solid #2393fa;
}
.float-button {
  position: fixed;
  right: 20px; /* 距离右侧 20px */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%);
  z-index: 1000; /* 保证在最上层 */
}
.report-container {
  overflow-x: auto;
}
.report {
  background: #021c33;
  // box-shadow: inset 0px 2px 28px #2194ff;
  margin-bottom: 20px;
  // padding: 20px;
}

.home {
  display: flex;
}

.conclusion {
  // margin: 10px 20px 0 20px;
  display: flex;
  justify-content: space-between;
}

.line {
  width: 25%;
  height: 250px;
  display: flex;
  flex-direction: column;
  border: 10px solid #021c33;
  border-image: url(@/assets/images/line.png) 10 round;

  .conclusion-title {
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    vertical-align: top;
  }

  .conclusion-content {
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 1);
    margin: 50px 0 0 30px;
    text-align: left;
    vertical-align: top;
  }

  .conclusion-content1 {
    height: 100%;
    overflow-y: auto;
    // margin: 30px 30px 0 30px;
    color: rgba(255, 255, 255, 1);
    span {
      display: block;
    }
    .infinite-list {
      list-style: none;
      margin: 0;
      padding: 0 0 0 5px;
    }

    &::-webkit-scrollbar-track {
      border-radius: 5px;
      background: #07355c;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgb(52, 86, 113);
      border-radius: 20px !important;
      border: 2px solid #07355c;
    }

    /* 针对 Firefox 浏览器 */
    scrollbar-width: thin;
    scrollbar-color: rgb(52, 86, 113) #07355c;
  }

  .recommendation-row {
    margin-bottom: 10px;
  }

  .recommendation-item {
    white-space: normal;
    word-break: break-word;
    cursor: pointer;
  }
}

:deep(.el-table__body-wrapper tr td.el-table-fixed-column--left) {
  background-color: rgba(7, 53, 92, 1) !important;
}
:deep(.el-table__body-wrapper tr:hover td.el-table-fixed-column--left) {
  background-color: rgba(7, 53, 92, 1) !important;
}
:deep(.el-table__header-wrapper th.el-table-fixed-column--left) {
  background-color: rgba(7, 53, 92, 1) !important;
}
:deep(.el-table__row > :nth-child(1) > .cell) {
  color: #b08805;
}
:deep(.el-table th, .el-table tr, .el-table td) {
  background-color: #07355c;
  color: #fff;
  font-size: 18px;
  height: 5px;
  font-weight: Normal;
}

:deep(.el-table__header-wrapper) {
}
:deep(.el-table__header-wrapper th) {
  border: 1px solid #00429c;
  background-color: rgba(7, 53, 92, 1);
  color: #b08805;
}

:deep(.el-table__body-wrapper td) {
  border-top: none !important;
  border-bottom: none !important;
  border-left: 1px solid #00429c !important;
  border-right: 1px solid #00429c !important;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background: #07355c !important;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
}

// 外层边框
:deep(.el-table) {
  --el-table-border-color: none !important;
  // --el-table-border-color: none;
}

:deep(.el-table__cell) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background-color: inherit !important;
}

:deep(.el-table__body-wrapper .el-table__row td) {
  background-color: inherit !important;
}
:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;

  color: #b08805;
}
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}

// 添加tab样式
// :deep(.el-tabs__header) {
//   background-color: #021c33;
//   margin: 0;
//  // margin-bottom: 20px;
// }

// :deep(.el-tabs__nav-wrap) {
//   background-color: #021c33;
// }

// :deep(.el-tabs__nav) {
//   background-color: #021c33;
// }

// :deep(.el-tabs__item) {
//   color: #ffffff;
//   background-color: #07355c;
//   border: 1px solid #00429c;
//   margin-right: 5px;
//   &:hover {
//     color: #b08805;
//   }
//   &.is-active {
//     color: #b08805;
//     background-color: #021c33;
//     border-bottom-color: #b08805;
//   }
// }

// :deep(.el-tabs__active-bar) {
//   background-color: #b08805;
// }

// :deep(.el-tabs__content) {
//   padding: 0;
// }
:deep(.el-tabs__item:hover) {
  // color: #fff;
}
:deep(.el-tabs__header) {
  background-color: #021c33;
  margin: 0;
}

:deep(.el-tabs--border-card) {
  background: rgba(2, 28, 51, 0.5);
}

:deep(.el-tabs--border-card > .el-tabs__header) {
  background: linear-gradient(180deg, rgba(33, 148, 255, 0) 0%, rgba(33, 148, 255, 0.2) 100%) !important;
  border-bottom: 1px solid rgba(33, 148, 255, 1);
}

:deep(.el-tabs--border-card) {
  border: none;
}

:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {
  border-radius: 2px;
  background: linear-gradient(180deg, rgb(6, 101, 243) 0%, rgba(119, 122, 126, 0.1) 100%);
  opacity: 0.8;
  // color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(0, 0, 0, 1);
}
:deep(.el-tabs--border-card > .el-tabs__content) {
  padding: 5px !important;
}
:deep(.el-tabs__nav-wrap:after) {
  background: none;
}
:deep(.el-tabs__item) {
  color: #ffffff;
  // background-color: #07355c;
  // border: 1px solid #00429c;
  margin-right: 5px;
  &:hover {
    color: #b08805;
  }
  &.is-active {
    color: #b08805;
    background-color: #021c33;
    border-bottom-color: #b08805;
  }
}
</style>
