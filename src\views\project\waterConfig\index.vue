<!--水质报告配置 -->

<template>
  <div class="p-2">
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['iot:water:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate(selectedRow)" v-hasPermi="['iot:water:edit']">
              修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete(selectedRow)" v-hasPermi="['iot:water:remove']">
              删除
            </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" /> -->
        </el-row>
      </template>
      <el-table :data="tableData" v-loading="loading" style="width: 100%">
        <el-table-column label="序号" align="center" prop="id" width="100" />
        <el-table-column label="水质报告名称" prop="name" align="center" />
        <el-table-column label="所属项目" prop="project" align="center" />
        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" v-hasPermi="['iot:water:remove']" @click="handleUpdate(scope.row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" v-hasPermi="['iot:water:edit']" @click="handleDelete(scope.row)" />
            </el-tooltip>
            <el-tooltip content="新增" placement="top">
              <el-button link type="primary" icon="Plus" v-hasPermi="['iot:water:add']" @click="wateAdd(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 弹框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="780px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="dialog.visible" ref="wateList" :model="waterData" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="name">
              <el-input v-model="waterData.name" placeholder="请输入项目地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属项目" prop="project">
              <el-select v-model="waterData.project" placeholder="请选择项目种类">
                <el-option v-for="dict in project_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 保存 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue'
import { waterConfigData } from '../label/api/configs.api'
import{ FormInstance} from 'element-plus'
const router = useRouter()
const wateList = ref<FormInstance | null>(null)
const loading = ref(false)
const tableData = ref([
    {id:1, name: '中水',project:'项目A', createTime: '2024-10-6', },
    {id:2, name: '软水',project:'项目B', createTime: '2024-10-6', },
    {id:3, name: '循环水',project:'项目C', createTime: '2024-10-6', },
])
const rules = {
    name: [{ required: true, message: '项目必选', trigger: 'blur' }],
    project: [{ required: true, message: '报告名称必填', trigger: 'blur' }]

}
// const tableData = ref<waterConfigData[]>([])
const waterData = reactive<waterConfigData>({
  id: undefined,
  name:'',
  project: '',
  createTime: 0,
})

const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
})
const total = ref(50) // 示例总数，根据实际情况调整
const dialog = reactive({
    visible: false,
    title: ''
})
// 新增
const handleAdd = () => {
    // console.log('Add item')
    dialog.visible = true
    dialog.title = '新增'
    Object.assign(waterData, {
        id: undefined,
        name:'',
        project: '',
  })
}
const handleUpdate = (row: { name: string }) => {
    if (row) {
    Object.assign(waterData, row)
  }
  dialog.visible = true
  dialog.title = '修改项目信息'
  nextTick(() => {
    wateList.value?.resetFields()
  })
}

const handleDelete = (row) => {
    // console.log('Delete item', row)
}
const getList = () => {
    // 获取数据逻辑
}
// 水质报告参数新增
const wateAdd = (row) => {
    // console.log('Add item', row)
    router.push({ path: '/project/waterAdd', query: { id: row.id } })
}

const submitForm = () => {
    wateList.value?.validate((valid) => {
    if (valid) {
      // console.log(wateList, '-----')
      // 执行保存操作，例如通过 API 提交数据
    }
  })
}
const cancel = () => {
    dialog.visible = false
}
</script>
