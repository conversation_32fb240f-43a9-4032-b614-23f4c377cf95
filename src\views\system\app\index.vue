<template>
  <div>
    <yt-crud
      ref="crudRef"
      :data="data"
      :column="column"
      v-model:page="state.page"
      v-model:query="state.query"
      :total="state.total"
      :loading="state.loading"
      :tableProps=" {
            selection: false,
            viewBtn: false,
            menuWidth: 100,
            dialogBtn:false
    
          }"
      @onLoad="getData"
      @delFun="onDelete"
      @saveFun="onSave"
    />
  </div>
</template>

<script lang="ts" setup>
import { IColumn } from '@/components/common/types/tableCommon'

import YtCrud from '@/components/common/yt-crud.vue'
import { listApp, addApp, delApp, updateApp } from '@/api/system/app'
import { AppVO } from '@/api/system/app/types'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import emitter from '@/utils/eventBus.js'
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
emitter.on('projectListChanged', (e) => {
  location.reload()
})
const column: IColumn[] = [
  {
    label: '应用名称',
    key: 'appName',
    search: true,
    rules: [{ required: true, message: '应用名称不能为空' }],
  },
  {
    label: 'appId',
    key: 'appId',
    search: true,
    editDisabled: true,
    rules: [{ required: true, message: 'appId不能为空' }],
  },
  {
    label: 'appSecret',
    key: 'appSecret',
    search: false,
    hide:true,
    rules: [{ required: true, message: 'appSecret不能为空' }],
  },
  {
    label: '应用类型',
    key: 'appType',
    sortable: true,
    type: 'select',
    rules: [{ required: true, message: '应用类型不能为空' }],
    componentProps: {
      options: [
        {
          label: 'APP',
          value: '0',
        },
        {
          label: '小程序',
          value: '1',
        },
        {
          label: '企业微信小程序',
          value: '12',
        },
      ],
    },
  },
  {
    label: '企业应用id',
    key: 'agentId',
    sortable: true,
  },
  {
    label: '备注',
    key: 'remark',
    sortable: true,
  },
]

const data = ref<AppVO[]>([])
const state = reactive({
  total: 0,
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  query: {},
  loading: false,
})
const { id: projectId } = cachedProjects
// 保存数据
const onSave = async ({ data, cancel }: any) => {
  const project = { ...data, projectId }
  // console.log(project)
  state.loading = true
  data.id ? await updateApp(toRaw(project)) : await addApp(toRaw(project))
  state.loading = false
  cancel()
  getData()
}
// 删除数据
const onDelete = async (row: any) => {
  state.loading = true
  await delApp(row.id)
  ElMessage.success('删除成功!')
  state.loading = false
  getData()
}
// 获取数据
const getData = async () => {
  state.loading = true
  listApp(state.page)
    .then((res) => {
      data.value = res.data.rows
      state.total = res.data.total
    })
    .finally(() => {
      state.loading = false
    })
}
</script>
<style scoped>
:deep(.el-select__wrapper){

color: #fff!important;
background: rgb(3, 43, 82) !important;
box-shadow:0 0 0 0px #034374 inset !important;
border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
color: #fff;
}
:deep(.el-card){
    background: rgba(2, 28, 51, 0.5);
    /* box-shadow:inset 0px 2px 28px  rgba(33, 148, 255, 0.5); */
    border:none;
}
:deep(.el-card__body){
    border: none;
}
:deep(.el-table, .el-table__expanded-cell ){
    background-color: transparent !important;
  }
:deep(.el-table__body tr, .el-table__body td) {
    padding: 0;
    height: 40px;
  }
:deep(.el-table tr) {
    border: none;
    background-color: transparent;
  }
:deep(.el-table th) {
    /* background-color: transparent; */
    background-color: rgba(7, 53, 92, 1);
    color: rgba(204, 204, 204, 1) !important;
    font-size: 14px;
    font-weight: 400;
  }
:deep(.el-table){
    --el-table-border-color: none;
  }
  /*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
:deep(.el-table__body-wrapper .el-table__row){
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
:deep(.el-table__body-wrapper .el-table__row:hover td ){
    background: none !important;
    /* 取消单元格背景色，确保整行背景色生效 */
  }
:deep(.el-table__header thead tr th) {
    background: rgba(7, 53, 92, 1) !important;
    color: #ffffff;
  }
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
    color: #fff;
  }
:deep(.el-tree){
    background-color: transparent;
  }
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content){
    background-color:   #07355c;
  }
  :deep(.el-tree-node__expand-icon){
    color: #fff;
  }
  :deep(.el-tree-node__label){
    color: #fff;
  }
  :deep(.el-tree-node__content) {
    &:hover {
      background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    }
  }
:deep(.el-select__tags .el-tag--info){
    background-color:#153059 !important;
}
:deep(.el-tag.el-tag--info){
  color: #fff !important;
}
</style>
