<template>
  <div class="data-filter-config p-2">
    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="primary" plain @click="handleAdd" icon="Plus">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain @click="handleDelete" :disabled="multiple" icon="Delete">删除</el-button>
          </el-col>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" border style="width: 100%">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column prop="title" label="筛选内容" align="center" />
        <el-table-column prop="filterType" label="筛选类型" align="center">
          <template #default="scope">
            {{ getFilterTypeLabel(scope.row.filterType) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" />
            </el-tooltip>
            <el-tooltip content="编辑" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改对话框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="800px"
      @close="closeDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="dialog.visible" :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="筛选类型" prop="filterType">
          <el-select v-model="form.filterType" @change="handleFilterTypeChange">
            <el-option v-for="item in filterTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <!-- 根据筛选类型动态显示不同的配置组件 -->
        <CirculatingWaterFilter v-if="form.filterType === '1'" v-model="form.item" />
        <CleanlinessFilter v-else-if="form.filterType === '2'" v-model="form.item" />
        <CirculatingPumpFilter v-else-if="form.filterType === '3'" v-model="form.item" />
        <FanGroupFilter v-else-if="form.filterType === '4'" v-model="form.item" />
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看对话框 -->
    <el-dialog title="查看筛选配置" v-model="viewDialog.visible" width="600px" @close="closeViewDialog" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="筛选类型">
          {{ getFilterTypeLabel(viewForm.filterType) }}
        </el-descriptions-item>

        <el-descriptions-item label="筛选配置">
          <pre>{{ JSON.stringify(viewForm.item, null, 2) }}</pre>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeViewDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, getCurrentInstance, nextTick, computed, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ComponentInternalInstance } from 'vue'
import CirculatingWaterFilter from './components/CirculatingWaterFilter.vue'
import CleanlinessFilter from './components/CleanlinessFilter.vue'
import CirculatingPumpFilter from './components/CirculatingPumpFilter.vue'
import FanGroupFilter from './components/FanGroupFilter.vue'
import { getDataFilterConfigList, addDataFilterConfig, editDataFilterConfig, deleteDataFilterConfig } from './index.api'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
const { wsCache } = useLocalCache()
import emitter from '@/utils/eventBus.js'
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
emitter.on('projectListChanged', (e) => {
  location.reload()
})

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { intelligent_data_filter_type, intelligent_data_filter_state } = toRefs<any>(
  proxy?.useDict('intelligent_data_filter_type', 'intelligent_data_filter_state')
)
console.log(intelligent_data_filter_state.value)

// 筛选类型选项 - 从字典获取
const filterTypeOptions = computed(() => {
  return (
    intelligent_data_filter_type.value || [
      {
        label: '循环水量',
        value: '1',
        elTagType: 'default',
        elTagClass: '',
      },
      {
        label: '清洁系数',
        value: '2',
        elTagType: 'default',
        elTagClass: '',
      },
      {
        label: '循泵组合电流及循环水量',
        value: '3',
        elTagType: 'default',
        elTagClass: '',
      },
      {
        label: '风机组合风机电流',
        value: '4',
        elTagType: 'default',
        elTagClass: '',
      },
    ]
  )
})

// 获取筛选类型的标签
const getFilterTypeLabel = (value: string | number) => {
  const stringValue = String(value)
  const option = filterTypeOptions.value.find((item) => item.value === stringValue)
  return option ? option.label : stringValue
}

// 定义数据类型
interface DataFilterConfig {
  id: number
  projectId?: number
  filterType: string | number
  type?: number
  title?: string
  item?: any
}

interface QueryParams {
  pageNum: number
  pageSize: number
}

interface FormData {
  id?: number
  filterType: string
  projectId?: number
  item?: any
}

// 响应式数据
const loading = ref(false)
const dataList = ref<DataFilterConfig[]>([])
const total = ref(0)
const ids = ref<number[]>([])
const single = ref(true)
const multiple = ref(true)

// 查询参数
const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
})

// 对话框相关
const dialog = reactive({
  visible: false,
  title: '',
})

const viewDialog = reactive({
  visible: false,
})

// 表单数据
const form = reactive<FormData>({
  filterType: '',
  item: {},
})

const viewForm = reactive<DataFilterConfig>({
  id: 0,
  filterType: '',
  item: {},
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  filterType: [{ required: true, message: '筛选类型不能为空', trigger: 'change' }],
}

// 模拟数据
const mockData: DataFilterConfig[] = [
  {
    id: 695965564538949,
    projectId: 679256716169285,
    filterType: 1,
    type: 0,
    title: '2台工频泵运行',
    item: {
      cond: {
        type: 0,
        data: '{"num": 2}',
        title: '2台工频泵运行',
      },
      range: [
        {
          type: 0,
          data: {
            min: 5500,
            max: 9000,
          },
          title: '5500~9000',
        },
        {
          type: 1,
          data: {
            min: 5000,
            max: 5499.99,
          },
          title: '＜5500',
        },
        {
          type: 2,
          data: {
            min: null,
            max: 4999.99,
          },
          title: '＜5000',
        },
        {
          type: 3,
          data: {
            min: 9000.01,
            max: 9500,
          },
          title: '＞9000',
        },
        {
          type: 4,
          data: null,
          title: '＞9500',
        },
      ],
      formula: null,
    },
  },
  {
    id: 695968729350213,
    projectId: 679256716169285,
    filterType: 2,
    type: 0,
    title: '循环水入口水温＞20℃',
    item: {
      cond: {
        type: 0,
        data: '{"inTemp": 20}',
        title: '循环水入口水温＞20℃',
      },
      range: [
        {
          type: 0,
          data: {
            min: 0.6,
            max: 1,
          },
          title: '0.6~1',
        },
        {
          type: 1,
          data: {
            min: 0.5,
            max: 0.599,
          },
          title: '＜0.6',
        },
        {
          type: 2,
          data: {
            min: null,
            max: 0.499,
          },
          title: '＜0.5',
        },
        {
          type: 3,
          data: {
            min: 1.001,
            max: 1.1,
          },
          title: '＞1',
        },
        {
          type: 4,
          data: null,
          title: '＞1.1',
        },
      ],
      formula: null,
    },
  },
  {
    id: 695968729350214,
    projectId: 679256716169285,
    filterType: 3,
    type: 1,
    title: '循泵组合电流及循环水量公式',
    item: {
      formula: '#currentCircleWaterFlow*(#planTotalPumpPower/#currentTotalPower)*(1d-Math.log(#planTotalPumpPower/#currentTotalPower))/4)',
    },
  },
  {
    id: 695968729350215,
    projectId: 679256716169285,
    filterType: 4,
    type: 1,
    title: '风机组合风机电流',
    item: null,
  },
]

// 处理筛选类型变化
const handleFilterTypeChange = (value: string) => {
  // 清空之前的配置
  form.item = {}

  // 根据筛选类型初始化默认配置
  switch (value) {
    case '1':
      form.item = {
        cond: {
          type: '',
          data: {
            num: '',
          },
          title: '',
        },
        range: [
          {
            type: '',
            data: {
              min: null,
              max: null,
            },
            title: '',
          },
        ],
      }
      break
    case '2':
      form.item = {
        cond: {
          type: '',
          data: {
            inTemp: '',
          },
          title: '',
        },
        range: [
          {
            type: '',
            data: {
              min: null,
              max: null,
            },
            title: '',
          },
        ],
      }
      break
    case '3':
      form.item = {
        formula: '',
      }
      break
    case '4':
      form.item = null
      break
  }
}

// 方法定义
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      projectId: cachedProjects?.id
    }
    const response = await getDataFilterConfigList(params)
    dataList.value = response.data.rows || []
    total.value = response.data.total || 0
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection: DataFilterConfig[]) => {
  ids.value = selection.map((item) => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

const handleAdd = () => {
  resetForm()
  dialog.title = '新增筛选配置'
  dialog.visible = true
}

const handleUpdate = (row?: DataFilterConfig) => {
  resetForm()
  const targetRow = row || dataList.value.find((item) => item.id === ids.value[0])
  if (targetRow) {
    Object.assign(form, targetRow)

    // 确保filterType是字符串类型
    form.filterType = String(form.filterType)

    // 深拷贝item对象，避免直接修改原数据
    form.item = JSON.parse(JSON.stringify(targetRow.item))

    // 对于循环水量和清洁系数类型，需要处理数据格式转换
    if ((form.filterType === '1' || form.filterType === '2') && form.item?.cond?.data) {
      // 如果cond.data是字符串，转换为对象
      if (typeof form.item.cond.data === 'string') {
        try {
          const parsedData = JSON.parse(form.item.cond.data)
          form.item.cond.data = parsedData
        } catch (e) {
          console.error('解析cond.data失败:', e)
          if (form.filterType === '1') {
            form.item.cond.data = { num: '' }
          } else if (form.filterType === '2') {
            form.item.cond.data = { inTemp: '' }
          }
        }
      }

      // 确保cond.type是字符串类型
      if (form.item.cond.type !== null && form.item.cond.type !== undefined) {
        form.item.cond.type = String(form.item.cond.type)
      }

      // 确保range中的type都是字符串类型
      if (form.item.range && Array.isArray(form.item.range)) {
        form.item.range = form.item.range.map(range => ({
          ...range,
          type: range.type !== null && range.type !== undefined ? String(range.type) : ''
        }))
      }
    }

    console.log('编辑数据处理后:', form.item)
    dialog.title = '编辑筛选配置'
    dialog.visible = true
  }
}

const handleView = (row: DataFilterConfig) => {
  Object.assign(viewForm, row)
  viewDialog.visible = true
}

const handleDelete = (row?: DataFilterConfig) => {
  const targetIds = row ? [row.id] : ids.value
  ElMessageBox.confirm(`是否确认删除选中的${targetIds.length}条数据项？`, '系统提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      // 批量删除选中的数据项
      await deleteDataFilterConfig(targetIds)
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

const submitForm = async () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 创建提交数据副本，进行数据格式转换
        const submitData = JSON.parse(JSON.stringify(form))

        // 添加projectId参数
        submitData.projectId = cachedProjects?.id

        // 对于循环水量和清洁系数类型，需要处理数据格式转换
        if ((submitData.filterType === '1' || submitData.filterType === '2') && submitData.item?.cond?.data) {
          // 如果cond.data是对象，转换为字符串
          if (typeof submitData.item.cond.data === 'object' && submitData.item.cond.data !== null) {
            submitData.item.cond.data = JSON.stringify(submitData.item.cond.data)
          }
        }

        // 调用实际的API接口
        if (submitData.id) {
          // 编辑
          await editDataFilterConfig(submitData)
        } else {
          // 新增
          delete submitData.id
          await addDataFilterConfig(submitData)
        }

        ElMessage.success(dialog.title.includes('新增') ? '新增成功' : '编辑成功')
        closeDialog()
        getList()
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败，请重试')
      }
    }
  })
}

const resetForm = () => {
  // 完全重置表单数据
  Object.assign(form, {
    id: undefined,
    filterType: '',
    item: {},
  })
  // 重置表单验证状态
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const closeDialog = () => {
  dialog.visible = false
  resetForm()
}

const closeViewDialog = () => {
  viewDialog.visible = false
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.data-filter-config {
  min-height: 100vh;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-tag.el-tag--info) {
  color: #000 !important;
}
:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  border: none;
}

:deep(.el-card__body) {
  border: none;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}

pre {
  background: rgba(3, 43, 82, 0.3);
  padding: 10px;
  border-radius: 4px;
  color: #fff;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

:deep(.el-select__wrapper) {
  background: transparent !important;
  box-shadow: none !important;
  border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder) {
  color: #fff !important;
}
</style>
