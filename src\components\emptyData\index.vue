<template>
  <div class="empty-data-page">
    <img :src="imageSrc" alt="empty" />
    <p class="text">暂无数据</p>
    <p class="text1">页面内容暂时为空，去别处看看吧</p>
  </div>
</template>
<script setup>
defineProps({
  imageSrc: {
    type: String,
    required: true,
  }
})
</script>

<style lang="scss" scoped>
.empty-data-page {
    background: rgb(2, 28, 51);
    box-shadow:inset 0px 2px 28px  rgb(33, 148, 255);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;  // 减去顶部的固定间距

  .text {
    text-align: center;  // 文本居中对齐
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 18.56px;
    color: rgba(255, 255, 255, 1);
  }
  .text1{
    font-size: 14px;
    font-weight: 300;
    letter-spacing: 0px;
    line-height: 18.56px;
    color: rgba(153, 153, 153, 1);
  }
}
</style>
