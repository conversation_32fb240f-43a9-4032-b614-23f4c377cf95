<template>
  <div class="demonstrate-container">
    <div class="demonstrate-header" v-if="!isFullscreen">
      <h2>{{ currentProject?.name || '组态演示' }}</h2>
      <div class="header-actions">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>
          返回
        </el-button>
        <el-button @click="toggleFullscreen">
          <el-icon><FullScreen /></el-icon>
          全屏
        </el-button>
      </div>
    </div>
    
    <div 
      class="demonstrate-canvas"
      :class="{ 'fullscreen': isFullscreen }"
      :style="{
        width: `${currentProject?.width || 1920}px`,
        height: `${currentProject?.height || 1080}px`,
        backgroundColor: currentProject?.backgroundColor || '#f0f0f0',
        transform: `scale(${scale})`,
      }"
    >
      <template v-if="currentProject">
        <component-renderer
          v-for="component in currentProject.components"
          :key="component.id"
          :component="component"
          :editing="false"
        />
      </template>
      <div v-else class="empty-canvas">
        <el-empty description="未找到组态项目" />
      </div>
      
      <div v-if="isFullscreen" class="fullscreen-controls">
        <el-button circle @click="toggleFullscreen">
          <el-icon><CloseBold /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useConfigurationStore } from '../stores/configurationStore'
import ComponentRenderer from '../components/ComponentRenderer.vue'
import type { ConfigurationProject, DataBinding } from '../types'

const route = useRoute()
const router = useRouter()
const configStore = useConfigurationStore()

// 当前项目
const currentProject = ref<ConfigurationProject | null>(null)

// 是否全屏
const isFullscreen = ref(false)

// 缩放比例
const scale = ref(1)

// 数据源连接
const dataConnections = ref<any[]>([])

// 计算容器尺寸和缩放比例
const calculateScale = () => {
  if (!currentProject.value) return
  
  const containerWidth = window.innerWidth - (isFullscreen.value ? 0 : 40)
  const containerHeight = window.innerHeight - (isFullscreen.value ? 0 : 100)
  
  const scaleX = containerWidth / currentProject.value.width
  const scaleY = containerHeight / currentProject.value.height
  
  // 使用较小的缩放比例，确保整个画布都能显示
  scale.value = Math.min(scaleX, scaleY)
}

// 监听窗口大小变化
const handleResize = () => {
  calculateScale()
}

// 加载项目
onMounted(async () => {
  const projectId = route.params.id as string
  if (projectId) {
    await loadProject(projectId)
    calculateScale()
    window.addEventListener('resize', handleResize)
  } else {
    ElMessage.error('未指定项目ID')
    router.push('/configuration/list')
  }
})

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  // 关闭所有数据连接
  closeAllDataConnections()
})

// 监听全屏状态变化
watch(isFullscreen, () => {
  calculateScale()
})

// 加载项目
const loadProject = async (id: string) => {
  try {
    // 这里应该从API加载项目数据
    // 临时使用示例数据
    const project: ConfigurationProject = {
      id,
      name: '示例组态项目',
      description: '这是一个示例组态项目',
      width: 1920,
      height: 1080,
      backgroundColor: '#f0f0f0',
      components: [],
      dataBindings: