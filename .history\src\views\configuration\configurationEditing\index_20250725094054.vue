<template>
  <div class="configuration-editor">
    <div class="editor-header">
      <div class="editor-title">
        <h2>{{ currentProject?.name || '组态编辑器' }}</h2>
      </div>
      <div class="editor-actions">
        <el-button-group>
          <el-button @click="undo" :disabled="!canUndo">
            <el-icon><ArrowLeft /></el-icon>
            撤销
          </el-button>
          <el-button @click="redo" :disabled="!canRedo">
            <el-icon><ArrowRight /></el-icon>
            重做
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button @click="copySelected" :disabled="!hasSelection">
            <el-icon><CopyDocument /></el-icon>
            复制
          </el-button>
          <el-button @click="pasteComponents" :disabled="!canPaste">
            <el-icon><Document /></el-icon>
            粘贴
          </el-button>
          <el-button @click="deleteSelected" :disabled="!hasSelection" type="danger">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button @click="saveProject" type="primary">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
          <el-button @click="previewProject">
            <el-icon><View /></el-icon>
            预览
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="editor-main">
      <div class="editor-sidebar">
        <component-library />
      </div>
      
      <div class="editor-canvas-container">
        <div class="editor-toolbar">
          <el-button-group>
            <el-button @click="zoomIn">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
            <el-button @click="zoomOut">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="resetZoom">
              <el-icon><FullScreen /></el-icon>
              {{ Math.round(editorState.zoom * 100) }}%
            </el-button>
          </el-button-group>
          
          <el-switch
            v-model="editorState.grid.show"
            inline-prompt
            active-text="网格"
            inactive-text="网格"
          />
          
          <el-switch
            v-model="editorState.grid.snap"
            inline-prompt
            active-text="吸附"
            inactive-text="吸附"
          />
        </div>
        
        <div 
          class="editor-canvas"
          :style="{
            width: `${currentProject?.width || 1920}px`,
            height: `${currentProject?.height || 1080}px`,
            backgroundColor: currentProject?.backgroundColor || '#f0f0f0',
            transform: `scale(${editorState.zoom})`,
            backgroundImage: editorState.grid.show ? 'linear-gradient(#ddd 1px, transparent 1px), linear-gradient(90deg, #ddd 1px, transparent 1px)' : 'none',
            backgroundSize: `${editorState.grid.size}px ${editorState.grid.size}px`
          }"
          @click="onCanvasClick"
          @dragover="onDragOver"
          @drop="onDrop"
        >
          <template v-if="currentProject">
            <component-renderer
              v-for="component in currentProject.components"
              :key="component.id"
              :component="component"
              :selected="isSelected(component.id)"
              :editing="true"
              @select="selectComponent"
              @update="updateComponent"
            />
          </template>
          <div v-else class="empty-canvas">
            <el-empty description="请创建或选择一个组态项目" />
          </div>
        </div>
      </div>
      
      <div class="editor-properties">
        <h3>属性面板</h3>
        <template v-if="hasSelection">
          <component-properties
            :component="selectedComponents[0]"
            @update="updateComponentProperties"
          />
        </template>
        <template v-else-if="currentProject">
          <project-properties
            :project="currentProject"
            @update="updateProjectProperties"
          />
        </template>
        <template v-else>
          <el-empty description="未选择任何对象" />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useConfigurationStore } from '../stores/configurationStore'
import ComponentLibrary from '../components/ComponentLibrary.vue'
import ComponentRenderer from '../components/ComponentRenderer.vue'
import ComponentProperties from '../components/ComponentProperties.vue'
import ProjectProperties from '../components/ProjectProperties.vue'
import type { ConfigurationComponent, ConfigurationProject, ComponentTemplate } from '../types'

const route = useRoute()
const router = useRouter()
const configStore = useConfigurationStore()

// 获取当前项目
const currentProject = computed(() => configStore.currentProject)

// 获取编辑器状态
const editorState = computed(() => configStore.editorState)

// 获取选中的组件
const selectedComponents = computed(() => configStore.selectedComponents)

// 是否有选中的组件
const hasSelection = computed(() => selectedComponents.value.length > 0)

// 是否可以撤销
const canUndo = computed(() => editorState.value.history.past.length > 0)

// 是否可以重做
const canRedo = computed(() => editorState.value.history.future.length > 0)

// 是否可以粘贴
const canPaste = computed(() => editorState.value.clipboard.length > 0)

// 组件是否被选中
const isSelected = (id: string) => {
  return editorState.value.selectedComponents.includes(id)
}

// 加载项目
onMounted(() => {
  const projectId = route.params.id as string
  if (projectId) {
    loadProject(projectId)
  } else {
    createNewProject()
  }
})

// 加载项目
const loadProject = async (id: string) => {
  try {
    // 这里应该从API加载项目数据
    // 临时使用示例数据
    const project: ConfigurationProject = {
      id,
      name: '示例组态项目',
      description: '这是一个示例组态项目',
      width: 1920,
      height: 1080,
      backgroundColor: '#f0f0f0',
      components: [],
      dataBindings: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'admin',
      status: 'draft'
    }
    
    configStore.setCurrentProject(project)
  } catch (error) {
    ElMessage.error('加载项目失败')
    router.push('/configuration/list')
  }
}

// 创建新项目
const createNewProject = () => {
  const project: ConfigurationProject = {
    id: 'project_' + Date.now(),
    name: '新建组态项目',
    description: '',
    width: 1920,
    height: 1080,
    backgroundColor: '#f0f0f0',
    components: [],
    dataBindings: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'admin',
    status: 'draft'
  }
  
  configStore.setCurrentProject(project)
}

// 画布点击事件
const onCanvasClick = (event: MouseEvent) => {
  // 如果点击的是画布本身，而不是组件，则清除选择
  if (event.target === event.currentTarget) {
    configStore.clearSelection()
  }
}

// 拖拽经过画布
const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy'
  }
}

// 拖拽放置到画布
const onDrop = (event: DragEvent) => {
  event.preventDefault()
  
  if (!event.dataTransfer || !currentProject.value) return
  
  const componentData = event.dataTransfer.getData('component')
  if (!componentData) return
  
  try {
    const template = JSON.parse(componentData) as ComponentTemplate
    
    // 获取放置位置（相对于画布）
    const canvasRect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    const x = (event.clientX - canvasRect.left) / editorState.value.zoom
    const y = (event.clientY - canvasRect.top) / editorState.value.zoom
    
    // 如果启用了网格吸附
    const gridSize = editorState.value.grid.size
    const snapX = editorState.value.grid.snap ? Math.round(x / gridSize) * gridSize : x
    const snapY = editorState.value.grid.snap ? Math.round(y / gridSize) * gridSize : y
    
    // 创建新组件
    const component: ConfigurationComponent = {
      id: 'component_' + Date.now() + '_' + Math.floor(Math.random() * 1000),
      type: template.type,
      name: template.name,
      x: snapX,
      y: snapY,
      z: 0,
      width: template.defaultProps.width || 100,
      height: template.defaultProps.height || 100,
      rotation: 0,
      opacity: 1,
      visible: true,
      locked: false,
      style: template.defaultProps.style || {},
      data: template.defaultProps.data || { static: null },
      ...(template.defaultProps as any)
    }
    
    // 添加组件到项目
    configStore.addComponent(component)
  } catch (error) {
    console.error('添加组件失败', error)
  }
}

// 选择组件
const selectComponent = (id: string, multiple = false) => {
  configStore.selectComponent(id, multiple)
}

// 更新组件
const updateComponent = (component: ConfigurationComponent) => {
  configStore.updateComponent(component)
}

// 更新组件属性
const updateComponentProperties = (component: ConfigurationComponent) => {
  configStore.updateComponent(component)
}

// 更新项目属性
const updateProjectProperties = (project: ConfigurationProject) => {
  if (!currentProject.value) return
  
  currentProject.value = {