<template>
  <div class="p-2">
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['iot:information:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate(selectedRow)" v-hasPermi="['iot:information:edit']">
              修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete(selectedRow)"
              v-hasPermi="['iot:information:remove']"
            >
              删除
            </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
        </el-row>
      </template>
      <el-table :data="tableData" v-loading="loading" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id" width="100" />
        <el-table-column label="项目名称" prop="projectName" align="center" width="100" />
        <el-table-column prop="address" label="项目地址" align="center" width="100" />
        <el-table-column label="项目种类" align="center" prop="projectType" width="100">
          <template #default="scope">
            <dict-tag :options="project_type" :value="scope.row.projectType" />
          </template>
        </el-table-column>
        <el-table-column prop="latitude" label="维度" align="center" width="100" />
        <el-table-column prop="longitude" label="经度" align="center" width="100" />
        <el-table-column prop="altitude" label="海拔" align="center" width="100" />
        <el-table-column prop="remark" label="描述" align="center" width="100" />
        <!-- 操作 -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['iot:information:edit']" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['iot:information:remove']" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改弹框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="780px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="dialog.visible" ref="FormDataList" :model="InformationData" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="InformationData.projectName" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目地址" prop="address">
              <el-input v-model="InformationData.address" placeholder="请输入项目地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目种类" prop="projectType">
              <el-select v-model="InformationData.projectType" placeholder="请选择项目种类">
                <el-option v-for="dict in project_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维度" prop="latitude">
              <el-input v-model="InformationData.latitude" placeholder="请输入维度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="InformationData.longitude" placeholder="请输入经度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="海拔" prop="altitude">
              <el-input v-model="InformationData.altitude" placeholder="请输入海拔" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述" prop="remark">
              <el-input type="textarea" :rows="4" v-model="InformationData.remark" placeholder="请输入描述" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 保存 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice" lang="ts">
import { ComponentInternalInstance, nextTick, ref, reactive, toRefs, getCurrentInstance } from 'vue'
import { Information, InformQuery } from '../label/api/configs.api'
import { FormInstance } from 'element-plus'

const FormDataList = ref<FormInstance | null>(null)
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { project_type } = toRefs<any>(proxy?.useDict('project_type'))
const loading = ref(false)
const single = ref(true)
const multiple = ref(true)
const showSearch = ref(true)
// 表单数据
const tableData = ref<Information[]>([])
const total = ref(0) // 总条数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
})
// 弹框设置
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
})
const InformationData = reactive<Information>({
  id: undefined,
  projectName: '',
  projectType: '',
  address: '',
  latitude: 0,
  longitude: 0,
  altitude: 0,
  remark: '',
  createAt: 0
})
const selectedRow = ref<Information | null>(null)

// 校验规则
const rules = {
  projectName: [
    { required: true, message: '项目名称是必填项', trigger: 'blur' }
  ]
}

// 新增按钮
const handleAdd = () => {
  dialog.visible = true
  dialog.title = '添加项目信息'
  Object.assign(InformationData, {
    id: undefined,
    projectName: '',
    projectType: '',
    address: '',
    latitude: 0,
    longitude: 0,
    altitude: 0,
    remark: '',
    createAt: 0
  })
  nextTick(() => {
    FormDataList.value?.resetFields()
  })
}

// 修改
const handleUpdate = (row?: Information) => {
  if (row) {
    Object.assign(InformationData, row)
  }
  dialog.visible = true
  dialog.title = '修改项目信息'
  nextTick(() => {
    FormDataList.value?.resetFields()
  })
}

// 删除
const handleDelete = (row?: Information) => {
  if (row) {
    // 执行删除操作
  }
}

// 查询
const getList = () => {
  loading.value = true
  // 模拟 API 调用
  setTimeout(() => {
    // 模拟查询结果
    const result = [
      { id: 1, projectName: '项目1', projectType: '4', address: '地址1', latitude: 1, longitude: 1, altitude: 1, remark: '备注1', createAt: Date.now() }
      // ...其他数据
    ]
    tableData.value = result
    total.value = result.length // 更新总条数
    loading.value = false
  }, 1000)
}

// 多选框选中数据
const handleSelectionChange = (selection: Information[]) => {
  if (selection.length === 1) {
    single.value = false
    multiple.value = false
    selectedRow.value = selection[0]
  } else if (selection.length > 1) {
    single.value = true
    multiple.value = false
    selectedRow.value = null
  } else {
    single.value = true
    multiple.value = true
    selectedRow.value = null
  }
}

// 重置
const reset = () => {
  FormDataList.value?.resetFields()
}

// 保存
const submitForm = () => {
  FormDataList.value?.validate((valid) => {
    if (valid) {
      // console.log(InformationData, '-----')
      // 执行保存操作，例如通过 API 提交数据
    }
  })
}

// 取消
const cancel = () => {
  dialog.visible = false
  reset()
}

// 初始化查询
getList()
</script>
