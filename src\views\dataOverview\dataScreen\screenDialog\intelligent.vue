<!--水质分析 -->
<template>
  <div class="p-2">
    <div class="pumpsegmented" v-if="segOptions.length > 1">
      <el-segmented v-model="selectedUnit" :options="segOptions" />
    </div>
    <el-card shadow="never">
      <el-table v-loading="state.loading" :data="tableData">
        <el-table-column prop="type" label="类别" />
        <el-table-column prop="currentValue" label="当前值">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: space-evenly; width: 60%; white-space: nowrap;">
              <!-- 数据部分：使用 flex:1 保证宽度，并添加溢出隐藏和省略号 -->
              <span style="flex: 1; overflow: hidden; text-overflow: ellipsis;">{{ scope.row.currentValue }}</span>
              <!-- 图标部分：根据 dataStatus 返回相应的图标 -->
              <img
                v-if="getStatusIcon(scope.row.dataStatus)"
                :src="getStatusIcon(scope.row.dataStatus)"
                alt="状态图标"
                style="width: 20px; height: 20px; margin-left: 8px;"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="suggestedValue" label="建议值" />
        <el-table-column prop="conclusion" label="结论" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import intelligentUpper from '@/assets/images/intelligentUpper.png'
import intelligentBelow from '@/assets/images/intelligentBelow.png'
import intelligentErr from '@/assets/images/intelligentErr.png'

// 定义 props 并接收外部传入的 dialogData 数据
const props = defineProps({
  dialogData: {
    type: Array,
    default: () => [],
  },
})
const { dialogData } = toRefs(props)
const selectedUnit = ref(0)

console.log('dialogData', dialogData.value)

// 生成 Segmented 选项： label 为机组名称、value 为索引
const segOptions = computed(() =>
  dialogData.value.map((item: any, index: number) => ({
    label: item.powerUnitName,
    value: index,
  }))
)
// 当前选中机组的数据
const tableData = computed(() => {
  return dialogData.value[selectedUnit.value]?.conclusionList || []
})

function getStatusIcon(dataStatus: number | string): string {
  const statusNum = Number(dataStatus)
  if (statusNum === 0) {
    return intelligentErr
  } else if (statusNum === 1) {
    return intelligentUpper
  } else if (statusNum === 3) {
    return intelligentBelow
  }
  return ''
}
// 定义状态，如加载状态等
const state = reactive({
  loading: false,
})
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  border: none;
}

:deep(.el-card__body) {
  border: none;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__body tr, .el-table__body td) {
  padding: 0;
  height: 40px;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

:deep(.el-table__cell) {
  // color: rgba(204, 204, 204, 1) !important;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}

:deep(.el-tree) {
  background-color: transparent;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #07355c;
}

:deep(.el-tree-node__expand-icon) {
  color: #fff;
}

:deep(.el-tree-node__label) {
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}

:deep(.el-select__tags .el-tag--info) {
  background-color: #153059 !important;
}

:deep(.el-tag.el-tag--info) {
  color: #fff !important;
}
:deep(.el-table__header thead tr th) {
  background: #1f5499 !important;
}
:deep(.el-table tr) {
  border: 1px solid #00aaff !important;
}
:deep(.el-card) {
  border: 2px solid #00aaff !important;
}
.pumpsegmented{
  text-align: center;
  margin-bottom: 20px;
  }
</style>
