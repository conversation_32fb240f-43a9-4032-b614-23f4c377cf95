<!-- 候选方案 -->
<template>
  <div class="candidate">
    <!-- 查询表单 -->
    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="方案名称:">
          <el-input v-model="searchForm.planName" placeholder="请输入方案名称" clearable style="width: 200px;" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleDownload" icon="Download">运维日志下载</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格 -->
    <el-table :data="tableData" stripe :span-method="spanMethod">
      <template #empty>
        <!-- 暂无备选方案 -->
        <div>暂无备选方案</div>
      </template>
      <el-table-column prop="planName" label="候选方案名称" width="200" />
      <el-table-column prop="powerUnitName" label="所属机组" />
      <el-table-column prop="exhaustTemperature" label="排汽温度(°C)" />
      <el-table-column prop="unitPressure" label="背压值(kPa)" />
      <el-table-column prop="inTemp" label="冷却水进口温度(°C)" />
      <el-table-column prop="coolApproach" label="逼近度(°C)" />
      <el-table-column prop="temperatureRise" label="冷却水温升(°C)" />
      <el-table-column prop="ttd" label="端差(°C)" />
      <el-table-column prop="vacuumValue" label="真空值(kPa)" />
      <el-table-column prop="improveEfficiency" label="提升发电效率(%)" />
      <el-table-column prop="improvePower" label="理论提升发电量(kW·h)" />
      <el-table-column prop="plusConsumption" label="增加常用电量(kW·h)" />
      <el-table-column prop="realImprovePower" label="理论综合提升发电量(kW·h)" />
      <el-table-column prop="bestValue" label="换算系数" />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useLocalCache, CACHE_KEY } from '@/hooks/web/useCache'
import { downloadReportLog } from '../index.api'

// 定义props
interface Props {
  candidateData?: any[]
  total?: number
  reportId?: string
}

const props = withDefaults(defineProps<Props>(), {
  candidateData: () => [],
  total: 0,
  reportId: ''
})

// 定义事件
const emit = defineEmits(['get-data'])

// 项目id
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => props.total)

// 查询表单
const searchForm = ref({
  planName: ''
})
const handleDownload = async () => {
  if (!props.reportId) {
    ElMessage.error('请先选择一个报告')
    return
  }
  try {
    const res = await downloadReportLog(props.reportId)
    // 获取实际的Blob数据
    let blob = res.data || res
    // 检查是否是Blob对象
    if (blob instanceof Blob) {
      // 将Blob转换为文本
      const text = await blob.text()
      let finalJsonString
      try {
        const jsonData = JSON.parse(text)
        finalJsonString = JSON.stringify(jsonData, null, 2)
      } catch (e) {
        finalJsonString = text
      }
      // 创建新的Blob用于下载
      const downloadBlob = new Blob([finalJsonString], { type: 'application/json;charset=utf-8' })
      const url = window.URL.createObjectURL(downloadBlob)
      const link = document.createElement('a')
      link.href = url

      // 设置下载文件名
      const fileName = `运维日志_${props.reportId}_${new Date().toISOString().slice(0, 10)}.json`
      link.download = fileName

      // 触发下载
      document.body.appendChild(link)
      link.click()

      // 清理资源
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      ElMessage.success('运维日志下载成功')
    } else {
      // 如果不是Blob，按原来的方法处理
      const jsonString = JSON.stringify(blob, null, 2)
      const downloadBlob = new Blob([jsonString], { type: 'application/json;charset=utf-8' })
      const url = window.URL.createObjectURL(downloadBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `运维日志_${props.reportId}_${new Date().toISOString().slice(0, 10)}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      ElMessage.success('运维日志下载成功')
    }
  } catch (error) {
    ElMessage.error('运维日志下载失败')
  }
}

// 直接使用props中的数据
const candidateData = computed(() => props.candidateData)

// 表格数据处理
const tableData = computed(() => {
  const result: any[] = []
  candidateData.value.forEach((plan) => {
    plan.items.forEach((item: any, itemIndex: number) => {
      result.push({
        id: `${plan.id}_${itemIndex}`,
        planId: plan.id,
        planName: plan.planName,
        powerUnitName: item.will.powerUnitName,
        exhaustTemperature: item.will.exhaustTemperature,
        unitPressure: item.will.unitPressure,
        inTemp: item.will.inTemp,
        coolApproach: item.will.coolApproach,
        temperatureRise: item.will.temperatureRise,
        ttd: item.will.ttd,
        vacuumValue: item.will.vacuumValue,
        improveEfficiency: item.improveEfficiency,
        improvePower: item.improvePower,
        plusConsumption: plan.plusConsumption,
        realImprovePower: plan.realImprovePower,
        bestValue: plan.bestValue,
        itemIndex: itemIndex,
        totalItems: plan.items.length,
      })
    })
  })
  return result
})

// 查询
const handleSearch = () => {
  currentPage.value = 1
  getData()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    planName: ''
  }
  currentPage.value = 1
  getData()
}

// 页面大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  getData()
}

// 当前页改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getData()
}

// 获取数据
const getData = () => {
  if (!props.reportId) return

  const params = {
    reportId: props.reportId,
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...(searchForm.value.planName && { planName: searchForm.value.planName })
  }
  emit('get-data', params)
}

// 监听reportId变化，重新获取数据
watch(() => props.reportId, (newVal) => {
  if (newVal) {
    currentPage.value = 1
    getData()
  }
}, { immediate: true })

// 单元格合并逻辑
const spanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
  // 需要合并的列：方案名称、增加常用电量、理论综合提升发电量、换算系数
  const mergeColumns = ['planName', 'plusConsumption', 'realImprovePower', 'bestValue']

  if (mergeColumns.includes(column.property)) {
    if (row.itemIndex === 0) {
      return {
        rowspan: row.totalItems,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
}
</script>

<style scoped>
.candidate {
  padding: 5px;
}

.search-form {
  margin: 5px 0;
  background-color: rgba(7, 53, 92, 0.5);
  border-radius: 4px;
  display: flex;
  align-items: center;
  min-height: 60px;
  padding: 10px;
}

.search-form :deep(.el-form-item__label) {
  color: #ffffff;
}

.search-form :deep(.el-input__inner) {
  border-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.search-form :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.6);
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.pagination-wrapper :deep(.el-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: rgba(7, 53, 92, 0.8);
  --el-pagination-text-color: #ffffff;
  --el-pagination-border-radius: 4px;
  --el-pagination-button-color: #ffffff;
  --el-pagination-button-bg-color: rgba(255, 255, 255, 0.1);
  --el-pagination-button-disabled-color: rgba(255, 255, 255, 0.3);
  --el-pagination-button-disabled-bg-color: rgba(255, 255, 255, 0.05);
  --el-pagination-hover-color: #409eff;
}
</style>
