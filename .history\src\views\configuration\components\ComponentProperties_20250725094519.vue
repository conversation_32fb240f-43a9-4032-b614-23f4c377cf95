<template>
  <div class="component-properties">
    <el-tabs>
      <el-tab-pane label="基础属性">
        <el-form label-position="top" size="small">
          <el-form-item label="组件名称">
            <el-input v-model="localComponent.name" @change="updateComponent" />
          </el-form-item>
          
          <el-form-item label="位置">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-input-number 
                  v-model="localComponent.x" 
                  :min="0" 
                  controls-position="right"
                  placeholder="X"
                  @change="updateComponent"
                />
              </el-col>
              <el-col :span="12">
                <el-input-number 
                  v-model="localComponent.y" 
                  :min="0" 
                  controls-position="right"
                  placeholder="Y"
                  @change="updateComponent"
                />
              </el-col>
            </el-row>
          </el-form-item>
          
          <el-form-item label="尺寸">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-input-number 
                  v-model="localComponent.width" 
                  :min="10" 
                  controls-position="right"
                  placeholder="宽度"
                  @change="updateComponent"
                />
              </el-col>
              <el-col :span="12">
                <el-input-number 
                  v-model="localComponent.height" 
                  :min="10" 
                  controls-position="right"
                  placeholder="高度"
                  @change="updateComponent"
                />
              </el-col>
            </el-row>
          </el-form-item>
          
          <el-form-item label="旋转角度">
            <el-slider 
              v-model="localComponent.rotation" 
              :min="0" 
              :max="360" 
              :step="1"
              show-input
              @change="updateComponent"
            />
          </el-form-item>
          
          <el-form-item label="透明度">
            <el-slider 
              v-model="localComponent.opacity" 
              :min="0" 
              :max="1" 
              :step="0.01"
              show-input
              @change="updateComponent"
            />
          </el-form-item>
          
          <el-form-item label="层级">
            <el-input-number 
              v-model="localComponent.z" 
              :min="0" 
              controls-position="right"
              @change="updateComponent"
            />
          </el-form-item>
          
          <el-form-item>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-switch
                  v-model="localComponent.visible"
                  active-text="可见"
                  inactive-text="隐藏"
                  @change="updateComponent"
                />
              </el-col>
              <el-col :span="12">
                <el-switch
                  v-model="localComponent.locked"
                  active-text="锁定"
                  inactive-text="解锁"
                  @change="updateComponent"
                />
              </el-col>
            </el-row>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式">
        <component-style-editor 
          :component="localComponent" 
          @update="updateStyle"
        />
      </el-tab-pane>
      
      <el-tab-pane label="数据">
        <component-data-editor 
          :component="localComponent" 
          @update="updateData"
        />
      </el-tab-pane>
      
      <el-tab-pane label="事件">
        <component-event-editor 
          :component="localComponent" 
          @update="updateEvents"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { ConfigurationComponent } from '../types'
import ComponentStyleEditor from './editors/ComponentStyleEditor.vue'
import ComponentDataEditor from './editors/ComponentDataEditor.vue'
import ComponentEventEditor from './editors/ComponentEventEditor.vue'

const props = defineProps<{
  component: ConfigurationComponent
}>()

const emit = defineEmits<{
  update: [component: ConfigurationComponent]
}>()

// 本地组件副本，用于编辑
const localComponent = ref<ConfigurationComponent>({...props.component})

// 监听组件变化，更新本地副本
watch(() => props.component, (newComponent) => {
  localComponent.value = {...newComponent}
}, { deep: true })

// 更新组件
const updateComponent = () => {
  emit('update', {...localComponent.value})
}

// 更新样式
const updateStyle = (style: any) => {
  localComponent.value.style = {...style}
  updateComponent()
}

// 更新数据
const updateData = (data: any) => {
  localComponent.value.data = {...data}
  updateComponent()
}

// 更新事件
const updateEvents = (events: any[]) => {
  localComponent.value.events = [...events]
  updateComponent()
}
</script>

<style scoped>
.component-properties {
  padding: 10px;
}
</style>