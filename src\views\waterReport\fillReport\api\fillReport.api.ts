import request from '@/utils/request'

enum Api {
  tabsList = '/project/powerPointConfig/all',
  waterFill = '/project/powerPointConfig/point/data',
  hisData = '/device/deviceProperty/downSampling/list',
  add = '/project/powerPointConfig/device/data/add',
}

// tabs切换
export const getWaterReport = (data) => {
  return request({
    url: Api.tabsList,
    method: 'post',
    data,
  })
}
// 查询tabs切换下的表单
export const getWaterFill = (data) => {
  return request({
    url: Api.waterFill,
    method: 'post',
    data,
  })
}

// 查询当前水质的历史数据
export const getHisData = (data) => {
  return request({
    url: Api.hisData,
    method: 'post',
    data,
  })
}
// 新增水质报告数据
export const addWaterReport = (data) => {
  return request({
    url: Api.add,
    method: 'post',
    data,
  })
}
