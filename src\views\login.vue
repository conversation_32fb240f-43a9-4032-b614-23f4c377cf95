<template>
  <div class="login flex">
    <div class="login-r">
      <div class="CIMStitle">CIMS智慧监控管理平台</div>
      <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
        <!-- <h3 class="logo">
          <img src="@/assets/logo/logo.png" alt="" />
        </h3> -->
        <div class="title">欢迎登录</div>
        <div class="fromitem">
          <el-form-item prop="tenantId" v-if="tenantEnabled">
            <el-select v-model="loginForm.tenantId" filterable placeholder="请选择/输入公司名称" style="width: 100%">
              <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId" />
              <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
            </el-select>
          </el-form-item>
          <el-form-item prop="username" class="elitem">
            <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号">
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password" class="elitem">
            <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码" @keyup.enter="handleLogin">
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="code" v-if="captchaEnabled" class="elitem">
            <el-input v-model="loginForm.code" size="large" auto-complete="off" placeholder="验证码" style="width: 67%" @keyup.enter="handleLogin">
              <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img" />
            </div>
          </el-form-item>
          <el-checkbox class="itemcheckbox" v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
          <el-form-item style="width:100%;" class="elitem">
            <el-button :loading="loading" size="large" type="primary" style="width:100%;" @click.prevent="handleLogin">
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
            <div style="float: right;" v-if="register">
              <router-link class="link-type" :to="'/register'">立即注册</router-link>
            </div>
          </el-form-item>
        </div>
      </el-form>
      <!-- <div class="round1 round"></div>
      <div class="round2 round"></div>
      <div class="round3 round"></div> -->
      <!-- <div class="el-login-footer">
        <span>重庆环际 © 2024 CIMS</span>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { getBackground } from './dataOverview/dataScreen/index.api'
import { getCodeImg, getTenantList } from '@/api/login'
import { setTenantId } from '@/utils/auth'
import Cookies from 'js-cookie'
import { encrypt, decrypt } from '@/utils/jsencrypt'
import { useUserStore } from '@/store/modules/user'
import { LoginData, TenantVO } from '@/api/types'
import { FormInstance, FormRules } from 'element-plus'
import { listProjectRelation } from '@/api/system/user'
import { to } from 'await-to-js'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import { useBackgroundStore } from '@/store/modules/background'
import { log } from 'console'
const backgroundStore = useBackgroundStore()
const userStore = useUserStore()
const router = useRouter()

// const title = import.meta.env.VITE_APP_TITLE
// const loginForm = ref<LoginData>({
//   tenantId: '000000',
//   username: 'admin',
//   password: 'admin123',
//   rememberMe: false,
//   code: '',
//   uuid: '',
// })
const loginForm = ref<LoginData>({
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: '',
})

const loginRules: FormRules = {
  // tenantId: [{ required: true, trigger: 'blur', message: '请输入您的租户编号' }],
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }],
}

const codeUrl = ref('')
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(true)
// 租户开关
const tenantEnabled = ref(false)

// 注册开关
const register = ref(false)
const redirect = ref(undefined)
const loginRef = ref<FormInstance>()
// 租户列表
const tenantList = ref<TenantVO[]>([])

const handleLogin = () => {
  loginRef.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      loading.value = true
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        // Cookies.set('tenantId', loginForm.value.tenantId, { expires: 30 })
        Cookies.set('username', loginForm.value.username, { expires: 30 })
        Cookies.set('password', String(encrypt(loginForm.value.password)), { expires: 30 })
        Cookies.set('rememberMe', String(loginForm.value.rememberMe), { expires: 30 })
      } else {
        // 否则移除
        Cookies.remove('username')
        Cookies.remove('password')
        Cookies.remove('rememberMe')
      }
      // 调用action的登录方法
      const [err] = await to(userStore.login(loginForm.value))
      if (!err) {
        const { wsCache } = useLocalCache()
        const [listErr, projectList] = await to(listProjectRelation())
        if (!listErr) {
          const cachedProjectList = wsCache.get(CACHE_KEY.projectLists)
          if (cachedProjectList) {
            wsCache.set(CACHE_KEY.projectLists, projectList)

            const selectedlistProjectItems = projectList.data[0]
            wsCache.set(CACHE_KEY.projectList, selectedlistProjectItems)
          } else {
            wsCache.set(CACHE_KEY.projectLists, projectList)

            const selectedlistProjectItems = projectList.data[0]
            wsCache.set(CACHE_KEY.projectList, selectedlistProjectItems)
          }
          const burl = {
            type: '0',
          }
          getBackground(burl).then((res) => {
            if (res.code === 200) {
              const backgroundurl = res.data[0].url
              const { wsCache } = useLocalCache()
              wsCache.set(CACHE_KEY.BACKGROUND, backgroundurl)
              // backgroundStore.setBackgroundUrl(backgroundurl)
            }
          })
          const burlogol = {
            type: '2',
          }
          getBackground(burlogol).then((res) => {
            if (res.code === 200) {
              const logourl = res.data[0].url
              console.log(logourl, 'logourl')
              const { wsCache } = useLocalCache()
              wsCache.set(CACHE_KEY.LOGO, logourl)
            }
          })
        } else {
          console.error('Failed to fetch project relation list:', listErr)
        }
        //
        // await router.push({ path: redirect.value || '/' })
        await router.push({ path: '/dataScreen' }).catch((error) => {
          console.error('跳转 /dataScreen 失败，重定向到首页：', error)
          return router.push({ path: '/' })
        })

        //刷新页面
        // location.reload()
        // ElNotification({
        //   type: 'success',
        //   dangerouslyUseHTMLString: false,
        //   message: '<img style="width: 120px;height: 20px" src="https://iita-factory.oss-cn-shanghai.aliyuncs.com/public/openiita.png" alt=""><div>欢迎使用CIMS智能运维平台</div>',
        //   duration: 2000
        // })
        // 调用 listProjectRelation 并存储到缓存
      } else {
        loading.value = false
        // 重新获取验证码
        if (captchaEnabled.value) {
          await getCode()
        }
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

/**
 * 获取验证码
 */
const getCode = async () => {
  const res = await getCodeImg()
  const { data } = res
  captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + data.img
    loginForm.value.uuid = data.uuid
  }
}

const getCookie = () => {
  // const tenantId = Cookies.get('tenantId')
  const username = Cookies.get('username')
  const password = Cookies.get('password')
  const rememberMe = Cookies.get('rememberMe')
  loginForm.value = {
    // tenantId: tenantId === undefined ? loginForm.value.tenantId : tenantId,
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : (decrypt(password) as string),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  }
}

/**
 * 获取租户列表
 */
const initTenantList = async () => {
  const { data } = await getTenantList()
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled
  if (tenantEnabled.value) {
    tenantList.value = data.voList
    if (tenantList.value != null && tenantList.value.length !== 0) {
      loginForm.value.tenantId = tenantList.value[0].tenantId
    }
  }
}

onMounted(() => {
  getCode()
  // initTenantList()
  getCookie()
})
</script>
<style lang="scss" scoped>

.login {
  height: 100%;
  background-image: url('@/assets/images/login/loginx.png');
  background-size: contain; /* 背景图片覆盖整个容器 */
  // background-repeat: no-repeat; /* 防止图片重复 */
  background-position: center center;
  &-l {
    width: 50%;
    padding: 0 60px;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 160px;
      height: 160px;
      border-radius: 50%;
      transform: translate(-30%, 40%);
      background: linear-gradient(18deg, #eaf2fbff 0%, #ebf2fc00 100%);
    }
    .title {
      font-size: 30px;
      color: #fff;
    }
    .desc {
      padding: 22px 0;
      font-size: 36px;
      color: #fff;
      font-weight: 500;
      span {
        display: inline-block;
        width: 50px;
      }
    }
    .icon {
      width: 100%;
      max-width: 600px;
      img {
        width: 100%;
        height: auto;
      }
    }
  }
  &-r {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-size: cover;
    position: relative;
    overflow: hidden;
    .CIMStitle {
      font-size: 36px;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 43.2px;
      color: rgba(255, 255, 255, 1);
      position: absolute; /* 让它绝对定位 */
      top: 20px; /* 调整距离顶部的距离 */
      left: 50%; /* 使它水平居中 */
      transform: translateX(-50%); /* 使用 translateX 来居中对齐 */
      z-index: 1000; /* 确保标题在其他元素之上 */
    }
    .round {
      position: absolute;
      border-radius: 50%;
      z-index: 1;
    }
    .logo {
      width: 230px;
      margin: 0 auto;
      margin-bottom: 80px;
      img {
        width: 100%;
      }
    }
    .title {
      font-size: 24px;
      font-weight: 500;
      letter-spacing: 0px;
      line-height: 31.82px;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      padding: 32px 0 44px 0;
    }
    .login-form {
      border-radius: 6px;
      background: rgba(19, 107, 194, 0.3);
      border: 2px solid rgb(20, 138, 193);
      box-shadow: inset 0px 0px 20px rgb(0, 160, 233);

      width: 480px;
      height: 500px;
      position: relative;
      z-index: 2;
      .fromitem {
        :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
          color: #fff !important;
        }
        :deep(.el-form-item--default) {
          margin-bottom: 30px !important;
        }
        padding: 0 40px;
        :deep(.elitem .el-input__wrapper) {
          background: rgb(1, 34, 59) !important;
          border: 1px solid rgb(33, 148, 255) !important;
          box-shadow: none !important;
          .el-input__inner {
            color: rgba(26, 77, 128, 1) !important;
            font-size: 18px;
            font-weight: 400;
          }
        }
      }
      .el-input {
        height: 40px;
        input {
          height: 40px;
        }
      }
      .input-icon {
        height: 39px;
        width: 14px;
        margin-left: 0px;
      }
    }
    .login-form * {
      // opacity: 1;
    }
    .login-tip {
      font-size: 13px;
      text-align: center;
      color: #bfbfbf;
    }
    .login-code {
      width: 33%;
      height: 40px;
      float: right;
      img {
        cursor: pointer;
        vertical-align: middle;
        width: 100%;
      }
    }
    .el-login-footer {
      position: absolute;
      bottom: 0;
      height: 40px;
      line-height: 40px;
      width: 50%;
      text-align: center;
      color: #87909d;
      font-family: Arial, serif;
      font-size: 12px;
      letter-spacing: 1px;
    }
    .login-code-img {
      height: 40px;
      padding-left: 12px;
    }
  }
}
</style>
