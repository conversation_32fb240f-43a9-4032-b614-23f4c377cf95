<template>
  <div class="p-2">
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" @click="handleReturn()"> 返回 </el-button>
          </el-col>
        </el-row>
      </template>
      <el-table :data="tableData" v-loading="loading" style="width: 100%">
        <el-table-column label="序号" align="center" prop="id" width="100" />
        <el-table-column label="点位" prop="name" align="center" />
        <el-table-column label="单位" prop="project" align="center" />
        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 弹框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="780px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="dialog.visible" ref="pointList" :model="pointData" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="name">
              <el-input v-model="pointData.name" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="project">
              <el-input v-model="pointData.project" placeholder="请输入单位" />
              <!-- <el-select v-model="pointData.project" placeholder="请选择项目种类">
                <el-option v-for="dict in project_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select> -->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 保存 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts" name="waterAdd">
import { useRoute } from 'vue-router'
import { ref, onMounted } from 'vue'
import{ FormInstance} from 'element-plus'
import { waterPointData } from '../label/api/configs.api'

const pointList = ref<FormInstance | null>(null)
const dialog = reactive({
    visible: false,
    title: ''
})
const rules = {
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],}
const router = useRouter()
const route = useRoute()
const total = ref(50)
const loading = ref(false)
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
})
const id = ref<string | null>(null)
    const tableData = ref([
    {id:1, name: '循环水PH',project:'', createTime: '2024-10-6', },
    {id:2, name: '循环水总碱度',project:'mg/L', createTime: '2024-10-6', },
    {id:3, name: '净水器浊度',project:'NTU', createTime: '2024-10-6', },
])
const pointData = reactive<waterPointData>({
  id: undefined,
  name:'',
  project: '',
  createTime: 0,
})
onMounted(() => {
  id.value = route.query.id as string | null
  // console.log('Received id:', id.value)
})

// 修改
const handleUpdate = (row: { name: string }) => {
    if (row) {
    Object.assign(pointData, row)
  }
  dialog.visible = true
  dialog.title = '修改项目信息'
  nextTick(() => {
    pointList.value?.resetFields()
  })
}
// 新增
const handleAdd = () => {
    // console.log('Add item')
    dialog.visible = true
    dialog.title = '新增'
    Object.assign(pointData, {
        id: undefined,
        name:'',
        project: '',
  })
}
// 提交
const submitForm = () => {
    pointList.value?.validate((valid) => {
    if (valid) {
      // console.log(pointList, '-----')
      // 执行保存操作，例如通过 API 提交数据
    }
  })
}
const cancel = () => {
    dialog.visible = false
}
// 返回上一级
const handleReturn = () => {
    router.back()
}

// 查询数据
const getList = () => {
}
</script>
