<template>
  <div ref="chartRef" class="power-chart"></div>
</template>

<script setup lang="ts" name="RealTimePowerChart">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  data?: Array<{ time: string; value: number }>
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  height: '100%'
})

const chartRef = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null
let resizeObserver: ResizeObserver | null = null

const waitForContainer = () => {
  return new Promise<void>((resolve) => {
    const checkContainer = () => {
      if (chartRef.value) {
        const rect = chartRef.value.getBoundingClientRect()
        if (rect.width > 0 && rect.height > 0) {
          resolve()
          return
        }
      }
      setTimeout(checkContainer, 50)
    }
    checkContainer()
  })
}

const initChart = async () => {
  if (!chartRef.value) return

  // 等待容器有明确的尺寸
  await waitForContainer()

  chart = echarts.init(chartRef.value)
  setOptions()

  // 确保图表正确渲染
  nextTick(() => {
    if (chart) {
      chart.resize()
    }
  })

  window.addEventListener('resize', handleResize)

  // 添加ResizeObserver监听容器尺寸变化
  if (chartRef.value && ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      if (chart) {
        chart.resize()
      }
    })
    resizeObserver.observe(chartRef.value)
  }
}

const setOptions = () => {
  if (!chart) return

  const xData = props.data.map(item => item.time)
  const yData = props.data.map(item => item.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 43, 137, 0.9)',
      borderColor: 'rgba(115, 208, 255, 0.6)',
      borderWidth: 1,
      textStyle: {
        color: '#73d0ff'
      },
      formatter: (params: any) => {
        const point = params[0]
        return `${point.name}<br/>实时功率: <span style="color: #4ddefc; font-weight: bold;">${point.value} kW</span>`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xData,
      axisLine: {
        lineStyle: {
          color: 'rgba(115, 208, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#b4d0fc',
        fontSize: 11
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#b4d0fc',
        fontSize: 11
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(115, 208, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'line',
        data: yData,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#73d0ff',
          width: 3,
          shadowColor: 'rgba(115, 208, 255, 0.4)',
          shadowBlur: 8
        },
        itemStyle: {
          color: '#73d0ff',
          borderColor: '#4ddefc',
          borderWidth: 2,
          shadowColor: 'rgba(115, 208, 255, 0.6)',
          shadowBlur: 6
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(115, 208, 255, 0.4)'
              },
              {
                offset: 1,
                color: 'rgba(115, 208, 255, 0.05)'
              }
            ]
          }
        },
        emphasis: {
          itemStyle: {
            scale: true,
            scaleSize: 8,
            shadowColor: 'rgba(77, 222, 252, 0.8)',
            shadowBlur: 10
          }
        }
      }
    ],
    backgroundColor: 'rgba(8, 42, 77, 0.2)'
  }

  chart.setOption(option)
}

const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

watch(
  () => props.data,
  () => {
    nextTick(() => {
      setOptions()
    })
  },
  { deep: true }
)

onMounted(() => {
  nextTick(() => {
    initChart()
    // 延迟确保容器完全渲染 - 增加延迟时间
    setTimeout(() => {
      if (chart) {
        chart.resize()
      }
    }, 300)

    // 添加更多重试机制
    setTimeout(() => {
      if (chart) {
        chart.resize()
      }
    }, 600)

    setTimeout(() => {
      if (chart) {
        chart.resize()
      }
    }, 1000)
  })
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.power-chart {
  width: 100%;
  height: 100%;
  flex: 1;
}
</style>
