<template>
  <div class="gauge-component">
    <v-chart 
      class="chart" 
      :option="chartOption" 
      :autoresize="true"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { GaugeChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import type { ConfigurationComponent } from '../../types'

// 注册 ECharts 组件
use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TitleComponent,
  TooltipComponent
])

const props = defineProps<{
  component: ConfigurationComponent
  editing?: boolean
}>()

// 仪表盘选项
const chartOption = computed(() => {
  const { data, style } = props.component
  
  // 获取仪表盘值
  let value = 0
  let min = 0
  let max = 100
  let title = ''
  let unit = ''
  
  // 如果有动态数据，优先使用动态数据
  if (data.dynamic && data.dynamic.value !== undefined) {
    value = Number(data.dynamic.value) || 0
    min = data.dynamic.min !== undefined ? Number(data.dynamic.min) : 0
    max = data.dynamic.max !== undefined ? Number(data.dynamic.max) : 100
    title = data.dynamic.title || ''
    unit = data.dynamic.unit || ''
  } else if (data.static) {
    // 使用静态数据
    if (typeof data.static === 'number') {
      value = data.static
    } else if (typeof data.static === 'object') {
      value = data.static.value !== undefined ? Number(data.static.value) : 0
      min = data.static.min !== undefined ? Number(data.static.min) : 0
      max = data.static.max !== undefined ? Number(data.static.max) : 100
      title = data.static.title || ''
      unit = data.static.unit || ''
    }
  }
  
  // 确保值在范围内
  value = Math.max(min, Math.min(max, value))
  
  // 计算百分比
  const percent = max > min ? (value - min) / (max - min) * 100 : 0
  
  // 获取颜色
  const color = getGaugeColor(percent)
  
  return {
    title: {
      text: title,
      left: 'center',
      show: !!title
    },
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        min,
        max,
        splitNumber: 10,
        radius: '100%',
        axisLine: {
          lineStyle: {
            width: 30,
            color: [
              [percent / 100, color],
              [1, '#eee']
            ]
          }
        },
        pointer: {
          icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
          length: '12%',
          width: 20,
          offsetCenter: [0, '-60%'],
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          length: 12,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        splitLine: {
          length: 20,
          lineStyle: {
            color: 'auto',
            width: 5
          }
        },
        axisLabel: {
          color: style.fontColor || '#464646',
          fontSize: style.fontSize || 12,
          distance: -60,
          formatter: function(value: number) {
            return value.toFixed(0)
          }
        },
        title: {
          offsetCenter: [0, '-20%'],
          fontSize: 20
        },
        detail: {
          fontSize: 30,
          offsetCenter: [0, '0%'],
          valueAnimation: true,
          formatter: function(value: number) {
            return value.toFixed(1) + (unit ? ' ' + unit : '')
          },
          color: 'auto'
        },
        data: [{
          value: value
        }]
      }
    ]
  }
})

// 根据百分比获取颜色
const getGaugeColor = (percent: number) => {
  if (percent <= 30) {
    return '#67C23A' // 绿色
  } else if (percent <= 70) {
    return '#E6A23C' // 黄色
  } else {
    return '#F56C6C' // 红色
  }
}

// 监听组件大小变化
const resizeObserver = ref<ResizeObserver | null>(null)

onMounted(() => {
  // 创建 ResizeObserver 监听容器大小变化
  if (window.ResizeObserver) {
    const chartContainer = document.querySelector('.gauge-component')
    if (chartContainer) {
      resizeObserver.value = new ResizeObserver(() => {
        // 触发图表重绘
        const chartInstance = document.querySelector('.chart') as any
        if (chartInstance && chartInstance.__chartInstance) {
          chartInstance.__chartInstance.resize()
        }
      })
      
      resizeObserver.value.observe(chartContainer)
    }
  }
})

onUnmounted(() => {
  // 清理 ResizeObserver
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
})
</script>

<style scoped>
.gauge-component {
  width: 100%;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>