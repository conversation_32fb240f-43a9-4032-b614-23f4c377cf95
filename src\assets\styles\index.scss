@import './variables.module.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './ruoyi.scss';
@import 'animate.css';
@import 'element-plus/dist/index.css';
// @import '../fonts/NotoSansSC-VariableFont_wght.ttf';
body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: 'Noto Sans CJK SC', '思源黑体',Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  color: #cccbcb;
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  // padding: 20px;
  // background: rgba(8, 16, 40, 1);

}
// search面板样式
.panel,
.search {
  margin-bottom: 0.75rem;
  border-radius: 0.25rem;
  background: rgba(2, 28, 51, 0.5);
  // box-shadow:inset 0px 2px 28px  rgba(33, 148, 255, 0.5);
  padding: 20px;
  .el-form-item__label{
    color: #fff ;
    font-size: 14px;
    font-weight: 400;
  }
  // 输入框
  .el-input__wrapper {
    border: 1px solid rgba(33, 148, 255, 1) !important;
    box-shadow: 0 0 0 0px #034374 inset !important;
    background: rgba(6, 49, 89, 1) !important;
    color: #FFFFFF !important;
  }
  .el-select__selection{
    width: 120px!important;
  }
  :deep(.el-select__wrapper){
    background-color: #063057 !important;
    // box-shadow: #2090f7 0 0 0 1px inset !important;
  }
  .el-select__placeholder{
    color: #fff   ;
  }
  .el-input__inner{
    color: #fff;
  }
  // 下拉框
    // .el-select-dropdown__list{
    //   margin: 0 !important;
    // }
    // .el-select-dropdown__item {
    //   background-color: #032b52 !important;
    //   color: #bab6b6 !important;
    // }

  &:hover {
    box-shadow: 0 2px 12px #0000001a;
    // transition: all ease 0.3s;
  }

}
 .el-select-dropdown__item.hover,
 .el-select-dropdown__item:hover{
  // background: rgba(33, 148, 255, 1) !important; 
color:#409eff !important;
}
.el-select__popper.el-popper{
  border:none !important;
}
.el-popper.is-light{
  border:none !important;

}
.el-select-dropdown__wrap{
  background-color: #032b52 !important;
}
 .el-select-dropdown__list{
      margin: 0 !important;
    }
    .el-select-dropdown__item {
      background-color: #032b52 !important;
      color: #bab6b6 !important;
    }
.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  // color: #337ab7;
  color: #fdfdfd;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}
.menu-cell {
  display: inline-flex;
  align-items: center;
}
.menu-cell .menu-btn:last-child .el-divider.el-divider--vertical {
  display: none;
}

.ov-text1 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.align-center {
  align-items: center;
}

// 弹窗
.el-dialog{
  background: rgba(3, 43, 82, 1) !important;
  box-shadow:inset 0px 0px 16px  rgba(33, 148, 255, 0.5) !important;

}
.el-dialog__title{
  color: #fff !important;
  font-size: 18px;
}
.el-form-item__label{
  color: rgba(255, 255, 255, 1) !important;

}
.el-form-item__label {
  text-align: left; /* 确保标签左对齐 */
  color: rgba(255, 255, 255, 1) !important;
}
.el-input-group__append{
  // border: 1px solid rgba(72, 86, 102, 1) !important;
  border: none;
  background: rgba(74, 83, 97, 0.5) !important;
  color: rgba(204, 204, 204, 1);
  font-size: 16px;
  box-shadow:none;
  }
  .el-input__wrapper,.el-textarea__inner{
    border: 1px solid #034374 !important;
    box-shadow: 0 0 0 0px #034374 inset !important;
    background: transparent !important;
    color: #FFFFFF !important;
  }
  .el-input__inner,.el-textarea__inner{
    color: rgba(255, 255, 255, 1) !important;
    font-size: 14px;
  }

  // 分段器
  .el-segmented{
    background:none !important;
    color:#fff !important;
    border: 1px solid rgba(108, 171, 251, 1);
    min-height:28px !important;
    border-radius:10px !important;
  }
  .el-segmented__item-selected{
    background: url(../../assets/images/TABactive.png) no-repeat center/100% 100%  !important;
    // background-size: cover !important;
  }
  .el-segmented__item:not(.is-disabled):not(.is-selected):hover{
    // background: #3268ad !important;
  }
  