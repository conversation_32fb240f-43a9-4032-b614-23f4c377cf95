<template>
  <div class="component-library">
    <div class="library-header">
      <h3>组件库</h3>
      <el-input
        v-model="searchText"
        placeholder="搜索组件"
        prefix-icon="Search"
        clearable
      />
    </div>
    
    <el-collapse v-model="activeCategories">
      <el-collapse-item 
        v-for="category in filteredCategories" 
        :key="category.id"
        :title="category.name"
        :name="category.id"
      >
        <div class="component-grid">
          <div
            v-for="component in category.components"
            :key="component.id"
            class="component-item"
            draggable="true"
            @dragstart="onDragStart($event, component)"
          >
            <div class="component-icon">
              <el-icon>
                <component :is="component.icon.replace('icon-', '')" />
              </el-icon>
            </div>
            <div class="component-name">{{ component.name }}</div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useConfigurationStore } from '../stores/configurationStore'
import type { ComponentTemplate } from '../types'

const configStore = useConfigurationStore()

// 搜索文本
const searchText = ref('')

// 展开的分类
const activeCategories = ref(configStore.componentLibrary.map(category => category.id))

// 过滤后的组件分类
const filteredCategories = computed(() => {
  if (!searchText.value) {
    return configStore.componentLibrary
  }
  
  const search = searchText.value.toLowerCase()
  
  return configStore.componentLibrary
    .map(category => {
      const filteredComponents = category.components.filter(component => 
        component.name.toLowerCase().includes(search)
      )
      
      return {
        ...category,
        components: filteredComponents
      }
    })
    .filter(category => category.components.length > 0)
})

// 拖拽开始
const onDragStart = (event: DragEvent, component: ComponentTemplate) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('component', JSON.stringify(component))
    event.dataTransfer.effectAllowed = 'copy'
  }
}
</script>

<style scoped>
.component-library {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--el-border-color-light);
}

.library-header {
  padding: 10px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.library-header h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 10px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  cursor: move;
  transition: all 0.3s;
}

.component-item:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.component-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.component-name {
  font-size: 12px;
  text-align: center;
}
</style>