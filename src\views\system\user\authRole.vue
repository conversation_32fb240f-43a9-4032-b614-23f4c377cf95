<template>
  <div class="p-2">
    <div class="panel">
      <h4 class="panel-title">基本信息</h4>
      <el-form :model="form" label-width="80px" :inline="true">
        <el-row :gutter="10">
          <el-col :span="2.5">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input v-model="form.nickName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="2.5">
            <el-form-item label="登录账号" prop="userName">
              <el-input v-model="form.userName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="panel">
      <h4 class="panel-title">角色信息</h4>
      <div>
        <el-table
          v-loading="loading"
          :row-key="getRowKey"
          @row-click="clickRow"
          ref="tableRef"
          @selection-change="handleSelectionChange"
          :data="roles.slice((pageNum - 1) * pageSize, pageNum * pageSize)"
        >
          <el-table-column label="序号" width="55" type="index" align="center">
            <template #default="scope">
              <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column type="selection" :reserve-selection="true" width="55" />
          <el-table-column label="角色编号" align="center" prop="roleId" />
          <el-table-column label="角色名称" align="center" prop="roleName" />
          <el-table-column label="权限字符" align="center" prop="roleKey" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="pageNum" v-model:limit="pageSize" />
        <div style="text-align: center;margin-left:-120px;margin-top:30px;">
          <el-button type="primary" @click="submitForm()">提交</el-button>
          <el-button @click="close()">返回</el-button>
        </div>
        <div></div>
      </div>
    </div>
  </div>
</template>

<script setup name="AuthRole" lang="ts">
import { RoleVO } from '@/api/system/role/types'
import { getAuthRole, updateAuthRole } from '@/api/system/user'
import { UserForm } from '@/api/system/user/types'
import { TableInstance } from 'element-plus'
import { ComponentInternalInstance } from 'vue'
const route = useRoute()
const { proxy } = getCurrentInstance() as ComponentInternalInstance

const loading = ref(true)
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(10)
const roleIds = ref<Array<string | number>>([])
const roles = ref<RoleVO[]>([])
const form = ref<Partial<UserForm>>({
  nickName: undefined,
  userName: '',
  userId: undefined,
})

const tableRef = ref<TableInstance>()

/** 单击选中行数据 */
const clickRow = (row: RoleVO) => {
  tableRef.value?.toggleRowSelection(row, true)
}
/** 多选框选中数据 */
const handleSelectionChange = (selection: RoleVO[]) => {
  roleIds.value = selection.map((item) => item.id)
}
/** 保存选中的数据编号 */
const getRowKey = (row: RoleVO): string => {
  return String(row.id)
}
/** 关闭按钮 */
const close = () => {
  const obj = { path: '/system/user' }
  proxy?.$tab.closeOpenPage(obj)
}
/** 提交按钮 */
const submitForm = async () => {
  const userId = form.value.id
  const rIds = roleIds.value
  await updateAuthRole({ userId: userId as string, roleIds: rIds })
  proxy?.$modal.msgSuccess('授权成功')
  close()
}

const getList = async () => {
  const userId = route.params && route.params.userId
  if (userId) {
    loading.value = true
    const res = await getAuthRole(userId as string)
    Object.assign(form.value, res.data.user)
    Object.assign(roles.value, res.data.roles)
    total.value = roles.value.length
    await nextTick(() => {
      roles.value.forEach((row) => {
        if (row?.flag) {
          tableRef.value?.toggleRowSelection(row, true)
        }
      })
    })
    loading.value = false
  }
}
onMounted(() => {
  getList()
})
</script>

<style scoped>
:deep(.el-card){
    background: rgba(2, 28, 51, 0.5);
    /* box-shadow:inset 0px 2px 28px  rgba(33, 148, 255, 0.5); */
    border:none;
}
:deep(.el-card__body){
    border: none;
}
:deep(.el-table, .el-table__expanded-cell ){
    background-color: transparent !important;
  }
:deep(.el-table__body tr, .el-table__body td) {
    padding: 0;
    height: 40px;
  }
:deep(.el-table tr) {
    border: none;
    background-color: transparent;
  }
:deep(.el-table th) {
    /* background-color: transparent; */
    background-color: rgba(7, 53, 92, 1);
    color: rgba(204, 204, 204, 1) !important;
    font-size: 14px;
    font-weight: 400;
  }
:deep(.el-table){
    --el-table-border-color: none;
  }
  :deep(.el-select__wrapper){

color: #fff!important;
background: rgb(3, 43, 82) !important;
box-shadow:0 0 0 0px #034374 inset !important;
border: 1px solid #034374 !important;
}
:deep(.el-select__placeholder){
color: #fff;
}
  /*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
:deep(.el-table__body-wrapper .el-table__row){
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
:deep(.el-table__body-wrapper .el-table__row:hover td ){
    background: none !important;
    /* 取消单元格背景色，确保整行背景色生效 */
  }
:deep(.el-table__header thead tr th) {
    background: rgba(7, 53, 92, 1) !important;
    color: #ffffff;
  }
:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
    color: #fff;
  }
:deep(.el-tree){
    background-color: transparent;
  }
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content){
    background-color:   #07355c;
  }
  :deep(.el-tree-node__expand-icon){
    color: #fff;
  }
  :deep(.el-tree-node__label){
    color: #fff;
  }
  :deep(.el-tree-node__content) {
    &:hover {
      background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    }
  }
:deep(.el-select__tags .el-tag--info){
    background-color:#153059 !important;
}
:deep(.el-tag.el-tag--info){
  color: #fff !important;
}
</style>
