import request from '@/utils/request'

enum Api {
  addbenifit = '/benifit/manage/add',
  getbenifitList = '/benifit/manage/list',
  editbenifit = '/benifit/manage/edit',
  deletebenifit = '/benifit/manage/delete',
}

// 新增效益配置
export const addbenifit = (data) => {
  return request({
    url: Api.addbenifit,
    method: 'post',
    data,
  })
}
// 获取效益列表
export const getbenifitList = (data) => {
  return request({
    url: Api.getbenifitList,
    method: 'post',
    data,
  })
}
// 编辑效益配置
export const editbenifit = (data) => {
  return request({
    url: Api.editbenifit,
    method: 'post',
    data,
  })
}

// 删除效益配置
export const deletebenifit = (data) => {
  return request({
    url: Api.deletebenifit,
    method: 'post',
    data,
  })
}
