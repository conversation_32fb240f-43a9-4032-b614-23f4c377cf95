<template>
  <g class="connection-line" @click="selectLine">
    <!-- 连接线路径 -->
    <path
      :d="pathData"
      :stroke="connection.style.strokeColor"
      :stroke-width="connection.style.strokeWidth"
      :stroke-dasharray="connection.style.strokeDasharray"
      fill="none"
      :class="{ 'animated': connection.animation?.enabled }"
      style="pointer-events: stroke; cursor: pointer;"
    />
    
    <!-- 箭头 -->
    <defs>
      <marker
        :id="`arrow-${connection.id}`"
        viewBox="0 0 10 10"
        :refX="connection.style.arrowSize"
        refY="3"
        :markerWidth="connection.style.arrowSize"
        :markerHeight="connection.style.arrowSize"
        orient="auto"
      >
        <path
          d="M0,0 L0,6 L9,3 z"
          :fill="connection.style.arrowColor"
        />
      </marker>
    </defs>
    
    <!-- 应用箭头到路径 -->
    <path
      :d="pathData"
      :stroke="connection.style.strokeColor"
      :stroke-width="connection.style.strokeWidth"
      :stroke-dasharray="connection.style.strokeDasharray"
      fill="none"
      :marker-end="`url(#arrow-${connection.id})`"
      :class="{ 'animated': connection.animation?.enabled }"
      style="pointer-events: stroke; cursor: pointer;"
    />
    
    <!-- 动画效果 -->
    <template v-if="connection.animation?.enabled">
      <!-- 流动动画 -->
      <circle
        v-if="connection.animation.type === 'flow'"
        r="3"
        :fill="connection.style.strokeColor"
        opacity="0.8"
      >
        <animateMotion
          :dur="`${connection.animation.speed}s`"
          repeatCount="indefinite"
          :path="pathData"
        />
      </circle>
      
      <!-- 脉冲动画 -->
      <path
        v-if="connection.animation.type === 'pulse'"
        :d="pathData"
        :stroke="connection.style.strokeColor"
        :stroke-width="connection.style.strokeWidth + 2"
        fill="none"
        opacity="0.5"
      >
        <animate
          attributeName="opacity"
          values="0.5;1;0.5"
          :dur="`${connection.animation.speed}s`"
          repeatCount="indefinite"
        />
      </path>
    </template>
  </g>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ComponentConnection, ConfigurationComponent } from '../types'

const props = defineProps<{
  connection: ComponentConnection
  sourceComponent: ConfigurationComponent | null
  targetComponent?: ConfigurationComponent | null
  tempEndPoint?: { x: number; y: number } | null
}>()

const emit = defineEmits<{
  select: [connection: ComponentConnection]
  delete: [connectionId: string]
}>()

// 计算路径数据
const pathData = computed(() => {
  if (!props.sourceComponent) return ''
  
  // 源点位置
  if (!props.sourceComponent) return ''
  
  // 源点位置
  const sourcePoint = getAnchorPoint(props.sourceComponent, props.connection.sourceAnchor.position)
  
  // 目标点位置
  let targetPoint: { x: number; y: number }
  
  if (props.tempEndPoint) {
    // 临时连接线，使用临时终点
    targetPoint = props.tempEndPoint
  } else if (props.targetComponent) {
    // 正常连接线，使用目标组件的锚点
    targetPoint = getAnchorPoint(props.targetComponent, props.connection.targetAnchor.position)
  } else {
    return ''
  }
  
  // 根据连接类型生成路径
  switch (props.connection.style.lineType) {
    case 'straight':
      return `M ${sourcePoint.x} ${sourcePoint.y} L ${targetPoint.x} ${targetPoint.y}`
    
    case 'curved':
      return generateCurvedPath(sourcePoint, targetPoint)
    
    case 'polyline':
      return generatePolylinePath(sourcePoint, targetPoint)
    
    default:
      return `M ${sourcePoint.x} ${sourcePoint.y} L ${targetPoint.x} ${targetPoint.y}`
  }
})

// 获取锚点位置
const getAnchorPoint = (component: ConfigurationComponent, position: string) => {
  const { x, y, width, height } = component
  
  switch (position) {
    case 'top-left':
      return { x, y }
    case 'top':
      return { x: x + width / 2, y }
    case 'top-right':
      return { x: x + width, y }
    case 'right':
      return { x: x + width, y: y + height / 2 }
    case 'bottom-right':
      return { x: x + width, y: y + height }
    case 'bottom':
      return { x: x + width / 2, y: y + height }
    case 'bottom-left':
      return { x, y: y + height }
    case 'left':
      return { x, y: y + height / 2 }
    case 'center':
    default:
      return { x: x + width / 2, y: y + height / 2 }
  }
}

// 生成曲线路径
const generateCurvedPath = (start: { x: number; y: number }, end: { x: number; y: number }) => {
  const dx = end.x - start.x
  const dy = end.y - start.y
  
  // 控制点偏移
  const offset = Math.abs(dx) * 0.5
  
  const cp1x = start.x + offset
  const cp1y = start.y
  const cp2x = end.x - offset
  const cp2y = end.y
  
  return `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`
}

// 生成折线路径
const generatePolylinePath = (start: { x: number; y: number }, end: { x: number; y: number }) => {
  const midX = (start.x + end.x) / 2
  
  return `M ${start.x} ${start.y} L ${midX} ${start.y} L ${midX} ${end.y} L ${end.x} ${end.y}`
}

// 选择连接线
const selectLine = () => {
  emit('select', props.connection)
}
</script>

<style scoped>
.connection-line {
  cursor: pointer;
}

.connection-line:hover path {
  stroke-width: 3;
  opacity: 0.8;
}

.animated {
  animation: dash 2s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -20;
  }
}
</style>