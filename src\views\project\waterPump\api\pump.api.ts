import request from '@/utils/request'

enum Api {
  list = '/project/waterPumpConfig/list',
  add = '/project/waterPumpConfig/add',
  saveRule = '/project/waterPumpConfig/edit',
  delete = '/project/waterPumpConfig/delete',
  ziList = '/project/subSystem/sublist',
  powerList = '/project/waterPumpPower/list',
  powerAdd = '/project/waterPumpPower/add',
  savePower = '/project/waterPumpPower/edit',
  powerDelete = '/project/waterPumpPower/delete',
  subSystList = '/project/subSystem/getDetail',
  waterAdd = 'project/waterPumpPower/water/edit',
  // 设备控制
  checkConfigList = '/control/checkConfig/list',
  checkConfigedit = '/control/checkConfig/edit',
  // 新增控制历史
  checkConfigAdd = '/control/history/add',
  // 查询设备列表
  getDeviceList = '/product/list',
  // 查询物模型点位
  getThingModelList = '/product/getThingModelByProductKey',
  // 新增水泵控制点位配置
  addWaterPumpCtrl = '/system/fanPumpControl/add',
  // 查询水泵控制点位配置
  getWaterPumpCtrlList = '/system/fanPumpControl/queryOneByCondition',
  // 修改水泵控制点位配置
  editWaterPumpCtrl = '/system/fanPumpControl/edit',
  getDeviceStatus = '/control/history/device/status',
  getControlHistory = '/control/history/list',
}
// 查询控制历史
export const getControlHistory = (data) => {
  return request({
    url: Api.getControlHistory,
    method: 'post',
    data,
  })
}
export const getDeviceStatus = (data) => {
  return request({
    url: Api.getDeviceStatus,
    method: 'post',
    data,
  })
}
// 删除水泵控制点位配置
export const editWaterPumpCtrl = (data) => {
  return request({
    url: Api.editWaterPumpCtrl,
    method: 'post',
    data,
  })
}
// 查询水泵控制点位配置
export const getWaterPumpCtrlList = (data) => {
  return request({
    url: Api.getWaterPumpCtrlList,
    method: 'post',
    data,
  })
}
// 新增水泵控制点位配置
export const addWaterPumpCtrl = (data) => {
  return request({
    url: Api.addWaterPumpCtrl,
    method: 'post',
    data,
  })
}
// 查询物模型点位
export const getThingModelList = (data) => {
  return request({
    url: Api.getThingModelList,
    method: 'post',
    data,
  })
}
// 查询设备列表
export const getDeviceList = (data) => {
  return request({
    url: Api.getDeviceList,
    method: 'post',
    data,
  })
}
// 设备控制新增
export const getCheckConfigAdd = (data) => {
  return request({
    url: Api.checkConfigAdd,
    method: 'post',
    data,
  })
}
// 设备编辑
export const getCheckConfigedit = (data) => {
  return request({
    url: Api.checkConfigedit,
    method: 'post',
    data,
  })
}

// 设备控制查询
export const getCheckConfigList = (data) => {
  return request({
    url: Api.checkConfigList,
    method: 'post',
    data,
  })
}
// 获取列表
export const getWaterPumpList = (data) => {
  return request({
    url: Api.list,
    method: 'post',
    data,
  })
}
// 获取子系统列表
export const getSubsystemList = (data) => {
  return request({
    url: Api.ziList,
    method: 'post',
    data,
  })
}
// 告警配置保存
export const savePumpList = (data) => {
  return request({
    url: data.id ? Api.saveRule : Api.add,
    method: 'post',
    data,
  })
}
// 删除
export const deleteWaterPump = (data) => {
  return request({
    url: Api.delete,
    method: 'post',
    data,
  })
}
// 查询功率列表
export const getwaterPumpPowerList = (data) => {
  return request({
    url: Api.powerList,
    method: 'post',
    data,
  })
}
// 功率保存
export const savepowerList = (data) => {
  return request({
    url: data.id ? Api.savePower : Api.powerAdd,
    method: 'post',
    data,
  })
}
// 功率删除
export const deletePower = (data) => {
  return request({
    url: Api.powerDelete,
    method: 'post',
    data,
  })
}
// 查询水量机组接口
export const subSystemDetail = (data) => {
  return request({
    url: Api.subSystList,
    method: 'post',
    data,
  })
}
// 水量保存
export const saveWater = (data) => {
  return request({
    url: Api.waterAdd,
    method: 'post',
    data,
  })
}
