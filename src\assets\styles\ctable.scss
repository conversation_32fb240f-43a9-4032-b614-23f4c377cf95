.el-card{
    background: rgba(2, 28, 51, 0.5);
    // box-shadow:inset 0px 2px 28px  rgba(33, 148, 255, 0.5);
    border:none;
}
.el-card__header{
    border: none;
}
.el-table, .el-table__expanded-cell {
    background-color: transparent !important;
  
  }
.el-table__body tr, .el-table__body td {
    padding: 0;
    height: 40px;
  }
.el-table tr {
    border: none;
    background-color: transparent;
  }
.el-table th {
    /* background-color: transparent; */
    background-color: rgba(7, 53, 92, 1);
    color: rgba(204, 204, 204, 1) !important;
    font-size: 14px;
    font-weight: 400;
  }
.el-table{
    --el-table-border-color: none;
  }
.el-table__cell {
    // color: rgb(255, 255, 255) !important;
  }
  /*选中边框 */
.el-table__body-wrapper .el-table__row:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    outline: 2px solid rgba(19, 89, 158, 1); /* 使用 outline 实现边框效果
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
.el-table__body-wrapper .el-table__row{
    /* 设置鼠标悬停时整行的背景色 */
    color: #fff;
  }
.el-table__body-wrapper .el-table__row:hover td {
    background: none !important;
    /* 取消单元格背景色，确保整行背景色生效 */
  }
.el-table__header thead tr th {
    background: rgba(7, 53, 92, 1) !important;
  
    color: #ffffff;
  }
// .el-table_1_column_1 .is-leaf .el-table__cell {
//     color: #fff;
//   }
  .el-tree{
    background-color: transparent;
  }
  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
    background-color:   #07355c;
  }
  .el-tree-node__expand-icon{
    color: #fff;
  }
  .el-tree-node__label{
    color: #fff;
  }
  .el-tree-node__content {
    &:hover {
      background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
    }
  }
.el-select__tags .el-tag--info{
    background-color:#153059 !important;
}
.el-tag.el-tag--info{
  color: #fff !important;
}