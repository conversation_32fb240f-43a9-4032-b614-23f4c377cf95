<!-- 循环水泵 -->
<template>
  <div>
    <!-- 弹框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="800px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form v-if="dialog.visible" ref="circulatingPumpData" :model="subData" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="型号" prop="">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入型号</span>
                </template>
                <template #suffix>
                  <span></span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流量" prop="">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入流量</span>
                </template>
                <template #suffix>
                  <span>m³/h</span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扬程" prop="">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入扬程</span>
                </template>
                <template #suffix>
                  <span>m</span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="转速" prop="">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入转速</span>
                </template>
                <template #suffix>
                  <span>r/min</span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="功率">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入功率</span>
                </template>
                <template #suffix>
                  <span>kw</span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="效率">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入效率</span>
                </template>
                <template #suffix>
                  <span>%</span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="必须汽蚀余量">
              <el-input v-model="subData">
                <template #prefix>
                  <span>请输入必须汽蚀余量</span>
                </template>
                <template #suffix>
                  <span>m</span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="circulatingPump" lang="ts">
import { ref } from 'vue'

const props = defineProps({
  dialog: {
    type: Object,
    required: true
  }
})
const subData=ref<[]>([])
const submitForm = () => {
}
const cancel = () => {
  props.dialog.visible = false
}
</script>
