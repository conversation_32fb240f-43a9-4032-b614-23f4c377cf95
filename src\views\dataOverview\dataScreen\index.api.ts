import request from '@/utils/request'

enum Api {
  // 查询大屏的所有标题
  getScreenTitle = '/project/powerPointConfig/list',
  // 查询标题下的所有内容
  getScreenContent = '/screenView/last/data',
  //   查询告警统计
  getAlarmCount = '/screenView/alert/stats',
  //   查询智能运维数据
  getSmartOperation = '/screenView/intelligent/operation',
  //   查询运行概览弹窗数据
  getRunOverview = '/device/dashboard/downSampling/list',
  //   查询水质分析弹窗数据
  getWaterQuality = '/device/dashboard/water/report',
  //   查询冷却系统概览弹窗数据
  //   查询告警统计弹窗数据
  getAlarmCountPopup = '/alert/list',
  //   查询水泵概览弹窗数据
  getLoopPump = '/screenView/pump/list',
  // 查询数据趋势总览
  getDataTrend = '/screenView/data/trend',
  // 查询智能运维弹窗数据
  getSmartOperationPopup = '/screenView/intelligent/operation/detail',
  getBackground = '/resource/oss/findByCondition',
}

// 查询大屏的所有标题
export const getScreenTitle = (data) => {
  return request({
    url: Api.getScreenTitle,
    method: 'post',
    data,
  })
}
// 查询标题下的所有内容
export const getScreenContent = (data) => {
  return request({
    url: Api.getScreenContent,
    method: 'post',
    data,
  })
}
// 查询告警统计
// 导出一个名为 getAlarmCount 的常量，它是一个函数，用于获取报警数量
export const getAlarmCount = (data) => {
  return request({
    url: Api.getAlarmCount,
    method: 'post',
    data,
  })
}
// 查询趋势总览数据
export const getDataTrend = (data) => {
  return request({
    url: Api.getDataTrend,
    method: 'post',
    data,
  })
}
export const getSmartOperation = (data) => {
  return request({
    url: Api.getSmartOperation,
    method: 'post',
    data,
  })
}

// 弹窗数据查询
// 查询运行概览弹窗数据
export const getRunOverview = (data) => {
  return request({
    url: Api.getRunOverview,
    method: 'post',
    data,
  })
}
// 查询水质分析弹窗数据
export const getWaterQuality = (data) => {
  return request({
    url: Api.getWaterQuality,
    method: 'post',
    data,
  })
}
// 查询冷却系统概览弹窗数据
export const getCoolingSystem = (data) => {}
// 查询循泵概览弹窗数据
export const getLoopPump = (data) => {
  return request({
    url: Api.getLoopPump,
    method: 'post',
    data,
  })
}
// 查询告警统计弹窗数据
export const getAlarmCountPopup = (data) => {
  return request({
    url: Api.getAlarmCountPopup,
    method: 'post',
    data,
  })
}
// 查询智能运维弹窗数据
export const getSmartOperationPopup = (data) => {
  return request({
    url: Api.getSmartOperationPopup,
    method: 'post',
    data,
  })
}

// 查询背景图片
export const getBackground = (data) => {
  return request({
    url: Api.getBackground,
    method: 'post',
    data,
  })
}
