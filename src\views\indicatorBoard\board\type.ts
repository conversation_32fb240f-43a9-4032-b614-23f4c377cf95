export interface powerPointConfigQuery {
  id?: number
  projectId: number
  configType: number
}
export interface powerPointConfigData {
  identifier: string
  name: string
  value?: string
  unit?: string
  deviceId: string
  remark?: string
}
export interface powerPointConfigVO {
  name: string
  id: number
  models: powerPointConfigData[]
}
export interface pointDataQuery {
  identifier: string
  name: string
  value?: string
  unit?: string
  deviceId: string
}
export interface pointData {
  id: number
  name: string
  value: number
  unit: string
  deviceId: string
  remark: string
  createTime: string
}
export interface pointDataVO {
  datas: pointData[]
}
